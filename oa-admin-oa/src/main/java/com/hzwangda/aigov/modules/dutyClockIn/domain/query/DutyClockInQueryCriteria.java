package com.hzwangda.aigov.modules.dutyClockIn.domain.query;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class DutyClockInQueryCriteria {
    @ApiModelProperty(value = "打卡日期")
    @Query(type = Query.Type.BETWEEN)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private List<Date> clockInTime;

    @ApiModelProperty(value = "部门id")
    private Long deptId;
}
