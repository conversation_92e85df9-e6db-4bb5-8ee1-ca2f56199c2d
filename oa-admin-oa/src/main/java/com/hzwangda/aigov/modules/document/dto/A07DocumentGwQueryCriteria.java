/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.modules.document.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-07-28
 **/
@Data
public class A07DocumentGwQueryCriteria {

    @ApiModelProperty(value = "状态(0:待办,1:已办,4或不传:查询我的待办、已办、申请)")
    private Integer status;

    @ApiModelProperty(value = "标题")
    @Query(type = Query.Type.INNER_LIKE)
    private String bt;

    @ApiModelProperty(value = "公文类型key")
    @Query(propName = "bpmProcessKey")
    private String processDefinitionKey;

    @Query(type = Query.Type.IN)
    private List<String> bpmInstanceId;

    private String bpmInstId;

    @ApiModelProperty(value = "待办创建开始结束时间")
    private List<String> timeRange;

    private String fileStatus;

    /**
     * 年份
     */
    private String year;
    /**
     * 代字
     */
    private String dz;
    /**
     * 公文文号序号
     */
    private String gwxh;
    /**
     * 来文文号序号
     */
    private String lwxh;

    /**
     * 收文文号序号
     */
    private String swxh;

    @Query(type = Query.Type.IN)
    private List<String> belongToDept;

    @ApiModelProperty("流程状态")
    @Query
    private String bpmStatus;

    @ApiModelProperty("拟稿人")
    private String ngr;
}
