package com.hzwangda.aigov.modules.duty.service;

import com.hzwangda.aigov.modules.duty.domain.dto.D15DutyLogCriteria;
import com.hzwangda.aigov.modules.duty.domain.entity.D15DutyLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface D15DutyLogService {
    Page<D15DutyLog> queryList(D15DutyLogCriteria criteria, Pageable pageable);

    Long save(D15DutyLog d15DutyLog);

    Long confirm(D15DutyLog d15DutyLog);

    D15DutyLog queryById(Long id);

    String delete(Long id);
}
