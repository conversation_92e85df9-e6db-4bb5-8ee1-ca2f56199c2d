package com.hzwangda.aigov.modules.document.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 收文单
 *
 * <AUTHOR>
 * @date 2021/6/21下午8:29
 */
@Data
public class A07DocumentSwDto extends BaseBpmDomain {

    @ApiModelProperty(value = "收文编号")
    private ReferenceNumber swbh;

    @ApiModelProperty(value = "来文编号")
    private String lwbh;

    @ApiModelProperty(value = "缓急")
    private String hj;

    @ApiModelProperty(value = "收文日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date swrq;

    @ApiModelProperty(value = "来文单位")
    private String lwdw;

    @ApiModelProperty(value = "收文类型")
    private String swlx;

    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "联系人")
    private String lxr;

    @ApiModelProperty(value = "联系人电话")
    private String lxrdh;

    @ApiModelProperty(value = "反馈日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date fkrq;

    @ApiModelProperty(value = "附件")
    private List<StorageBizDto> fj;

    @ApiModelProperty(value = "拟办意见")
    private String yjNb;

    @ApiModelProperty(value = "领导签批")
    private String yjLdqp;

    @ApiModelProperty(value = "办理结果")
    private String yjBljg;

    @ApiModelProperty(value = "流程任务相关信息")
    private FlowTaskInfoDto flowTaskInfoDto;
}
