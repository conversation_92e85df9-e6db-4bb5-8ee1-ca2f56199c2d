/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.modules.document.repository;

import com.hzwangda.aigov.modules.document.entity.A07DocumentFw;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-06-04
 **/
@Repository
public interface A07DocumentFwRepository extends JpaRepository<A07DocumentFw, Long>, JpaSpecificationExecutor<A07DocumentFw> {

    A07DocumentFw findFirstByBpmInstanceId(String procInstanceId);

    A07DocumentFw findFirstByOldId(Long oldId);

    @Modifying
    @Query("update A07DocumentFw m set m.createBy=?1,m.createTime=?2 where m.id=?3")
    void updateCreateByAndCreateTimeById(String createBy, Timestamp createTime, Long id);

    List<A07DocumentFw> findByOldIdNotNull();

    @Modifying
    void deleteByBpmInstanceId(String instanceId);

    @Modifying
    @Query("update A07DocumentFw m set m.zcwj='1' where m.bpmInstanceId=?1")
    void updateZcwjByBpmInstanceId(String procInstanceId);
}
