package com.hzwangda.aigov.modules.appControl.service.impl;

import com.hzwangda.aigov.modules.appControl.domain.Z08SelfHomePage;
import com.hzwangda.aigov.modules.appControl.dto.SelfHomePageCriteria;
import com.hzwangda.aigov.modules.appControl.repository.SelfHomePageRepository;
import com.hzwangda.aigov.modules.appControl.service.SelfAppSpaceService;
import com.hzwangda.aigov.modules.appControl.service.SelfHomePageService;
import com.wangda.oa.modules.workflow.enums.application.AppStatusEnum;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/23
 * @description
 */
@Service
@Slf4j
public class SelfHomePageServiceImpl implements SelfHomePageService {
    @Resource
    private SelfHomePageRepository selfHomePageRepository;

    @Resource
    private SelfAppSpaceService selfAppSpaceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Z08SelfHomePage addOrUpdateBasics(Z08SelfHomePage z08SelfHomePage) {
        Z08SelfHomePage app = new Z08SelfHomePage();
        if(z08SelfHomePage.getId() != null) {
            app = selfHomePageRepository.findById(z08SelfHomePage.getId()).orElseGet(Z08SelfHomePage::new);
        }else{
            app.setAppStatus(AppStatusEnum.ONLINE);
        }
        app.copy(z08SelfHomePage);
        Z08SelfHomePage save = selfHomePageRepository.save(app);
        return save;
    }

    @Override
    public Z08SelfHomePage getById(Long id) {
        Z08SelfHomePage Z08SelfHomePage = selfHomePageRepository.findById(id).orElseGet(Z08SelfHomePage::new);
        return Z08SelfHomePage;
    }

    @Override
    public Page<Z08SelfHomePage> queryListPage(SelfHomePageCriteria criteria, Pageable pageable) {
        Specification<Z08SelfHomePage> sp = (root, cq, cb) -> {
            Predicate pe = QueryHelp.getPredicate(root, criteria, cb);
            cq.orderBy(cb.asc(root.get("createDate")));
            return pe;
        };

        Page<Z08SelfHomePage> Z08SelfHomePagePage = selfHomePageRepository.findAll(sp, pageable);
        return Z08SelfHomePagePage;
    }

    @Override
    public Boolean delete(Long id) {
        selfHomePageRepository.deleteById(id);
        return true;
    }



    @Override
    public List<Z08SelfHomePage> getByZ08SelfHomePageName(String Z08SelfHomePageName) {
        List<Z08SelfHomePage> Z08SelfHomePages = selfHomePageRepository.findByNameLike("%" + Z08SelfHomePageName + "%");
        return Z08SelfHomePages;
    }

    @Override
    public List<Z08SelfHomePage> getByType(String type) {
        List<Z08SelfHomePage> Z08SelfHomePages = selfHomePageRepository.findByType(type);
        return Z08SelfHomePages;
    }

    @Override
    public List<Z08SelfHomePage> getByApplicationName(String applicationName) {
        List<Z08SelfHomePage> applications = selfHomePageRepository.findByNameLike("%" + applicationName + "%");
        return applications;
    }


    public String getHomePageLink(String systemType){
        List<Z08SelfHomePage> list = selfHomePageRepository.findByAppStatus(AppStatusEnum.ONLINE.getValue(), systemType);
        if(list.size()>0){
            List<Z08SelfHomePage> result= list.stream().filter(selfHomePage ->{return  selfAppSpaceService.validatePermission(selfHomePage.getAppliedRange(), SecurityUtils.getCurrentUsername());} ).sorted(Comparator.comparing(Z08SelfHomePage::getType)).collect(Collectors.toList());
            if(result.size()>0){
                return result.get(0).getInternalLink();
            }
        }
        return "/";
    }

}
