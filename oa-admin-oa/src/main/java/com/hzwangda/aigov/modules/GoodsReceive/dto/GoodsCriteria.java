package com.hzwangda.aigov.modules.GoodsReceive.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class GoodsCriteria {
    @ApiModelProperty(value = "物品分类")
    @Query(type = Query.Type.INNER_LIKE)
    private String wpfl;

    @ApiModelProperty(value = "用途分类")
    @Query(type = Query.Type.INNER_LIKE)
    private String ytfl;

    @ApiModelProperty(value = "名称型号")
    @Query(type = Query.Type.INNER_LIKE)
    private String mcxh;

    @ApiModelProperty(value = "开始时间")
    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> createTime;


}
