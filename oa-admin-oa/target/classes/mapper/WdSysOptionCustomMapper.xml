<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wangda.oa.modules.extension.mapper.WdSysOptionCustomMapper">
    <resultMap id="UserDataDto" type="com.wangda.oa.modules.extension.dto.UserDataDto">
        <id column="user_id" javaType="long" jdbcType="INTEGER" property="userId"/>
        <result column="dept_id" javaType="long" jdbcType="INTEGER" property="deptId"/>
        <result column="username" javaType="string" jdbcType="VARCHAR" property="username"/>
        <result column="nick_name" javaType="string" jdbcType="VARCHAR" property="nickName"/>
        <result column="email" javaType="string" jdbcType="VARCHAR" property="email"/>
        <result column="phone" javaType="string" jdbcType="VARCHAR" property="phone"/>
        <result column="abbr" javaType="string" jdbcType="VARCHAR" property="abbr"/>
        <result column="gender" javaType="string" jdbcType="VARCHAR" property="gender"/>
        <result column="is_admin" javaType="boolean" jdbcType="BIT" property="isAdmin"/>
        <result column="create_time" javaType="java.sql.Date" jdbcType="DATE" property="createTime"/>
        <result column="post" javaType="string" jdbcType="VARCHAR" property="post"/>
        <result column="user_type" javaType="INTEGER" jdbcType="INTEGER" property="userType"/>
        <result column="sort" javaType="INTEGER" jdbcType="INTEGER" property="sort"/>
        <collection property="concurDept" ofType="long" javaType="arraylist">
            <constructor>
                <arg column="concur_dept"/>
            </constructor>
        </collection>
        <collection property="userDeptIds" ofType="long" javaType="arraylist">
            <constructor>
                <arg column="user_deptIds"/>
            </constructor>
        </collection>
        <collection property="orderFlags" ofType="long" javaType="arraylist">
            <constructor>
                <arg column="order_flag"/>
            </constructor>
        </collection>
    </resultMap>

    <resultMap id="UnitUserPickDto" type="com.wangda.oa.modules.system.service.dto.UnitUserPickDto">
        <result column="user_id" javaType="long" jdbcType="INTEGER" property="userId"/>
        <result column="order_flag" javaType="long" jdbcType="INTEGER" property="orderFlag"/>
        <result column="dept_id" javaType="long" jdbcType="INTEGER" property="deptId"/>
        <result column="user_type" javaType="INTEGER" jdbcType="INTEGER" property="userType"/>
        <result column="user_name" javaType="string" jdbcType="VARCHAR" property="username"/>
        <result column="nick_name" javaType="string" jdbcType="VARCHAR" property="nickName"/>
        <result column="enabled" javaType="boolean" jdbcType="BIT" property="enabled"/>
    </resultMap>


    <select id="getUserInfoByDeptId" resultMap="UnitUserPickDto">
        SELECT
            ud.user_id,
            ud.dept_id,
            ud.order_flag,
            ud.enabled,
            su.nick_name,
            su.username,
            su.user_type
        FROM
            wd_users_depts ud
                right JOIN sys_user su ON ud.user_id = su.user_id
        WHERE
            ud.dept_id = #{deptId}
          and ud.enabled=true and su.enabled=true
    </select>

    <select id="getUserListByEnabled" resultMap="UserDataDto">
        SELECT depts.order_flag as order_flag, us.*, de.dept_id AS concur_dept, depts.dept_id AS user_deptIds
        FROM sys_user us
                     LEFT JOIN sys_users_depts de ON us.user_id = de.user_id
                     LEFT JOIN sys_dept dept ON dept.dept_id = de.dept_id
                     LEFT JOIN wd_users_depts depts ON depts.user_id = us.user_id  and depts.enabled=true
        WHERE us.enabled = true
        ORDER BY dept.dept_sort, depts.order_flag
    </select>

    <select id="getUserListByDeptIds" resultMap="UserDataDto">
        SELECT depts.order_flag as order_flag, us.*, de.dept_id AS concur_dept, depts.dept_id AS user_deptIds
        FROM sys_user us
                     LEFT JOIN sys_users_depts de ON us.user_id = de.user_id
                     LEFT JOIN sys_dept dept ON dept.dept_id = de.dept_id
                     LEFT JOIN wd_users_depts depts ON depts.user_id = us.user_id and depts.enabled=true
        WHERE us.enabled = true
        <if test="deptIds != null and deptIds.size > 0">
            and depts.dept_id in
            <foreach item="item" collection="deptIds" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY dept.dept_sort, depts.order_flag
    </select>

    <select id="getDocDeptUserListByDeptIds" resultMap="UserDataDto">
        SELECT depts.order_flag as order_flag, us.*, de.dept_id AS concur_dept, depts.dept_id AS user_deptIds
        FROM sys_user us
        LEFT JOIN sys_users_depts de ON us.user_id = de.user_id
        LEFT JOIN sys_dept dept ON dept.dept_id = de.dept_id
        LEFT JOIN wd_users_depts depts ON depts.user_id = us.user_id and depts.enabled=true
        WHERE us.enabled = true
        <if test="deptIds != null and deptIds.size > 0">
            and us.dept_id in
            <foreach item="item" collection="deptIds" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY dept.dept_sort, us.sort
    </select>

    <select id="getUserListByDeptId" resultMap="UserDataDto">
        SELECT depts.order_flag as order_flag, us.*, de.dept_id AS concur_dept, depts.dept_id AS user_deptIds FROM
        sys_user us LEFT JOIN sys_users_depts
        de ON us.user_id = de.user_id LEFT JOIN sys_dept dept ON dept.dept_id = de.dept_id LEFT JOIN wd_users_depts
        depts ON depts.user_id = us.user_id and depts.enabled=true
        WHERE us.enabled = true
        <if test="nickName != null and nickName != ''">
            and us.nick_name like
            CONCAT('%', #{nickName}, '%')
        </if>
        <if test="deptIds != null and deptIds.size > 0">
            and us.dept_id in
            <foreach item="item" collection="deptIds" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY dept.dept_sort, depts.order_flag
    </select>

    <select id="getLeadershipList" resultType="com.wangda.oa.modules.extension.dto.LeadershipListDto">
        SELECT distinct
        a.*
        FROM
        sys_user a
        LEFT JOIN sys_dept_user_position b ON a.user_id = b.user_id
        WHERE
        a.enabled = true
        AND b.type = 'hallLeadership'
        <if test="userIds != null and userIds.size > 0">
            and a.user_id in
            <foreach item="item" collection="userIds" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY
        a.sort
    </select>

    <select id="getUserListByUserIdAndEnabled" resultMap="UserDataDto">
        SELECT depts.order_flag as order_flag, us.*, de.dept_id AS concur_dept, depts.dept_id AS user_deptIds
        FROM sys_user us
        LEFT JOIN sys_users_depts de ON us.user_id = de.user_id
        LEFT JOIN sys_dept dept ON dept.dept_id = de.dept_id
        LEFT JOIN wd_users_depts depts ON depts.user_id = us.user_id and depts.enabled=true
        WHERE us.enabled = true
        <if test="users != null and users.size > 0">
            AND us.user_id in
            <foreach item="item" collection="users" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY dept.dept_sort, depts.order_flag
    </select>
</mapper>
