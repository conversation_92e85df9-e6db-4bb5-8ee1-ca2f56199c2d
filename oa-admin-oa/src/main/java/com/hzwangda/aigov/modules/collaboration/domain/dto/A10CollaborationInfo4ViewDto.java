package com.hzwangda.aigov.modules.collaboration.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationHandleLog;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationLink;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import com.wangda.oa.service.dto.LocalStorageDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @program: oa-mgt-server
 * @description:
 * @author: liux
 * @create: 2021-08-12 14:57
 */
@Data
@ApiModel(value = "返回工作协同-查看")
public class A10CollaborationInfo4ViewDto {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "主题")
    private String subject;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "相关附件")
    private List<LocalStorageDto> attachments;


    @ApiModelProperty(value = "附件")
    private List<StorageBiz> fj;

    @ApiModelProperty(value = "相关附件Id")
    private List<Long> fileId;

    @ApiModelProperty(value = "有意见的操作记录")
    private List<A10CollaborationHandleLog> remarks;
    //需处理成非空的操作记录

    @ApiModelProperty(value = "接收用户")
    private List<SimpleUserDto> assignees;
    //需处理SimpleUserDto类型

    @ApiModelProperty(value = "状态[草稿(draft),已发送(sent)]")
    private String status;

    @ApiModelProperty(value = "发送时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date sendTime;

    private A10CollaborationLink link;

    private String createBy;

    private Boolean hasCollection;
}
