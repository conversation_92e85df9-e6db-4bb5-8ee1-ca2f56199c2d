package com.hzwangda.aigov.modules.document.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.hzwangda.aigov.modules.document.service.Z01SysService;
import com.hzwangda.aigov.modules.rule.look.RuleExpressionExecuteCmdLook;
import com.wangda.oa.modules.system.domain.Dept;
import com.wangda.oa.modules.system.domain.Role;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.DeptRepositoryCustom;
import com.wangda.oa.modules.system.repository.RoleRepositoryCustom;
import com.wangda.oa.modules.system.repository.UserRepositoryCustom;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.system.service.mapstruct.UserMapper;
import com.wangda.oa.modules.workflow.dto.workflow.FlowUserRuleDto;
import com.wangda.oa.modules.workflow.utils.FlowableUtils;
import com.wangda.oa.modules.workflow.utils.UserRuleUtils;
import com.wangda.oa.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.ManagementService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class Z01SysServiceImpl implements Z01SysService {
    // 表元数据服务
    @Resource
    protected ManagementService managementService;
    @Resource
    private UserService userService;
    @Resource
    private UserMapper userMapper;
    @Resource
    private RoleRepositoryCustom roleRepositoryCustom;
    @Resource
    private UserRepositoryCustom userRepositoryCustom;
    @Resource
    private DeptRepositoryCustom deptRepositoryCustom;

    @Override
    public Object getUserList(String elStr, String username) {
        if(StringUtils.isBlank(username)) {
            username = SecurityUtils.getCurrentUsername();
        }
        // 判断是否自定义表达式
        if(FlowableUtils.isExpressionRule(elStr)) {
            List<UserDto> userDtos = managementService.executeCommand(new RuleExpressionExecuteCmdLook(elStr, username));
            return userDtos;
        }else {
            FlowUserRuleDto userRuleDto = UserRuleUtils.formatUserRule(elStr);
            if(CollUtil.isNotEmpty(userRuleDto.getUserList())) {
                // 用户
                List<String> userList = userRuleDto.getUserList();
                List<UserDto> byUserNames = userService.findByUserNames(userList);
                return byUserNames;
            }

            if(CollUtil.isNotEmpty(userRuleDto.getRoleList())) {
                // 角色
                List<String> roleList = userRuleDto.getRoleList();
                List<Role> roles = roleRepositoryCustom.findFirstByValueIn(roleList);
                List<Long> roleIds = roles.stream().map(Role::getId).collect(Collectors.toList());
                List<User> users = userRepositoryCustom.findRoleIdIn(roleIds);
                return userMapper.toDto(users);
            }

            if(CollUtil.isNotEmpty(userRuleDto.getUnitList())) {
                // 单位
                List<String> unitList = userRuleDto.getUnitList();
                List<Dept> depts = deptRepositoryCustom.findByBelongToDeptIn(unitList);
                List<Long> collect = depts.stream().map(Dept::getId).collect(Collectors.toList());
                List<UserDto> byDeptIds = userService.findByDeptIds(collect, 1);
                return byDeptIds;
            }
            if(CollUtil.isNotEmpty(userRuleDto.getOrgList())) {
                // TODO: 不做处理
            }
        }
        return null;
    }
}
