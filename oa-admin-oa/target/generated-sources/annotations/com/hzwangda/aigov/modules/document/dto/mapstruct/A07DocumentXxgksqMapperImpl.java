package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07DocumentXxgksqDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentXxgksq;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A07DocumentXxgksqMapperImpl implements A07DocumentXxgksqMapper {

    @Override
    public List<A07DocumentXxgksqDto> toDto(List<A07DocumentXxgksq> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A07DocumentXxgksqDto> list = new ArrayList<A07DocumentXxgksqDto>( entityList.size() );
        for ( A07DocumentXxgksq a07DocumentXxgksq : entityList ) {
            list.add( toDto( a07DocumentXxgksq ) );
        }

        return list;
    }

    @Override
    public A07DocumentXxgksq toEntity(A07DocumentXxgksqDto dto) {
        if ( dto == null ) {
            return null;
        }

        A07DocumentXxgksq a07DocumentXxgksq = new A07DocumentXxgksq();

        a07DocumentXxgksq.setBelongToDept( dto.getBelongToDept() );
        a07DocumentXxgksq.setBpmInstanceId( dto.getBpmInstanceId() );
        a07DocumentXxgksq.setBpmProcessKey( dto.getBpmProcessKey() );
        a07DocumentXxgksq.setBpmStatus( dto.getBpmStatus() );
        a07DocumentXxgksq.setBpmSubject( dto.getBpmSubject() );
        a07DocumentXxgksq.setCreateBy( dto.getCreateBy() );
        a07DocumentXxgksq.setCreateTime( dto.getCreateTime() );
        a07DocumentXxgksq.setId( dto.getId() );
        List<String> list = dto.getParticipateUser();
        if ( list != null ) {
            a07DocumentXxgksq.setParticipateUser( new ArrayList<String>( list ) );
        }
        a07DocumentXxgksq.setUpdateBy( dto.getUpdateBy() );
        a07DocumentXxgksq.setUpdateTime( dto.getUpdateTime() );
        a07DocumentXxgksq.setVersion( dto.getVersion() );
        a07DocumentXxgksq.setFkrq( dto.getFkrq() );
        a07DocumentXxgksq.setFrDzyx( dto.getFrDzyx() );
        a07DocumentXxgksq.setFrFddbrhfzr( dto.getFrFddbrhfzr() );
        a07DocumentXxgksq.setFrLxdh( dto.getFrLxdh() );
        a07DocumentXxgksq.setFrLxrxm( dto.getFrLxrxm() );
        a07DocumentXxgksq.setFrMc( dto.getFrMc() );
        a07DocumentXxgksq.setFrYyzzxx( dto.getFrYyzzxx() );
        a07DocumentXxgksq.setFrZzjgdm( dto.getFrZzjgdm() );
        a07DocumentXxgksq.setGklx( dto.getGklx() );
        a07DocumentXxgksq.setGmDzyx( dto.getGmDzyx() );
        a07DocumentXxgksq.setGmGzdw( dto.getGmGzdw() );
        a07DocumentXxgksq.setGmLxdh( dto.getGmLxdh() );
        a07DocumentXxgksq.setGmTxdz( dto.getGmTxdz() );
        a07DocumentXxgksq.setGmXm( dto.getGmXm() );
        a07DocumentXxgksq.setGmYzbm( dto.getGmYzbm() );
        a07DocumentXxgksq.setGmZjhm( dto.getGmZjhm() );
        a07DocumentXxgksq.setGmZjmc( dto.getGmZjmc() );
        a07DocumentXxgksq.setHqxxfs( dto.getHqxxfs() );
        a07DocumentXxgksq.setJsqtfs( dto.getJsqtfs() );
        a07DocumentXxgksq.setSfjmfy( dto.getSfjmfy() );
        a07DocumentXxgksq.setSlsj( dto.getSlsj() );
        a07DocumentXxgksq.setSqrsf( dto.getSqrsf() );
        a07DocumentXxgksq.setSxxxnrms( dto.getSxxxnrms() );
        a07DocumentXxgksq.setSxxxytms( dto.getSxxxytms() );
        Set<String> set = dto.getSxxxzdtgfs();
        if ( set != null ) {
            a07DocumentXxgksq.setSxxxzdtgfs( new HashSet<String>( set ) );
        }
        a07DocumentXxgksq.setYjCsbl( dto.getYjCsbl() );
        a07DocumentXxgksq.setYjFgc( dto.getYjFgc() );
        a07DocumentXxgksq.setYjZrfh( dto.getYjZrfh() );

        return a07DocumentXxgksq;
    }

    @Override
    public List<A07DocumentXxgksq> toEntity(List<A07DocumentXxgksqDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A07DocumentXxgksq> list = new ArrayList<A07DocumentXxgksq>( dtoList.size() );
        for ( A07DocumentXxgksqDto a07DocumentXxgksqDto : dtoList ) {
            list.add( toEntity( a07DocumentXxgksqDto ) );
        }

        return list;
    }

    @Override
    public A07DocumentXxgksqDto toDto(A07DocumentXxgksq entity) {
        if ( entity == null ) {
            return null;
        }

        A07DocumentXxgksqDto a07DocumentXxgksqDto = new A07DocumentXxgksqDto();

        a07DocumentXxgksqDto.setBelongToDept( entity.getBelongToDept() );
        a07DocumentXxgksqDto.setBpmInstanceId( entity.getBpmInstanceId() );
        a07DocumentXxgksqDto.setBpmProcessKey( entity.getBpmProcessKey() );
        a07DocumentXxgksqDto.setBpmStatus( entity.getBpmStatus() );
        a07DocumentXxgksqDto.setBpmSubject( entity.getBpmSubject() );
        a07DocumentXxgksqDto.setCreateBy( entity.getCreateBy() );
        a07DocumentXxgksqDto.setCreateTime( entity.getCreateTime() );
        a07DocumentXxgksqDto.setId( entity.getId() );
        List<String> list = entity.getParticipateUser();
        if ( list != null ) {
            a07DocumentXxgksqDto.setParticipateUser( new ArrayList<String>( list ) );
        }
        a07DocumentXxgksqDto.setUpdateBy( entity.getUpdateBy() );
        a07DocumentXxgksqDto.setUpdateTime( entity.getUpdateTime() );
        a07DocumentXxgksqDto.setVersion( entity.getVersion() );
        a07DocumentXxgksqDto.setFkrq( entity.getFkrq() );
        a07DocumentXxgksqDto.setFrDzyx( entity.getFrDzyx() );
        a07DocumentXxgksqDto.setFrFddbrhfzr( entity.getFrFddbrhfzr() );
        a07DocumentXxgksqDto.setFrLxdh( entity.getFrLxdh() );
        a07DocumentXxgksqDto.setFrLxrxm( entity.getFrLxrxm() );
        a07DocumentXxgksqDto.setFrMc( entity.getFrMc() );
        a07DocumentXxgksqDto.setFrYyzzxx( entity.getFrYyzzxx() );
        a07DocumentXxgksqDto.setFrZzjgdm( entity.getFrZzjgdm() );
        a07DocumentXxgksqDto.setGklx( entity.getGklx() );
        a07DocumentXxgksqDto.setGmDzyx( entity.getGmDzyx() );
        a07DocumentXxgksqDto.setGmGzdw( entity.getGmGzdw() );
        a07DocumentXxgksqDto.setGmLxdh( entity.getGmLxdh() );
        a07DocumentXxgksqDto.setGmTxdz( entity.getGmTxdz() );
        a07DocumentXxgksqDto.setGmXm( entity.getGmXm() );
        a07DocumentXxgksqDto.setGmYzbm( entity.getGmYzbm() );
        a07DocumentXxgksqDto.setGmZjhm( entity.getGmZjhm() );
        a07DocumentXxgksqDto.setGmZjmc( entity.getGmZjmc() );
        a07DocumentXxgksqDto.setHqxxfs( entity.getHqxxfs() );
        a07DocumentXxgksqDto.setJsqtfs( entity.getJsqtfs() );
        a07DocumentXxgksqDto.setSfjmfy( entity.getSfjmfy() );
        a07DocumentXxgksqDto.setSlsj( entity.getSlsj() );
        a07DocumentXxgksqDto.setSqrsf( entity.getSqrsf() );
        a07DocumentXxgksqDto.setSxxxnrms( entity.getSxxxnrms() );
        a07DocumentXxgksqDto.setSxxxytms( entity.getSxxxytms() );
        Set<String> set = entity.getSxxxzdtgfs();
        if ( set != null ) {
            a07DocumentXxgksqDto.setSxxxzdtgfs( new HashSet<String>( set ) );
        }
        a07DocumentXxgksqDto.setYjCsbl( entity.getYjCsbl() );
        a07DocumentXxgksqDto.setYjFgc( entity.getYjFgc() );
        a07DocumentXxgksqDto.setYjZrfh( entity.getYjZrfh() );

        splitTime( entity, a07DocumentXxgksqDto );

        return a07DocumentXxgksqDto;
    }
}
