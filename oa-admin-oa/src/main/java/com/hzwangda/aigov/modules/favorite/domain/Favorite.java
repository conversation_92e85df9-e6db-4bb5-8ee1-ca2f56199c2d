package com.hzwangda.aigov.modules.favorite.domain;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@Entity
@Table
public class Favorite extends BaseDomain {

    @ApiModelProperty("logo")
    private String logo;

    @ApiModelProperty("url")
    private String url;

    @ApiModelProperty("流程实例id")
    private String procInstanceId;

    @ApiModelProperty("0关注1收藏")
    @NotNull
    private Integer type;

    @Transient
    private String title;

    @Transient
    private Date date;

    @Transient
    private String handle;


    @Transient
    private List<String> handlers;
}
