package com.hzwangda.aigov.modules.document.entity;

import com.hzwangda.aigov.modules.archive.entity.A07FileArchive;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Immutable
@Subselect("select * from gw_view")
@Entity
@Data
public class A07DocumentGwView extends BaseBpmDomain implements Serializable {
    @Id
    private Long id;


    @Column(name = "bt")
    @ApiModelProperty(value = "标题")
    private String bt;

    @Column(name = "file_status")
    @ApiModelProperty(value = "归档状态 0待归档，1预归档,2暂不归档，3不归档,4已归档，5归档中，-1移交失败")
    private String fileStatus;

    @ApiModelProperty(value = "类型")
    private String moduleType;

    @Column(name = "cjr")
    @ApiModelProperty(value = "创建人")
    private String cjr;

    @ApiModelProperty(value = "文号")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "wh_id", referencedColumnName = "id")
    private ReferenceNumber gwwh;

    @ApiModelProperty(value = "来文文号")
    private String lwwh;

    @ApiModelProperty(value = "公文类型（0收文1发文）")
    @Column(name = "gw_type")
    private String gwType;

    @ApiModelProperty(value = "业务表名）")
    @Column(name = "tablename")
    private String tablename;

    @OneToOne
    @JoinColumn(name = "id", referencedColumnName = "doc_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private A07FileArchive archive;

    private String belongToDept;
}
