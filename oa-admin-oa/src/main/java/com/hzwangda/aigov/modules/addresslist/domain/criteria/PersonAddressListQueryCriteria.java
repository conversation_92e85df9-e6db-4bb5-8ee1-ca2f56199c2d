/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.modules.addresslist.domain.criteria;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 个人通讯录查询
 * @updateTime 2022/8/5 14:49
 */

@Data
public class PersonAddressListQueryCriteria {

    @ApiModelProperty(value = "姓名")
    @Query(type = Query.Type.INNER_LIKE)
    private String nickName;

    @ApiModelProperty(value = "分组id")
    @Query(type = Query.Type.EQUAL)
    private Long groupId;
}
