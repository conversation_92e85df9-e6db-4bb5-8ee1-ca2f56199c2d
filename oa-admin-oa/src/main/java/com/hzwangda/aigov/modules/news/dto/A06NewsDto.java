package com.hzwangda.aigov.modules.news.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.hzwangda.aigov.modules.document.dto.FlowTaskInfoDto;
import com.hzwangda.aigov.modules.news.entity.A06NewsAnnouncement;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 动态信息-主表
 *
 * <AUTHOR>
 * @date 2021/6/9 上午9:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class A06NewsDto extends BaseBpmDomain {

    @ApiModelProperty(value = "类型(0:电子公告,1:今日择报,2:工作交流,3:教育参阅)")
    private Integer infoType;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "发布部门")
    private String deptName;

    @ApiModelProperty(value = "发送类型(0:普发,1:部分人员),目前都普发")
    private Integer sendType;

    @ApiModelProperty(value = "发布时间")
    @JSONField(format = "yyyy-MM-dd")
    private Date releaseDate;

    @ApiModelProperty(value = "正文")
    private String content;

    @ApiModelProperty(value = "json格式的意见")
    private String remark;

    @ApiModelProperty(value = "引导图片")
    private StorageBizDto guidePicture;

    @ApiModelProperty(value = "附件")
    private List<StorageBizDto> attachments;

    @ApiModelProperty(value = "是否已读(0:未读,1:已读)")
    private Integer read;

    @JSONField(serialzeFeatures = SerializerFeature.WriteDateUseDateFormat)
    private A06NewsAnnouncement announcement;

    @ApiModelProperty(value = "流程任务相关信息")
    private FlowTaskInfoDto flowTaskInfoDto;

    private Boolean revoke;


}
