package com.hzwangda.aigov.modules.GoodsReceive.service;


import com.hzwangda.aigov.modules.GoodsReceive.dto.GoodsCriteria;
import com.hzwangda.aigov.modules.GoodsReceive.dto.QueryCriteria;
import org.springframework.data.domain.Pageable;

public interface GoodsReceiveService {
    public Object queryList(QueryCriteria queryCriteria, Pageable pageable);


    public Object goodsPageList(GoodsCriteria queryCriteria, Pageable pageable);

    /**
     * 物品领用，减库存
     * @param processInstanceId
     * @return
     */
    String adopt(String processInstanceId);

    Object queryCount(GoodsCriteria queryCriteria, Pageable pageable);

    void restore(String processInstanceId);

    Object getByMcxh(String mcxh);
}
