package com.hzwangda.aigov.modules.document.dto;

import com.hzwangda.openserch.annotation.WdSearchId;
import com.hzwangda.openserch.annotation.WdSearchMetaData;
import com.hzwangda.openserch.annotation.field.CreateTimeField;
import com.hzwangda.openserch.annotation.field.TitleField;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@WdSearchMetaData(indexName = "sw")
public class SwSearchDto {

    @WdSearchId
    private String id;

    @TitleField
    private String bt;

    @CreateTimeField
    private Date createTime;
    private List<String> permission;

}
