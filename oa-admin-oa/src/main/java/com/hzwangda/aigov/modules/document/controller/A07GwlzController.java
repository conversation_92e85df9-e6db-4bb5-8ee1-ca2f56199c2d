package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.dto.A07DocumentGwlzDto;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwlzQueryCriteria;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwlzUserDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlz;
import com.hzwangda.aigov.modules.document.service.A07GwlzService;
import com.hzwangda.aigov.modules.workflow.bo.GwlzUserListBO;
import com.wangda.oa.annotation.Log;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "公文交换（流转）管理")
@RequestMapping("/api/aigov/document/gwlz")
@CrossOrigin
public class A07GwlzController {

    private final A07GwlzService a07GwlzService;

    @Log("公文流转列表")
    @ApiOperation("公文流转列表")
    @GetMapping(value = "/getGwlzList")
    public ResponseEntity<Object> getGwlzList(A07DocumentGwlzQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07GwlzService.gwlzList(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping("/saveGwlz")
    @Log("新增或修改公文流转")
    @ApiOperation("新增或修改公文流转")
    public ResponseEntity<Object> saveGwlz(@Validated @RequestBody A07DocumentGwlz resources) {
        return new ResponseEntity<>(a07GwlzService.createOrUpdate(resources), HttpStatus.OK);
    }

    @Log("用户公文流转列表")
    @ApiOperation("用户公文流转列表")
    @GetMapping(value = "/getGwlzUserList")
    public ResponseEntity<Object> getGwlzUserList(GwlzUserListBO gwlzUserListBO) {
        return new ResponseEntity<>(a07GwlzService.getGwlzUserList(gwlzUserListBO), HttpStatus.CREATED);
    }

    @Log("公文流转详情")
    @ApiOperation("公文流转详情")
    @PostMapping(value = "/getGwlzInfo")
    public ResponseEntity<A07DocumentGwlz> getGwlzInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07GwlzService.getGwlzInfo(id), HttpStatus.OK);
    }

    @Log("公文流转详情-转换")
    @ApiOperation("公文流转详情-转换")
    @PostMapping(value = "/getGwlzInfoDto")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<A07DocumentGwlzDto> getGwlzInfoDto(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07GwlzService.getGwlzInfoDto(id), HttpStatus.OK);
    }

    @Log("公文流转用户详情")
    @ApiOperation("公文流转用户详情")
    @PostMapping(value = "/getGwlzUserInfo")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<A07DocumentGwlzUserDto> getGwlzUserInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07GwlzService.getGwlzUserInfo(id), HttpStatus.OK);
    }

    @PostMapping("/gwlzQs")
    @Log("公文流转签收")
    @ApiOperation("公文流转签收")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Object> gwlzQs(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07GwlzService.gwlzQs(id), HttpStatus.OK);
    }

    @PostMapping("/gwlzCy")
    @Log("公文流转查阅")
    @ApiOperation("公文流转查阅")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Object> gwlzCy(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07GwlzService.gwlzCy(id), HttpStatus.OK);
    }

    @PostMapping("/gwlzLwSc")
    @Log("公文流转来文删除")
    @ApiOperation("公文流转来文删除")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Object> gwlzLwSc(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07GwlzService.gwlzLwSc(id), HttpStatus.OK);
    }

    @PostMapping("/gwlzSc")
    @Log("公文流转删除")
    @ApiOperation("公文流转删除")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Object> gwlzSc(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07GwlzService.gwlzSc(id), HttpStatus.OK);
    }

    @PostMapping("/gwlzCh")
    @Log("公文流转撤回")
    @ApiOperation("公文流转撤回")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Object> gwlzCh(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07GwlzService.gwlzCh(id), HttpStatus.OK);
    }

}
