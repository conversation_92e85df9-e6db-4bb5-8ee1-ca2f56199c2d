package com.hzwangda.aigov.modules.zjedu.goodsmanagement.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.goodsmanagement.domain.dto.D06GoodsSuppliesDto;
import com.hzwangda.aigov.oa.domain.A05OfficeSuppliesDetail;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class D06GoodsSuppliesDetailMapperImpl implements D06GoodsSuppliesDetailMapper {

    @Override
    public D06GoodsSuppliesDto toDto(A05OfficeSuppliesDetail entity) {
        if ( entity == null ) {
            return null;
        }

        D06GoodsSuppliesDto d06GoodsSuppliesDto = new D06GoodsSuppliesDto();

        d06GoodsSuppliesDto.setCreateBy( entity.getCreateBy() );
        d06GoodsSuppliesDto.setCreateTime( entity.getCreateTime() );
        d06GoodsSuppliesDto.setUpdateTime( entity.getUpdateTime() );
        d06GoodsSuppliesDto.setCategory( entity.getCategory() );
        d06GoodsSuppliesDto.setGoods( entity.getGoods() );
        d06GoodsSuppliesDto.setGoodsCategory( entity.getGoodsCategory() );
        d06GoodsSuppliesDto.setGoodsName( entity.getGoodsName() );
        d06GoodsSuppliesDto.setGoodsNum( entity.getGoodsNum() );
        d06GoodsSuppliesDto.setGoodsUnit( entity.getGoodsUnit() );
        d06GoodsSuppliesDto.setId( entity.getId() );

        return d06GoodsSuppliesDto;
    }

    @Override
    public List<D06GoodsSuppliesDto> toDto(List<A05OfficeSuppliesDetail> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<D06GoodsSuppliesDto> list = new ArrayList<D06GoodsSuppliesDto>( entityList.size() );
        for ( A05OfficeSuppliesDetail a05OfficeSuppliesDetail : entityList ) {
            list.add( toDto( a05OfficeSuppliesDetail ) );
        }

        return list;
    }

    @Override
    public A05OfficeSuppliesDetail toEntity(D06GoodsSuppliesDto dto) {
        if ( dto == null ) {
            return null;
        }

        A05OfficeSuppliesDetail a05OfficeSuppliesDetail = new A05OfficeSuppliesDetail();

        a05OfficeSuppliesDetail.setCreateBy( dto.getCreateBy() );
        a05OfficeSuppliesDetail.setCreateTime( dto.getCreateTime() );
        a05OfficeSuppliesDetail.setUpdateTime( dto.getUpdateTime() );
        a05OfficeSuppliesDetail.setCategory( dto.getCategory() );
        a05OfficeSuppliesDetail.setGoods( dto.getGoods() );
        a05OfficeSuppliesDetail.setGoodsCategory( dto.getGoodsCategory() );
        a05OfficeSuppliesDetail.setGoodsName( dto.getGoodsName() );
        a05OfficeSuppliesDetail.setGoodsNum( dto.getGoodsNum() );
        a05OfficeSuppliesDetail.setGoodsUnit( dto.getGoodsUnit() );
        a05OfficeSuppliesDetail.setId( dto.getId() );

        return a05OfficeSuppliesDetail;
    }

    @Override
    public List<A05OfficeSuppliesDetail> toEntity(List<D06GoodsSuppliesDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A05OfficeSuppliesDetail> list = new ArrayList<A05OfficeSuppliesDetail>( dtoList.size() );
        for ( D06GoodsSuppliesDto d06GoodsSuppliesDto : dtoList ) {
            list.add( toEntity( d06GoodsSuppliesDto ) );
        }

        return list;
    }
}
