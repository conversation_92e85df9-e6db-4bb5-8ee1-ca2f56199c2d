package com.hzwangda.aigov.modules.addresslist.service;

import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.extension.bo.CommonControlsBO;
import com.wangda.oa.modules.extension.bo.UserListBO;
import com.wangda.oa.modules.extension.dto.org.UserListDto;

import java.util.List;

public interface AddressListService {
    /**
     * 查询组织架构
     * @return
     */
    ResultJson<Object> getOrgList(CommonControlsBO bo);

    ResultJson<Object> getOrgUserList(CommonControlsBO bo);

    ResultJson<List<UserListDto>> getUserList(UserListBO bo);

}
