package com.hzwangda.aigov.modules.document.dto;

import com.hzwangda.aigov.modules.document.entity.SysDeptUserMain;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 协同
 *
 * <AUTHOR>
 * @date 2021/6/21下午8:29
 */
@Data
public class A08GzxtDto extends BaseBpmDomain {

    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "接收单位/人员")
    private SysDeptUserMain jsry;

    @ApiModelProperty(value = "内容")
    private String nr;

    @ApiModelProperty(value = "附件")
    private List<StorageBizDto> fj;

    @ApiModelProperty(value = "流转意见")
    private String yjLz;

    @ApiModelProperty(value = "流程任务相关信息")
    private FlowTaskInfoDto flowTaskInfoDto;
}
