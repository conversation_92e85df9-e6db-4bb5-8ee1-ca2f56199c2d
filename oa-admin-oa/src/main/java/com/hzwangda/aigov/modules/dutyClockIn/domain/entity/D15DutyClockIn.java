package com.hzwangda.aigov.modules.dutyClockIn.domain.entity;

import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Entity
@Data
@Table(name = "d15_duty_clock_in")
@ApiModel("值班打卡")
@DynamicInsert
public class D15DutyClockIn extends BaseDomain {

    @ApiModelProperty(value = "用户名")
    @Column(name = "username")
    private String username;

    @ApiModelProperty(value = "真实姓名")
    @Column(name = "nick_name")
    private String nickName;

    @ApiModelProperty(value = "内容")
    @Column(name = "content",columnDefinition = "text")
    private String content;

    @ApiModelProperty(value = "打卡时间")
    @Column(name = "clock_in_time")
    @Temporal(TemporalType.TIMESTAMP)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date clockInTime;

    @ApiModelProperty(value = "打卡地址")
    @Column(name = "address")
    private String address;

    @ApiModelProperty(value = "打卡图片")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='D15DutyClockIn.fj'")
    private List<StorageBiz> fj;

    @ApiModelProperty(value = "经度")
    @Column(name = "longitude")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    @Column(name = "latitude")
    private String latitude;

    @ApiModelProperty(value = "地图json")
    @Column( name="geolocation_json", columnDefinition = "text")
    private String geolocationJSON;

    @Override
    public void preUpdate() {
        super.preUpdate();
        if(!CollectionUtils.isEmpty(this.fj)) {
            for(StorageBiz storageBiz : this.fj) {
                if(Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                    storageBiz.setBizType("D15DutyClockIn.fj");
                }
            }
        }
    }
    @Override
    public void preCreateEntity() {
        super.preCreateEntity();
        if(!CollectionUtils.isEmpty(this.fj)) {
            for(StorageBiz storageBiz : this.fj) {
                if(Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                    storageBiz.setBizType("D15DutyClockIn.fj");
                }
            }
        }
    }
    public void setFj(List<StorageBiz> fj) {
        if(fj != null) {
            if(this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }
}
