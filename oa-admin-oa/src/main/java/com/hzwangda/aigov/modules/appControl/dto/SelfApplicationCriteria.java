package com.hzwangda.aigov.modules.appControl.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2022/8/23 11:09
 * @description:
 */
@Getter
@Setter
@ApiModel(value = "应用请求对象")
public class SelfApplicationCriteria {

    @ApiModelProperty(value = "应用空间id")
    @Query(joinName = "appSpace", propName = "id", join = Query.Join.INNER, type = Query.Type.EQUAL)
    private Long appSpaceId;

    @Query(type = Query.Type.INNER_LIKE)
    @ApiModelProperty("名称")
    private String name;

    @Query(type = Query.Type.EQUAL)
    @ApiModelProperty("分类")
    private String type;
}
