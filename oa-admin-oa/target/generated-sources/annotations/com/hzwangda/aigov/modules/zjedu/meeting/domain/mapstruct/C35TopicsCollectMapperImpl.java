package com.hzwangda.aigov.modules.zjedu.meeting.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35TopicsCollectDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35TopicsCollect;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class C35TopicsCollectMapperImpl implements C35TopicsCollectMapper {

    @Override
    public C35TopicsCollectDto toDto(C35TopicsCollect entity) {
        if ( entity == null ) {
            return null;
        }

        C35TopicsCollectDto c35TopicsCollectDto = new C35TopicsCollectDto();

        c35TopicsCollectDto.setBelongToDept( entity.getBelongToDept() );
        c35TopicsCollectDto.setBpmInstanceId( entity.getBpmInstanceId() );
        c35TopicsCollectDto.setBpmProcessKey( entity.getBpmProcessKey() );
        c35TopicsCollectDto.setBpmStatus( entity.getBpmStatus() );
        c35TopicsCollectDto.setBpmSubject( entity.getBpmSubject() );
        c35TopicsCollectDto.setCreateBy( entity.getCreateBy() );
        c35TopicsCollectDto.setCreateTime( entity.getCreateTime() );
        List<String> list = entity.getParticipateUser();
        if ( list != null ) {
            c35TopicsCollectDto.setParticipateUser( new ArrayList<String>( list ) );
        }
        c35TopicsCollectDto.setUpdateBy( entity.getUpdateBy() );
        c35TopicsCollectDto.setUpdateTime( entity.getUpdateTime() );
        c35TopicsCollectDto.setVersion( entity.getVersion() );
        c35TopicsCollectDto.setContext( entity.getContext() );
        c35TopicsCollectDto.setDescription( entity.getDescription() );
        List<StorageBiz> list1 = entity.getFiles();
        if ( list1 != null ) {
            c35TopicsCollectDto.setFiles( new ArrayList<StorageBiz>( list1 ) );
        }
        c35TopicsCollectDto.setId( entity.getId() );
        c35TopicsCollectDto.setIsFast( entity.getIsFast() );
        c35TopicsCollectDto.setIsLeaderAgree( entity.getIsLeaderAgree() );
        c35TopicsCollectDto.setIsSecret( entity.getIsSecret() );
        c35TopicsCollectDto.setMeetingTopics( entity.getMeetingTopics() );
        c35TopicsCollectDto.setName( entity.getName() );
        c35TopicsCollectDto.setOpinion1( entity.getOpinion1() );
        c35TopicsCollectDto.setOpinion2( entity.getOpinion2() );
        c35TopicsCollectDto.setOpinion3( entity.getOpinion3() );
        c35TopicsCollectDto.setReportTime( entity.getReportTime() );
        c35TopicsCollectDto.setStatus( entity.getStatus() );
        c35TopicsCollectDto.setTopicType( entity.getTopicType() );
        List<String> list2 = entity.getViewUsers();
        if ( list2 != null ) {
            c35TopicsCollectDto.setViewUsers( new ArrayList<String>( list2 ) );
        }

        return c35TopicsCollectDto;
    }

    @Override
    public List<C35TopicsCollectDto> toDto(List<C35TopicsCollect> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<C35TopicsCollectDto> list = new ArrayList<C35TopicsCollectDto>( entityList.size() );
        for ( C35TopicsCollect c35TopicsCollect : entityList ) {
            list.add( toDto( c35TopicsCollect ) );
        }

        return list;
    }

    @Override
    public C35TopicsCollect toEntity(C35TopicsCollectDto dto) {
        if ( dto == null ) {
            return null;
        }

        C35TopicsCollect c35TopicsCollect = new C35TopicsCollect();

        c35TopicsCollect.setBelongToDept( dto.getBelongToDept() );
        c35TopicsCollect.setBpmInstanceId( dto.getBpmInstanceId() );
        c35TopicsCollect.setBpmProcessKey( dto.getBpmProcessKey() );
        c35TopicsCollect.setBpmStatus( dto.getBpmStatus() );
        c35TopicsCollect.setBpmSubject( dto.getBpmSubject() );
        c35TopicsCollect.setCreateBy( dto.getCreateBy() );
        c35TopicsCollect.setCreateTime( dto.getCreateTime() );
        c35TopicsCollect.setId( dto.getId() );
        List<String> list = dto.getParticipateUser();
        if ( list != null ) {
            c35TopicsCollect.setParticipateUser( new ArrayList<String>( list ) );
        }
        c35TopicsCollect.setUpdateBy( dto.getUpdateBy() );
        c35TopicsCollect.setUpdateTime( dto.getUpdateTime() );
        c35TopicsCollect.setVersion( dto.getVersion() );
        c35TopicsCollect.setContext( dto.getContext() );
        c35TopicsCollect.setDescription( dto.getDescription() );
        c35TopicsCollect.setIsFast( dto.getIsFast() );
        c35TopicsCollect.setIsLeaderAgree( dto.getIsLeaderAgree() );
        c35TopicsCollect.setIsSecret( dto.getIsSecret() );
        c35TopicsCollect.setMeetingTopics( dto.getMeetingTopics() );
        c35TopicsCollect.setName( dto.getName() );
        c35TopicsCollect.setOpinion1( dto.getOpinion1() );
        c35TopicsCollect.setOpinion2( dto.getOpinion2() );
        c35TopicsCollect.setOpinion3( dto.getOpinion3() );
        c35TopicsCollect.setReportTime( dto.getReportTime() );
        c35TopicsCollect.setStatus( dto.getStatus() );
        c35TopicsCollect.setTopicType( dto.getTopicType() );
        List<String> list1 = dto.getViewUsers();
        if ( list1 != null ) {
            c35TopicsCollect.setViewUsers( new ArrayList<String>( list1 ) );
        }
        List<StorageBiz> list2 = dto.getFiles();
        if ( list2 != null ) {
            c35TopicsCollect.setFiles( new ArrayList<StorageBiz>( list2 ) );
        }

        return c35TopicsCollect;
    }

    @Override
    public List<C35TopicsCollect> toEntity(List<C35TopicsCollectDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<C35TopicsCollect> list = new ArrayList<C35TopicsCollect>( dtoList.size() );
        for ( C35TopicsCollectDto c35TopicsCollectDto : dtoList ) {
            list.add( toEntity( c35TopicsCollectDto ) );
        }

        return list;
    }
}
