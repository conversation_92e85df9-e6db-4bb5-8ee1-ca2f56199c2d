package com.hzwangda.aigov.modules.document.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.hzwangda.aigov.modules.archive.entity.A07FileArchive;
import com.hzwangda.aigov.modules.archive.mapstruct.A07FileArchiveMapper;
import com.hzwangda.aigov.modules.archive.repository.A07FondsNumberRepository;
import com.hzwangda.aigov.modules.archive.repository.FileArchiveRepository;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwQueryCriteria;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07DocumentGwViewMapper;
import com.hzwangda.aigov.modules.document.entity.A07DocumentFwView;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwView;
import com.hzwangda.aigov.modules.document.entity.SwAllView;
import com.hzwangda.aigov.modules.document.repository.A07DocumentFwViewRepository;
import com.hzwangda.aigov.modules.document.repository.A07DocumentGwViewRepository;
import com.hzwangda.aigov.modules.document.repository.SwAllViewRepository;
import com.hzwangda.aigov.modules.document.service.A30GwService;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.hzwangda.aigov.oa.domain.WdArchiveInfo;
import com.hzwangda.aigov.oa.repository.ArchiveInfoRepository;
import com.hzwangda.aigov.oa.repository.StorageBizRepository;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.config.ElPermissionConfig;
import com.wangda.oa.modules.extension.dto.UserDataDto;
import com.wangda.oa.modules.extension.mapper.WdSysOptionCustomMapper;
import com.wangda.oa.modules.security.service.dto.JwtUserDto;
import com.wangda.oa.modules.system.domain.Role;
import com.wangda.oa.modules.system.repository.RoleRepositoryCustom;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.domain.workflow.BpmTaskUserReadHandle;
import com.wangda.oa.modules.workflow.domain.workflow.vo.TaskUserReadVo;
import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import com.wangda.oa.modules.workflow.repository.workflow.BpmTaskUserReadHandleRepository;
import com.wangda.oa.modules.workflow.repository.workflow.BpmTaskUserReadRepository;
import com.wangda.oa.modules.workflow.service.workflow.FlowInstanceService;
import com.wangda.oa.repository.LocalStorageRepository;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class A30GwServiceImpl implements A30GwService {

    private final A07DocumentGwViewRepository gwViewRepository;

    private final LocalStorageRepository storageRepository;

    private final ElPermissionConfig elPermissionConfig;

    private final A07DocumentGwViewMapper a07DocumentGwViewMapper;

    private final A07DocumentFwViewRepository a07DocumentFwViewRepository;

    private final SwAllViewRepository swAllViewRepository;

//    private final A07DocumentLdpsRepository a07DocumentLdpsRepository;
//
//    private final A07DocumentClqpRepository a07DocumentClqpRepository;
//
//    private final A07DocumentXxgksqRepository a07DocumentXxgksqRepository;
//
//    private final A07DocumentConfidentialRepository a07DocumentConfidentialRepository;
//
//    private final A07DocumentHqRepository a07DocumentHqRepository;

    private final FileArchiveRepository fileArchiveRepository;

    private final A07FileArchiveMapper a07FileArchiveMapper;

    private final HistoryService historyService;

    private final RepositoryService repositoryService;

    private final BpmTaskUserReadHandleRepository userReadHandleRepository;

    private final TaskService taskService;

    private final RuntimeService runtimeService;

    private final FlowInstanceService flowInstanceService;

    private final BpmTaskUserReadRepository readRepository;

    private final BpmTaskUserReadHandleRepository readHandleRepository;

    private final StorageBizRepository storageBizRepository;

    private final A07FondsNumberRepository a07FondsNumberRepository;

    private final RoleRepositoryCustom roleRepositoryCustom;

    private final WdSysOptionCustomMapper wdSysOptionCustomMapper;

    private final ArchiveInfoRepository archiveInfoRepository;


    @Override
    public Map<String, Object> getGwWaitArchiveList(A07DocumentGwQueryCriteria criteria, Pageable page) {
        List<String> belongs = criteria.getBelongToDept();
        if (!elPermissionConfig.check()) {
            if (belongs == null || belongs.isEmpty()) {
                Role firstByValue = roleRepositoryCustom.findFirstByValue("bpmAdmin");
                if (Objects.isNull(firstByValue)) {
                    return PageUtil.toPage(Page.empty());
                }
                List<Long> userIds = roleRepositoryCustom.findByUserIdAndRoleId(SecurityUtils.getCurrentUserId(), firstByValue.getId());
                if (CollUtil.isEmpty(userIds)) {
                    return PageUtil.toPage(Page.empty());
                }
                List<UserDataDto> userInfoDtoList = wdSysOptionCustomMapper.getUserListByUserIdAndEnabled(userIds);
                belongs = userInfoDtoList.stream().map(UserDataDto::getUsername).collect(Collectors.toList());
            }
        }

        List<String> fwBpmProcessKeys = a07DocumentFwViewRepository.findDistinctBpmProcessKey();
        List<String> swBpmProcessKeys = swAllViewRepository.findDistinctBpmProcessKey();
        fwBpmProcessKeys.addAll(swBpmProcessKeys);
        Specification<A07DocumentGwView> sp = (root, criteriaQuery, criteriaBuilder) -> {
            /*Subquery<Long> ids = criteriaQuery.subquery(Long.class);
            Root<A07DocumentFwView> a07DocumentFwViewRoot = ids.from(A07DocumentFwView.class);
            ids.where(a07DocumentFwViewRoot.get("zw").isNull());
            ids.select(a07DocumentFwViewRoot.get("id"));*/
            Predicate predicate = criteriaBuilder.and(
                    root.get("bpmProcessKey").in(fwBpmProcessKeys),
                    root.get("bpmStatus").in(ProcStatusEnum.TG.getValue(), ProcStatusEnum.END.getValue()),
                    QueryHelp.getPredicate(root, criteria, criteriaBuilder)
                    /*,
                    // 文号不能空
                    root.get("gwwh").isNotNull(),
                    // 发文不能没有正文
                    criteriaBuilder.not(root.get("id").in(ids))*/
            );


            String bt = criteria.getBt();
            if (StringUtils.isNotEmpty(bt)) {
                predicate = criteriaBuilder.and(
                        predicate,
                        criteriaBuilder.like(root.get("bt"), "%" + bt + "%")
                );
            }

            Subquery<Long> gwIds = criteriaQuery.subquery(Long.class);
            Root<A07FileArchive> a07FileArchiveRoot = gwIds.from(A07FileArchive.class);
            gwIds.select(a07FileArchiveRoot.get("docId"));
            Predicate p = a07FileArchiveRoot.get("docId").isNotNull();

            String fileStatus = criteria.getFileStatus();
            if (StringUtils.isNotEmpty(fileStatus) && !fileStatus.equals("0")) {
                gwIds.where(p, criteriaBuilder.equal(a07FileArchiveRoot.get("status"), fileStatus));
                predicate = criteriaBuilder.and(
                        predicate,
                        root.get("id").in(gwIds)
                );
            } else {
                if ("0".equals(fileStatus)) {
                    gwIds.where(p, a07FileArchiveRoot.get("status").in(1, 2, 3, 4, 5, -1));
                } else {
                    gwIds.where(p, a07FileArchiveRoot.get("status").in(1, 4, 5, -1));
                }
                predicate = criteriaBuilder.and(
                        predicate,
                        criteriaBuilder.not(root.get("id").in(gwIds))
                );
            }

            return predicate;
        };
        Page<A07DocumentGwView> pageDto = gwViewRepository.findAll(sp, page);
        pageDto.stream().forEach(a07DocumentGwView -> {
            A07FileArchive byDocId = fileArchiveRepository.findByDocId(a07DocumentGwView.getId());
            if (byDocId != null) {
                a07DocumentGwView.setFileStatus(byDocId.getStatus());
            }
        });
        return PageUtil.toPage(pageDto);
    }


    /**
     * @param id :
     * @description 待归档整理
     * <AUTHOR>
     * @updateTime 2021/11/4 19:34
     * @return: java.lang.Object
     */
    @Override
    public Object getGwPutInOrder(Long id, String type) {
        A07FileArchive fileArchiveList = fileArchiveRepository.findFirstByErpNumIsNotNullOrderByErpNumDesc();
        int erpNum;
        if (fileArchiveList == null) {
            erpNum = 1;
        } else {
            erpNum = fileArchiveList.getErpNum() + 1;
        }
        String erpFileId = "2-" + DateUtil.year(new Date()) + "-" + StrUtil.padPre(String.valueOf(erpNum + 1), 5, '0');

        A07FileArchive.A07FileArchiveBuilder builder = A07FileArchive.builder();
        switch (type) {
            case "1":
                A07DocumentFwView a07DocumentFwView = a07DocumentFwViewRepository.findById(id).get();
                ReferenceNumber gwwh = a07DocumentFwView.getGwwh();
                StringBuffer stringBuffer = new StringBuffer();
                if (gwwh != null) {
                    if (StringUtils.isNotEmpty(gwwh.getDz())) {
                        stringBuffer.append(gwwh.getDz());
                    }
                    if (gwwh.getNh() != null) {
                        stringBuffer.append("〔");
                        stringBuffer.append(gwwh.getNh());
                        stringBuffer.append("〕");
                    }
                    if (gwwh.getXh() != null) {
                        stringBuffer.append(gwwh.getXh());
                    }
                    stringBuffer.append("号");
                }
                String belongToDept = a07DocumentFwView.getBelongToDept();
//                String code = a07FondsNumberRepository.findCodeByBelongToDept(belongToDept);
                List<WdArchiveInfo> list = archiveInfoRepository.findAll((root, query, criteriaBuilder) -> {
                    return criteriaBuilder.and(
                            criteriaBuilder.equal(root.get("belongToDept"), belongToDept),
                            root.get("archiveNum").isNotNull()
                    );
                });
                String code = null;
                if (list.size() > 0) {
                    code = list.get(0).getArchiveNum();
                }
                A07FileArchive archive = builder
                        .bpmProcessKey(a07DocumentFwView.getBpmProcessKey())
                        .procInstId(a07DocumentFwView.getBpmInstanceId())
                        .year(DateUtil.year(new Date()) + "")
                        .status("1")
                        .subject(a07DocumentFwView.getBt())
                        .fondsNumber(code)
                        .docType("发文")
                        .docNo(stringBuffer.toString())
                        .erpNum(erpNum)
                        .erpFileId(erpFileId)
                        .docId(a07DocumentFwView.getId())
                        /*.fileDate(a07DocumentFw.getYfrq())*/
                        .responsiblePerson(((JwtUserDto) SecurityUtils.getCurrentUser()).getUser().getNickName())
                        .fj(a07DocumentFwView.getFj())
                        .build();
                archive.setBelongToDept(belongToDept);

                return a07FileArchiveMapper.toDto(archive);
            case "0":
                SwAllView swAllView = swAllViewRepository.findById(id).get();
                ReferenceNumber lwbh = swAllView.getSwbh();
                stringBuffer = new StringBuffer();
                if (lwbh != null) {
                    if (StringUtils.isNotEmpty(lwbh.getDz())) {
                        stringBuffer.append(lwbh.getDz());
                    }
                    if (lwbh.getNh() != null) {
                        stringBuffer.append("〔");
                        stringBuffer.append(lwbh.getNh());
                        stringBuffer.append("〕");
                    }
                    if (lwbh.getXh() != null) {
                        stringBuffer.append(lwbh.getXh());
                    }
                    stringBuffer.append("号");
                }
                belongToDept = swAllView.getBelongToDept();
//                code = a07FondsNumberRepository.findCodeByBelongToDept(belongToDept);
                list = archiveInfoRepository.findAll((root, query, criteriaBuilder) -> {
                    return criteriaBuilder.and(
                            criteriaBuilder.equal(root.get("belongToDept"), belongToDept),
                            root.get("archiveNum").isNotNull()
                    );
                });
                code = null;
                if (list.size() > 0) {
                    code = list.get(0).getArchiveNum();
                }
                archive = builder
                        .bpmProcessKey(swAllView.getBpmProcessKey())
                        .procInstId(swAllView.getBpmInstanceId())
                        .year(DateUtil.year(new Date()) + "")
                        .status("1")
                        .subject(swAllView.getBt())
                        .fondsNumber(code)
                        .docType("收文")
                        .docNo(stringBuffer.toString())
                        .erpNum(erpNum)
                        .erpFileId(erpFileId)
                        .docId(swAllView.getId())
                        .fileDate(swAllView.getSwrq())
                        .responsiblePerson(((JwtUserDto) SecurityUtils.getCurrentUser()).getUser().getNickName())
                        .build();
                Specification<StorageBiz> sp = (root, query, criteriaBuilder) -> {
                    Predicate predicate = criteriaBuilder.equal(root.get("bizId"), swAllView.getId());
                    query.orderBy(criteriaBuilder.asc(root.get("sorted")));
                    return predicate;
                };
                List<StorageBiz> all = storageBizRepository.findAll(sp);
                archive.setFj(all);
                archive.setBelongToDept(belongToDept);
                return a07FileArchiveMapper.toDto(archive);

            default:

        }
        return null;
    }


    /**
     * 传阅意见上表单
     *
     * @param remark
     * @param bpmProcInstId
     * @param users
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void remarkToFormField(String remark, String bpmProcInstId, String users) {
        A07DocumentGwView gwView = gwViewRepository.findByBpmInstanceId(bpmProcInstId);
       /* if(Objects.nonNull(gwView)&&gwView.getGwType().equals("1")&&gwView.getBpmStatus().equals(ProcStatusEnum.INPROGRESS.getValue())){
            HistoricProcessInstance hiInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(bpmProcInstId).singleResult();
            // bpm定义模型
            BpmnModel bpmnModel = repositoryService.getBpmnModel(hiInstance.getProcessDefinitionId());
            Process process = bpmnModel.getProcess(null);
            boolean isLabLead = documentHandle.isLabLead(SecurityUtils.getCurrentUsername());
            String procKey="";
            if(isLabLead){
                procKey="usertask4";
            }else {
                procKey="usertask5";
            }
            // 当前节点
            FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(procKey);
            // 意见上表单
            String remarkToFormField = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKTOFORMFIELD);
            if(org.apache.commons.lang3.StringUtils.isNotBlank(remarkToFormField)) {
                UserDto byName = userService.findByName(SecurityUtils.getCurrentUsername());
                FlowFormRemarkDto flowFormRemarkDto = FlowFormRemarkDto.builder()
                        .remark(remark)
                        .username(byName.getUsername())
                        .nickName(byName.getNickName())
                        .deptId(byName.getDeptId())
                        .deptName(byName.getDept().getName())
                        .processedTime(new Date())
                        .fj("")
                        .build();

                boolean append = true;
                String remarkCoverd = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKCOVERD);
                if(org.apache.commons.lang3.StringUtils.isNotBlank(remarkCoverd) && Boolean.parseBoolean(remarkCoverd)) {
                    append = false;
                }

                String processRemarkSort = process.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_PROCESS_REMARK_SORT);
                boolean insertBefore = false;
                if(org.apache.commons.lang3.StringUtils.isNotBlank(processRemarkSort)) {
                    insertBefore = Boolean.parseBoolean(processRemarkSort);
                }
                // 变量信息
                ProcessInstance processInstance =
                        runtimeService.createProcessInstanceQuery().processInstanceId(bpmProcInstId).includeProcessVariables().singleResult();
                Map<String, Object> variables = processInstance.getProcessVariables();
                ResponseInfo responseInfo = null;
                try {
                    responseInfo = formTemplateService.updateFormFieldData(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()), processInstance.getProcessInstanceId(), remarkToFormField, flowFormRemarkDto, append, insertBefore);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if(ResultCodeEnum.SUCCESS.getCode() == responseInfo.getCode()) {
                    // 更新意见上表单数据
                    JSONObject formData = (JSONObject) variables.get(ProcessConstants.BPM_FORM_DATA);
                    JSONObject jsonObject = (JSONObject)responseInfo.getData();
                    List<String> remarkToFormFieldList = FlowableUtils.findAllRemarkToFormField(process);
                    for (String key: remarkToFormFieldList) {
                        if(jsonObject.containsKey(key) && Objects.nonNull(jsonObject.get(key))) {
                            formData.put(key, jsonObject.get(key));
                        }
                    }
                    variables.replace(ProcessConstants.BPM_FORM_DATA, formData);
                    runtimeService.setVariables(bpmProcInstId, variables);
                }
            }
        }else{
            if(Objects.nonNull(gwView)&&gwView.getGwglType()==1&&gwView.getBpmStatus().equals(ProcStatusEnum.TG.getValue())){
                try {
                    updateFormField(bpmProcInstId,remark);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
*/
        if (org.apache.commons.lang3.StringUtils.isNotBlank(users)) {
            if (Objects.nonNull(gwView)) {
                if ("0".equals(gwView.getGwType())) {
                    if (!gwView.getBpmStatus().equals(ProcStatusEnum.TG.getValue())) {
                        /*if(gwView.getBpmProcessKey().equals(WorkflowType.DOCUMENT_DWFW.getValue())||
                                gwView.getBpmProcessKey().equals(WorkflowType.DOCUMENT_DWBGHJY.getValue())){
                            dwFwToCsdwmsAssignee(gwView,users);
                        }else{
                            fwToCsdwmsAssignee(gwView,users);
                        }*/
                        // 传阅操作记录表
                        BpmTaskUserReadHandle bpmTaskUserReadHandle = new BpmTaskUserReadHandle();
                        bpmTaskUserReadHandle.setRemark(com.wangda.oa.utils.StringUtils.isNotEmpty(remark) ? remark : "");
                        bpmTaskUserReadHandle.setProcessInstanceId(bpmProcInstId);
                        bpmTaskUserReadHandle.setOperator(SecurityUtils.getCurrentUsername());
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(users)) {
                            bpmTaskUserReadHandle.setAssignees(CollectionUtil.toList(users.split("\\,")));
                        }
                        userReadHandleRepository.save(bpmTaskUserReadHandle);

                    }/*else{
                        //增加传阅人员
                        a30FwService.saveTaskUserRead(remark,users,bpmProcInstId);
                    }*/
                } else {
                    List<Task> taskLists = taskService.createTaskQuery().processInstanceId(bpmProcInstId).list();
                    if (CollectionUtil.isNotEmpty(taskLists)) {
                        String defkey = taskLists.get(0).getTaskDefinitionKey();
                        String[] split = defkey.split("usertask");
                        if (Integer.valueOf(split[1]) <= 4) {
                            ProcessInstance processInstance =
                                    runtimeService.createProcessInstanceQuery().processInstanceId(bpmProcInstId).includeProcessVariables().singleResult();
                            Map<String, Object> variables = processInstance.getProcessVariables();
                            variables.replace(ProcessConstants.BPM_BPM_PRESIGNCOPYTOASSIGNEE, variables.get(ProcessConstants.BPM_BPM_PRESIGNCOPYTOASSIGNEE) + splicPreSignCopyToAssignee(variables, users));
                            runtimeService.setVariables(bpmProcInstId, variables);

                            // 传阅操作记录表
                            BpmTaskUserReadHandle bpmTaskUserReadHandle = new BpmTaskUserReadHandle();
                            bpmTaskUserReadHandle.setRemark(com.wangda.oa.utils.StringUtils.isNotEmpty(remark) ? remark : "");
                            bpmTaskUserReadHandle.setProcessInstanceId(bpmProcInstId);
                            bpmTaskUserReadHandle.setOperator(SecurityUtils.getCurrentUsername());
                            if (org.apache.commons.lang3.StringUtils.isNotBlank(users)) {
                                bpmTaskUserReadHandle.setAssignees(CollectionUtil.toList(users.split("\\,")));
                            }
                            userReadHandleRepository.save(bpmTaskUserReadHandle);

                        }/*else{
                            //增加传阅人员
                            a30FwService.saveTaskUserRead(remark,users,bpmProcInstId);
                        }*/
                    }/*else{
                        //增加传阅人员
                        a30FwService.saveTaskUserRead(remark,users,bpmProcInstId);
                    }*/
                }
            }
        } else {
            // 传阅操作记录表
            BpmTaskUserReadHandle bpmTaskUserReadHandle = new BpmTaskUserReadHandle();
            bpmTaskUserReadHandle.setRemark(com.wangda.oa.utils.StringUtils.isNotEmpty(remark) ? remark : "");
            bpmTaskUserReadHandle.setProcessInstanceId(bpmProcInstId);
            bpmTaskUserReadHandle.setOperator(SecurityUtils.getCurrentUsername());
            userReadHandleRepository.save(bpmTaskUserReadHandle);
        }

        //更改已阅状态
        TaskUserReadVo taskUserReadVo = new TaskUserReadVo();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(remark)) {
            taskUserReadVo.setRemark(remark);
        }
        taskUserReadVo.setProcessInstanceId(bpmProcInstId);
        flowInstanceService.saveHasRead(taskUserReadVo);

    }

    /**
     * 拼接收文待阅人员
     */
    public String splicPreSignCopyToAssignee(Map<String, Object> variables, String users) {
        String splicCopyToAssignee = "";
        String preSignCopyToAssignee = variables.get(ProcessConstants.BPM_BPM_PRESIGNCOPYTOASSIGNEE).toString();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(preSignCopyToAssignee)) {
            for (String user : users.split(",")) {
                if (preSignCopyToAssignee.contains(user)) {
                    continue;
                }
                splicCopyToAssignee += "," + user + "$usertask4";
            }
        } else {
            for (String user : users.split(",")) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(splicCopyToAssignee)) {
                    splicCopyToAssignee += "," + user + "$usertask4";
                } else {
                    splicCopyToAssignee += "usertask4:" + user + "$usertask4";
                }
            }
        }
        return splicCopyToAssignee;
    }


    @Override
    public List<Map<String, String>> getFlowDocReadOperateTable(String bpmProcInstId) {
        List<Map<String, String>> reads = readHandleRepository.findByProcessInstanceId(bpmProcInstId);
        return reads;
    }

    @Override
    public List<Map<String, String>> getFlowDocReadTable(String bpmProcInstId) {
        List<Map<String, String>> reads = readRepository.findByProcessInstanceId(bpmProcInstId);
        return reads;
    }

    @Override
    public String getRepetitionReadUsers(String bpmProcInstId, String users) {
        String[] split = users.split(",");
        List<String> userList = new ArrayList<>();
        for (String user : split) {
            userList.add(user);
        }
        String reads = readRepository.findByProcInstIdAndAssignees(bpmProcInstId, userList);
        return reads;
    }
}
