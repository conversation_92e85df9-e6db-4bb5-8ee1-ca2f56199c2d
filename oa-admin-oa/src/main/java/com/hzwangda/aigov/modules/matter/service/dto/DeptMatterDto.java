package com.hzwangda.aigov.modules.matter.service.dto;

import com.hzwangda.aigov.modules.matter.domain.Matter;
import com.wangda.oa.modules.system.domain.Dept;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/18 20:03
 * @describe 部门和事项的返回集合体
 */
@Data
public class DeptMatterDto {
    /**
     * 部门名
     */
    private String deptName;
    /**
     * 部门
     */
    private String deptCode;
    /**
     * 该部门有几个事项
     */
    private String matterNumStr;
    /**
     * 事项名
     */
    private String matterName;

    /**
     * 部门集合
     */
    private List<Dept> deptList;

    /**
     * 事项集合
     */
    private List<Matter> matterList;

    /**
     * 总数
     */
    private Integer total;

}
