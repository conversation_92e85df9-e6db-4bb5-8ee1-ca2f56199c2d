package com.hzwangda.aigov.modules.document.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@ApiModel
@Data
public class A07HhInfoQueryCriteria {

    @ApiModelProperty(value = "类型")
    private String gwzl;

    @Query(propName = "subject", type = Query.Type.INNER_LIKE)
    @ApiModelProperty(value = "搜索内容")
    private String searchKeys;

    @ApiModelProperty(value = "状态: 0:待,1:已")
    private Integer status;

    @Query(propName = "isPost", type = Query.Type.EQUAL)
    @ApiModelProperty(value = "是否已推送")
    private String isPost;

    @Query(propName = "startDate", type = Query.Type.BETWEEN)
    @ApiModelProperty(value = "起始时间")
    private List<Timestamp> timeRange;


}
