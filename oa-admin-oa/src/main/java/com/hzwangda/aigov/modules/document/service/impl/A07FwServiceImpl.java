package com.hzwangda.aigov.modules.document.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.hzwangda.aigov.bpm.repository.A07DocumentGwlzRepository;
import com.hzwangda.aigov.docconvert.common.domain.dto.StorageConversionDto;
import com.hzwangda.aigov.docconvert.common.domain.dto.StorageConversionInfoDto;
import com.hzwangda.aigov.docconvert.common.service.FileConversionService;
import com.hzwangda.aigov.docconvert.common.util.StrKit;
import com.hzwangda.aigov.modules.document.constant.DocumentConstant;
import com.hzwangda.aigov.modules.document.dto.*;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07DocumentFwMapper;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07DocumentHqMapper;
import com.hzwangda.aigov.modules.document.entity.*;
import com.hzwangda.aigov.modules.document.enums.TableClassNameEnum;
import com.hzwangda.aigov.modules.document.repository.*;
import com.hzwangda.aigov.modules.document.service.A07FwService;
import com.hzwangda.aigov.modules.document.service.A07GwService;
import com.hzwangda.aigov.modules.document.service.Z08DocumentTypeService;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.modules.workflow.enums.workflow.WorkflowType;
import com.hzwangda.aigov.modules.workflow.repository.ReferenceNumberRepository;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.hzwangda.aigov.oa.repository.StorageBizRepository;
import com.hzwangda.aigov.tables.domain.IdClass;
import com.hzwangda.aigov.tables.service.TablesService;
import com.taiyu.typrecenter.util.HttpClientUtil;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.domain.workflow.BpmTaskUserRead;
import com.wangda.oa.modules.workflow.domain.workflow.BpmTaskUserReadHandle;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskVariablesDto;
import com.wangda.oa.modules.workflow.enums.form.AppTypeEnum;
import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import com.wangda.oa.modules.workflow.repository.workflow.BpmTaskUserReadHandleRepository;
import com.wangda.oa.modules.workflow.repository.workflow.BpmTaskUserReadRepository;
import com.wangda.oa.modules.workflow.service.IUserRuleService;
import com.wangda.oa.modules.workflow.service.workflow.FlowInstanceService;
import com.wangda.oa.modules.workflow.service.workflow.FlowTaskService;
import com.wangda.oa.repository.LocalStorageRepository;
import com.wangda.oa.service.IStorageService;
import com.wangda.oa.utils.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.poi.ss.formula.functions.T;
import org.flowable.engine.HistoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.persistence.criteria.*;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class A07FwServiceImpl implements A07FwService {

    Logger logger = LoggerFactory.getLogger(A07FwServiceImpl.class);

    private final A07DocumentFwRepository a07DocumentFwRepository;
    private final A07DocumentFwMapper a07DocumentFwMapper;
    private final A07DocumentHqRepository a07DocumentHqRepository;
    private final A07DocumentHqMapper a07DocumentHqMapper;
    private final A07DocumentInfoWwfbRepository a07DocumentInfoWwfbRepository;
    private final A07DocumentFwViewRepository a07DocumentFwViewRepository;
    private final TaskService taskService;
    private final FlowTaskService flowTaskService;
    private final ReferenceNumberRepository referenceNumberRepository;
    private final A07DocumentGwlzRepository gwlzRepository;
    private final Z08DocumentTypeService documentTypeService;
    private final IUserRuleService iUserRuleService;
    private final HistoryService historyService;
    private final FlowInstanceService flowInstanceService;
    private final A07DocumentGwViewRepository gwViewRepository;
    private final BpmTaskUserReadRepository userReadRepository;
    private final BpmTaskUserReadHandleRepository userReadHandleRepository;
    private final UserService sysUserService;
    private final TablesService tablesService;
    private final FileConversionService conversionService;
    private final A07GwService gwService;

    @Override
    public Map<String, Object> getFwList(A07DocumentFwQueryCriteria criteria, Pageable pageable) {
        A07SearchDtoCriteria searchDtoCriteria = new A07SearchDtoCriteria();
        searchDtoCriteria.setBt(criteria.getBt());
        searchDtoCriteria.setType(criteria.getStatus());
        Map<String, FlowTaskInfoDto> map = this.getProcessInstanceIdMap(criteria.getStatus(), WorkflowType.DOCUMENT_POST.getValue(), pageable, searchDtoCriteria);
        if(CollectionUtils.isEmpty(map)) {
            return PageUtil.toPage(Page.empty());
        }
        criteria.setBpmInstanceId(map.keySet());
        Page<A07DocumentFw> page = a07DocumentFwRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Page<A07DocumentFwDto> pageDto = page.map(a07DocumentFwMapper::toDto);
        pageDto.getContent().forEach(a07DocumentFwDto -> {
            FlowTaskInfoDto flowTaskInfoDto = map.get(a07DocumentFwDto.getBpmInstanceId());
            a07DocumentFwDto.setFlowTaskInfoDto(flowTaskInfoDto);
        });
        return PageUtil.toPage(pageDto);
    }

    @Override
    public Map<String, Object> getHqList(A07DocumentHqQueryCriteria criteria, Pageable pageable) {
        A07SearchDtoCriteria searchDtoCriteria = new A07SearchDtoCriteria();
        searchDtoCriteria.setBt(criteria.getBt());
        searchDtoCriteria.setType(criteria.getStatus());
        Map<String, FlowTaskInfoDto> map = this.getProcessInstanceIdMap(criteria.getStatus(), WorkflowType.DOCUMENT_SIGN.getValue(), pageable, searchDtoCriteria);
        if(CollectionUtils.isEmpty(map)) {
            return PageUtil.toPage(Page.empty());
        }
        criteria.setBpmInstanceId(map.keySet());
        Page<A07DocumentHq> page = a07DocumentHqRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Page<A07DocumentHqDto> pageDto = page.map(a07DocumentHqMapper::toDto);
        pageDto.getContent().forEach(a07DocumentHqDto -> {
            FlowTaskInfoDto flowTaskInfoDto = map.get(a07DocumentHqDto.getBpmInstanceId());
            a07DocumentHqDto.setFlowTaskInfoDto(flowTaskInfoDto);
        });
        return PageUtil.toPage(pageDto);
    }

    @Override
    public A07DocumentFwDto findByDocumentInfo(Long id) {
        A07DocumentFw a07DocumentFw = a07DocumentFwRepository.findById(id).orElseGet(A07DocumentFw::new);
        ValidationUtil.isNull(a07DocumentFw.getId(), "a07DocumentFw", "id", a07DocumentFw.getId());
        A07DocumentFwDto a06NewsDto = a07DocumentFwMapper.toDto(a07DocumentFw);
        return a06NewsDto;
    }

    @Override
    public A07DocumentHqDto getHqInfo(Long id) {
        A07DocumentHq hq = a07DocumentHqRepository.findById(id).orElseGet(A07DocumentHq::new);
        ValidationUtil.isNull(hq.getId(), "hq", "id", id);
        return a07DocumentHqMapper.toDto(hq);
    }

    @Override
    public Map<String, Object> getFwListRetrieve(A07DocumentGwRetrieveQueryCriteria criteria, Pageable pageable) {
        criteria.setGwglType(0);
        Page<A07DocumentFwView> page = a07DocumentFwViewRepository.findAll((Specification) retrieveFilter(criteria), pageable);
        page.getContent().stream().forEach(p -> {
            ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(p.getBpmStatus());
            p.setBpmStatus(procStatusEnum == null ? null : procStatusEnum.getName());
        });
        return PageUtil.toPage(page);
    }

    @Override
    public Boolean deleteHq(List<Long> ids) {
        for(Long id : ids) {
            a07DocumentHqRepository.deleteById(id);
        }
        return true;
    }

    @Override
    public List<A07DocumentInfoWwfb> getWwfbList() {
        return a07DocumentInfoWwfbRepository.findAll(Sort.by("sort").ascending());
    }

    @Override
    public Boolean deleteDocumentInfo(List<Long> ids) {
        for(Long id : ids) {
            a07DocumentFwRepository.deleteById(id);
        }
        return true;
    }

    @Override
    public Integer getXhByDz(String dz, String nh, String procInstId) {
        A07DocumentGwView byBpmInstanceId = gwViewRepository.findByBpmInstanceId(procInstId);
        String currentBelongToDept = "";
        if(Objects.isNull(byBpmInstanceId)) {
            currentBelongToDept = SecurityUtils.getCurrentBelongToDept();
        }else {
            currentBelongToDept = byBpmInstanceId.getBelongToDept();
        }

        Integer xh = referenceNumberRepository.getMaxXhByDzAndNh(dz, nh, currentBelongToDept);
        if(ObjectUtil.isEmpty(xh)) {
            xh = 0;
        }
        return ++xh;
    }

    /**
     * 查询我的不同状态的流程实例id
     * @param status
     * @param processDefinitionKey
     * @param pageable
     * @return
     */
    private Map<String, FlowTaskInfoDto> getProcessInstanceIdMap(Integer status, String processDefinitionKey, Pageable pageable, A07SearchDtoCriteria searchDto) {
        Map<String, List<FlowTaskInfoDto>> result;
        Map<String, FlowTaskInfoDto> returnResult = new HashMap<>();
        if(DocumentConstant.QUERY_STATUS_DB == status || DocumentConstant.QUERY_STATUS_DY == status) {
            //待办任务列表
            TaskQuery taskQuery = taskService.createTaskQuery()
                    .active()
                    .processDefinitionKey(processDefinitionKey)
                    .taskAssignee(SecurityUtils.getCurrentUsername()) // userName关联处理人
                    .includeProcessVariables()
                    .orderByTaskCreateTime().desc();
            if(StringUtils.isNotEmpty(searchDto.getBt())) {
                taskQuery.processVariableValueLikeIgnoreCase(ProcessConstants.BPM_FORM_TITLE, searchDto.getBt() + "%");
            }
            if(StringUtils.isNotEmpty(searchDto.getSwlx())) {
                if(DocumentConstant.QUERY_STATUS_DY == status) {
                    taskQuery.processVariableValueEquals(ProcessConstants.BPM_FORM_DOCUMENT_REVIEW, searchDto.getSwlx());
                }else {
                    taskQuery.processVariableValueNotEquals(ProcessConstants.BPM_FORM_DOCUMENT_REVIEW, searchDto.getSwlx());
                }
            }

            List<Task> taskList = taskQuery.listPage(pageable.getPageNumber() * pageable.getPageSize(), pageable.getPageSize());
            if(CollectionUtils.isEmpty(taskList)) {
                return returnResult;
            }
            result = taskList.stream().collect(Collectors.groupingBy(Task::getProcessInstanceId, Collectors.mapping(FlowTaskInfoDto::new, Collectors.toList())));
        }else {
            //根据查询条件改成已办未完结和已办已完结
            ResultJson resultJson = flowTaskService.finishedList(pageable.getPageNumber(), pageable.getPageSize(), processDefinitionKey, searchDto);
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<FlowTaskDto> page = (com.baomidou.mybatisplus.extension.plugins.pagination.Page<FlowTaskDto>) resultJson.getData();
            result = page.getRecords().stream().collect(Collectors.groupingBy(FlowTaskDto::getProcInsId, Collectors.mapping(FlowTaskInfoDto::new, Collectors.toList())));
        }
        for(Map.Entry<String, List<FlowTaskInfoDto>> m : result.entrySet()) {
            returnResult.put(m.getKey(), m.getValue() == null ? null : m.getValue().get(0));
        }
        return returnResult;
    }

    /**
     * 检索条件
     * @param params
     * @return
     */
    private Specification<T> retrieveFilter(A07DocumentGwRetrieveQueryCriteria params) {
        return (root, query, cb) -> {
            //封装and查询条件
            ArrayList<Predicate> andList = new ArrayList<>();
            if(StringUtils.isNotBlank(params.getBt())) {
                //标题不为空
                andList.add(cb.like(root.get("bt"), "%" + params.getBt() + "%"));
            }
            if(StringUtils.isNotBlank(params.getBpmStatus())) {
                //流程状态不为空
                andList.add(cb.equal(root.get("bpmStatus"), params.getBpmStatus()));
            }
            if(StringUtils.isNotBlank(params.getStartTime()) && StringUtils.isNotBlank(params.getEndTime())) {
                if(params.getMyParticipate() != null && params.getMyParticipate()) {
                    //我参与的
                    andList.add(cb.isMember(cb.literal(SecurityUtils.getCurrentUsername()), root.<Collection<String>>get("participateUser")));//存入条件集合里
                }
                //自定义时间
                andList.add(cb.between(root.get("createTime").as(Timestamp.class), cn.hutool.core.date.DateUtil.parse(params.getStartTime(), "yyyy-MM-dd"), DateUtil.parse(params.getEndTime(), "yyyy-MM-dd")));
            }
            if(StringUtils.isNotBlank(params.getDz()) || params.getNh() != null
                    || params.getXh() != null) {
                if(StringUtils.isNotBlank(params.getDz())) {
                    setWh(root, cb, params, andList, "dz");
                }
                if(params.getNh() != null) {
                    setWh(root, cb, params, andList, "nh");
                }
                if(params.getXh() != null) {
                    setWh(root, cb, params, andList, "xh");
                }
            }
            return cb.and(andList.toArray(new Predicate[andList.size()]));
        };
    }

    /**
     * @param root
     * @param cb
     * @param params
     * @param andList
     * @param name
     */
    private void setWh(Root<T> root, CriteriaBuilder cb, A07DocumentGwRetrieveQueryCriteria params, ArrayList<Predicate> andList, String name) {
        if(params.getGwglType() == null) {
            return;
        }
        ArrayList<Predicate> orList = new ArrayList<>();
        if(0 == params.getGwglType() || 1 == params.getGwglType() || 2 == params.getGwglType()) {
            //0:发文 1:会签 2:领导批示
            Join<T, ReferenceNumber> gwwh = root.join("gwwh", JoinType.LEFT);
            if("dz".equals(name)) {
                //文号-代字不为空
                orList.add(cb.equal(gwwh.get("dz"), params.getDz()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }else if("nh".equals(name)) {
                //文号-年号不为空
                orList.add(cb.equal(gwwh.get("nh"), params.getNh()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }else if("xh".equals(name)) {
                //文号-年号不为空
                orList.add(cb.equal(gwwh.get("xh"), params.getXh()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }
        }else if(3 == params.getGwglType()) {
            //3:收文
            Join<T, ReferenceNumber> swbh = root.join("swbh", JoinType.LEFT);
            Join<T, ReferenceNumber> lwbh = root.join("lwbh", JoinType.LEFT);
            if("dz".equals(name)) {
                //文号-代字不为空
                orList.add(cb.equal(swbh.get("dz"), params.getDz()));
                orList.add(cb.equal(lwbh.get("dz"), params.getDz()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }else if("nh".equals(name)) {
                //文号-年号不为空
                orList.add(cb.equal(swbh.get("nh"), params.getNh()));
                orList.add(cb.equal(lwbh.get("nh"), params.getNh()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }else if("xh".equals(name)) {
                //文号-年号不为空
                orList.add(cb.equal(swbh.get("xh"), params.getXh()));
                orList.add(cb.equal(lwbh.get("xh"), params.getXh()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveFfGwlz(String bpmInstanceId) {
        A07DocumentFwView fwView = a07DocumentFwViewRepository.findByBpmInstanceId(bpmInstanceId);
        if(Objects.isNull(fwView)) {
            return null;
        }
        A07DocumentGwlz gwlz = new A07DocumentGwlz();

        Map<String, Object> appIdAndFormData = gwService.getAppIdAndFormData(bpmInstanceId);
        if(CollUtil.isEmpty(appIdAndFormData)) {
            throw new BadRequestException("表单数据为空！");
        }
        IdClass idClass = new IdClass();
        idClass.setTargetClassName(TableClassNameEnum.GWJH.getValue());
        idClass.setAppId(Long.valueOf(appIdAndFormData.get("appId").toString()));
        Object formData = appIdAndFormData.get("formData");
        gwlz = (A07DocumentGwlz) tablesService.mapToTargetClass(idClass, fwView);
        gwlz.setDocPronInstId(bpmInstanceId);
        /*List<StorageBiz> fj = gwlz.getFj();
        if(fj != null) {
            // new file
            List<StorageBiz> list = fj.stream().map(storage -> {
                LocalStorage localStorage = localStorageRepository.findById(new Long(storage.getStorageId())).get();
                String name = localStorage.getName();
                InputStream inputStream = iStorageService.getInputStream(localStorage.getPath());
                FileItem fileItem = new DiskFileItemFactory().createItem("file", MediaType.ALL_VALUE, true, name);
                OutputStream outputStream = null;
                try {
                    outputStream = fileItem.getOutputStream();
                    IOUtils.copy(inputStream, outputStream);
                }catch(IOException e) {
                    logger.error("复制附件{}出现错误:{}", name, e);
                    e.printStackTrace();
                }
                LocalStorage save = iStorageService.create(name, new CommonsMultipartFile(fileItem));
                StorageBiz storageBiz = new StorageBiz();
                storageBiz.setStorageId(save.getId().toString());
                storageBiz.setBizType("A07DocumentGwlz.fj");
                return storageBiz;
            }).collect(Collectors.toList());
            gwlz.setFj(list);
        }*/

        StorageBiz zw = gwlz.getZw();
        if(Objects.nonNull(zw)) {
            //TODO 取的原始文件，是否直接取pdf文件？
            String zwid = zw.getStorageId();
            gwlz.setZwId(zwid);
            StorageConversionDto storageConversionDto = conversionService.queryFileConversionInfo(Long.parseLong(zw.getStorageId()));
            Map<String, StorageConversionInfoDto> map = storageConversionDto.getConversionStorage();
            //若有正文签章文件则取其签章文件
            if(!Objects.isNull(map)) {
                StorageConversionInfoDto signFile = map.get("sign");
                if(!Objects.isNull(signFile)) {
                    zwid = String.valueOf(signFile.getId());
                    zw.setStorageId(zwid);
                    zw.setId(null);
                }
            }
            gwlz.setZwId(zwid);
            /*LocalStorage localStorage = localStorageRepository.findById(new Long(zwid)).get();
            String name = localStorage.getName();
            InputStream inputStream = iStorageService.getInputStream(localStorage.getPath());
            FileItem fileItem = new DiskFileItemFactory().createItem("file", MediaType.ALL_VALUE, true, name);
            OutputStream outputStream = null;
            try {
                outputStream = fileItem.getOutputStream();
                IOUtils.copy(inputStream, outputStream);
            }catch(IOException e) {
                logger.error("复制正文{}出现错误:{}", name, e);
                e.printStackTrace();
            }
            LocalStorage save = iStorageService.create(name, new CommonsMultipartFile(fileItem));
            gwlz.setZwId(String.valueOf(save.getId()));*/
        }

        //保存为草稿
        gwlz.setGwzt("0");
        //添加人为单位管理员
        gwlz.setAddUser(SecurityUtils.getBindDeptUserName());

        //手动添加主送抄送
        if(Objects.nonNull(fwView.getZsdw())) {
            List<A07DocumentGwlzUser> zsList = new ArrayList<>();
            SysDeptUserMain zsdw = fwView.getZsdw();
            zsdw.getData().stream().forEach(zsData -> {
                A07DocumentGwlzUser zs = new A07DocumentGwlzUser();
                zs.setId(zsData.getOId());
                zs.setUsername(zsData.getNickName());
                zs.setQsr(zsData.getUserName());
                zs.setQszt(0);
                zs.setQslx("zs");
                zsList.add(zs);
            });
            gwlz.setZsdwms(zsdw.getMs());
            gwlz.setZsdwDepts(zsList);
        }

        if(Objects.nonNull(fwView.getCsdw())) {
            List<A07DocumentGwlzUser> csList = new ArrayList<>();
            SysDeptUserMain csdw = fwView.getCsdw();
            csdw.getData().stream().forEach(csData -> {
                A07DocumentGwlzUser cs = new A07DocumentGwlzUser();
                cs.setId(csData.getOId());
                cs.setUsername(csData.getNickName());
                cs.setQsr(csData.getUserName());
                cs.setQszt(0);
                cs.setQslx("cs");
                csList.add(cs);
            });
            gwlz.setCsdwms(csdw.getMs());
            gwlz.setCsdwDepts(csList);
        }
        A07DocumentGwlz save = gwlzRepository.save(gwlz);
        return save.getId();
    }

    @Override
    public List<Map<String, Object>> getFwCreateByDz() {
        String username = SecurityUtils.getCurrentUsername();
        List<AppAndPermissionDto> application = a07DocumentFwViewRepository.getCreateByDz("gw_fw");
        List<Map<String, Object>> appIds = new ArrayList<>();
        for(AppAndPermissionDto a : application) {
            boolean b = iUserRuleService.validatePermission(a.getCreateManager(), username);
            if(b) {
                List<Z08DocumentType> byAppId = documentTypeService.getByAppId(a.getApplicationId(),SecurityUtils.getCurrentBelongToDept());
                if(Objects.isNull(byAppId)||byAppId.size()==0){
                    byAppId = documentTypeService.getByAppIdWithoutDeptId(a.getApplicationId());
                }
                byAppId.stream().forEach(t -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", IdWorker.generateId());
                    map.put("name", t.getApplicationName());
                    map.put("appId", t.getApplicationId());
                    map.put("dz", t.getDz());
                    appIds.add(map);

                });
            }
        }
        return appIds;
    }

    @Override
    public Object getFwDzSwitchover(String procInstId, String appid) {
        HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(procInstId).singleResult();
        String businessKey = processInstance.getBusinessKey();
        Map<String, Object> map = new HashMap<>();
        if(businessKey.equals(appid)) {
            map.put("type", "equal");
            return map;
        }else {
            map.put("type", "noequal");
            return map;
        }
    }

    /**
     * @param procInstId :
     * @description 发文文种切换数据查询
     * <AUTHOR>
     * @updateTime 2022/11/1 10:13
     * @return: java.lang.Object
     */
    @Override
    public Object getFwDzSwitchoverByProcInstId(String procInstId) {
        ResultJson resultJson = flowInstanceService.flowRecord(procInstId, AppTypeEnum.PC_TYPE);
        FlowTaskVariablesDto data = (FlowTaskVariablesDto) resultJson.getData();
        return data.getFormData();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveTaskUserRead(String remark, String users, String bpmInstanceId) {
        A07DocumentGwView gwView = gwViewRepository.findByBpmInstanceId(bpmInstanceId);
        String[] userArray = users.split(",");
        List<Map<String, Object>> list = new ArrayList<>();
        if(Objects.isNull(gwView)) {
            throw new RuntimeException("当前流程不存在，请联系管理员！");
        }

        // 传阅操作记录表
        BpmTaskUserReadHandle bpmTaskUserReadHandle = new BpmTaskUserReadHandle();
        bpmTaskUserReadHandle.setRemark(StringUtils.isNotEmpty(remark) ? remark : "");
        bpmTaskUserReadHandle.setProcessInstanceId(bpmInstanceId);
        bpmTaskUserReadHandle.setOperator(SecurityUtils.getCurrentUsername());
        if(StringUtils.isNotBlank(users)) {
            bpmTaskUserReadHandle.setAssignees(CollectionUtil.toList(userArray));
        }
        Long readHandleId = userReadHandleRepository.save(bpmTaskUserReadHandle).getId();

        String nickName = "";
        for(String username : userArray) {
            BpmTaskUserRead userRead = userReadRepository.findFirstByProcessInstanceIdAndAssignee(bpmInstanceId, username);
            if(Objects.nonNull(userRead)) {
                UserDto byName = sysUserService.findByName(userRead.getAssignee());
                if(StringUtils.isNotBlank(nickName)) {
                    nickName += "," + byName.getNickName();
                }else {
                    nickName += byName.getNickName();
                }
                continue;
            }
            BpmTaskUserRead bpmTaskUserRead = new BpmTaskUserRead();
            bpmTaskUserRead.setAssignee(username);
            bpmTaskUserRead.setProcessInstanceId(String.valueOf(bpmInstanceId));
            bpmTaskUserRead.setBt(gwView.getBt());
            bpmTaskUserRead.setProcessDefKey(gwView.getBpmProcessKey());
            bpmTaskUserRead.setHandleId(readHandleId);
            userReadRepository.save(bpmTaskUserRead);

        }

        if(StringUtils.isNotBlank(nickName)) {
            return nickName + " 已被传阅，请勿重复添加";
        }else {
            return "添加成功";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<Object> publicDoc(String bpmInstanceId) {
         String synDocUrl = "https://cms.huzhou.gov.cn/hzgov/";
         String synDocAppid = "atv35fme";
         String synDocAppsecret = "chmkvi1np8kxr6kc";
         String synDocSiteid = "133";
         String synDocToken = "nxqOA";// 令牌
         String synDocChannelid = "72067";// 栏目id
         String synDocLoginname = "nxq_oa";
        String url = synDocUrl + "openapi/info/add.do";
        A07DocumentFwView doc = a07DocumentFwViewRepository.findByBpmInstanceId(bpmInstanceId);
        if(Objects.isNull(doc)){
            return new ResponseEntity<>("未找到对应发文", HttpStatus.BAD_REQUEST);
        }

        CloseableHttpClient httpClient = HttpClients.createDefault();

        HttpPost httpPost = new HttpPost(url);

        Map<String, Object> map = new HashMap();
        map.put("appid", synDocAppid);
        // 设置应用密钥
        map.put("appsecret", synDocAppsecret);
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        // 设置请求时间戳
        map.put("timestamp", timestamp);

        map.put("siteid", synDocSiteid);

        // 设置签名
        map.put("sign", SecureUtil.md5(synDocAppid + synDocAppsecret + timestamp + synDocToken));
        // 指定栏目id
        map.put("channelid", synDocChannelid);

        map.put("title", doc.getBt());
        map.put("loginname", synDocLoginname);

        map.put("opendate", DateUtil.date(doc.getNgrq().getTime() * 1000).toString());
        map.put("content", doc.getZw().getStorageId());
        /*
         * String attachStrs = doc.getAttach(); for (String attach :
         * attachStrs.split("#")) {
         *
         * }
         */
        String result = new HttpUtil().post(url, map);

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

}
