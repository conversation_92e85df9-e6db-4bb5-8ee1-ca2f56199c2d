package com.hzwangda.aigov.modules.GoodsReceive.dto;

import com.hzwangda.aigov.modules.workflow.dto.ListVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.SuperBuilder;

@SuperBuilder
public class GoodsReceiveVo extends ListVo {

    public String getWpfl() {
        return wpfl;
    }

    public void setWpfl(String wpfl) {
        this.wpfl = wpfl;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getYtfl() {
        return ytfl;
    }

    public void setYtfl(String ytfl) {
        this.ytfl = ytfl;
    }

    public String getWpmc() {
        return wpmc;
    }

    public void setWpmc(String wpmc) {
        this.wpmc = wpmc;
    }

    @ApiModelProperty(value = "物品分类")
    private String wpfl;

    @ApiModelProperty(value = "部门")
    private String deptName;

    @ApiModelProperty(value = "用途分类")
    private String ytfl;

    @ApiModelProperty(value = "物品名称数量")
    private String wpmc;

    @ApiModelProperty(value = "领用说明")
    private String lysm;

    public GoodsReceiveVo(ListVoBuilder<?, ?> b) {
        super(b);
    }

    public String getLysm() {
        return lysm;
    }

    public void setLysm(String lysm) {
        this.lysm = lysm;
    }
}
