package com.hzwangda.aigov.modules.form.rest;

import com.hzwangda.aigov.modules.form.domain.A23ReportForm;
import com.hzwangda.aigov.modules.form.dto.A23ReportFormDto;
import com.hzwangda.aigov.modules.form.dto.ReportFormQueryCriteria;
import com.hzwangda.aigov.modules.form.service.ReportFromService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

@RestController
@RequestMapping("/api/report/form")
@RequiredArgsConstructor
@Api(tags = "数据填报模版管理")
public class ReportFormController {

    private final ReportFromService service;

    @ApiOperation(value = "分页查询表单模版", notes = "分页查询表单模版")
    @GetMapping
    public Page<A23ReportFormDto> query(ReportFormQueryCriteria criteria, Pageable pageable) {
        return service.query(criteria, pageable, true);
    }

    @ApiOperation(value = "删除表单模版", notes = "删除表单模版")
    @PostMapping("delete")
    public void delete(@RequestBody Set<Long> ids) {
        service.delete(ids);
    }

    @PostMapping("update")
    @ApiOperation(value = "修改表单模版", notes = "修改表单模版")
    public A23ReportForm update(@RequestBody A23ReportForm resources) {
        return service.update(resources);
    }

    @ApiOperation(value = "创建表单模版", notes = "创建表单模版")
    @PostMapping
    public A23ReportForm create(@RequestBody A23ReportForm resources) {
        return service.save(resources);
    }

    @ApiOperation(value = "根据id查找记录", notes = "根据id查找记录")

    @GetMapping("{id}")
    public A23ReportForm queryById(@PathVariable Long id) {

        return service.queryById(id);
    }

}
