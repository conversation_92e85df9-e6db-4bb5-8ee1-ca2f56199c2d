/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.modules.document.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-06-04
 **/
@Data
public class A07DocumentZcwjQueryCriteria {

    @ApiModelProperty(value = "类型(0:内网发布,1:外网发布)")
    private Integer type;

    /**
     * 内网发布
     */
    @Query(type = Query.Type.EQUAL)
    @ApiModelProperty(value = "无需传值")
    private String nwfb;

    /**
     * 外网字段
     */
    @Query(type = Query.Type.EQUAL)
    @ApiModelProperty(value = "无需传值")
    private String wwfblmCode;

    /**
     * 公开类型
     */
    @Query(type = Query.Type.EQUAL)
    @ApiModelProperty(value = "公开类型")
    private String gklx;

    @Query(type = Query.Type.INNER_LIKE)
    @ApiModelProperty(value = "标题")
    private String bt;

    @Query(type = Query.Type.BETWEEN)
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private List<Timestamp> createTime;

    @Query(type = Query.Type.EQUAL)
    @ApiModelProperty(value = "发文状态")
    private String zcwj;


}
