package com.hzwangda.aigov.modules.collaboration.service.impl;


import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationAssignee;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationHandleLog;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationInfo;
import com.hzwangda.aigov.modules.collaboration.repository.A10CollaborationAssigneeRepository;
import com.hzwangda.aigov.modules.collaboration.repository.A10CollaborationHandleLogRepository;
import com.hzwangda.aigov.modules.collaboration.repository.A10CollaborationInfoRepository;
import com.hzwangda.aigov.modules.collaboration.service.A10CollaborationHandleLogService;
import com.hzwangda.aigov.modules.task.base.TaskConstants;
import com.hzwangda.aigov.modules.task.utils.SendMessage;
import com.hzwangda.aigov.oa.bo.MasSendContentBO;
import com.hzwangda.aigov.oa.bo.MasUserBO;
import com.hzwangda.aigov.oa.constant.AuthorityConstant;
import com.hzwangda.aigov.oa.repository.WdSysOptionRepository;
import com.hzwangda.aigov.oa.service.MasBusinessService;
import com.hzwangda.aigov.oa.util.OaUtil;
import com.hzwangda.aigov.oa.util.SMSFormatUtil;
import com.hzwangda.aigov.zwdd.service.ZwddService;
import com.wangda.oa.config.ElPermissionConfig;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationBO;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationLinkBO;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.extension.domain.SysUserPlatform;
import com.wangda.oa.modules.extension.domain.WdSysOption;
import com.wangda.oa.modules.extension.repository.SysUserPlatformRepository;
import com.wangda.oa.modules.security.service.dto.JwtUserDto;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.system.service.mapstruct.UserMapper;
import com.wangda.oa.modules.workflow.dto.AlreadyUserListBO;
import com.wangda.oa.modules.workflow.dto.BacklogDto;
import com.wangda.oa.modules.workflow.dto.BacklogUserDto;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class A10CollaborationHandleLogServiceImpl implements A10CollaborationHandleLogService {

    private final A10CollaborationHandleLogRepository collaborationHandleLogRepository;
    private final A10CollaborationAssigneeRepository collaborationAssigneeRepository;
    private final UserRepository userRepository;
    private final WdSysOptionRepository wdSysOptionRepository;
    private final MasBusinessService masBusinessService;
    private final A10CollaborationInfoRepository a10CollaborationInfoRepository;
    private final SendMessage sendMessage;
    private final ZwddProperties zwddProperties;
    private final ZwddService zwddService;
    private final SysUserPlatformRepository sysUserPlatformRepository;
    private final ElPermissionConfig elPermissionConfig;
    private final UserMapper userMapper;

    @Override
    @Transactional
    public Long doHandle(A10CollaborationHandleLog log) {
        String handleType = log.getHandleType();
        final String currentUsername=log.getUserType()== TaskConstants.NUMBER_TWO?SecurityUtils.getBindDeptUserName():SecurityUtils.getCurrentUsername();
        if(log.getUserType()== TaskConstants.NUMBER_TWO){
            log.setPrefixDeptName(SecurityUtils.getBindDeptName());
        }else{
            UserDto user = ((JwtUserDto) SecurityUtils.getCurrentUser()).getUser();
            log.setPrefixDeptName(SecurityUtils.getUnitAbbrName(Long.valueOf(SecurityUtils.getRedisCurrentDeptCode().get("deptId")+""))+"-"+user.getNickName());
        }
        log.setHandleUser(currentUsername);
        log.setHandleTime(Calendar.getInstance().getTime());

        //签收，将所有待签签收
        if (handleType.equals("sign")) {

            log.setRemark(null);
            List<A10CollaborationAssignee> list = collaborationAssigneeRepository.findByInfoIdAndHandleUserAndStatusIn(log.getInfoId(),
                    currentUsername, new String[]{"new", "unsign"});
            if (list.size() > 0) {
                list.forEach(a10CollaborationAssignee -> {
                    a10CollaborationAssignee.setStatus("signed");
                    a10CollaborationAssignee.setSignedTime(Calendar.getInstance().getTime());
                });
                collaborationAssigneeRepository.saveAll(list);
            } else {
                return null;
            }
        }
        //办结，并将所有待签签收
        if (handleType.equals("finish")) {

            // 待办消除
            List<AlreadyUserListBO> alreadyUserList = new ArrayList<>();
            AlreadyUserListBO alreadyUser = new AlreadyUserListBO();
            alreadyUser.setTransactionTime(System.currentTimeMillis() / 1000);

            if(log.getUserType()== TaskConstants.NUMBER_TWO){
                alreadyUser.setUserId(SecurityUtils.getBindDeptUserName());
            }else{
                alreadyUser.setUserId(SecurityUtils.getCurrentUsername());
            }
            alreadyUserList.add(alreadyUser);

            BacklogDto backlogDto = new BacklogDto();
            backlogDto.setAppId("OA");
            backlogDto.setBizId(log.getInfoId().toString());
            backlogDto.setModuleCode("collaborationInfo");
            backlogDto.setModuleName("工作协同");
            backlogDto.setExtensionJson("{}");
            backlogDto.setPcUrl(zwddProperties.getWebUrl() + "/#/document/collaboration/info?sidebarHide=false&navbar=false&id=" + log.getInfoId());
            //backlogDto.setPcUrl(zwddProperties.getWebUrl()+"/#/meetingMgt/meetingNote/feedBackIndex");
           // backlogDto.setUrl(zwddProperties.getAppUrl() + "/#/document/collaboration/info?sidebarHide=false&navbar=false&id=" + log.getInfoId());
            backlogDto.setUrl(zwddProperties.getAppUrl() + "workbench");
            backlogDto.setUrgent(0);
            Long infoId = log.getInfoId();
            A10CollaborationInfo a10CollaborationInfo = a10CollaborationInfoRepository.findById(infoId).get();
            if (a10CollaborationInfo != null) {
                backlogDto.setUserId(a10CollaborationInfo.getCreateBy());
            }
            backlogDto.setAlreadyUserList(alreadyUserList);
            backlogDto.setLogo("gzxt");
            Boolean bool = OaUtil.pushToBacklog(backlogDto);


            List<A10CollaborationAssignee> list = collaborationAssigneeRepository.findByInfoIdAndHandleUserAndStatusIn(log.getInfoId(),
                    currentUsername, new String[]{"signed"});
            if (list.size() > 0) {
                list.forEach(a10CollaborationAssignee -> {
                    a10CollaborationAssignee.setStatus("finish");
                    a10CollaborationAssignee.setEndTime(Calendar.getInstance().getTime());
                });
                collaborationAssigneeRepository.saveAll(list);
            } else {
                return null;
            }
        }
        //转发，将所有签收，并创建新的A10CollaborationAssignee
        if ("forward".equals(handleType)) {
            log.getAssignees().forEach(a10CollaborationAssignee -> {
                UserDto assigneeDto=userMapper.toDto(userRepository.findByUsername(a10CollaborationAssignee.getHandleUser()));
                //TODO 若单位账号只需显示单位账号的nick,则需判断其是否为单位账号。
                a10CollaborationAssignee.setPrefixDeptName(SecurityUtils.getUnitAbbrName(assigneeDto.getDept().getId())+"-"+a10CollaborationAssignee.getHandleUserName());
                a10CollaborationAssignee.setFromUser(currentUsername);
                a10CollaborationAssignee.setStatus("new");
            });

            WdSysOption firstByKey = wdSysOptionRepository.getFirstByKey("A10Collaboration.SMS");
//            Assert.notNull(firstByKey, "短信模板没有配置!");
            String value = firstByKey.getValue();
            Map map = new HashMap();
            String sendName="";
            if(log.getUserType()==TaskConstants.NUMBER_ONE){
                sendName= ((JwtUserDto) SecurityUtils.getCurrentUser()).getUser().getNickName();
            }else if(log.getUserType()==TaskConstants.NUMBER_TWO){
                sendName=SecurityUtils.getBindDeptName();
            }
            map.put("username", sendName);
            A10CollaborationInfo a10CollaborationInfo = a10CollaborationInfoRepository.findById(log.getInfoId()).get();
            map.put("bt", a10CollaborationInfo.getSubject());
            String content = SMSFormatUtil.processTemplate(value, map);

            // 短信通知接收人
            Boolean sms = log.getSms();
            if (sms != null && sms) {
                MasSendContentBO bo = new MasSendContentBO();
                bo.setContent(content);
                List<MasUserBO> userBOList = log.getAssignees().stream().map(assignee -> {
                    MasUserBO masUserBO = new MasUserBO();
                    User handleUser = userRepository.findByUsername(assignee.getHandleUser());
                    masUserBO.setId(String.valueOf(handleUser.getId()));
                    masUserBO.setNickName(handleUser.getNickName());
                    masUserBO.setPhone(handleUser.getPhone());

                    return masUserBO;
                }).collect(Collectors.toList());
                bo.setUserBOList(userBOList);
                bo.setSendDate(new Date());
                bo.setTiming(0);
                masBusinessService.sendSMContent(bo, 0, 0);
            }

            // 发送钉钉消息
            Boolean ding = log.getDing();
            if (ding != null && ding) {
                log.getAssignees().stream().forEach(assignee -> {
//                    sendMessage.sendZwddMessage(assignee.getHandleUser(), content);
                    List<SysUserPlatform> sysUserPlatforms = sysUserPlatformRepository.findByUserNameAndType(assignee.getHandleUser(), zwddProperties.getType());
                    String receiverIds = sysUserPlatforms.stream().map(SysUserPlatform::getPlatformUserId).collect(Collectors.joining(","));
                    WorkNotificationBO workNotificationBO = new WorkNotificationBO();
                    workNotificationBO.setReceiverIds(receiverIds);
                    workNotificationBO.setType(1);
                    WorkNotificationLinkBO workNotificationLinkBO = new WorkNotificationLinkBO();
                    workNotificationLinkBO.setText(content);
                    workNotificationLinkBO.setTitle("工作协同");
                    String serviceUrl = zwddProperties.getAppUrl() + "workbench";
                    //String serviceUrl = zwddProperties.getAppUrl() + "/#/checkSee?id=" + log.getInfoId();
                    workNotificationLinkBO.setMessageUrl(serviceUrl);
                    workNotificationBO.setLinkBO(workNotificationLinkBO);
                    zwddService.workNotification(workNotificationBO);

       /*             String[] split = receiverIds.split(",");
                    for (String id : split) {
                        TaskCreateBO taskBO = TaskCreateBO.builder()
                                .subject(a10CollaborationInfo.getSubject())
                                .creatorId(SecurityUtils.getCurrentUsername())
                                .bizTaskId(log.getInfoId().toString())
                                .url(serviceUrl)
                                .mobileUrl(serviceUrl)
                                .assigneeId(id)
                                .build();
                        zwddService.createTask(taskBO);
                    }*/
                });
            }

            List<BacklogUserDto> backlogUserList = log.getAssignees().stream().map(assignee -> {
                BacklogUserDto backlogUser = new BacklogUserDto();
                backlogUser.setCc(0);
                backlogUser.setUserId(assignee.getHandleUser());
                backlogUser.setCreateDate(System.currentTimeMillis() / 1000);
                return backlogUser;
            }).collect(Collectors.toList());
            BacklogDto backlogDto = new BacklogDto();
            backlogDto.setAppId("OA");
            backlogDto.setBizId(log.getInfoId().toString());
            backlogDto.setModuleCode("collaborationInfo");
            backlogDto.setModuleName("工作协同");
            backlogDto.setTitle(a10CollaborationInfo.getSubject());
            backlogDto.setExtensionJson("{}");
            backlogDto.setPcUrl(zwddProperties.getWebUrl() + "/#/document/collaboration/info?sidebarHide=false&navbar=false&id=" + a10CollaborationInfo.getId());
            //backlogDto.setPcUrl(zwddProperties.getWebUrl()+"/#/meetingMgt/meetingNote/feedBackIndex");
            //backlogDto.setUrl(zwddProperties.getAppUrl() + "/#/checkSee?id=" + a10CollaborationInfo.getId());
            backlogDto.setUrl(zwddProperties.getAppUrl() + "workbench");
            backlogDto.setUrgent(0);
            backlogDto.setUserId(SecurityUtils.getCurrentUserId().toString());
            backlogDto.setBacklogUserList(backlogUserList);
            backlogDto.setLogo("gzxt");
            Boolean bool = OaUtil.pushToBacklog(backlogDto);
        }


        return collaborationHandleLogRepository.save(log).getId();
    }

    @Override
    public Page<A10CollaborationHandleLog> queryHandleLog(Long infoId, Pageable pageable) {
//        Page<A10CollaborationHandleLog> page = collaborationHandleLogRepository.findByInfoId(infoId, pageable);
        Specification<A10CollaborationHandleLog> sp = (root, criteriaQuery, criteriaBuilder) -> {
            return criteriaBuilder.and(
                    criteriaBuilder.equal(root.get("infoId"), infoId),
                    criteriaBuilder.or(
                            criteriaBuilder.notEqual(root.get("handleType"), "comment"),
                            criteriaBuilder.and(
                                    criteriaBuilder.equal(root.get("handleType"), "comment"),
                                    criteriaBuilder.isNotNull(root.get("remark")),
                                    criteriaBuilder.notEqual(root.get("remark"), "")
                            )
                    )
            );
        };
        Page<A10CollaborationHandleLog> page = collaborationHandleLogRepository.findAll(sp, pageable);

        page.getContent().forEach(a10CollaborationHandleLog -> {
            //User user = userRepository.findByUsername(a10CollaborationHandleLog.getHandleUser());
            //if (user != null) {
                a10CollaborationHandleLog.setHandleUserName(a10CollaborationHandleLog.getPrefixDeptName());
            //}
            for (A10CollaborationAssignee a10CollaborationAssignee : a10CollaborationHandleLog.getAssignees()) {
                User byUsername = userRepository.findByUsername(a10CollaborationAssignee.getHandleUser());
                if (byUsername != null) {
                    a10CollaborationAssignee.setHandleUserName(byUsername.getNickName());
                }
            }
        });
        return page;
    }

    @Override
    public List<A10CollaborationAssignee> queryUnsignedAssignees(Long infoId) {
        Specification<A10CollaborationAssignee> sp = (root, criteriaQuery, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.and(
                    criteriaBuilder.equal(root.get("infoId"), infoId),
                    criteriaBuilder.equal(root.get("status"), "new")
            );
            if (!elPermissionConfig.check(AuthorityConstant.AUTHORITY_COLLABORATION_ADMIN)) {
                predicate = criteriaBuilder.and(
                        predicate,
                        criteriaBuilder.equal(root.get("fromUser"), SecurityUtils.getCurrentUsername())
                );
            }
            criteriaQuery.orderBy(
                    criteriaBuilder.asc(root.get("fromUser")),
                    criteriaBuilder.asc(root.get("createTime"))
            );
            return predicate;
        };
        List<A10CollaborationAssignee> list = collaborationAssigneeRepository.findAll(sp);
        setNickName(list.stream());
        return list;
    }

    @Override
    public Page<A10CollaborationAssignee> queryAssignees(Long infoId, String status, String keyword, Pageable pageable) {
        Specification<A10CollaborationAssignee> sp = (root, criteriaQuery, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.equal(root.get("infoId"), infoId);
            if (!status.equals("all")) {
                predicate = criteriaBuilder.and(
                        predicate,
                        criteriaBuilder.equal(root.get("status"), status)
                );
            }
            if (StringUtils.isNotEmpty(keyword)) {
                List<User> users = userRepository.findByNickNameLike("%" + keyword + "%");
                if (users.size() > 0) {
                    List<String> usernames = users.stream().map(User::getUsername).collect(Collectors.toList());
                    predicate = criteriaBuilder.and(
                            predicate,
                            root.get("handleUser").in(usernames)
                    );
                }
            }
            return predicate;
        };

        Page<A10CollaborationAssignee> all = collaborationAssigneeRepository.findAll(sp, pageable);
        setNickName(all.stream());
        return all;
    }

    private void setNickName(Stream<A10CollaborationAssignee> stream) {
        stream.forEach(assignee -> {
            User fromUser = userRepository.findByUsername(assignee.getFromUser());
            if (fromUser != null) {
                assignee.setFromUser(fromUser.getNickName());
            }
            User handleUser = userRepository.findByUsername(assignee.getHandleUser());
            if (handleUser != null) {
                assignee.setHandleUserName(handleUser.getNickName());
            }
        });
    }
}
