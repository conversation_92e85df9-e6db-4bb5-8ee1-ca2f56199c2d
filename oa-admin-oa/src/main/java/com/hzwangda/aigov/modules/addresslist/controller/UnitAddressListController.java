package com.hzwangda.aigov.modules.addresslist.controller;

import com.hzwangda.aigov.modules.addresslist.domain.criteria.UnitAddressListQueryCriteria;
import com.hzwangda.aigov.modules.addresslist.domain.dto.UnitAddressListDto;
import com.hzwangda.aigov.modules.addresslist.service.UnitAddressListService;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.extension.bo.UserListBO;
import com.wangda.oa.modules.extension.dto.org.UserListDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: zhangzhanlong
 * @date: 2022/8/5 13:20
 * @description: 单位通讯录
 */
@RestController
@RequestMapping("unitAddressList")
@Api(tags = "单位通讯录")
public class UnitAddressListController {
    @Resource
    private UnitAddressListService addressListService;

    @ApiOperation(value = "单位通讯录查询全部")
    @GetMapping("/queryList")
    public ResponseEntity queryList(UnitAddressListQueryCriteria criteria, Pageable pageable) {
        return ResponseEntity.ok(addressListService.queryList(criteria, pageable));
    }

    @ApiOperation(value = "单位通讯录查询单个")
    @GetMapping("/queryOne")
    public ResponseEntity queryOne(@RequestParam("id") Long id) {
        return ResponseEntity.ok(addressListService.queryOne(id));
    }

    @ApiOperation(value = "单位通讯录新增")
    @PostMapping("/create")
    public ResponseEntity create(@RequestBody UnitAddressListDto addressListDto) {
        return ResponseEntity.ok(addressListService.create(addressListDto));
    }

    @ApiOperation(value = "单位通讯录更新")
    @PostMapping("/update")
    public ResponseEntity update(@RequestBody UnitAddressListDto addressListDto) {
        return ResponseEntity.ok(addressListService.update(addressListDto));
    }

    @ApiOperation(value = "单位通讯录删除")
    @PostMapping("/del")
    public ResponseEntity del(@RequestBody Long id) {
        return ResponseEntity.ok(addressListService.del(id));
    }

    @ApiOperation(value = "单位通讯录控件查询")
    @GetMapping(value = "/queryModule")
    public ResponseEntity queryModule(UnitAddressListQueryCriteria criteria) {
        return ResponseEntity.ok(addressListService.queryModule(criteria));
    }

    /**
     * 根据用户名查询用户集合
     *
     * @return
     */
    @ApiOperation(value = "根据用户名查询用户集合", notes = "根据用户名查询用户集合")
    @RequestMapping(value = "/getUserList", method = RequestMethod.POST)
    public ResultJson<List<UserListDto>> getUserList(@RequestBody UserListBO bo) {
        return addressListService.getUserList(bo);
    }
}
