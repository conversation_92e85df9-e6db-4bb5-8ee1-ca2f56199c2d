package com.hzwangda.aigov.modules.appControl.controller;

import com.hzwangda.aigov.modules.appControl.domain.Z08SelfApplication;
import com.hzwangda.aigov.modules.appControl.dto.SelfApplicationCriteria;
import com.hzwangda.aigov.modules.appControl.service.SelfApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: cy
 * @date: 2022/8/23 10:10
 * @description: 应用基础设置
 */
@Api(tags = "首页及自选应用设置接口")
@RestController
@RequestMapping("/api/selfApplication")
public class SelfApplicationController {
    @Resource
    private SelfApplicationService applicationService;

    @ApiOperation(value = "新增或修改基础设置")
    @RequestMapping(value = "/addOrUpdateBasics", method = RequestMethod.POST)
    public ResponseEntity<Z08SelfApplication> addOrUpdateBasics(@RequestBody Z08SelfApplication application) {
        return ResponseEntity.ok(applicationService.addOrUpdateBasics(application));
    }

    @ApiOperation(value = "获取应用基础设置信息")
    @GetMapping(value = "/getById/{id}")
    public ResponseEntity<Z08SelfApplication> getById(@ApiParam(value = "应用Id", required = true) @PathVariable Long id) {
        return ResponseEntity.ok(applicationService.getById(id));
    }

    @ApiOperation(value = "查询应用列表分页")
    @GetMapping(value = "/queryListPage")
    public ResponseEntity<Page<Z08SelfApplication>> queryListPage(SelfApplicationCriteria criteria, Pageable pageable) {
        return ResponseEntity.ok(applicationService.queryListPage(criteria, pageable));
    }

    @ApiOperation(value = "删除应用")
    @PostMapping("/delete/{id}")
    public ResponseEntity<Boolean> delete(@ApiParam(value = "应用Id", required = true) @PathVariable Long id) {
        return ResponseEntity.ok(applicationService.delete(id));
    }


    @ApiOperation(value = "根据应用名称查询应用")
    @GetMapping(value = "/getByApplicationName")
    public ResponseEntity<List<Z08SelfApplication>> getByApplicationName(String applicationName) {
        return ResponseEntity.ok(applicationService.getByApplicationName(applicationName));
    }

    @ApiOperation(value = "根据应用类别查询应用")
    @GetMapping(value = "/getByType")
    public ResponseEntity<List<Z08SelfApplication>> getByType(String type) {
        return ResponseEntity.ok(applicationService.getByType(type));
    }

}
