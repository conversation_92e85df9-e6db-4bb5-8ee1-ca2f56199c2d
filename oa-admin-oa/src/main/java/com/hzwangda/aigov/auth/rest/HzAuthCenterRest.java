package com.hzwangda.aigov.auth.rest;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DateUtil;
import com.hzwangda.aigov.auth.config.HzAuthRequestConfig;
import com.hzwangda.aigov.auth.config.HzAuthResponse;
import com.wangda.oa.annotation.rest.AnonymousGetMapping;
import com.wangda.oa.annotation.rest.AnonymousPostMapping;
import com.wangda.oa.domain.Log;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.security.config.bean.LoginProperties;
import com.wangda.oa.modules.security.config.bean.SecurityProperties;
import com.wangda.oa.modules.security.security.CASAuthToken;
import com.wangda.oa.modules.security.security.TokenProvider;
import com.wangda.oa.modules.security.service.OnlineUserService;
import com.wangda.oa.modules.security.service.dto.JwtUserDto;
import com.wangda.oa.modules.security.service.dto.JwtUserVo;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.system.repository.UserRepositoryCustom;
import com.wangda.oa.repository.LogRepository;
import com.wangda.oa.service.LogService;
import com.wangda.oa.service.dto.LogQueryCriteria;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.StringUtils;
import io.swagger.annotations.ApiOperation;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

import static jdk.nashorn.api.scripting.ScriptUtils.unwrap;

@RequestMapping("/hz-auth")
@RestController
@Slf4j
public class HzAuthCenterRest {

    @Resource
    private HzAuthRequestConfig hzAuthRequestConfig;
    @Resource
    private AuthenticationManagerBuilder authenticationManagerBuilder;
    @Resource
    private TokenProvider tokenProvider;
    @Resource
    private OnlineUserService onlineUserService;
    @Resource
    private SecurityProperties properties;
    @Resource
    private LoginProperties loginProperties;
    @Resource
    private UserRepository userRepository;
    @Resource
    private UserRepositoryCustom userRepositoryCustom;
    @Resource
    private LogRepository logRepository;
    @PersistenceContext
    private EntityManager em;

    @AnonymousPostMapping("/login")
    public ResponseEntity login(@RequestParam String accessToken, HttpServletRequest request) {
        log.info("获取操作日志,时间戳1：{}", System.currentTimeMillis());
        HzAuthResponse response = hzAuthRequestConfig.auth(accessToken);
        log.info("获取操作日志,时间戳2：{}", System.currentTimeMillis());
        if (response.getSuccess()) {
            String accountId = response.getAccountId();
            // TODO: 登录换取token
            Map<String, Object> authInfo = getToken(request, accountId);
            return ResponseEntity.ok(authInfo);
        } else {
            return ResponseEntity.badRequest().body(response.getMessage());
        }
    }

    @ApiOperation(value = "操作日志")
    @AnonymousGetMapping("/log/getList")
    public Result operationLogs(@RequestParam String token, @RequestParam(defaultValue = "1", required = false) int pageNo, @RequestParam(defaultValue = "10", required = false) int pageSize,
                                @RequestParam(required = false) String userName,
                                @RequestParam(required = false) String module,
                                @RequestParam(required = false) String ip,
                                @RequestParam(required = false) String timeStart,
                                @RequestParam(required = false) String timeEnd
    ) {
        try {
            log.info("获取操作日志,时间戳1：{}", System.currentTimeMillis());
            HzAuthResponse response = hzAuthRequestConfig.auth(token);
            log.info("获取操作日志,时间戳2：{}", System.currentTimeMillis());
            if (response.getSuccess()) {
                StringBuffer countSql = new StringBuffer("SELECT COUNT(*) FROM sys_log l WHERE l.username!='' AND l.username is not null ");
                StringBuffer sql = new StringBuffer("SELECT l.*,u.nick_name from sys_log l LEFT JOIN sys_user u ON l.username=u.username WHERE l.username!='' AND l.username is not null ");
                List<Object[]> params = new ArrayList();
                if (userName != null) {
                    List<User> users = userRepository.findByNickName(userName);
                    if (!users.isEmpty()) {
                        countSql.append(" AND l.username =:username ");
                        sql.append(" AND l.username =:username ");
                        params.add(new Object[]{"username", users.get(0).getUsername()});
                    }
                }
                if (timeStart != null && timeEnd != null) {
                    countSql.append(" AND l.create_time BETWEEN :startTime AND :endTime ");
                    sql.append(" AND l.create_time BETWEEN :startTime AND :endTime ");
                    params.add(new Object[]{"startTime", DateUtil.parse(timeStart).getTime()});
                    params.add(new Object[]{"endTime", DateUtil.parse(timeEnd).getTime()});
                }
                if (ip != null) {
                    countSql.append(" AND l.request_ip =:ip ");
                    sql.append(" AND l.request_ip =:ip ");
                    params.add(new Object[]{"ip", ip});
                }
                if (module != null) {
                    countSql.append(" AND l.description like :module ");
                    sql.append(" AND l.description like :module ");
                    params.add(new Object[]{"module", "%" + module + "%"});
                }
                sql.append(" ORDER BY l.create_time DESC ");
                Query countQuery = em.createNativeQuery(countSql.toString());
                Query nativeQuery = em.createNativeQuery(sql.toString());
                for (Object[] param : params) {
                    countQuery.setParameter((String) param[0], param[1]);
                    nativeQuery.setParameter((String) param[0], param[1]);
                }
                Object singleResult = countQuery.getSingleResult();
                List<Map> resultList = nativeQuery
                        .unwrap(SQLQuery.class)
                        .setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
                        .setFirstResult((pageNo - 1) * pageSize)
                        .setMaxResults(pageSize)
                        .list();
                log.info("获取操作日志,时间戳3：{}", System.currentTimeMillis());
                return Result.builder()
                        .success(true)
                        .msg("成功")
                        .code(200)
                        .data(PageResult.builder()
                                .current(pageNo)
                                .size(pageSize)
                                .total(Long.valueOf(singleResult.toString()))
                                .records(resultList
                                        .stream()
                                        .map(item -> {
                                            Map<String, Object> map = new HashMap<>();
                                            map.put("id", item.get("id"));
                                            map.put("ip", item.get("request_ip"));
                                            map.put("module", item.get("description"));
                                            map.put("systemName", "湖州市南浔区政务协同系统");
                                            map.put("operation", item.get("description"));
                                            map.put("userName", item.get("nick_name"));
                                            map.put("gmtCreate", item.get("create_time"));
                                            return map;
                                        }).collect(Collectors.toList())
                                )
                                .build()
                        )
                        .build();
            } else {
                return Result.builder()
                        .success(false)
                        .code(400)
                        .msg(response.getMessage())
                        .build();
            }
        } catch (BadRequestException e) {
            String message = e.getMessage();
            String[] split = message.split(":");
            return Result.builder()
                    .success(false)
                    .msg(split[1])
                    .code(Integer.valueOf(split[0]))
                    .build();
        } finally {
            log.info("获取操作日志,时间戳4：{}", System.currentTimeMillis());
        }
    }

    private Map<String, Object> getToken(HttpServletRequest request, String accountId) {
        CASAuthToken authenticationToken = new CASAuthToken(accountId);
        Authentication authentication = authenticationManagerBuilder.getObject().authenticate(authenticationToken);
        SecurityContextHolder.getContext().setAuthentication(authentication);
        String token = tokenProvider.createToken(authentication);
        JwtUserDto jwtUserDto = (JwtUserDto) authentication.getPrincipal();
        onlineUserService.save(jwtUserDto, token, request);
        // 过滤密码
        JwtUserVo jwtUserVo = new JwtUserVo();
        BeanUtil.copyProperties(jwtUserDto, jwtUserVo, CopyOptions.create().ignoreError());

        // 返回 token 与 用户信息
        Map<String, Object> authInfo = new HashMap<String, Object>(2) {{
            put("token", properties.getTokenStartWith() + token);
            put("user", jwtUserVo);
        }};
        if (loginProperties.isSingleLogin()) {
            //踢掉之前已经登录的token
            onlineUserService.checkLoginOnUser(accountId, token);
        }
        return authInfo;
    }


    @Data
    @Builder
    static class Result {
        private Boolean success;
        private String msg;
        private Integer code;
        private PageResult data;
    }

    @Data
    @Builder
    static class PageResult<T> {
        private List<T> records;
        private Long total;
        private Integer size;
        private Integer current;
        private Integer pages;
    }
}
