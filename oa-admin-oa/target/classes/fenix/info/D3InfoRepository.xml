<?xml version="1.0" encoding="UTF-8"?>
<fenixs namespace="com.hzwangda.aigov.modules.zjedu.info.repository.D3InfoRepository">
    <fenix id="getStatusAndCreateTimeAndContributeDeptName">
        select
        max(d.createBy) as createBy,
        d.contributeDeptName as contributeDeptName,
        max(d.contributeDept) as contributeDept ,
        max(d.contributeUser) as contributeUser,
        count(d.id) as submitCount
        from D3Info d
        <trimWhere>
            <andNotEqual field="d.status" value="status" match="status != empty"/>
            <andBetween field="d.updateTime" start="startTime" end="endTime"
                        match="startTime != null &amp;&amp; startTime != empty &amp;&amp; endTime != null &amp;&amp; endTime != empty"/>
            <andEqual field="d.contributeDeptName" name="contributeDeptName" value="contributeDeptName"
                      match="contributeDeptName != empty"/>
        </trimWhere>
        group by d.contributeDeptName
    </fenix>
</fenixs>
