package com.hzwangda.aigov.modules.news.dto.mapstruct;

import com.hzwangda.aigov.modules.news.dto.A06NewsDto;
import com.hzwangda.aigov.modules.news.entity.A06News;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A06NewsMapperImpl implements A06NewsMapper {

    @Autowired
    private StorageMapperUtil storageMapperUtil;

    @Override
    public List<A06NewsDto> toDto(List<A06News> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A06NewsDto> list = new ArrayList<A06NewsDto>( entityList.size() );
        for ( A06News a06News : entityList ) {
            list.add( toDto( a06News ) );
        }

        return list;
    }

    @Override
    public A06News toEntity(A06NewsDto dto) {
        if ( dto == null ) {
            return null;
        }

        A06News a06News = new A06News();

        a06News.setBelongToDept( dto.getBelongToDept() );
        a06News.setBpmInstanceId( dto.getBpmInstanceId() );
        a06News.setBpmProcessKey( dto.getBpmProcessKey() );
        a06News.setBpmStatus( dto.getBpmStatus() );
        a06News.setBpmSubject( dto.getBpmSubject() );
        a06News.setCreateBy( dto.getCreateBy() );
        a06News.setCreateTime( dto.getCreateTime() );
        a06News.setId( dto.getId() );
        List<String> list = dto.getParticipateUser();
        if ( list != null ) {
            a06News.setParticipateUser( new ArrayList<String>( list ) );
        }
        a06News.setUpdateBy( dto.getUpdateBy() );
        a06News.setUpdateTime( dto.getUpdateTime() );
        a06News.setVersion( dto.getVersion() );
        a06News.setAnnouncement( dto.getAnnouncement() );
        a06News.setContent( dto.getContent() );
        a06News.setDeptName( dto.getDeptName() );
        a06News.setInfoType( dto.getInfoType() );
        a06News.setReleaseDate( dto.getReleaseDate() );
        a06News.setRemark( dto.getRemark() );
        a06News.setSendType( dto.getSendType() );
        a06News.setTitle( dto.getTitle() );
        a06News.setAttachments( storageMapperUtil.toConvertToId( dto.getAttachments() ) );

        return a06News;
    }

    @Override
    public List<A06News> toEntity(List<A06NewsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A06News> list = new ArrayList<A06News>( dtoList.size() );
        for ( A06NewsDto a06NewsDto : dtoList ) {
            list.add( toEntity( a06NewsDto ) );
        }

        return list;
    }

    @Override
    public A06NewsDto toDto(A06News entity) {
        if ( entity == null ) {
            return null;
        }

        A06NewsDto a06NewsDto = new A06NewsDto();

        a06NewsDto.setBelongToDept( entity.getBelongToDept() );
        a06NewsDto.setBpmInstanceId( entity.getBpmInstanceId() );
        a06NewsDto.setBpmProcessKey( entity.getBpmProcessKey() );
        a06NewsDto.setBpmStatus( entity.getBpmStatus() );
        a06NewsDto.setBpmSubject( entity.getBpmSubject() );
        a06NewsDto.setCreateBy( entity.getCreateBy() );
        a06NewsDto.setCreateTime( entity.getCreateTime() );
        a06NewsDto.setId( entity.getId() );
        List<String> list = entity.getParticipateUser();
        if ( list != null ) {
            a06NewsDto.setParticipateUser( new ArrayList<String>( list ) );
        }
        a06NewsDto.setUpdateBy( entity.getUpdateBy() );
        a06NewsDto.setUpdateTime( entity.getUpdateTime() );
        a06NewsDto.setVersion( entity.getVersion() );
        a06NewsDto.setAnnouncement( entity.getAnnouncement() );
        a06NewsDto.setContent( entity.getContent() );
        a06NewsDto.setDeptName( entity.getDeptName() );
        a06NewsDto.setInfoType( entity.getInfoType() );
        a06NewsDto.setReleaseDate( entity.getReleaseDate() );
        a06NewsDto.setRemark( entity.getRemark() );
        a06NewsDto.setSendType( entity.getSendType() );
        a06NewsDto.setTitle( entity.getTitle() );

        a06NewsDto.setRead( storageMapperUtil.toConvertToRead(entity.getId()) );
        a06NewsDto.setGuidePicture( storageMapperUtil.toConvertToGuide(entity.getGuideId()) );
        a06NewsDto.setAttachments( storageMapperUtil.toConvertToLocalStorageSimpleDto(entity.getAttachments()) );

        splitTime( entity, a06NewsDto );

        return a06NewsDto;
    }
}
