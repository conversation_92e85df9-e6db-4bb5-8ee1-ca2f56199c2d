package com.hzwangda.aigov.modules.document.service;

import com.hzwangda.aigov.modules.document.dto.*;
import com.hzwangda.aigov.modules.document.entity.A07DocumentInfoWwfb;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface A07FwService {

    /**
     * 发文列表
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> getFwList(A07DocumentFwQueryCriteria criteria, Pageable pageable);

    /**
     * 会签列表
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> getHqList(A07DocumentHqQueryCriteria criteria, Pageable pageable);

    /**
     * 发文详情
     * @param id
     * @return
     */
    A07DocumentFwDto findByDocumentInfo(Long id);

    /**
     * 会签详情
     * @param id
     * @return
     */
    A07DocumentHqDto getHqInfo(Long id);

    /**
     * 发文检索
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> getFwListRetrieve(A07DocumentGwRetrieveQueryCriteria criteria, Pageable pageable);

    /**
     * 删除会签
     * @param ids
     * @return
     */
    Boolean deleteHq(List<Long> ids);

    /**
     * 外网发布数据集合
     * @return
     */
    List<A07DocumentInfoWwfb> getWwfbList();

    /**
     * 删除发文
     * @param ids
     * @return
     */
    Boolean deleteDocumentInfo(List<Long> ids);

    /**
     * 根据代字获取最新的序号值
     * @param dz
     * @return
     */
    Integer getXhByDz(String dz, String nh, String procInstId);

    Long saveFfGwlz(String bpmInstanceId);

    /**
     * 发文信息同步至湖州市cms，用于信息公开
     * @param bpmInstanceId
     * @return
     */
    ResponseEntity<Object> publicDoc(String bpmInstanceId);

    List<Map<String, Object>> getFwCreateByDz();

    /**
     * @param procInstId:
     * @param appid:
     * @description 文种转换判断
     * <AUTHOR>
     * @updateTime 2022/11/1 9:20
     * @return: java.lang.Object
     */

    Object getFwDzSwitchover(String procInstId, String appid);

    /**
     * @param procInstId:
     * @description 发文文种切换数据查询
     * <AUTHOR>
     * @updateTime 2022/11/1 10:13
     * @return: java.lang.Object
     */

    Object getFwDzSwitchoverByProcInstId(String procInstId);


    String saveTaskUserRead(String remark, String users, String bpmInstanceId);
}
