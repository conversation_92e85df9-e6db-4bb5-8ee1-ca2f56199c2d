/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.modules.document.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-06-25
 **/
@Data
public class A07DocumentGwlzQueryCriteria {

    @Query(type = Query.Type.EQUAL)
    @ApiModelProperty(value = "公文状态(草稿,正式)")
    private String gwzt;

    @Query(type = Query.Type.INNER_LIKE)
    @ApiModelProperty(value = "标题")
    private String bt;

    @Query(type = Query.Type.EQUAL)
    @ApiModelProperty(value = "创建人id")
    private Long creatorId;
}
