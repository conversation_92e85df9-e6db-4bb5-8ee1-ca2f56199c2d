package com.hzwangda.aigov.modules.matter.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.wangda.oa.base.BaseDTO;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 事项库申请材料 DTO
 *
 * <AUTHOR>
 * @date 2022/01/14 10:24
 **/
@Data
public class MatterAppMaterialsDto extends BaseDTO implements Serializable {
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    private Timestamp createDate;
    private Long creatorId;
    private Boolean enabled;
    private Timestamp modifiedDate;
    private Long modifiedId;
    private Integer version;
    private String blankFormUrl;
    private Integer isNecessity;
    private Integer isTolerance;
    private String materialsName;
    private String materialsType;
    private Integer numberMateria;
    private String sampleSheetUrl;
    private String specification;
    private Long matterId;
}
