/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.bpm.repository;

import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlz;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-05-14
 **/
public interface A07DocumentGwlzRepository extends JpaRepository<A07DocumentGwlz, Long>, JpaSpecificationExecutor<A07DocumentGwlz> {

    @Modifying
    @Transactional
    @Query("delete from A07DocumentGwlz where id in (?1)")
    int deleteAllByIdIn(Long[] ids);

    A07DocumentGwlz findFirstByOldDocID(String id);

    List<A07DocumentGwlz> findByOldDocIDNotNull();

    @Override
    Page<A07DocumentGwlz> findAll(Pageable pageable);

    List<A07DocumentGwlz> findAllByIdIn(Set<Long> ids);

    // 根据原流程实例id查询是否已分发
    A07DocumentGwlz findFirstByDocPronInstIdAndGwzt(String procInstId, String gwzt);
}
