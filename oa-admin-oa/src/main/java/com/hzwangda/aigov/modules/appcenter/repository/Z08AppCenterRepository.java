package com.hzwangda.aigov.modules.appcenter.repository;

import com.hzwangda.aigov.modules.appcenter.domain.entity.Z08AppCenter;
import com.hzwangda.aigov.modules.document.dto.AppAndSpaceDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


public interface Z08AppCenterRepository extends JpaRepository<Z08AppCenter, Long>, JpaSpecificationExecutor<Z08AppCenter> {

    /**
     * @description 根据appid查询应用名称，空间名称
     * <AUTHOR>
     * @updateTime 2022/11/7 10:06
     */
    @Query("select new com.hzwangda.aigov.modules.document.dto.AppAndSpaceDto(asp.id,asp.name,app.id,app.name,ap.manager,ap.businessManager,ap.createManager)" +
            " from Application  app" +
            " left join AppPermission ap on app.id=ap.applicationId" +
            " left join AppSpace asp on ap.spaceId=asp.id" +
            " where app.appStatus='ONLINE' and app.id in (:appids)")
    List<AppAndSpaceDto> getAppByAppIds(List<Long> appids);

    /**
     * @description 根据appid查询应用名称，空间名称
     * <AUTHOR>
     * @updateTime 2022/11/7 10:06
     */
    @Query("select new com.hzwangda.aigov.modules.document.dto.AppAndSpaceDto(asp.id,asp.name,app.id,app.name,ap.manager,ap.businessManager,ap.createManager)" +
            " from Application  app" +
            " left join AppPermission ap on app.id=ap.applicationId" +
            " left join AppSpace asp on ap.spaceId=asp.id" +
            " where app.appStatus='ONLINE' and app.type in (:type)" +
            " and app.rev=(select max(rev) from Application where appKey=app.appKey )")
    List<AppAndSpaceDto> getAppByTypeIn(List<String> type);


    Z08AppCenter getByUsernameAndAppId(String username, Long appId);

    Z08AppCenter findFirstByUsernameAndAppId(String currentUsername, Long appId);

    List<Z08AppCenter> findByUsername(String currentUsername);

    @Query("select new com.hzwangda.aigov.modules.document.dto.AppAndSpaceDto(asp.id,asp.name,app.id,app.name,ap.manager,ap.businessManager,ap.createManager)" +
            " from Application  app" +
            " left join AppPermission ap on app.id=ap.applicationId" +
            " left join AppSpace asp on ap.spaceId=asp.id" +
            " where app.appStatus='ONLINE' and app.id in (:appIds) and app.name like :name")
    List<AppAndSpaceDto> getAppByAppIdsAndName(List<Long> appIds, String name);

    @Query("select key from AppType where key like :key")
    List<String> getAppTypeByKeyLike(String key);
}
