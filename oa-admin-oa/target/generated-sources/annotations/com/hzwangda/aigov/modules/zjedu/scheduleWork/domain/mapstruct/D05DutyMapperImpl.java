package com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.dto.D05DutyDto;
import com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.entity.D05Duty;
import com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.entity.D05Duty.D05DutyBuilder;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class D05DutyMapperImpl implements D05DutyMapper {

    @Override
    public D05DutyDto toDto(D05Duty entity) {
        if ( entity == null ) {
            return null;
        }

        D05DutyDto d05DutyDto = new D05DutyDto();

        d05DutyDto.setCreateBy( entity.getCreateBy() );
        d05DutyDto.setCreateTime( entity.getCreateTime() );
        d05DutyDto.setUpdateTime( entity.getUpdateTime() );
        d05DutyDto.setUpdatedBy( entity.getUpdatedBy() );
        d05DutyDto.setDepartment( entity.getDepartment() );
        d05DutyDto.setDutyDate( entity.getDutyDate() );
        d05DutyDto.setEndTime( entity.getEndTime() );
        d05DutyDto.setId( entity.getId() );
        d05DutyDto.setSchedule( entity.getSchedule() );
        d05DutyDto.setScheduleOreder( entity.getScheduleOreder() );
        d05DutyDto.setShiftLeader( entity.getShiftLeader() );
        d05DutyDto.setShiftLeaderTel( entity.getShiftLeaderTel() );
        d05DutyDto.setStartTime( entity.getStartTime() );
        d05DutyDto.setStatus( entity.getStatus() );
        d05DutyDto.setTel( entity.getTel() );
        d05DutyDto.setUserName( entity.getUserName() );

        return d05DutyDto;
    }

    @Override
    public List<D05DutyDto> toDto(List<D05Duty> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<D05DutyDto> list = new ArrayList<D05DutyDto>( entityList.size() );
        for ( D05Duty d05Duty : entityList ) {
            list.add( toDto( d05Duty ) );
        }

        return list;
    }

    @Override
    public D05Duty toEntity(D05DutyDto dto) {
        if ( dto == null ) {
            return null;
        }

        D05DutyBuilder d05Duty = D05Duty.builder();

        d05Duty.department( dto.getDepartment() );
        d05Duty.dutyDate( dto.getDutyDate() );
        d05Duty.endTime( dto.getEndTime() );
        d05Duty.id( dto.getId() );
        d05Duty.schedule( dto.getSchedule() );
        d05Duty.scheduleOreder( dto.getScheduleOreder() );
        d05Duty.shiftLeader( dto.getShiftLeader() );
        d05Duty.shiftLeaderTel( dto.getShiftLeaderTel() );
        d05Duty.startTime( dto.getStartTime() );
        d05Duty.status( dto.getStatus() );
        d05Duty.tel( dto.getTel() );
        d05Duty.userName( dto.getUserName() );

        return d05Duty.build();
    }

    @Override
    public List<D05Duty> toEntity(List<D05DutyDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<D05Duty> list = new ArrayList<D05Duty>( dtoList.size() );
        for ( D05DutyDto d05DutyDto : dtoList ) {
            list.add( toEntity( d05DutyDto ) );
        }

        return list;
    }
}
