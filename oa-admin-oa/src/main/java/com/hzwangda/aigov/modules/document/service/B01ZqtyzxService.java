package com.hzwangda.aigov.modules.document.service;

import com.hzwangda.aigov.modules.document.dto.B01ZqtyzxQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @description:
 * @author: zzl
 * @date: 2024/1/23 09:44
 **/
public interface B01ZqtyzxService {
    Map<String, Object> getAllZqtyzxList(B01ZqtyzxQueryCriteria criteria, Pageable pageable);

    Object queryAllBq();
    Object queryAllKz();

    void exportZqtyzx(HttpServletResponse response, B01ZqtyzxQueryCriteria queryCriteria);

    Object queryAllFzr();
}
