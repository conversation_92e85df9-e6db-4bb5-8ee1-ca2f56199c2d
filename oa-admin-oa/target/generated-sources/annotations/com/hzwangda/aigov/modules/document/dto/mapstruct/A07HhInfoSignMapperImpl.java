package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07HhInfoSignDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlzUser;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A07HhInfoSignMapperImpl implements A07HhInfoSignMapper {

    @Override
    public A07HhInfoSignDto toDto(A07DocumentGwlzUser entity) {
        if ( entity == null ) {
            return null;
        }

        A07HhInfoSignDto a07HhInfoSignDto = new A07HhInfoSignDto();

        a07HhInfoSignDto.setQsr( entity.getQsr() );
        a07HhInfoSignDto.setUsername( entity.getUsername() );

        return a07HhInfoSignDto;
    }

    @Override
    public List<A07HhInfoSignDto> toDto(List<A07DocumentGwlzUser> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A07HhInfoSignDto> list = new ArrayList<A07HhInfoSignDto>( entityList.size() );
        for ( A07DocumentGwlzUser a07DocumentGwlzUser : entityList ) {
            list.add( toDto( a07DocumentGwlzUser ) );
        }

        return list;
    }

    @Override
    public A07DocumentGwlzUser toEntity(A07HhInfoSignDto dto) {
        if ( dto == null ) {
            return null;
        }

        A07DocumentGwlzUser a07DocumentGwlzUser = new A07DocumentGwlzUser();

        a07DocumentGwlzUser.setQsr( dto.getQsr() );
        a07DocumentGwlzUser.setUsername( dto.getUsername() );

        return a07DocumentGwlzUser;
    }

    @Override
    public List<A07DocumentGwlzUser> toEntity(List<A07HhInfoSignDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A07DocumentGwlzUser> list = new ArrayList<A07DocumentGwlzUser>( dtoList.size() );
        for ( A07HhInfoSignDto a07HhInfoSignDto : dtoList ) {
            list.add( toEntity( a07HhInfoSignDto ) );
        }

        return list;
    }
}
