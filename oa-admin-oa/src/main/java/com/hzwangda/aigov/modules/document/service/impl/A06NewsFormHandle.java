package com.hzwangda.aigov.modules.document.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.news.entity.A06News;
import com.hzwangda.aigov.modules.news.repository.A06NewsRepository;
import com.wangda.oa.modules.workflow.dto.BacklogListDto;
import com.wangda.oa.modules.workflow.dto.MyWorkDto;
import com.wangda.oa.modules.workflow.service.FlowFormHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/13
 * @description 信息服务表单处理
 */
@Component
@Slf4j
public class A06NewsFormHandle implements FlowFormHandle {

    @Autowired
    private A06NewsRepository a06NewsRepository;

    @Override
    public void handleFormForMyWork(BacklogListDto myWork, MyWorkDto myWorkDto) {

    }

    @Override
    public String handleSubjectRule(JSONObject formDataObj, String subjectRule) {
        return null;
    }

    @Override
    public Object handleFormRecord(String procInstanceId, String taskDefKey, JSONObject bpmFormData) {
        // 如果表单数据有值且不需要处理，直接返回
        if (Objects.nonNull(bpmFormData)) {
            return bpmFormData;
        }
        A06News document = a06NewsRepository.findFirstByBpmInstanceId(procInstanceId);
        return JSONObject.toJSON(document);
    }

    @Override
    public void deleteFormRecord(String instanceId) {
        a06NewsRepository.deleteByBpmInstanceId(instanceId);
    }
}
