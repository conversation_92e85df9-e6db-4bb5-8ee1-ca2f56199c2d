package com.hzwangda.aigov.modules.document.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import com.wangda.oa.domain.LocalStorage;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.BooleanUtils;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 收文单
 * <AUTHOR>
 * @date 2021/6/21下午8:29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class A07AllSwDto extends BaseBpmDomain {

    @ApiModelProperty(value = "收文文号")
    private ReferenceNumber swbh;

    @ApiModelProperty(value = "收文文号展示")
    private String swbhStr;

    @ApiModelProperty(value = "来文文号")
    private String lwwh;

    @ApiModelProperty(value = "来文单位")
    private String lwdw;

    @ApiModelProperty(value = "收文登记人")
    private String createBy;

    @ApiModelProperty(value = "类型")
    private String moduleType;

    @ApiModelProperty(value = "收文日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date swrq;

    @ApiModelProperty(value = "印发日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date yfrq;

    @ApiModelProperty(value = "收文类型")
    private String swlx;

    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "缓急")
    private String hj;

    @ApiModelProperty("任务环节名称")
    private String taskName;

    @ApiModelProperty("任务处理人名称")
    private String assigneeName;

    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty(value = "附件")
    private List<StorageBizDto> fj;

    @ApiModelProperty(value = "意见附件")
    private List<LocalStorage> yjFj;

    @ApiModelProperty(value = "拟办意见")
    private String yjNb;

    @ApiModelProperty(value = "领导签批")
    private String yjLdqp;

    @ApiModelProperty(value = "办理结果")
    private String yjBljg;

    @ApiModelProperty("办理时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date dealDate;

    @ApiModelProperty(value = "办理期限")
    @JSONField(format = "yyyy-MM-dd")
    private Date deadlineDate;

    @ApiModelProperty(value = "归档状态 0待归档，1预归档,2暂不归档，3不归档,4已归档，5归档中，-1移交失败")
    private String fileStatus;

    @ApiModelProperty(value = "是否可以办理")
    private Integer isHandle;

    @ApiModelProperty(value = "是否关注")
    private Boolean isConcern;

    @ApiModelProperty(value = "是否逾期")
    private Boolean isOverdue;

    @ApiModelProperty(value = "是否锁定")
    private Boolean isSuspended;

    @JSONField(serialize = false)
    @ApiModelProperty(value = "排序")
    private Date sorted;

    @JSONField(format = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "待阅已阅接收时间")
    private Date createDate;

    @ApiModelProperty(value = "最后办理时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date endTime;

    public void setId(BigInteger id) {
        this.id = id.longValue();
    }

    public void setIsOverdue(Object overdue) {
        if(Objects.isNull(overdue)) {
            this.isOverdue = false;
            return;
        }

        if(overdue instanceof Integer || overdue instanceof BigInteger) {
            this.isOverdue = BooleanUtils.toBoolean(((Number) overdue).intValue());
            return;
        }

        this.isOverdue = BooleanUtils.toBoolean(overdue.toString());
    }

    public void setIsSuspended(Object isSuspended) {
        if(Objects.isNull(isSuspended)) {
            this.isSuspended = false;
        }else if(isSuspended instanceof Integer || isSuspended instanceof BigInteger) {
            this.isSuspended = BooleanUtils.toBoolean(((Number) isSuspended).intValue());
        }else {
            this.isSuspended = BooleanUtils.toBoolean(isSuspended.toString());
        }
    }
}
