/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.bpm.service;

import com.hzwangda.aigov.bpm.domain.criteria.BpmButtonQueryCriteria;
import com.hzwangda.aigov.bpm.domain.entity.BpmButton;
import org.springframework.data.domain.Pageable;

import java.util.Map;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务接口
 * @date 2021-05-14
 **/
public interface BpmButtonService {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(BpmButtonQueryCriteria criteria, Pageable pageable);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return BpmButtonDto
     */
    BpmButton findById(Long id);

    /**
     * 创建或编辑
     *
     * @param resources /
     * @return BpmButtonDto
     */
    BpmButton createOrUpdate(BpmButton resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Long[] ids);

}