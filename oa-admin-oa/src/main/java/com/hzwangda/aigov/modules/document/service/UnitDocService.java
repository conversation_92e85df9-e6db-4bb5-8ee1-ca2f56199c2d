package com.hzwangda.aigov.modules.document.service;

import com.hzwangda.aigov.modules.document.dto.UnitDocCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Map;

public interface UnitDocService {
    /**
     * 转办单列表
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> getUnitDocList(UnitDocCriteria criteria, Pageable pageable);

    Page transferDoc(UnitDocCriteria unitDocCriteria, Pageable pageable);
}
