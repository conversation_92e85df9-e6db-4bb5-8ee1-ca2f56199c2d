package com.hzwangda.aigov.modules.taskmanagement.domain.mapstruct;

import com.hzwangda.aigov.modules.taskmanagement.domain.dto.TmFinishDto;
import com.hzwangda.aigov.modules.taskmanagement.domain.entity.TmFinish;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class TmFinishMapperImpl implements TmFinishMapper {

    @Override
    public TmFinishDto toDto(TmFinish entity) {
        if ( entity == null ) {
            return null;
        }

        TmFinishDto tmFinishDto = new TmFinishDto();

        tmFinishDto.setCreateBy( entity.getCreateBy() );
        tmFinishDto.setCreateTime( entity.getCreateTime() );
        tmFinishDto.setUpdateTime( entity.getUpdateTime() );
        tmFinishDto.setUpdatedBy( entity.getUpdatedBy() );
        tmFinishDto.setDescription( entity.getDescription() );
        tmFinishDto.setId( entity.getId() );
        tmFinishDto.setNextPlan( entity.getNextPlan() );
        tmFinishDto.setPreviousFinish( entity.getPreviousFinish() );
        tmFinishDto.setQuarter( entity.getQuarter() );
        tmFinishDto.setTask( entity.getTask() );
        tmFinishDto.setTrouble( entity.getTrouble() );

        return tmFinishDto;
    }

    @Override
    public List<TmFinishDto> toDto(List<TmFinish> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmFinishDto> list = new ArrayList<TmFinishDto>( entityList.size() );
        for ( TmFinish tmFinish : entityList ) {
            list.add( toDto( tmFinish ) );
        }

        return list;
    }

    @Override
    public TmFinish toEntity(TmFinishDto dto) {
        if ( dto == null ) {
            return null;
        }

        TmFinish tmFinish = new TmFinish();

        tmFinish.setCreateBy( dto.getCreateBy() );
        tmFinish.setCreateTime( dto.getCreateTime() );
        tmFinish.setUpdateTime( dto.getUpdateTime() );
        tmFinish.setUpdatedBy( dto.getUpdatedBy() );
        tmFinish.setDescription( dto.getDescription() );
        tmFinish.setId( dto.getId() );
        tmFinish.setNextPlan( dto.getNextPlan() );
        tmFinish.setPreviousFinish( dto.getPreviousFinish() );
        tmFinish.setQuarter( dto.getQuarter() );
        tmFinish.setTask( dto.getTask() );
        tmFinish.setTrouble( dto.getTrouble() );

        return tmFinish;
    }

    @Override
    public List<TmFinish> toEntity(List<TmFinishDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TmFinish> list = new ArrayList<TmFinish>( dtoList.size() );
        for ( TmFinishDto tmFinishDto : dtoList ) {
            list.add( toEntity( tmFinishDto ) );
        }

        return list;
    }
}
