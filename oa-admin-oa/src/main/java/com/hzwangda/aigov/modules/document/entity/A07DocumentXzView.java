package com.hzwangda.aigov.modules.document.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.Immutable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@Immutable
@Table(name = "xz_view")
@Entity
@Data
public class A07DocumentXzView {
    @Id
    private Long id;

    @Column(name = "bpm_status")
    private String bpmStatus;

    @ApiModelProperty(value = "类型")
    private String moduleType;

    @Column(name = "cjr")
    @ApiModelProperty(value = "创建人")
    private String cjr;

    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @Column(name = "bpm_instance_id")
    private String bpmInstanceId;

    @Column(name = "update_time")
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @Column(name = "belong_to_dept")
    @ApiModelProperty(value = "归属部门", hidden = true)
    private String belongToDept;
}
