package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.dto.GwCriteria;
import com.hzwangda.aigov.modules.document.service.A07GwService;
import com.wangda.oa.annotation.AnonymousAccess;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "公文管理")
@RequestMapping("/api/aigov/document/gw")
@CrossOrigin
public class A07GwController {

    private final A07GwService a07GwService;

    @ApiOperation("查询流程启动表单默认值")
    @GetMapping(value = "/getGwDefault")
    public ResponseEntity<Object> getFwList() {
        return new ResponseEntity<>(a07GwService.getGwDefault(), HttpStatus.OK);
    }

    @ApiOperation("根据单位查询红头模板")
    @GetMapping(value = "/getDeptHeaderTemplateList")
    public ResponseEntity<Object> getDeptHeaderTemplateList(String procInstId) {
        return new ResponseEntity<>(a07GwService.getDeptHeaderTemplateList(procInstId), HttpStatus.OK);
    }

    @ApiOperation("根据单位查询代字")
    @GetMapping(value = "/getDeptDzList")
    public ResponseEntity<List<String>> getDeptDzList(String dzType) {
        return new ResponseEntity<>(a07GwService.getDeptDzList(dzType), HttpStatus.OK);
    }

    @ApiOperation("公文流程新建过滤")
    @GetMapping(value = "/getGwPermission")
    public ResponseEntity<List<Map<String, Object>>> getGwPermission(String gwType) {
        return new ResponseEntity<>(a07GwService.getGwPermission(gwType), HttpStatus.OK);
    }

    @ApiOperation("公文流程分类查询")
    @GetMapping(value = "/getGwClassify")
    public ResponseEntity<List<Map<String, Object>>> getGwClassify(@RequestParam("gwType") String gwType) {
        return new ResponseEntity<>(a07GwService.getGwClassify(gwType), HttpStatus.OK);
    }

    @ApiOperation("查询公文催办内容")
    @GetMapping(value = "/getGwUrgeSubject")
    public ResponseEntity<Object> getGwUrgeSubject(@RequestParam("procInstId") String procInstId, @RequestParam("key") String key) {
        return new ResponseEntity<>(a07GwService.getGwUrgeSubject(procInstId, key), HttpStatus.OK);
    }

    @ApiOperation("查询公文打印地址")
    @GetMapping(value = "/getGwPrintAddress")
    public ResponseEntity<Object> getGwPrintAddress(@RequestParam("procInstId") String procInstId) {
        return new ResponseEntity<>(a07GwService.getGwPrintAddress(procInstId), HttpStatus.OK);
    }

    @ApiOperation("根据任务id查询办理人")
    @GetMapping(value = "/getUsernameByTaskId")
    public ResponseEntity<String> getUsernameByTaskId(@RequestParam("taskId") String taskId) {
        return new ResponseEntity<>(a07GwService.getUsernameByTaskId(taskId), HttpStatus.OK);
    }

    @ApiOperation("统计公文办理数量")
    @GetMapping(value = "/count")
    @AnonymousAccess
    public ResponseEntity count(GwCriteria criteria) {
        return new ResponseEntity<>(a07GwService.count(criteria.getYear(), criteria.getGwType(), criteria.getCreateTime()), HttpStatus.OK);
    }

}
