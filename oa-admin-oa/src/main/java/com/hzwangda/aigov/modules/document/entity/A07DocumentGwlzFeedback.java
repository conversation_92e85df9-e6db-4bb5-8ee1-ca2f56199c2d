package com.hzwangda.aigov.modules.document.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 公文流转
 *
 * <AUTHOR>
 * @date 2021/7/15 上午11:18
 */
@Data
@Entity
@Table(name = "a07_document_gwlz_feedback")
public class A07DocumentGwlzFeedback extends BaseDomain implements Serializable {

    @Column(name = "doc_id")
    @ApiModelProperty(value = "公文id")
    private Long docID;

    @Column(name = "contact_unit", length = 100)
    @ApiModelProperty(value = "单位名称")
    private String contactUnit;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @Column(name = "contact_person", length = 100)
    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    @Column(name = "contact_zw", length = 100)
    @ApiModelProperty(value = "职务")
    private String contactZW;

    @Column(name = "contact_tel", length = 100)
    @ApiModelProperty(value = "联系方式")
    private String contactTel;

    @Column(name = "memo", length = 500)
    @ApiModelProperty(value = "备注")
    private String memo;

    @Column(name = "feed_user", length = 50)
    @ApiModelProperty(value = "反馈用户")
    private String feedUser;

    @Column(name = "feedback_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "反馈时间")
    private Timestamp feedbackTime;

    @Column(name = "feedback_status", length = 50)
    @ApiModelProperty(value = "反馈状态")
    private String feedbackStatus;

    @Column(name = "gender", length = 50)
    private String gender;

    @Column(name = "isstay", length = 50)
    private String isstay;
}
