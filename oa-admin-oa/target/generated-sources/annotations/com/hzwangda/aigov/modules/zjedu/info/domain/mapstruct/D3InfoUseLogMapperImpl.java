package com.hzwangda.aigov.modules.zjedu.info.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.info.domain.dto.D3InfoUseLogDto;
import com.hzwangda.aigov.modules.zjedu.info.domain.entity.D3InfoUseLog;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class D3InfoUseLogMapperImpl implements D3InfoUseLogMapper {

    @Override
    public D3InfoUseLogDto toDto(D3InfoUseLog entity) {
        if ( entity == null ) {
            return null;
        }

        D3InfoUseLogDto d3InfoUseLogDto = new D3InfoUseLogDto();

        d3InfoUseLogDto.setContributeDept( entity.getContributeDept() );
        d3InfoUseLogDto.setContributeDeptName( entity.getContributeDeptName() );
        d3InfoUseLogDto.setContributeUserDeptId( entity.getContributeUserDeptId() );
        d3InfoUseLogDto.setContributeUsername( entity.getContributeUsername() );
        d3InfoUseLogDto.setCreateDeptName( entity.getCreateDeptName() );
        d3InfoUseLogDto.setD3Info( entity.getD3Info() );
        d3InfoUseLogDto.setId( entity.getId() );
        d3InfoUseLogDto.setLogType( entity.getLogType() );
        d3InfoUseLogDto.setMemo( entity.getMemo() );
        d3InfoUseLogDto.setScore( entity.getScore() );
        d3InfoUseLogDto.setUseDate( entity.getUseDate() );
        d3InfoUseLogDto.setUseInfo( entity.getUseInfo() );
        d3InfoUseLogDto.setUseTitle( entity.getUseTitle() );

        return d3InfoUseLogDto;
    }

    @Override
    public List<D3InfoUseLogDto> toDto(List<D3InfoUseLog> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<D3InfoUseLogDto> list = new ArrayList<D3InfoUseLogDto>( entityList.size() );
        for ( D3InfoUseLog d3InfoUseLog : entityList ) {
            list.add( toDto( d3InfoUseLog ) );
        }

        return list;
    }

    @Override
    public D3InfoUseLog toEntity(D3InfoUseLogDto dto) {
        if ( dto == null ) {
            return null;
        }

        D3InfoUseLog d3InfoUseLog = new D3InfoUseLog();

        d3InfoUseLog.setContributeDept( dto.getContributeDept() );
        d3InfoUseLog.setContributeDeptName( dto.getContributeDeptName() );
        d3InfoUseLog.setContributeUserDeptId( dto.getContributeUserDeptId() );
        d3InfoUseLog.setContributeUsername( dto.getContributeUsername() );
        d3InfoUseLog.setCreateDeptName( dto.getCreateDeptName() );
        d3InfoUseLog.setD3Info( dto.getD3Info() );
        d3InfoUseLog.setId( dto.getId() );
        d3InfoUseLog.setLogType( dto.getLogType() );
        d3InfoUseLog.setMemo( dto.getMemo() );
        d3InfoUseLog.setScore( dto.getScore() );
        d3InfoUseLog.setUseDate( dto.getUseDate() );
        d3InfoUseLog.setUseInfo( dto.getUseInfo() );
        d3InfoUseLog.setUseTitle( dto.getUseTitle() );

        return d3InfoUseLog;
    }

    @Override
    public List<D3InfoUseLog> toEntity(List<D3InfoUseLogDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<D3InfoUseLog> list = new ArrayList<D3InfoUseLog>( dtoList.size() );
        for ( D3InfoUseLogDto d3InfoUseLogDto : dtoList ) {
            list.add( toEntity( d3InfoUseLogDto ) );
        }

        return list;
    }
}
