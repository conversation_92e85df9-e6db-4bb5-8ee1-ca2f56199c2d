package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07DocumentHqDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentHq;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A07DocumentHqMapperImpl implements A07DocumentHqMapper {

    @Autowired
    private A07DocumentMapperUtil a07DocumentMapperUtil;

    @Override
    public List<A07DocumentHqDto> toDto(List<A07DocumentHq> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A07DocumentHqDto> list = new ArrayList<A07DocumentHqDto>( entityList.size() );
        for ( A07DocumentHq a07DocumentHq : entityList ) {
            list.add( toDto( a07DocumentHq ) );
        }

        return list;
    }

    @Override
    public A07DocumentHq toEntity(A07DocumentHqDto dto) {
        if ( dto == null ) {
            return null;
        }

        A07DocumentHq a07DocumentHq = new A07DocumentHq();

        a07DocumentHq.setBelongToDept( dto.getBelongToDept() );
        a07DocumentHq.setBpmInstanceId( dto.getBpmInstanceId() );
        a07DocumentHq.setBpmProcessKey( dto.getBpmProcessKey() );
        a07DocumentHq.setBpmStatus( dto.getBpmStatus() );
        a07DocumentHq.setBpmSubject( dto.getBpmSubject() );
        a07DocumentHq.setCreateBy( dto.getCreateBy() );
        a07DocumentHq.setCreateTime( dto.getCreateTime() );
        a07DocumentHq.setId( dto.getId() );
        List<String> list = dto.getParticipateUser();
        if ( list != null ) {
            a07DocumentHq.setParticipateUser( new ArrayList<String>( list ) );
        }
        a07DocumentHq.setUpdateBy( dto.getUpdateBy() );
        a07DocumentHq.setUpdateTime( dto.getUpdateTime() );
        a07DocumentHq.setVersion( dto.getVersion() );
        a07DocumentHq.setBt( dto.getBt() );
        a07DocumentHq.setBz( dto.getBz() );
        a07DocumentHq.setCsdw( dto.getCsdw() );
        a07DocumentHq.setGwwh( dto.getGwwh() );
        a07DocumentHq.setHj( dto.getHj() );
        a07DocumentHq.setNgdw( dto.getNgdw() );
        a07DocumentHq.setYjCssh( dto.getYjCssh() );
        a07DocumentHq.setYjFgzrsh( dto.getYjFgzrsh() );
        a07DocumentHq.setYjHq( dto.getYjHq() );
        a07DocumentHq.setYjMsksh( dto.getYjMsksh() );
        a07DocumentHq.setYjNb( dto.getYjNb() );
        a07DocumentHq.setYjQf( dto.getYjQf() );
        a07DocumentHq.setZsdw( dto.getZsdw() );
        a07DocumentHq.setZw( storageBizDtoToStorageBiz( dto.getZw() ) );
        a07DocumentHq.setFj( a07DocumentMapperUtil.toConvertToId( dto.getFj() ) );

        return a07DocumentHq;
    }

    @Override
    public List<A07DocumentHq> toEntity(List<A07DocumentHqDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A07DocumentHq> list = new ArrayList<A07DocumentHq>( dtoList.size() );
        for ( A07DocumentHqDto a07DocumentHqDto : dtoList ) {
            list.add( toEntity( a07DocumentHqDto ) );
        }

        return list;
    }

    @Override
    public A07DocumentHqDto toDto(A07DocumentHq entity) {
        if ( entity == null ) {
            return null;
        }

        A07DocumentHqDto a07DocumentHqDto = new A07DocumentHqDto();

        a07DocumentHqDto.setBelongToDept( entity.getBelongToDept() );
        a07DocumentHqDto.setBpmInstanceId( entity.getBpmInstanceId() );
        a07DocumentHqDto.setBpmProcessKey( entity.getBpmProcessKey() );
        a07DocumentHqDto.setBpmStatus( entity.getBpmStatus() );
        a07DocumentHqDto.setBpmSubject( entity.getBpmSubject() );
        a07DocumentHqDto.setCreateBy( entity.getCreateBy() );
        a07DocumentHqDto.setCreateTime( entity.getCreateTime() );
        a07DocumentHqDto.setId( entity.getId() );
        List<String> list = entity.getParticipateUser();
        if ( list != null ) {
            a07DocumentHqDto.setParticipateUser( new ArrayList<String>( list ) );
        }
        a07DocumentHqDto.setUpdateBy( entity.getUpdateBy() );
        a07DocumentHqDto.setUpdateTime( entity.getUpdateTime() );
        a07DocumentHqDto.setVersion( entity.getVersion() );
        a07DocumentHqDto.setBt( entity.getBt() );
        a07DocumentHqDto.setBz( entity.getBz() );
        a07DocumentHqDto.setCsdw( entity.getCsdw() );
        a07DocumentHqDto.setGwwh( entity.getGwwh() );
        a07DocumentHqDto.setHj( entity.getHj() );
        a07DocumentHqDto.setNgdw( entity.getNgdw() );
        a07DocumentHqDto.setYjCssh( entity.getYjCssh() );
        a07DocumentHqDto.setYjFgzrsh( entity.getYjFgzrsh() );
        a07DocumentHqDto.setYjHq( entity.getYjHq() );
        a07DocumentHqDto.setYjMsksh( entity.getYjMsksh() );
        a07DocumentHqDto.setYjNb( entity.getYjNb() );
        a07DocumentHqDto.setYjQf( entity.getYjQf() );
        a07DocumentHqDto.setZsdw( entity.getZsdw() );

        a07DocumentHqDto.setFj( a07DocumentMapperUtil.toFj(entity.getFj()) );
        a07DocumentHqDto.setZw( a07DocumentMapperUtil.toZw(entity.getZw()) );

        splitTime( entity, a07DocumentHqDto );

        return a07DocumentHqDto;
    }

    protected StorageBiz storageBizDtoToStorageBiz(StorageBizDto storageBizDto) {
        if ( storageBizDto == null ) {
            return null;
        }

        StorageBiz storageBiz = new StorageBiz();

        storageBiz.setBizId( storageBizDto.getBizId() );
        storageBiz.setBizType( storageBizDto.getBizType() );
        storageBiz.setSorted( storageBizDto.getSorted() );

        return storageBiz;
    }
}
