package com.hzwangda.aigov.modules.document.enums;

/**
 * 权限范围类型
 * <AUTHOR>
 */
public enum AuthoryScopeEnum {

    GW("gw", "公文"),
    MEETING("meeting", "会议室");

    private final String value;
    private final String name;

    AuthoryScopeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static AuthoryScopeEnum getByValue(String value) {
        for(AuthoryScopeEnum effectiveTypeEnum : AuthoryScopeEnum.values()) {
            if(effectiveTypeEnum.getValue().equals(value)) {
                return effectiveTypeEnum;
            }
        }
        return null;
    }

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return this.name;
    }
}
