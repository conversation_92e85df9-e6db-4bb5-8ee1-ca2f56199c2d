package com.hzwangda.aigov.modules.meetingRoomSubscribe.mapstruct;

import com.hzwangda.aigov.modules.meetingRoomSubscribe.domain.MeetingRoomFamily;
import com.hzwangda.aigov.modules.meetingRoomSubscribe.domain.MeetingRoomFamilyManager;
import com.hzwangda.aigov.modules.meetingRoomSubscribe.dto.MeetingRoomFamilyDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class MeetingRoomFamilyMapperImpl implements MeetingRoomFamilyMapper {

    @Override
    public MeetingRoomFamilyDto toDto(MeetingRoomFamily entity) {
        if ( entity == null ) {
            return null;
        }

        MeetingRoomFamilyDto meetingRoomFamilyDto = new MeetingRoomFamilyDto();

        meetingRoomFamilyDto.setCreateBy( entity.getCreateBy() );
        meetingRoomFamilyDto.setId( entity.getId() );
        meetingRoomFamilyDto.setDeptCode( entity.getDeptCode() );
        List<MeetingRoomFamilyManager> list = entity.getFamilyManagers();
        if ( list != null ) {
            meetingRoomFamilyDto.setFamilyManagers( new ArrayList<MeetingRoomFamilyManager>( list ) );
        }
        meetingRoomFamilyDto.setFamilyName( entity.getFamilyName() );
        meetingRoomFamilyDto.setGroupSort( entity.getGroupSort() );

        return meetingRoomFamilyDto;
    }

    @Override
    public List<MeetingRoomFamilyDto> toDto(List<MeetingRoomFamily> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MeetingRoomFamilyDto> list = new ArrayList<MeetingRoomFamilyDto>( entityList.size() );
        for ( MeetingRoomFamily meetingRoomFamily : entityList ) {
            list.add( toDto( meetingRoomFamily ) );
        }

        return list;
    }

    @Override
    public MeetingRoomFamily toEntity(MeetingRoomFamilyDto dto) {
        if ( dto == null ) {
            return null;
        }

        MeetingRoomFamily meetingRoomFamily = new MeetingRoomFamily();

        meetingRoomFamily.setCreateBy( dto.getCreateBy() );
        meetingRoomFamily.setDeptCode( dto.getDeptCode() );
        List<MeetingRoomFamilyManager> list = dto.getFamilyManagers();
        if ( list != null ) {
            meetingRoomFamily.setFamilyManagers( new ArrayList<MeetingRoomFamilyManager>( list ) );
        }
        meetingRoomFamily.setFamilyName( dto.getFamilyName() );
        meetingRoomFamily.setGroupSort( dto.getGroupSort() );
        meetingRoomFamily.setId( dto.getId() );

        return meetingRoomFamily;
    }

    @Override
    public List<MeetingRoomFamily> toEntity(List<MeetingRoomFamilyDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MeetingRoomFamily> list = new ArrayList<MeetingRoomFamily>( dtoList.size() );
        for ( MeetingRoomFamilyDto meetingRoomFamilyDto : dtoList ) {
            list.add( toEntity( meetingRoomFamilyDto ) );
        }

        return list;
    }
}
