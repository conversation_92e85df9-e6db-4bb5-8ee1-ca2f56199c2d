package com.hzwangda.aigov.modules.GoodsReceive.domain;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "goods_inventory")
public class GoodsInventory extends BaseDomain {

    private String name;

    private Long pid;

    private Integer num;

    private Boolean leaf;

    @ApiModelProperty(
            value = "是否需归还",
            hidden = true
    )
    private Boolean returnStatus = false;
}
