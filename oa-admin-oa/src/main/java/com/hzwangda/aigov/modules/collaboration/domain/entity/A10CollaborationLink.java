package com.hzwangda.aigov.modules.collaboration.domain.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.wangda.boot.platform.idWorker.IdWorker;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "a10_collaboration_link", uniqueConstraints = @UniqueConstraint(columnNames = {"bizId", "bizType"}))
public class A10CollaborationLink implements Serializable {

    @Id
    private Long id;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "info_id")
    @JSONField(serialize = false)
    private A10CollaborationInfo a10CollaborationInfo;

    @Column(nullable = false)
    private Long bizId;

    @Column(nullable = false)
    @ApiModelProperty(value = "业务类名")
    private String bizType;

    @Column(nullable = false)
    private String bizLink;

    @Transient
    private Long infoId;

    public Long getInfoId() {
        if (a10CollaborationInfo != null) {
            return a10CollaborationInfo.getId();
        }
        return null;
    }


    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }
}
