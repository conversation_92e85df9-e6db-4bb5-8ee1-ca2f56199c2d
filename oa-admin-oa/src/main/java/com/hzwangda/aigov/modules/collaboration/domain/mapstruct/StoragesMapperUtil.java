package com.hzwangda.aigov.modules.collaboration.domain.mapstruct;


import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.service.dto.LocalStorageDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class StoragesMapperUtil {

    @Autowired
    private StorageManageService localStorageService;

    public List<LocalStorageDto> listDto2Long(List<Long> ids) {
        List<LocalStorageDto> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(ids)) {
            return list;
        }
        for (Long id : ids) {
            LocalStorageDto dto = map(id);
            if (Objects.nonNull(dto)) {
                list.add(dto);
            }
        }

        return list;
    }

    public List<Long> listLong2Dto(List<LocalStorageDto> dtoList) {
        List<Long> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(dtoList)) {
            return list;
        }
        for (LocalStorageDto dto : dtoList) {
            Long id = map(dto);
            if (Objects.nonNull(id)) {
                list.add(id);
            }
        }

        return list;
    }

    public LocalStorageDto map(Long storageId) {
        return localStorageService.findById(storageId);
    }

    public Long map(LocalStorageDto dto) {
        return dto.getId();
    }
}
