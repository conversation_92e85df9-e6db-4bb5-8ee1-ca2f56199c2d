package com.hzwangda.aigov.modules.document.entity;

import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 公务用车审批
 *
 * <AUTHOR>
 * @date 2021/7/15 上午11:18
 */
@Data
@Entity
@Table(name = "a07_document_hfzgwycw_sp")
public class A07DocumentHfzgwycwSp extends BaseBpmDomain implements Serializable {

    @Column(name = "sq_bm", length = 2000)
    @ApiModelProperty(value = "申请部门")
    private String sqBm;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @Column(name = "sqr")
    @ApiModelProperty(value = "申请人")
    private String sql;

    @Column(name = "lxdh")
    @ApiModelProperty(value = "联系电话")
    private String lxdh;

    @Column(name = "ycsj")
    @ApiModelProperty(value = "用车时间")
    private String ycsj;

    @Column(name = "xclx")
    @ApiModelProperty(value = "行车路线")
    private String xclx;

    @Column(name = "ycsy")
    @ApiModelProperty(value = "用车事由")
    private String ycsy;

    @Column(name = "ycrs")
    @ApiModelProperty(value = "用车人数")
    private Integer ycrs;

    @Column(name = "clap")
    @ApiModelProperty(value = "车辆安排")
    private String clap;

    @ApiModelProperty(value = "科室负责人意见")
    @Lob
    private String yjksfzr;

    @ApiModelProperty(value = "审批领导审核")
    @Lob
    private String yjLdsh;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A07DocumentHfzgwycwSp.fj'")
    private List<StorageBiz> fj;

    @ApiModelProperty(value = "领导签批")
    @Lob
    private String yjLdqp;

    public void setFj(List<StorageBiz> fj) {
        if (fj != null) {
            if (this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }
}
