package com.hzwangda.aigov.modules.zjedu.meeting.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35MeetingMaterialsDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35MeetingMaterials;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class C35MeetingMaterialsMapperImpl implements C35MeetingMaterialsMapper {

    @Override
    public C35MeetingMaterialsDto toDto(C35MeetingMaterials entity) {
        if ( entity == null ) {
            return null;
        }

        C35MeetingMaterialsDto c35MeetingMaterialsDto = new C35MeetingMaterialsDto();

        c35MeetingMaterialsDto.setCreateBy( entity.getCreateBy() );
        c35MeetingMaterialsDto.setCreateTime( entity.getCreateTime() );
        c35MeetingMaterialsDto.setUpdateTime( entity.getUpdateTime() );
        c35MeetingMaterialsDto.setUpdatedBy( entity.getUpdatedBy() );
        c35MeetingMaterialsDto.setFilePath( entity.getFilePath() );
        c35MeetingMaterialsDto.setId( entity.getId() );
        c35MeetingMaterialsDto.setMaterialsName( entity.getMaterialsName() );
        c35MeetingMaterialsDto.setMaterialsType( entity.getMaterialsType() );
        c35MeetingMaterialsDto.setNum( entity.getNum() );
        c35MeetingMaterialsDto.setSourceId( entity.getSourceId() );
        c35MeetingMaterialsDto.setSourceType( entity.getSourceType() );

        return c35MeetingMaterialsDto;
    }

    @Override
    public List<C35MeetingMaterialsDto> toDto(List<C35MeetingMaterials> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<C35MeetingMaterialsDto> list = new ArrayList<C35MeetingMaterialsDto>( entityList.size() );
        for ( C35MeetingMaterials c35MeetingMaterials : entityList ) {
            list.add( toDto( c35MeetingMaterials ) );
        }

        return list;
    }

    @Override
    public C35MeetingMaterials toEntity(C35MeetingMaterialsDto dto) {
        if ( dto == null ) {
            return null;
        }

        C35MeetingMaterials c35MeetingMaterials = new C35MeetingMaterials();

        c35MeetingMaterials.setCreateBy( dto.getCreateBy() );
        c35MeetingMaterials.setCreateTime( dto.getCreateTime() );
        c35MeetingMaterials.setUpdateTime( dto.getUpdateTime() );
        c35MeetingMaterials.setUpdatedBy( dto.getUpdatedBy() );
        c35MeetingMaterials.setFilePath( dto.getFilePath() );
        c35MeetingMaterials.setId( dto.getId() );
        c35MeetingMaterials.setMaterialsName( dto.getMaterialsName() );
        c35MeetingMaterials.setMaterialsType( dto.getMaterialsType() );
        c35MeetingMaterials.setNum( dto.getNum() );
        c35MeetingMaterials.setSourceId( dto.getSourceId() );
        c35MeetingMaterials.setSourceType( dto.getSourceType() );

        return c35MeetingMaterials;
    }

    @Override
    public List<C35MeetingMaterials> toEntity(List<C35MeetingMaterialsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<C35MeetingMaterials> list = new ArrayList<C35MeetingMaterials>( dtoList.size() );
        for ( C35MeetingMaterialsDto c35MeetingMaterialsDto : dtoList ) {
            list.add( toEntity( c35MeetingMaterialsDto ) );
        }

        return list;
    }
}
