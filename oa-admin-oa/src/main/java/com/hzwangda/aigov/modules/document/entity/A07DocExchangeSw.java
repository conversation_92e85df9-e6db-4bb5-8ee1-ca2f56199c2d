package com.hzwangda.aigov.modules.document.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * 公文流转
 *
 * <AUTHOR>
 * @date 2021/7/15 上午11:18
 */
@Data
@Entity
@Table(name = "a07_document_gwlz")
public class A07DocExchangeSw extends BaseDomain implements Serializable {

    @Column(name = "gwzt")
    @ApiModelProperty(value = "公文类型(0:草稿,1:正式,2:撤回)")
    private String gwzt;

    @Column(name = "gwzl")
    @ApiModelProperty(value = "公文种类(公文,会议)")
    private String gwzl;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @Column(name = "sfgk")
    @ApiModelProperty(value = "是否公开(主动公开,依申请公开,不予公开)")
    private String sfgk;

    @ApiModelProperty(value = "文号")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "wh_id", referencedColumnName = "id")
    private ReferenceNumber gwwh;

    @Column(name = "hj")
    @ApiModelProperty(value = "缓急(特急,加急,平件)")
    private String hj;

    @Column(name = "yj_qfr", length = 2000)
    @ApiModelProperty(value = "签发人")
    private String yjQfr;

    @Column(name = "bt")
    @ApiModelProperty(value = "标题")
    private String bt;

    @Transient
    @ApiModelProperty(value = "正文")
//    @OneToOne(cascade = CascadeType.ALL)
//    @JoinColumn(name="zw", referencedColumnName = "storage_id")
    private StorageBiz zw;

    @Column(name = "zw")
    private String zwId;

    @ApiModelProperty(value = "主送单位")
    @OneToMany(targetEntity = A07DocumentGwlzUser.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "gwid", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private List<A07DocumentGwlzUser> zsdwDepts;


    @Column(name = "fwdw")
    @ApiModelProperty(value = "发文单位")
    private String fwdw;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "发文日期")
    private Timestamp fwrq;

    @Column(name = "fwlxr")
    @ApiModelProperty(value = "发文联系人")
    private String fwlxr;

    @Column(name = "lxfs")
    @ApiModelProperty(value = "联系方式")
    private String lxfs;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "操作日期")
    private Timestamp czrq;

    @Column(name = "dxtz")
    @ApiModelProperty(value = "短信通知")
    private String dxtz;


    @Column(name = "swdw")
    @ApiModelProperty(value = "收文单位")
    private String swdw;


    @Column(name = "lybs")
    @ApiModelProperty(value = "来源标识")
    private String lybs;

    @Column(name = "lyxtdm")
    @ApiModelProperty(value = "来源系统代码")
    private String lyxtdm;

    public void copy(A07DocExchangeSw source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }

    public void setZsdwDepts(List<A07DocumentGwlzUser> zsdwDepts) {
        if (zsdwDepts != null) {
            if (this.zsdwDepts == null) {
                this.zsdwDepts = new ArrayList<>();
            }
            this.zsdwDepts.clear();
            this.zsdwDepts.addAll(zsdwDepts);
        }
    }

    public StorageBiz getZw() {
        if (StringUtils.isNotEmpty(zwId)) {
            zw = new StorageBiz();
            zw.setBizType("A07DocumentGwlz.zw");
            zw.setStorageId(zwId);
        }
        return zw;
    }

}
