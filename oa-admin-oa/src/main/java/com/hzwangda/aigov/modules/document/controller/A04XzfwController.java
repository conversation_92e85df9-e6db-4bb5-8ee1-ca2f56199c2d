package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.service.A04XzfwService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping("/api/a04xzfw")
@CrossOrigin
@Api(tags = "行政服务")
public class A04XzfwController {
    @Resource
    private A04XzfwService a04XzfwService;

    /**
     * 导出入职管理
     */
    @RequestMapping("/export")
    public void export(HttpServletRequest request, HttpServletResponse response) throws IOException {
        a04XzfwService.exportAll(request, response);
    }
}
