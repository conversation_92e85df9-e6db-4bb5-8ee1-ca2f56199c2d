package com.hzwangda.aigov.modules.archive.service.impl;


import com.hzwangda.aigov.modules.archive.constant.ArchiveConstant;
import com.hzwangda.aigov.modules.archive.dto.A12ArchivesAddListDto;
import com.hzwangda.aigov.modules.archive.dto.A12ArchivesCriteria;
import com.hzwangda.aigov.modules.archive.entity.A12Archives;
import com.hzwangda.aigov.modules.archive.mapstruct.A12ArchivesMapper;
import com.hzwangda.aigov.modules.archive.repository.A12ArchivesRepository;
import com.hzwangda.aigov.modules.archive.service.A12ArchivesService;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.QueryHelp;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/9/7 下午4:47
 **/
@Service
@RequiredArgsConstructor
public class A12ArchivesServiceImpl implements A12ArchivesService {

    private final A12ArchivesRepository a12ArchivesRepository;

    private final StorageManageService localStorageService;

    private final A12ArchivesMapper a12ArchivesMapper;

    @Override
    public Boolean createOrUpdate(A12ArchivesAddListDto dto) {
        for (A12Archives archives : dto.getList()) {
            archives.setDirectoryId(dto.getDirectoryId());
            if (archives.getId() != null) {
                A12Archives oldArchives = a12ArchivesRepository.findById(archives.getId()).orElse(null);
                if (oldArchives == null) {
                    throw new BadRequestException("文件档案不存在！");
                }
                //若文件修改则删除原有文件数据
                if (oldArchives.getLocalStorageId().longValue() != archives.getLocalStorageId().longValue()) {
                    localStorageService.deleteAll(new Long[]{oldArchives.getLocalStorageId()});
                }
                oldArchives.copy(archives);
                a12ArchivesRepository.save(oldArchives);
            } else {
                a12ArchivesRepository.save(archives);
            }
        }
        return true;
    }

    @Override
    public Boolean delete(Long[] ids) {
        for (Long id : ids) {
            A12Archives archives = a12ArchivesRepository.findById(id).orElse(null);
            if (archives == null) {
                throw new BadRequestException("文件档案不存在！");
            }
            if (!ArchiveConstant.ARCHIVE_TAG_FW.equals(archives.getTag())) {
                localStorageService.deleteAll(new Long[]{archives.getLocalStorageId()});
            }
            a12ArchivesRepository.delete(archives);
        }
        return true;
    }

    @Override
    public Map query(A12ArchivesCriteria criteria, Pageable pageable) {
        Page<A12Archives> page = a12ArchivesRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(a12ArchivesMapper::toDto));
    }

    @Override
    public A12Archives findByDirectoryIdAndStorageId(Long directoryId, Long storageId) {
        return a12ArchivesRepository.findFirstByDirectoryIdAndLocalStorageId(directoryId, storageId);
    }
}
