package com.hzwangda.aigov.modules.collaboration.domain.criteria;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Transient;
import java.util.List;

@ApiModel
@Data
public class SmsInfo {

    @ApiModelProperty(value = "主表id")
    private Long id;

    @ApiModelProperty(value = "短信内容")
    private String info;


    @ApiModelProperty(value = "发送范围 0未签收/1已签收/2全部")
    private Integer type;


    private List<String> names;

    @Transient
    @ApiModelProperty(value = "短信通知")
    private Boolean sms;

    @Transient
    @ApiModelProperty(value = "钉钉消息通知")
    private Boolean ding;

    @Transient
    @ApiModelProperty(value = "操作人的用户类型")
    private Long userType;
}
