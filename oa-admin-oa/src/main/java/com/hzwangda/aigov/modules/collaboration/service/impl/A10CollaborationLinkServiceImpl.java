package com.hzwangda.aigov.modules.collaboration.service.impl;

import com.hzwangda.aigov.modules.collaboration.domain.criteria.ReferralInfo;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationAssignee;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationHandleLog;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationInfo;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationLink;
import com.hzwangda.aigov.modules.collaboration.repository.A10CollaborationLinkRepository;
import com.hzwangda.aigov.modules.collaboration.service.A10CollaborationInfoService;
import com.hzwangda.aigov.modules.collaboration.service.A10CollaborationLinkService;
import com.wangda.oa.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class A10CollaborationLinkServiceImpl implements A10CollaborationLinkService {

    @Resource
    private A10CollaborationInfoService a10CollaborationInfoService;
    @Resource
    private A10CollaborationLinkRepository a10CollaborationLinkRepository;
    @PersistenceContext
    private EntityManager entityManager;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public A10CollaborationLink referral(ReferralInfo referralInfo) throws NoSuchFieldException, ClassNotFoundException, IllegalAccessException {

        A10CollaborationInfo a10CollaborationInfo = new A10CollaborationInfo();
        a10CollaborationInfo.setSubject(referralInfo.getSubject());
        a10CollaborationInfo.setContent(referralInfo.getContent());
        a10CollaborationInfo.setUrgent("0");
        a10CollaborationInfo.setStatus("sent");
        List<Long> collect = referralInfo.getAttachments().stream().map(storageBiz -> Long.valueOf(storageBiz.getStorageId())).collect(Collectors.toList());
        a10CollaborationInfo.setAttachments(collect);
        List<String> sendTo = referralInfo.getSendTo();
        String currentUsername = SecurityUtils.getCurrentUsername();
        List<A10CollaborationAssignee> assignees = sendTo.stream().map(s -> {
            A10CollaborationAssignee a10CollaborationAssignee = new A10CollaborationAssignee();
            a10CollaborationAssignee.setHandleUser(s);
            a10CollaborationAssignee.setFromUser(currentUsername);
            return a10CollaborationAssignee;
        }).collect(Collectors.toList());
        a10CollaborationInfo.setAssignees(assignees);

        List<A10CollaborationHandleLog> handleLogs = new ArrayList<>();
        A10CollaborationHandleLog handleLog = new A10CollaborationHandleLog();
        handleLogs.add(handleLog);
        a10CollaborationInfo.setHandleLogs(handleLogs);

        a10CollaborationInfoService.doSave(a10CollaborationInfo);

        A10CollaborationLink a10CollaborationLink = new A10CollaborationLink();
        a10CollaborationLink.setA10CollaborationInfo(a10CollaborationInfo);
        a10CollaborationLink.setBizId(referralInfo.getBizId());
        a10CollaborationLink.setBizType(referralInfo.getBizType());
        a10CollaborationLink.setBizLink(referralInfo.getBizLink());
        a10CollaborationLinkRepository.save(a10CollaborationLink);


        saveLink(referralInfo.getClassName(), referralInfo.getBizId(), a10CollaborationLink);

        return a10CollaborationLink;
    }

    @Override
    public Object findById(String className, Long id) throws ClassNotFoundException {
        Class<?> aClass = Class.forName(className);
        return entityManager.find(aClass, id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveLink(String className, Long id, A10CollaborationLink link) throws ClassNotFoundException, NoSuchFieldException, IllegalAccessException {
        Class<?> aClass = Class.forName(className);
        Object o = entityManager.find(aClass, id);
        Field link1 = o.getClass().getDeclaredField("link");
        link1.setAccessible(true);
        link1.set(o, link);
        entityManager.merge(o);
        return id;
    }
}
