package com.hzwangda.aigov.modules.zjedu.meeting.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35MeetingRoomApplyDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35MeetingRoomApply;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import com.wangda.oa.modules.system.service.mapstruct.UserMapperUtil;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class C35MeetingRoomApplyMapperImpl implements C35MeetingRoomApplyMapper {

    @Autowired
    private UserMapperUtil userMapperUtil;

    @Override
    public C35MeetingRoomApplyDto toDto(C35MeetingRoomApply entity) {
        if ( entity == null ) {
            return null;
        }

        C35MeetingRoomApplyDto c35MeetingRoomApplyDto = new C35MeetingRoomApplyDto();

        c35MeetingRoomApplyDto.setBelongToDept( entity.getBelongToDept() );
        c35MeetingRoomApplyDto.setBpmInstanceId( entity.getBpmInstanceId() );
        c35MeetingRoomApplyDto.setBpmProcessKey( entity.getBpmProcessKey() );
        c35MeetingRoomApplyDto.setBpmStatus( entity.getBpmStatus() );
        c35MeetingRoomApplyDto.setBpmSubject( entity.getBpmSubject() );
        c35MeetingRoomApplyDto.setCreateBy( entity.getCreateBy() );
        c35MeetingRoomApplyDto.setCreateTime( entity.getCreateTime() );
        List<String> list = entity.getParticipateUser();
        if ( list != null ) {
            c35MeetingRoomApplyDto.setParticipateUser( new ArrayList<String>( list ) );
        }
        c35MeetingRoomApplyDto.setUpdateBy( entity.getUpdateBy() );
        c35MeetingRoomApplyDto.setUpdateTime( entity.getUpdateTime() );
        c35MeetingRoomApplyDto.setVersion( entity.getVersion() );
        c35MeetingRoomApplyDto.setApplyUser( entity.getApplyUser() );
        c35MeetingRoomApplyDto.setAttendNumber( entity.getAttendNumber() );
        c35MeetingRoomApplyDto.setEndTime( entity.getEndTime() );
        c35MeetingRoomApplyDto.setId( entity.getId() );
        c35MeetingRoomApplyDto.setIsLeader( entity.getIsLeader() );
        c35MeetingRoomApplyDto.setIsOpen( entity.getIsOpen() );
        c35MeetingRoomApplyDto.setMeeting( entity.getMeeting() );
        c35MeetingRoomApplyDto.setMeetingRoom( entity.getMeetingRoom() );
        c35MeetingRoomApplyDto.setName( entity.getName() );
        c35MeetingRoomApplyDto.setStartTime( entity.getStartTime() );
        c35MeetingRoomApplyDto.setTelephone( entity.getTelephone() );
        c35MeetingRoomApplyDto.setTimeTnterval( entity.getTimeTnterval() );
        c35MeetingRoomApplyDto.setUseDate( entity.getUseDate() );
        c35MeetingRoomApplyDto.setViewUsers( stringListToSimpleUserDtoSet( entity.getViewUsers() ) );

        return c35MeetingRoomApplyDto;
    }

    @Override
    public List<C35MeetingRoomApplyDto> toDto(List<C35MeetingRoomApply> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<C35MeetingRoomApplyDto> list = new ArrayList<C35MeetingRoomApplyDto>( entityList.size() );
        for ( C35MeetingRoomApply c35MeetingRoomApply : entityList ) {
            list.add( toDto( c35MeetingRoomApply ) );
        }

        return list;
    }

    @Override
    public C35MeetingRoomApply toEntity(C35MeetingRoomApplyDto dto) {
        if ( dto == null ) {
            return null;
        }

        C35MeetingRoomApply c35MeetingRoomApply = new C35MeetingRoomApply();

        c35MeetingRoomApply.setBelongToDept( dto.getBelongToDept() );
        c35MeetingRoomApply.setBpmInstanceId( dto.getBpmInstanceId() );
        c35MeetingRoomApply.setBpmProcessKey( dto.getBpmProcessKey() );
        c35MeetingRoomApply.setBpmStatus( dto.getBpmStatus() );
        c35MeetingRoomApply.setBpmSubject( dto.getBpmSubject() );
        c35MeetingRoomApply.setCreateBy( dto.getCreateBy() );
        c35MeetingRoomApply.setCreateTime( dto.getCreateTime() );
        c35MeetingRoomApply.setId( dto.getId() );
        List<String> list = dto.getParticipateUser();
        if ( list != null ) {
            c35MeetingRoomApply.setParticipateUser( new ArrayList<String>( list ) );
        }
        c35MeetingRoomApply.setUpdateBy( dto.getUpdateBy() );
        c35MeetingRoomApply.setUpdateTime( dto.getUpdateTime() );
        c35MeetingRoomApply.setVersion( dto.getVersion() );
        c35MeetingRoomApply.setApplyUser( dto.getApplyUser() );
        c35MeetingRoomApply.setAttendNumber( dto.getAttendNumber() );
        c35MeetingRoomApply.setEndTime( dto.getEndTime() );
        c35MeetingRoomApply.setIsLeader( dto.getIsLeader() );
        c35MeetingRoomApply.setIsOpen( dto.getIsOpen() );
        c35MeetingRoomApply.setMeeting( dto.getMeeting() );
        c35MeetingRoomApply.setMeetingRoom( dto.getMeetingRoom() );
        c35MeetingRoomApply.setName( dto.getName() );
        c35MeetingRoomApply.setStartTime( dto.getStartTime() );
        c35MeetingRoomApply.setTelephone( dto.getTelephone() );
        c35MeetingRoomApply.setTimeTnterval( dto.getTimeTnterval() );
        c35MeetingRoomApply.setUseDate( dto.getUseDate() );
        c35MeetingRoomApply.setViewUsers( simpleUserDtoSetToStringList( dto.getViewUsers() ) );

        return c35MeetingRoomApply;
    }

    @Override
    public List<C35MeetingRoomApply> toEntity(List<C35MeetingRoomApplyDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<C35MeetingRoomApply> list = new ArrayList<C35MeetingRoomApply>( dtoList.size() );
        for ( C35MeetingRoomApplyDto c35MeetingRoomApplyDto : dtoList ) {
            list.add( toEntity( c35MeetingRoomApplyDto ) );
        }

        return list;
    }

    protected Set<SimpleUserDto> stringListToSimpleUserDtoSet(List<String> list) {
        if ( list == null ) {
            return null;
        }

        Set<SimpleUserDto> set = new HashSet<SimpleUserDto>( Math.max( (int) ( list.size() / .75f ) + 1, 16 ) );
        for ( String string : list ) {
            set.add( userMapperUtil.toUser( string ) );
        }

        return set;
    }

    protected List<String> simpleUserDtoSetToStringList(Set<SimpleUserDto> set) {
        if ( set == null ) {
            return null;
        }

        List<String> list = new ArrayList<String>( set.size() );
        for ( SimpleUserDto simpleUserDto : set ) {
            list.add( userMapperUtil.toName( simpleUserDto ) );
        }

        return list;
    }
}
