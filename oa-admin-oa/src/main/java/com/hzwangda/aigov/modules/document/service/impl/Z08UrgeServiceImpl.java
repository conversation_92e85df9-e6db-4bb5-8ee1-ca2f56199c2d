package com.hzwangda.aigov.modules.document.service.impl;

import com.hzwangda.aigov.modules.document.service.Z08UrgeService;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.HistoryService;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @author: zhangzhanlong
 * @date: 2022/10/26 9:55
 * @description:
 */
@Service
@RequiredArgsConstructor
public class Z08UrgeServiceImpl implements Z08UrgeService {
    private final HistoryService historyService;
    private final UserService userService;

    @Override
    public Map<String, Object> getUrgeUserList(String procInstId) {
        List<Map<String, Object>> noHandle = new ArrayList<>();
        List<Map<String, Object>> haveHandle = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        List<HistoricTaskInstance> list = historyService.createHistoricTaskInstanceQuery().processInstanceId(procInstId).orderByHistoricTaskInstanceEndTime().asc().list();
        for(HistoricTaskInstance h : list) {
            if(Objects.isNull(h.getEndTime())) {
                Map<String, Object> nomap = new HashMap<>();
                UserDto byName = userService.findByName(h.getAssignee());
                nomap.put("username", byName.getUsername());
                nomap.put("nikeName", byName.getNickName());
                nomap.put("procInstId", procInstId);
                nomap.put("name", h.getName());
                noHandle.add(nomap);
            }else {
                Map<String, Object> havemap = new HashMap<>();
                UserDto byName = userService.findByName(h.getAssignee());
                havemap.put("username", byName.getUsername());
                havemap.put("nikeName", byName.getNickName());
                havemap.put("procInstId", procInstId);
                havemap.put("name", h.getName());
                haveHandle.add(havemap);
            }
        }
        // 未办理
        map.put("noHandle", noHandle);
        // 已办理
        map.put("haveHandle", haveHandle);
        return map;
    }
}
