package com.hzwangda.aigov.modules.taskmanagement.domain.mapstruct;

import com.hzwangda.aigov.modules.taskmanagement.domain.dto.TmIndicatorsDto;
import com.hzwangda.aigov.modules.taskmanagement.domain.entity.TmIndicators;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class TmIndicatorsMapperImpl implements TmIndicatorsMapper {

    @Override
    public TmIndicatorsDto toDto(TmIndicators entity) {
        if ( entity == null ) {
            return null;
        }

        TmIndicatorsDto tmIndicatorsDto = new TmIndicatorsDto();

        tmIndicatorsDto.setCalculationDiameter( entity.getCalculationDiameter() );
        tmIndicatorsDto.setCategory( entity.getCategory() );
        tmIndicatorsDto.setCreateBy( entity.getCreateBy() );
        tmIndicatorsDto.setCreateTime( entity.getCreateTime() );
        tmIndicatorsDto.setCurrentValue( entity.getCurrentValue() );
        tmIndicatorsDto.setCurrentValueDate( entity.getCurrentValueDate() );
        tmIndicatorsDto.setDefine( entity.getDefine() );
        tmIndicatorsDto.setIndicatorsId( entity.getIndicatorsId() );
        tmIndicatorsDto.setName( entity.getName() );
        tmIndicatorsDto.setStartValue( entity.getStartValue() );
        tmIndicatorsDto.setTag( entity.getTag() );
        tmIndicatorsDto.setTargetValue( entity.getTargetValue() );
        tmIndicatorsDto.setThresholdInterval( entity.getThresholdInterval() );
        tmIndicatorsDto.setType( entity.getType() );
        tmIndicatorsDto.setUnit( entity.getUnit() );
        tmIndicatorsDto.setUpdateCycle( entity.getUpdateCycle() );
        tmIndicatorsDto.setUpdateTime( entity.getUpdateTime() );

        return tmIndicatorsDto;
    }

    @Override
    public List<TmIndicatorsDto> toDto(List<TmIndicators> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmIndicatorsDto> list = new ArrayList<TmIndicatorsDto>( entityList.size() );
        for ( TmIndicators tmIndicators : entityList ) {
            list.add( toDto( tmIndicators ) );
        }

        return list;
    }

    @Override
    public TmIndicators toEntity(TmIndicatorsDto dto) {
        if ( dto == null ) {
            return null;
        }

        TmIndicators tmIndicators = new TmIndicators();

        tmIndicators.setCreateBy( dto.getCreateBy() );
        tmIndicators.setCreateTime( dto.getCreateTime() );
        tmIndicators.setUpdateTime( dto.getUpdateTime() );
        tmIndicators.setCalculationDiameter( dto.getCalculationDiameter() );
        tmIndicators.setCategory( dto.getCategory() );
        tmIndicators.setCurrentValue( dto.getCurrentValue() );
        tmIndicators.setCurrentValueDate( dto.getCurrentValueDate() );
        tmIndicators.setDefine( dto.getDefine() );
        tmIndicators.setIndicatorsId( dto.getIndicatorsId() );
        tmIndicators.setName( dto.getName() );
        tmIndicators.setStartValue( dto.getStartValue() );
        tmIndicators.setTag( dto.getTag() );
        tmIndicators.setTargetValue( dto.getTargetValue() );
        tmIndicators.setThresholdInterval( dto.getThresholdInterval() );
        tmIndicators.setType( dto.getType() );
        tmIndicators.setUnit( dto.getUnit() );
        tmIndicators.setUpdateCycle( dto.getUpdateCycle() );

        return tmIndicators;
    }

    @Override
    public List<TmIndicators> toEntity(List<TmIndicatorsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TmIndicators> list = new ArrayList<TmIndicators>( dtoList.size() );
        for ( TmIndicatorsDto tmIndicatorsDto : dtoList ) {
            list.add( toEntity( tmIndicatorsDto ) );
        }

        return list;
    }
}
