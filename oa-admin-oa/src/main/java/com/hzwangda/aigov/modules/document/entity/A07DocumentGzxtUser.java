package com.hzwangda.aigov.modules.document.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 公文用户关联表
 *
 * <AUTHOR>
 * @date 2021/6/15 上午10:18
 */
@Data
@Entity
@Table(name = "a07_document_GzxtUser")
public class A07DocumentGzxtUser extends BaseDomain implements Serializable {


    @ApiModelProperty(value = "文件ID")
    @JoinColumn(name = "wjid", referencedColumnName = "id")
    private String gwid;

    @Column(name = "qsr")
    @ApiModelProperty(value = "签收人")
    private String qsr;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "签收时间")
    private Timestamp qsrq;

    @Column(name = "qszt", length = 2000)
    @ApiModelProperty(value = "签收状态")
    private String qszt;


    public void copy(A07DocumentGzxtUser source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }

}
