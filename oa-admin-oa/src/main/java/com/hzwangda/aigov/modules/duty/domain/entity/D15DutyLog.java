package com.hzwangda.aigov.modules.duty.domain.entity;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Data
@Table(name = "d15_duty_log")
@Entity
public class D15DutyLog extends BaseDomain {

    @ApiModelProperty(value = "值班日期")
    @Temporal(value = TemporalType.DATE)
    private Date dutyTime;

    @ApiModelProperty(value = "时间段")
    private String timeInterval;

    private String beginTime;
    private String endTime;

    @ApiModelProperty(value = "交办人（原值班人员）")
    private String oldUsername;

    private String oldUsercode;

    @ApiModelProperty(value = "接班人员")
    private String username;

    @ApiModelProperty(value = "是否同意交接班")
    private Integer type;

    @ApiModelProperty(value = "D15DUTY值班表id")
    private Long d15dutyId;

    private Long d05dutyId;

}
