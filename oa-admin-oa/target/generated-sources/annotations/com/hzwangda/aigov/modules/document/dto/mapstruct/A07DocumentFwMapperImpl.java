package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07DocumentFwDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentFw;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A07DocumentFwMapperImpl implements A07DocumentFwMapper {

    @Autowired
    private A07DocumentMapperUtil a07DocumentMapperUtil;

    @Override
    public List<A07DocumentFwDto> toDto(List<A07DocumentFw> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A07DocumentFwDto> list = new ArrayList<A07DocumentFwDto>( entityList.size() );
        for ( A07DocumentFw a07DocumentFw : entityList ) {
            list.add( toDto( a07DocumentFw ) );
        }

        return list;
    }

    @Override
    public A07DocumentFw toEntity(A07DocumentFwDto dto) {
        if ( dto == null ) {
            return null;
        }

        A07DocumentFw a07DocumentFw = new A07DocumentFw();

        a07DocumentFw.setBelongToDept( dto.getBelongToDept() );
        a07DocumentFw.setBpmInstanceId( dto.getBpmInstanceId() );
        a07DocumentFw.setBpmProcessKey( dto.getBpmProcessKey() );
        a07DocumentFw.setBpmStatus( dto.getBpmStatus() );
        a07DocumentFw.setBpmSubject( dto.getBpmSubject() );
        a07DocumentFw.setCreateBy( dto.getCreateBy() );
        a07DocumentFw.setCreateTime( dto.getCreateTime() );
        a07DocumentFw.setId( dto.getId() );
        List<String> list = dto.getParticipateUser();
        if ( list != null ) {
            a07DocumentFw.setParticipateUser( new ArrayList<String>( list ) );
        }
        a07DocumentFw.setUpdateBy( dto.getUpdateBy() );
        a07DocumentFw.setUpdateTime( dto.getUpdateTime() );
        a07DocumentFw.setVersion( dto.getVersion() );
        a07DocumentFw.setBt( dto.getBt() );
        a07DocumentFw.setBz( dto.getBz() );
        a07DocumentFw.setCsdw( dto.getCsdw() );
        a07DocumentFw.setGklx( dto.getGklx() );
        a07DocumentFw.setGwwh( dto.getGwwh() );
        a07DocumentFw.setHj( dto.getHj() );
        a07DocumentFw.setHqdw( dto.getHqdw() );
        a07DocumentFw.setJdr( dto.getJdr() );
        a07DocumentFw.setNgdw( dto.getNgdw() );
        a07DocumentFw.setNgr( dto.getNgr() );
        a07DocumentFw.setYffs( dto.getYffs() );
        a07DocumentFw.setYjHq( dto.getYjHq() );
        a07DocumentFw.setYjQf( dto.getYjQf() );
        a07DocumentFw.setZbbm( dto.getZbbm() );
        a07DocumentFw.setZsdw( dto.getZsdw() );
        a07DocumentFw.setZw( storageBizDtoToStorageBiz( dto.getZw() ) );
        a07DocumentFw.setFj( a07DocumentMapperUtil.toConvertToId( dto.getFj() ) );

        return a07DocumentFw;
    }

    @Override
    public List<A07DocumentFw> toEntity(List<A07DocumentFwDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A07DocumentFw> list = new ArrayList<A07DocumentFw>( dtoList.size() );
        for ( A07DocumentFwDto a07DocumentFwDto : dtoList ) {
            list.add( toEntity( a07DocumentFwDto ) );
        }

        return list;
    }

    @Override
    public A07DocumentFwDto toDto(A07DocumentFw entity) {
        if ( entity == null ) {
            return null;
        }

        A07DocumentFwDto a07DocumentFwDto = new A07DocumentFwDto();

        a07DocumentFwDto.setBelongToDept( entity.getBelongToDept() );
        a07DocumentFwDto.setBpmInstanceId( entity.getBpmInstanceId() );
        a07DocumentFwDto.setBpmProcessKey( entity.getBpmProcessKey() );
        a07DocumentFwDto.setBpmStatus( entity.getBpmStatus() );
        a07DocumentFwDto.setBpmSubject( entity.getBpmSubject() );
        a07DocumentFwDto.setCreateBy( entity.getCreateBy() );
        a07DocumentFwDto.setCreateTime( entity.getCreateTime() );
        a07DocumentFwDto.setId( entity.getId() );
        List<String> list = entity.getParticipateUser();
        if ( list != null ) {
            a07DocumentFwDto.setParticipateUser( new ArrayList<String>( list ) );
        }
        a07DocumentFwDto.setUpdateBy( entity.getUpdateBy() );
        a07DocumentFwDto.setUpdateTime( entity.getUpdateTime() );
        a07DocumentFwDto.setVersion( entity.getVersion() );
        a07DocumentFwDto.setBt( entity.getBt() );
        a07DocumentFwDto.setBz( entity.getBz() );
        a07DocumentFwDto.setCsdw( entity.getCsdw() );
        a07DocumentFwDto.setGklx( entity.getGklx() );
        a07DocumentFwDto.setGwwh( entity.getGwwh() );
        a07DocumentFwDto.setHj( entity.getHj() );
        a07DocumentFwDto.setHqdw( entity.getHqdw() );
        a07DocumentFwDto.setJdr( entity.getJdr() );
        a07DocumentFwDto.setNgdw( entity.getNgdw() );
        a07DocumentFwDto.setNgr( entity.getNgr() );
        a07DocumentFwDto.setYffs( entity.getYffs() );
        a07DocumentFwDto.setYjHq( entity.getYjHq() );
        a07DocumentFwDto.setYjQf( entity.getYjQf() );
        a07DocumentFwDto.setZbbm( entity.getZbbm() );
        a07DocumentFwDto.setZsdw( entity.getZsdw() );

        a07DocumentFwDto.setFj( a07DocumentMapperUtil.toFj(entity.getFj()) );
        a07DocumentFwDto.setZw( a07DocumentMapperUtil.toZw(entity.getZw()) );

        splitTime( entity, a07DocumentFwDto );

        return a07DocumentFwDto;
    }

    protected StorageBiz storageBizDtoToStorageBiz(StorageBizDto storageBizDto) {
        if ( storageBizDto == null ) {
            return null;
        }

        StorageBiz storageBiz = new StorageBiz();

        storageBiz.setBizId( storageBizDto.getBizId() );
        storageBiz.setBizType( storageBizDto.getBizType() );
        storageBiz.setSorted( storageBizDto.getSorted() );

        return storageBiz;
    }
}
