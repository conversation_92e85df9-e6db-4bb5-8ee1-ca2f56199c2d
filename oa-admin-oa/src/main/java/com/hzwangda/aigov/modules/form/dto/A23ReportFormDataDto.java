package com.hzwangda.aigov.modules.form.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.wangda.oa.base.BaseDTO;
import com.wangda.oa.modules.system.service.dto.DeptDto;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import lombok.Data;

@Data
public class A23ReportFormDataDto extends BaseDTO {
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    private SimpleUserDto create;
    private DeptDto dept;
    private A23ReportFormInstantiateDto instantiate;


}
