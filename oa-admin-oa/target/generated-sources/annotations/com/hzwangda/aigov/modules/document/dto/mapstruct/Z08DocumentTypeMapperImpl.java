package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.Z08DocumentTypeDto;
import com.hzwangda.aigov.modules.document.entity.Z08DocumentType;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import com.wangda.oa.modules.workflow.domain.common.WFStorageBiz;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class Z08DocumentTypeMapperImpl implements Z08DocumentTypeMapper {

    @Autowired
    private Z08FileMapperUtil z08FileMapperUtil;

    @Override
    public List<Z08DocumentTypeDto> toDto(List<Z08DocumentType> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<Z08DocumentTypeDto> list = new ArrayList<Z08DocumentTypeDto>( entityList.size() );
        for ( Z08DocumentType z08DocumentType : entityList ) {
            list.add( toDto( z08DocumentType ) );
        }

        return list;
    }

    @Override
    public Z08DocumentType toEntity(Z08DocumentTypeDto dto) {
        if ( dto == null ) {
            return null;
        }

        Z08DocumentType z08DocumentType = new Z08DocumentType();

        z08DocumentType.setBelongToDept( dto.getBelongToDept() );
        z08DocumentType.setBpmInstanceId( dto.getBpmInstanceId() );
        z08DocumentType.setBpmProcessKey( dto.getBpmProcessKey() );
        z08DocumentType.setBpmStatus( dto.getBpmStatus() );
        z08DocumentType.setBpmSubject( dto.getBpmSubject() );
        z08DocumentType.setCreateBy( dto.getCreateBy() );
        z08DocumentType.setCreateTime( dto.getCreateTime() );
        z08DocumentType.setId( dto.getId() );
        List<String> list = dto.getParticipateUser();
        if ( list != null ) {
            z08DocumentType.setParticipateUser( new ArrayList<String>( list ) );
        }
        z08DocumentType.setUpdateBy( dto.getUpdateBy() );
        z08DocumentType.setUpdateTime( dto.getUpdateTime() );
        z08DocumentType.setVersion( dto.getVersion() );
        z08DocumentType.setApplicationId( dto.getApplicationId() );
        z08DocumentType.setApplicationName( dto.getApplicationName() );
        z08DocumentType.setDeptId( dto.getDeptId() );
        z08DocumentType.setDeptName( dto.getDeptName() );
        z08DocumentType.setDz( dto.getDz() );
        z08DocumentType.setMaxXh( dto.getMaxXh() );
        z08DocumentType.setSort( dto.getSort() );
        z08DocumentType.setFj( storageBizDtoToWFStorageBiz( dto.getFj() ) );
        z08DocumentType.setThumbnail( storageBizDtoToWFStorageBiz( dto.getThumbnail() ) );

        return z08DocumentType;
    }

    @Override
    public List<Z08DocumentType> toEntity(List<Z08DocumentTypeDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Z08DocumentType> list = new ArrayList<Z08DocumentType>( dtoList.size() );
        for ( Z08DocumentTypeDto z08DocumentTypeDto : dtoList ) {
            list.add( toEntity( z08DocumentTypeDto ) );
        }

        return list;
    }

    @Override
    public Z08DocumentTypeDto toDto(Z08DocumentType entity) {
        if ( entity == null ) {
            return null;
        }

        Z08DocumentTypeDto z08DocumentTypeDto = new Z08DocumentTypeDto();

        z08DocumentTypeDto.setFj( z08FileMapperUtil.toZw( entity.getFj() ) );
        z08DocumentTypeDto.setThumbnail( z08FileMapperUtil.toZw( entity.getThumbnail() ) );
        z08DocumentTypeDto.setBelongToDept( entity.getBelongToDept() );
        z08DocumentTypeDto.setBpmInstanceId( entity.getBpmInstanceId() );
        z08DocumentTypeDto.setBpmProcessKey( entity.getBpmProcessKey() );
        z08DocumentTypeDto.setBpmStatus( entity.getBpmStatus() );
        z08DocumentTypeDto.setBpmSubject( entity.getBpmSubject() );
        z08DocumentTypeDto.setCreateBy( entity.getCreateBy() );
        z08DocumentTypeDto.setCreateTime( entity.getCreateTime() );
        z08DocumentTypeDto.setId( entity.getId() );
        List<String> list = entity.getParticipateUser();
        if ( list != null ) {
            z08DocumentTypeDto.setParticipateUser( new ArrayList<String>( list ) );
        }
        z08DocumentTypeDto.setUpdateBy( entity.getUpdateBy() );
        z08DocumentTypeDto.setUpdateTime( entity.getUpdateTime() );
        z08DocumentTypeDto.setVersion( entity.getVersion() );
        z08DocumentTypeDto.setApplicationId( entity.getApplicationId() );
        z08DocumentTypeDto.setApplicationName( entity.getApplicationName() );
        z08DocumentTypeDto.setDeptId( entity.getDeptId() );
        z08DocumentTypeDto.setDeptName( entity.getDeptName() );
        z08DocumentTypeDto.setDz( entity.getDz() );
        z08DocumentTypeDto.setMaxXh( entity.getMaxXh() );
        z08DocumentTypeDto.setSort( entity.getSort() );

        return z08DocumentTypeDto;
    }

    protected WFStorageBiz storageBizDtoToWFStorageBiz(StorageBizDto storageBizDto) {
        if ( storageBizDto == null ) {
            return null;
        }

        WFStorageBiz wFStorageBiz = new WFStorageBiz();

        wFStorageBiz.setBizId( storageBizDto.getBizId() );
        wFStorageBiz.setBizType( storageBizDto.getBizType() );
        wFStorageBiz.setSorted( storageBizDto.getSorted() );

        return wFStorageBiz;
    }
}
