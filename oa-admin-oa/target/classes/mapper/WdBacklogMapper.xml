<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wangda.oa.backlog.mapper.WdBacklogMapper">
    <select id="getBacklogList" resultType="com.wangda.oa.backlog.dto.BacklogListDto"
            parameterType="com.wangda.oa.backlog.bo.BacklogListBO">
        SELECT * from(
        SELECT
        WD.ID,
        WD.BIZ_ID,
        WD.TITLE,
        WD.APP_ID,
        WM.MODULE_NAME,
        WD.EXTENSION_JSON,
        WD.URL,
        WD.PC_URL,
        WD.LOGO,
        WT.SHELVE_ABORT_TIME,
        WT.URGENT,
        if(WT.END_READ_TIME is null,WR.END_READ_TIME,WT.END_READ_TIME) END_READ_TIME,
        WT.ABORT_TIME,
        <!--排序字段-->
        case
        when WT.CREATE_DATE is not null then WT.CREATE_DATE
        when WR.CREATE_DATE is not null then WR.CREATE_DATE
        else WD.CREATE_DATE
        end CREATE_DATE,
        CASE
        WHEN WR.TRANSACTION_TIME is not null THEN WR.TRANSACTION_TIME
        WHEN WT.CREATE_DATE is not null THEN WT.CREATE_DATE
        ELSE WD.CREATE_DATE
        END TRANSACTION_DATE,
        <choose>
            <when test="qry.reversedBy != null and  qry.reversedBy!= ''">
                ROW_NUMBER ( ) OVER ( PARTITION BY WD.BIZ_ID  ORDER BY WR.TRANSACTION_TIME desc ) RN
            </when>
            <otherwise>
                ROW_NUMBER ( ) OVER ( PARTITION BY WD.BIZ_ID) RN
            </otherwise>
        </choose>
        FROM
        WD_BACKLOG WD
        LEFT JOIN WD_BACKLOG_TRANSACTOR WT ON WD.ID = WT.BACKLOG_ID
        <!--待办列表过滤-->
        <if test="(qry.collect == null or qry.collect != 1) and qry.status == 1 and qry.userId != null">
            and WT.TRANSACTOR_ID = #{qry.userId}
        </if>
        LEFT JOIN WD_BACKLOG_ALREADY_RECORD WR ON WD.ID = WR.BACKLOG_ID
        <if test="qry.reversedBy != null and  qry.reversedBy!= ''">
            and WR.TRANSACTOR_ID =#{qry.userId}
        </if>
        <!--已办列表过滤-->
        <if test="(qry.collect == null or qry.collect != 1) and qry.status == 0 and qry.userId != null">
            and WR.TRANSACTOR_ID = #{qry.userId}
        </if>
        LEFT JOIN WD_BACKLOG_MODULE WM ON WD.MODULE_ID = WM.ID
        LEFT JOIN WD_BACKLOG_COLLECT_LABEL WL ON WD.ID = WL.BACKLOG_ID
        <if test="qry.businessSearchBOList != null and qry.businessSearchBOList.size > 0">
            <foreach item="item" collection="qry.businessSearchBOList" index="index">
                INNER JOIN wd_backlog_business WBB${index} ON WBB${index}.backlog_id=WD.ID and
                WBB${index}.key_type=#{item.key}
                and WBB${index}.value
                <choose>
                    <when test="item.type == 'contains'">
                        like CONCAT(CONCAT('%', #{item.value}), '%')
                    </when>
                    <otherwise>
                        = #{item.value}
                    </otherwise>
                </choose>
            </foreach>
        </if>
        WHERE
        WD.ENABLED = true
        <if test="qry.appId != null and qry.appId != ''">
            and WD.APP_ID = #{qry.appId}
        </if>
        <if test="qry.moduleId != null">
            and WD.MODULE_ID = #{qry.moduleId}
        </if>
        <if test="qry.categoryType == null">
            <choose>
                <when test="qry.moduleCodes != null and qry.moduleCodes.size > 0">
                    and WM.MODULE_CODE in
                    <foreach item="item" collection="qry.moduleCodes" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <when test="qry.moduleCode != null and qry.moduleCode != ''">
                    and WM.MODULE_CODE = #{qry.moduleCode}
                </when>
            </choose>
        </if>
        <if test="qry.categoryType != null">
            <if test="qry.moduleCodes != null and qry.moduleCodes.size > 0">
                and WM.MODULE_CODE not in
                <foreach item="item" collection="qry.moduleCodes" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <!--收藏过滤-->
        <if test="qry.collect != null and qry.collect == 1">
            <if test="qry.userId != null">
                and WL.TRANSACTOR_ID = #{qry.userId}
            </if>
        </if>
        <!--在不需要收藏的情况下过滤-->
        <if test="qry.collect == null or qry.collect != 1">
            <!--待办列表过滤-->
            <if test="qry.status == 0">
                <if test="qry.userId != null">
                    and WT.TRANSACTOR_ID = #{qry.userId}
                </if>
                <if test="qry.urgent != null">
                    and WT.URGENT = #{qry.urgent}
                </if>
                <if test="qry.shelve != null">
                    <if test="qry.shelve == 0">
                        and WT.SHELVE_ABORT_TIME IS NULL
                    </if>
                    <if test="qry.shelve == 1">
                        and WT.SHELVE_ABORT_TIME &gt; sysdate
                    </if>
                </if>
                <if test="qry.readType != null">
                    and WT.READ_TYPE = #{qry.readType}
                </if>
                <if test="qry.abortStartTime != null and qry.abortStartTime != '' and qry.abortEndTime != null and qry.abortEndTime != ''">
                    AND str_to_date( WT.ABORT_TIME, '%Y-%m-%d' )
                    BETWEEN #{qry.abortStartTime} AND #{qry.abortEndTime}
                </if>
            </if>
            <!--已办列表过滤-->
            <if test="qry.status == 1">
                <if test="qry.userId != null">
                    and (WR.TRANSACTOR_ID = #{qry.userId} or WD.USER_ID = #{qry.userId})
                </if>
            </if>
            <!--我的申请列表过滤-->
            <if test="qry.status == 2">
                <if test="qry.userId != null">
                    and WD.USER_ID = #{qry.userId}
                </if>
            </if>
            <!--抄送给我列表过滤-->
            <if test="qry.status == 3">
                <if test="qry.userId != null">
                    and (WT.TRANSACTOR_ID = #{qry.userId} or WR.TRANSACTOR_ID = #{qry.userId})
                    and (WT.CC = 1 or WR.CC = 1)
                </if>
            </if>
            <!--4:查询我的待办、已办、申请列表过滤-->
            <if test="qry.status == 4">
                <if test="qry.userId != null and qry.adminModuleCodes == null">
                    and (WT.TRANSACTOR_ID = #{qry.userId} or WR.TRANSACTOR_ID = #{qry.userId} or WD.USER_ID =
                    #{qry.userId})
                </if>
                <if test="qry.userId != null and qry.adminModuleCodes != null">
                    and (WT.TRANSACTOR_ID = #{qry.userId} or WR.TRANSACTOR_ID = #{qry.userId} or WD.USER_ID =
                    #{qry.userId} or WM.MODULE_CODE in
                    <foreach item="item" collection="qry.adminModuleCodes" index="index" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
            <!--待办列表过滤领办任务-->
            <if test="qry.collect != null and qry.collect == 0">
                <if test="qry.userId != null">
                    and WD.ID not in (select backlog_id from WD_BACKLOG_COLLECT_LABEL where transactor_id=#{qry.userId}
                    and create_date > WT.create_date)
                </if>
            </if>
        </if>
        <if test="qry.bt != null and qry.bt != ''">
        and WD.TITLE like CONCAT(CONCAT('%', #{qry.bt}), '%')
        </if>

        <if test="qry.bts != null and qry.bts.size > 0">
        and
            <foreach item="item" collection="qry.bts" index="index" open="(" separator="or"
                     close=")">
                WD.TITLE like CONCAT(CONCAT('%', #{item}), '%')
            </foreach>
        </if>

        <if test="qry.createStartTime != null and qry.createStartTime != '' and qry.createEndTime != null and qry.createEndTime != ''">
            AND to_char(WD.CREATE_DATE, 'YYYY-mm-dd' )
            BETWEEN #{qry.createStartTime} AND #{qry.createEndTime}
        </if>
        )A where A.rn=1
    </select>


    <select id="getModuleCountByWaitBacklog" resultType="map">
        SELECT
        count( wb.module_id ) number,
        min(wbm.module_code) module_code
        FROM
        wd_backlog wb
        INNER JOIN wd_backlog_module wbm ON wb.module_id = wbm.id
        INNER JOIN wd_backlog_transactor wbt ON wb.id = wbt.backlog_id
        where wbt.transactor_id=#{userName}
        and wb.ID not in (select backlog_id from WD_BACKLOG_COLLECT_LABEL where transactor_id=#{userName}
        and create_date > wbt.create_date)
        GROUP BY
        wb.module_id
    </select>
</mapper>