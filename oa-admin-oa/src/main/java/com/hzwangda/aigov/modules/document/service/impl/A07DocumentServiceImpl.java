package com.hzwangda.aigov.modules.document.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.hzwangda.aigov.bpm.repository.A07DocumentGwlzRepository;
import com.hzwangda.aigov.bpm.repository.A07DocumentgwlzUserRepository;
import com.hzwangda.aigov.bpm.repository.A07DocumentgzxtRepository;
import com.hzwangda.aigov.modules.archive.entity.A07FileArchive;
import com.hzwangda.aigov.modules.archive.repository.FileArchiveRepository;
import com.hzwangda.aigov.modules.document.constant.DocumentConstant;
import com.hzwangda.aigov.modules.document.dto.*;
import com.hzwangda.aigov.modules.document.dto.mapstruct.*;
import com.hzwangda.aigov.modules.document.entity.*;
import com.hzwangda.aigov.modules.document.repository.*;
import com.hzwangda.aigov.modules.document.service.A07DocumentService;
import com.hzwangda.aigov.modules.document.service.RoleAuthoritiesService;
import com.hzwangda.aigov.modules.workflow.bo.GwlzUserListBO;
import com.hzwangda.aigov.modules.workflow.bo.GzxtUserListBO;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.modules.workflow.dto.AnnualConferenceRegistrationListDto;
import com.hzwangda.aigov.modules.workflow.dto.MyConferenceListDto;
import com.hzwangda.aigov.modules.workflow.enums.workflow.WorkflowType;
import com.hzwangda.aigov.modules.zjedu.domain.dto.DocumentAddressCriteria;
import com.hzwangda.aigov.oa.domain.*;
import com.hzwangda.aigov.oa.mapper.A07documentMapper;
import com.hzwangda.aigov.oa.repository.*;
import com.hzwangda.aigov.oa.service.DocumentFlowTaskService;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.backlog.bo.BacklogBusinessSearchBO;
import com.wangda.oa.backlog.bo.BacklogListBO;
import com.wangda.oa.backlog.domain.WdBacklog;
import com.wangda.oa.backlog.dto.BacklogListDto;
import com.wangda.oa.backlog.dto.BacklogListPageDto;
import com.wangda.oa.backlog.service.WdBacklogService;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.UserCustomRepository;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskDto;
import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import com.wangda.oa.modules.workflow.service.workflow.FlowTaskService;
import com.wangda.oa.modules.workflow.utils.FlowableUtils;
import com.wangda.oa.utils.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.criteria.*;
import java.sql.Timestamp;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class A07DocumentServiceImpl implements A07DocumentService {

    private final Pattern yyyyPattern = Pattern.compile("^[1-2]\\d{3}$");
    private final Pattern numberPattern = Pattern.compile("[0-9]*");


    private final A07DocumentFwRepository a07DocumentFwRepository;

    private final A07DocumentFwMapper a07DocumentFwMapper;

    private final A07DocumentSwRepository a07DocumentSwRepository;

    private final A07DocumentSwMapper a07DocumentSwMapper;

    private final TaskService taskService;

    private final FlowTaskService flowTaskService;

    private final A07DocumentHqRepository a07DocumentHqRepository;

    private final A07DocumentHqMapper a07DocumentHqMapper;

    private final A07DocumentLdpsRepository a07DocumentLdpsRepository;

    private final A07DocumentLdpsMapper a07DocumentLdpsMapper;

    private final A07DocumentClqpRepository a07DocumentClqpRepository;

    private final A07DocumentClqpMapper a07DocumentClqpMapper;

    private final A07DocumentXxgksqRepository a07DocumentXxgksqRepository;
    private final A07DocumentgwlzUserRepository a07DocumentgwlzUserRepository;

    private final A07DocumentXxgksqMapper a07DocumentXxgksqMapper;

    private final A07documentMapper a07documentMapper;

    private final A08GzxtRepository a08GzxtRepository;

    private final A08GzxtMapper a08GzxtMapper;

    private final A07DocumentGwlzRepository a07DocumentgwlzRepository;

    private final A07DocumentgzxtRepository a07DocumentgzxtRepository;

    private final A07DocumentGwlzMapper a07DocumentGwlzMapper;

    private final A07DocumentInfoWwfbRepository a07DocumentInfoWwfbRepository;

    private final A03BusinessTripRepository a03BusinessTripRepository;

    private final A04JobEntryRepository a04JobEntryRepository;

    private final A04JobLeaveRepository a04JobLeaveRepository;

    private final A03LeaveApprovalRepository a03LeaveApprovalRepository;

    private final A05OfficeSuppliesRepository a05OfficeSuppliesRepository;

    private final A02RentalCarsRepository a02RentalCarsRepository;

    private final A02SealApprovalRepository a02SealApprovalRepository;

    private final A03TravelExpensesRepository a03TravelExpensesRepository;

    private final A03WorkOvertimeRepository a03WorkOvertimeRepository;

    private final HistoryService historyService;

    private final UserService sysUserService;

    private final RepositoryService repositoryService;

    private final A07DocumentFwViewRepository a07DocumentFwViewRepository;

    private final A07DocumentSwViewRepository a07DocumentSwViewRepository;

    private final A07DocumentGwViewRepository a07DocumentGwViewRepository;

    private final A07DocumentXzViewRepository a07DocumentXzViewRepository;

    private final UserRepository userRepository;

    private final WdBacklogService wdBacklogService;

    private final DocumentFlowTaskService documentFlowTaskService;
    private final RoleAuthoritiesService roleAuthoritiesService;

    @PersistenceContext
    private EntityManager em;

    private final UserCustomRepository userCustomRepository;

    private final FileArchiveRepository fileArchiveRepository;
    @Override
    public Map<String, Object> getPolicyDocumentList(A07DocumentZcwjQueryCriteria criteria, Pageable pageable) {
        //criteria.setGklx("主动公开");
        if (criteria.getType() != null) {
            if (criteria.getType() == 0) {
                criteria.setNwfb("是");
            } else {
                criteria.setNwfb("否");
            }
        }
        criteria.setZcwj("1");
        Page<A07DocumentFw> page = a07DocumentFwRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page);
    }

    @Override
    public Map<String, Object> getPolicyDocumentListForAPP(A07DocumentZcwjQueryCriteria criteria, Pageable pageable) {
        //criteria.setGklx("主动公开");
        Page<A07DocumentFw> page = a07DocumentFwRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page);
    }

    @Override
    public A07DocumentFwDto findByDocumentInfo(Long id) {
        A07DocumentFw a07DocumentFw = a07DocumentFwRepository.findById(id).orElseGet(A07DocumentFw::new);
        ValidationUtil.isNull(a07DocumentFw.getId(), "a07DocumentFw", "id", a07DocumentFw.getId());
        A07DocumentFwDto a06NewsDto = a07DocumentFwMapper.toDto(a07DocumentFw);
        return a06NewsDto;
    }

    @Override
    public Boolean deleteDocumentInfo(List<Long> ids) {
        for (Long id : ids) {
            a07DocumentFwRepository.deleteById(id);
        }
        return true;
    }

    @Override
    public Map<String, Object> getSwList(A07DocumentSwQueryCriteria criteria, Pageable pageable) {
        A07SearchDtoCriteria searchDtoCriteria = new A07SearchDtoCriteria();
        searchDtoCriteria.setBt(criteria.getBt());
        searchDtoCriteria.setType(criteria.getStatus());
        searchDtoCriteria.setSwlx(criteria.getSwlx());
        Map<String, FlowTaskInfoDto> map = this.getProcessInstanceIdMap(criteria.getStatus(), WorkflowType.DOCUMENT_ADDRESSEE.getValue(), pageable, searchDtoCriteria);
        if (CollectionUtils.isEmpty(map)) {
            return PageUtil.toPage(Page.empty());
        }
        criteria.setBpmInstanceId(map.keySet());
        Page<A07DocumentSw> page = a07DocumentSwRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Page<A07DocumentSwDto> pageDto = page.map(a07DocumentSwMapper::toDto);
        pageDto.getContent().forEach(a07DocumentSwDto -> {
            FlowTaskInfoDto flowTaskInfoDto = map.get(a07DocumentSwDto.getBpmInstanceId());
            a07DocumentSwDto.setFlowTaskInfoDto(flowTaskInfoDto);
        });
        return PageUtil.toPage(pageDto);
    }

    @Override
    public Map<String, Object> getSwListSearch(DocumentAddressCriteria criteria, Pageable pageable) {
        Page<A07DocumentSw> page = a07DocumentSwRepository.findAll(searchFilter(criteria), pageable);
        return PageUtil.toPage(page.map(a07DocumentSwMapper::toDto));
    }

    /**
     * 组装查询条件 按照空格分割 如果包含数字就查询年号相等的文件 如果包含正整数就匹配数字文号 如果都不是就模糊查询标题
     * TODO 如果已经存在年号文号就不走上面的逻辑了
     *
     * @param params
     * @return
     */
    private Specification<A07DocumentSw> searchFilter(DocumentAddressCriteria params) {
        return (root, query, cb) -> {
            ArrayList<Predicate> andList = new ArrayList<>();
            String[] keywords = params.getBlurry().split("\\s+");

            Join<A07DocumentSw, ReferenceNumber> swbh = root.join("swbh", JoinType.LEFT);
            Join<A07DocumentSw, ReferenceNumber> lwbh = root.join("lwbh", JoinType.LEFT);
            for (String keyword : keywords) {
                if (yyyyPattern.matcher(keyword).matches()) { // 是4位数字（年）
                    ArrayList<Predicate> orList = new ArrayList<>();
                    orList.add(cb.like(root.get("title").as(String.class), "%" + keyword + "%"));
                    orList.add(cb.equal(swbh.get("nh").as(String.class), keyword));
                    orList.add(cb.equal(swbh.get("xh").as(String.class), keyword));
                    orList.add(cb.equal(lwbh.get("nh").as(String.class), keyword));
                    orList.add(cb.equal(lwbh.get("xh").as(String.class), keyword));
                    andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));

                } else if (numberPattern.matcher(keyword).matches()) { // 是数字文号
                    ArrayList<Predicate> orList = new ArrayList<>();
                    orList.add(cb.like(root.get("title").as(String.class), "%" + keyword + "%"));
                    orList.add(cb.equal(swbh.get("xh").as(String.class), keyword));
                    orList.add(cb.equal(lwbh.get("xh").as(String.class), keyword));
                    andList.add(cb.or(orList.toArray(new Predicate[andList.size()])));
                } else {
                    andList.add(cb.like(root.get("title").as(String.class), "%" + keyword + "%"));
                }
            }
            return cb.and(andList.toArray(new Predicate[andList.size()]));
        };
    }


    @Override
    public A07DocumentSwDto getSwInfo(Long id) {
        A07DocumentSw sw = a07DocumentSwRepository.findById(id).orElseGet(A07DocumentSw::new);
        ValidationUtil.isNull(sw.getId(), "sw", "id", id);
        return a07DocumentSwMapper.toDto(sw);
    }

    @Override
    public Map<String, Object> getHqList(A07DocumentHqQueryCriteria criteria, Pageable pageable) {
        A07SearchDtoCriteria searchDtoCriteria = new A07SearchDtoCriteria();
        searchDtoCriteria.setBt(criteria.getBt());
        searchDtoCriteria.setType(criteria.getStatus());
        Map<String, FlowTaskInfoDto> map = this.getProcessInstanceIdMap(criteria.getStatus(), WorkflowType.DOCUMENT_SIGN.getValue(), pageable, searchDtoCriteria);
        if (CollectionUtils.isEmpty(map)) {
            return PageUtil.toPage(Page.empty());
        }
        criteria.setBpmInstanceId(map.keySet());
        Page<A07DocumentHq> page = a07DocumentHqRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Page<A07DocumentHqDto> pageDto = page.map(a07DocumentHqMapper::toDto);
        pageDto.getContent().forEach(a07DocumentHqDto -> {
            FlowTaskInfoDto flowTaskInfoDto = map.get(a07DocumentHqDto.getBpmInstanceId());
            a07DocumentHqDto.setFlowTaskInfoDto(flowTaskInfoDto);
        });
        return PageUtil.toPage(pageDto);
    }

    @Override
    public Map<String, Object> getLdpsList(A07DocumentLdpsQueryCriteria criteria, Pageable pageable) {
        A07SearchDtoCriteria searchDtoCriteria = new A07SearchDtoCriteria();
        searchDtoCriteria.setBt(criteria.getBt());
        searchDtoCriteria.setType(criteria.getStatus());
        Map<String, FlowTaskInfoDto> map = this.getProcessInstanceIdMap(criteria.getStatus(), WorkflowType.DOCUMENT_LDPS.getValue(), pageable, searchDtoCriteria);
        if (CollectionUtils.isEmpty(map)) {
            return PageUtil.toPage(Page.empty());
        }
        criteria.setBpmInstanceId(map.keySet());
        Page<A07DocumentLdps> page = a07DocumentLdpsRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Page<A07DocumentLdpsDto> pageDto = page.map(a07DocumentLdpsMapper::toDto);
        pageDto.getContent().forEach(a07DocumentLdpsDto -> {
            FlowTaskInfoDto flowTaskInfoDto = map.get(a07DocumentLdpsDto.getBpmInstanceId());
            a07DocumentLdpsDto.setFlowTaskInfoDto(flowTaskInfoDto);
        });
        return PageUtil.toPage(pageDto);
    }

    @Override
    public Map<String, Object> getClqpList(A07DocumentClqpQueryCriteria criteria, Pageable pageable) {
        A07SearchDtoCriteria searchDtoCriteria = new A07SearchDtoCriteria();
        searchDtoCriteria.setBt(criteria.getBt());
        searchDtoCriteria.setType(criteria.getStatus());
        Map<String, FlowTaskInfoDto> map = this.getProcessInstanceIdMap(criteria.getStatus(), WorkflowType.DOCUMENT_QPBL.getValue(), pageable, searchDtoCriteria);
        if (CollectionUtils.isEmpty(map)) {
            return PageUtil.toPage(Page.empty());
        }
        criteria.setBpmInstanceId(map.keySet());
        Page<A07DocumentClqp> page = a07DocumentClqpRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Page<A07DocumentClqpDto> pageDto = page.map(a07DocumentClqpMapper::toDto);
        pageDto.getContent().forEach(a07DocumentClqpDto -> {
            FlowTaskInfoDto flowTaskInfoDto = map.get(a07DocumentClqpDto.getBpmInstanceId());
            a07DocumentClqpDto.setFlowTaskInfoDto(flowTaskInfoDto);
        });
        return PageUtil.toPage(pageDto);
    }

    @Override
    public Map<String, Object> getXtList(A08GzxtQueryCriteria criteria, Pageable pageable) {
        A07SearchDtoCriteria searchDtoCriteria = new A07SearchDtoCriteria();
        searchDtoCriteria.setBt(criteria.getBt());
        searchDtoCriteria.setType(criteria.getStatus());
        Map<String, FlowTaskInfoDto> map = this.getProcessInstanceIdMap(criteria.getStatus(), WorkflowType.DOCUMENT_XT.getValue(), pageable, searchDtoCriteria);
        if (CollectionUtils.isEmpty(map)) {
            return PageUtil.toPage(Page.empty());
        }
        criteria.setBpmInstanceId(map.keySet());
        Page<A08Gzxt> page = a08GzxtRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Page<A08GzxtDto> pageDto = page.map(a08GzxtMapper::toDto);
        pageDto.getContent().forEach(a08GzxtDto -> {
            FlowTaskInfoDto flowTaskInfoDto = map.get(a08GzxtDto.getBpmInstanceId());
            a08GzxtDto.setFlowTaskInfoDto(flowTaskInfoDto);
        });
        return PageUtil.toPage(pageDto);
    }

    @Override
    public Map<String, Object> getXxgksqList(A07DocumentXxgksqQueryCriteria criteria, Pageable pageable) {
        A07SearchDtoCriteria searchDtoCriteria = new A07SearchDtoCriteria();
        searchDtoCriteria.setBt(criteria.getBt());
        searchDtoCriteria.setType(criteria.getStatus());
        Map<String, FlowTaskInfoDto> map = this.getProcessInstanceIdMap(criteria.getStatus(), WorkflowType.DOCUMENT_XXGKSQ.getValue(), pageable, searchDtoCriteria);
        if (CollectionUtils.isEmpty(map)) {
            return PageUtil.toPage(Page.empty());
        }
        criteria.setBpmInstanceId(map.keySet());
        Page<A07DocumentXxgksq> page = a07DocumentXxgksqRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Page<A07DocumentXxgksqDto> pageDto = page.map(a07DocumentXxgksqMapper::toDto);
        pageDto.getContent().forEach(a07DocumentXxgksqDto -> {
            FlowTaskInfoDto flowTaskInfoDto = map.get(a07DocumentXxgksqDto.getBpmInstanceId());
            a07DocumentXxgksqDto.setFlowTaskInfoDto(flowTaskInfoDto);
        });
        return PageUtil.toPage(pageDto);
    }

    @Override
    public A07DocumentHqDto getHqInfo(Long id) {
        A07DocumentHq hq = a07DocumentHqRepository.findById(id).orElseGet(A07DocumentHq::new);
        ValidationUtil.isNull(hq.getId(), "hq", "id", id);
        return a07DocumentHqMapper.toDto(hq);
    }

    @Override
    public A07DocumentLdpsDto getLdpsInfo(Long id) {
        A07DocumentLdps ldps = a07DocumentLdpsRepository.findById(id).orElseGet(A07DocumentLdps::new);
        ValidationUtil.isNull(ldps.getId(), "ldps", "id", id);
        return a07DocumentLdpsMapper.toDto(ldps);
    }

    @Override
    public A07DocumentClqpDto getClqpInfo(Long id) {
        A07DocumentClqp clqp = a07DocumentClqpRepository.findById(id).orElseGet(A07DocumentClqp::new);
        ValidationUtil.isNull(clqp.getId(), "clqp", "id", id);
        return a07DocumentClqpMapper.toDto(clqp);
    }

    @Override
    public A08GzxtDto getXtInfo(Long id) {
        A08Gzxt gzxt = a08GzxtRepository.findById(id).orElseGet(A08Gzxt::new);
        ValidationUtil.isNull(gzxt.getId(), "gzxt", "id", id);
        return a08GzxtMapper.toDto(gzxt);
    }

    @Override
    public A07DocumentXxgksqDto getXxgksqInfo(Long id) {
        A07DocumentXxgksq xxgksq = a07DocumentXxgksqRepository.findById(id).orElseGet(A07DocumentXxgksq::new);
        ValidationUtil.isNull(xxgksq.getId(), "xxgksq", "id", id);
        return a07DocumentXxgksqMapper.toDto(xxgksq);
    }

    @Override
    public Boolean deleteSw(List<Long> ids) {
        for (Long id : ids) {
            a07DocumentSwRepository.deleteById(id);
        }
        return true;
    }

    @Override
    public Boolean deleteHq(List<Long> ids) {
        for (Long id : ids) {
            a07DocumentHqRepository.deleteById(id);
        }
        return true;
    }

    @Override
    public Boolean deleteLdps(List<Long> ids) {
        for (Long id : ids) {
            a07DocumentLdpsRepository.deleteById(id);
        }
        return true;
    }

    @Override
    public Boolean deleteClqp(List<Long> ids) {
        for (Long id : ids) {
            a07DocumentClqpRepository.deleteById(id);
        }
        return true;
    }

    @Override
    public Boolean deleteXt(List<Long> ids) {
        for (Long id : ids) {
            a08GzxtRepository.deleteById(id);
        }
        return true;
    }

    @Override
    public Boolean deleteXxgksq(List<Long> ids) {
        for (Long id : ids) {
            a07DocumentXxgksqRepository.deleteById(id);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public A07DocumentGwlz createOrUpdate(A07DocumentGwlz resources) {
        if (resources.getId() == null) {
            resources.setCreatorId(SecurityUtils.getCurrentUserId());
            return a07DocumentgwlzRepository.save(resources);
        } else {
            A07DocumentGwlz a07DocumentGwlz = a07DocumentgwlzRepository.findById(resources.getId()).orElseGet(A07DocumentGwlz::new);
            a07DocumentGwlz.copy(resources);
            return a07DocumentgwlzRepository.save(a07DocumentGwlz);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public A07DocumentGzxt createOrUpdateGzxt(A07DocumentGzxt resources) {
        if (resources.getId() == null) {
            return a07DocumentgzxtRepository.save(resources);
        } else {
            A07DocumentGzxt a07DocumentGzxt = a07DocumentgzxtRepository.findById(resources.getId()).orElseGet(A07DocumentGzxt::new);
            a07DocumentGzxt.copy(resources);
            return a07DocumentgzxtRepository.save(a07DocumentGzxt);
        }
    }


    @Override
    public Map<String, Object> gwlzList(A07DocumentGwlzQueryCriteria criteria, Pageable pageable) {
        criteria.setCreatorId(SecurityUtils.getCurrentUserId());
        Page<A07DocumentGwlz> page = a07DocumentgwlzRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(a07DocumentGwlzMapper::toDto));
    }

    @Override
    public List<A07DocumentInfoWwfb> getWwfbList() {
        return a07DocumentInfoWwfbRepository.findAll(Sort.by("sort").ascending());
    }


    @Override
    public Object getGwlzUserList(GwlzUserListBO bo) {
        Map map = new HashMap();
        bo.setUsername(SecurityUtils.getCurrentUsername());
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<A07DocumentGwlz> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(bo.getPageNo(), bo.getSize());
        IPage<A07DocumentGwlzDto> listDtoIPage = a07documentMapper.getUserGwList(page, bo);
        map.put("content", listDtoIPage.getRecords());
        map.put("totalElements", listDtoIPage.getTotal());
        return map;
    }

    @Override
    public A07DocumentGwlz getGwlzInfo(Long id) {
        A07DocumentGwlz a07DocumentGwlz = a07DocumentgwlzRepository.findById(id).orElseGet(A07DocumentGwlz::new);
        ValidationUtil.isNull(a07DocumentGwlz.getId(), "a07DocumentGwlz", "id", id);
        return a07DocumentGwlz;
    }

    @Override
    public A07DocumentGwlzUserDto getGwlzUserInfo(Long id) {
        A07DocumentGwlzUserDto documentGwlzUserDto = new A07DocumentGwlzUserDto();

        List<A07DocumentGwlzUser> waitList = a07DocumentgwlzUserRepository.findByGwidAndQszt(String.valueOf(id), 0);
        List<A07DocumentGwlzUser> completeList = a07DocumentgwlzUserRepository.findByGwidAndQszt(String.valueOf(id), 1);
        if (waitList != null && waitList.size() > 0) {
            documentGwlzUserDto.setWaitList(new HashSet<>(waitList));
        }
        if (completeList != null && completeList.size() > 0) {
            documentGwlzUserDto.setCompleteList(new HashSet<>(completeList));
        }
        return documentGwlzUserDto;
    }

    @Override
    public Boolean gwlzQs(Long id) {
        List<A07DocumentGwlzUser> list = a07DocumentgwlzUserRepository.findByGwidAndQsztAndUsername(String.valueOf(id), 0, SecurityUtils.getCurrentUsername());
        if (list != null && list.size() > 0) {
            list.stream().forEach(p -> {
                p.setQsrq(new Timestamp(new Date().getTime()));
                p.setQszt(1);
            });
        }
        a07DocumentgwlzUserRepository.saveAll(list);
        return true;
    }

    @Override
    public Boolean gwlzLwSc(Long id) {
        a07DocumentgwlzUserRepository.deleteByUsernameAndGwid(SecurityUtils.getCurrentUsername(), String.valueOf(id));
        return true;
    }

    @Override
    public Boolean gwlzSc(Long id) {
        a07DocumentgwlzRepository.deleteById(id);
        return true;
    }

    @Override
    public Boolean gwlzCh(Long id) {
        A07DocumentGwlz a07DocumentGwlz = a07DocumentgwlzRepository.findById(id).orElseGet(A07DocumentGwlz::new);
        ValidationUtil.isNull(a07DocumentGwlz.getId(), "a07DocumentGwlz", "id", id);
        a07DocumentGwlz.setGwzt("草稿");
        a07DocumentgwlzRepository.save(a07DocumentGwlz);
        return true;
    }

    @Override
    public MyConferenceListDto getGzxtUserList(GzxtUserListBO bo) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<AnnualConferenceRegistrationListDto> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(bo.getPageNo(), bo.getSize());
        IPage<AnnualConferenceRegistrationListDto> listDtoIPage = a07documentMapper.getUserGzxtList(page, bo);
        MyConferenceListDto dto = new MyConferenceListDto();
        dto.setList(listDtoIPage.getRecords());
        dto.setTotal(listDtoIPage.getTotal());
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public A07DocumentGwlzUser createOrUpdateUser(A07DocumentGwlzUser resources) {
        if (resources.getId() == null) {
            return a07DocumentgwlzUserRepository.save(resources);
        } else {
            A07DocumentGwlzUser a07DocumentGwlzUser = a07DocumentgwlzUserRepository.findById(resources.getId()).orElseGet(A07DocumentGwlzUser::new);
            a07DocumentGwlzUser.copy(resources);
            return a07DocumentgwlzUserRepository.save(a07DocumentGwlzUser);
        }
    }

    @Override
    public Map<String, Object> getSwListRetrieve(A07DocumentRetrieveQueryCriteria criteria, Pageable pageable) {
        criteria.setGwglType(3);
        Page<A07DocumentSwView> page = a07DocumentSwViewRepository.findAll((Specification) retrieveFilter(criteria), pageable);
        return PageUtil.toPage(page);
    }

    @Override
    public Map<String, Object> getFwListRetrieve(A07DocumentRetrieveQueryCriteria criteria, Pageable pageable) {
        criteria.setGwglType(0);
        Page<A07DocumentFwView> page = a07DocumentFwViewRepository.findAll((Specification) retrieveFilter(criteria), pageable);
        return PageUtil.toPage(page);
    }

    @Override
    public com.baomidou.mybatisplus.extension.plugins.pagination.Page<FlowTaskDto> getFwsxListRetrieve(A07DocumentRetrieveQueryCriteria criteria, Pageable pageable) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<FlowTaskDto> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>();
        List<String> instanceIds;
        WorkflowType workflowType = WorkflowType.getByValue(criteria.getFwsxType());
        if (workflowType == null) {
            return page;
        }
        switch (workflowType) {
            case BUSINESS_TRIP:
                //公差备案
                Page<A03BusinessTrip> gwPage = a03BusinessTripRepository.findAll((Specification) retrieveFilter(criteria), pageable);
                //获取实例id
                List<A03BusinessTrip> gwList = gwPage.getContent();
                instanceIds = gwList.stream().map(A03BusinessTrip::getBpmInstanceId).filter(x -> StringUtils.isNotBlank(x)).collect(Collectors.toList());
                page.setTotal(gwPage.getTotalElements());
                break;
            case JOB_ENTRY:
                //入职管理
                Page<A04JobEntry> rzPage = a04JobEntryRepository.findAll((Specification) retrieveFilter(criteria), pageable);
                //获取实例id
                List<A04JobEntry> rzList = rzPage.getContent();
                instanceIds = rzList.stream().map(A04JobEntry::getBpmInstanceId).filter(x -> StringUtils.isNotBlank(x)).collect(Collectors.toList());
                page.setTotal(rzPage.getTotalElements());
                break;
            case JOB_LEAVE:
                //调离管理
                Page<A04JobLeave> dlPage = a04JobLeaveRepository.findAll((Specification) retrieveFilter(criteria), pageable);
                //获取实例id
                List<A04JobLeave> dlList = dlPage.getContent();
                instanceIds = dlList.stream().map(A04JobLeave::getBpmInstanceId).filter(x -> StringUtils.isNotBlank(x)).collect(Collectors.toList());
                page.setTotal(dlPage.getTotalElements());
                break;
            case LEAVE_APPROVAL:
                //因私请假
                Page<A03LeaveApproval> ysPage = a03LeaveApprovalRepository.findAll((Specification) retrieveFilter(criteria), pageable);
                //获取实例id
                List<A03LeaveApproval> ysList = ysPage.getContent();
                instanceIds = ysList.stream().map(A03LeaveApproval::getBpmInstanceId).filter(x -> StringUtils.isNotBlank(x)).collect(Collectors.toList());
                page.setTotal(ysPage.getTotalElements());
                break;
            case OFFICE_SUPPLIES:
                //物品领用
                Page<A05OfficeSupplies> wplyPage = a05OfficeSuppliesRepository.findAll((Specification) retrieveFilter(criteria), pageable);
                //获取实例id
                List<A05OfficeSupplies> wplyList = wplyPage.getContent();
                instanceIds = wplyList.stream().map(A05OfficeSupplies::getBpmInstanceId).filter(x -> StringUtils.isNotBlank(x)).collect(Collectors.toList());
                page.setTotal(wplyPage.getTotalElements());
                break;
            case RENTAL_CARS:
                //用车审批
                Page<A02RentalCars> ycspPage = a02RentalCarsRepository.findAll((Specification) retrieveFilter(criteria), pageable);
                //获取实例id
                List<A02RentalCars> ycspList = ycspPage.getContent();
                instanceIds = ycspList.stream().map(A02RentalCars::getBpmInstanceId).filter(x -> StringUtils.isNotBlank(x)).collect(Collectors.toList());
                page.setTotal(ycspPage.getTotalElements());
                break;
            case SEAL_APPROVAL:
                //用印审批
                Page<A02SealApproval> yyspPage = a02SealApprovalRepository.findAll((Specification) retrieveFilter(criteria), pageable);
                //获取实例id
                List<A02SealApproval> yyspList = yyspPage.getContent();
                instanceIds = yyspList.stream().map(A02SealApproval::getBpmInstanceId).filter(x -> StringUtils.isNotBlank(x)).collect(Collectors.toList());
                page.setTotal(yyspPage.getTotalElements());
                break;
            case TRAVEL_EXPENSES:
                //出差报销
                Page<A03TravelExpenses> ccbxPage = a03TravelExpensesRepository.findAll((Specification) retrieveFilter(criteria), pageable);
                //获取实例id
                List<A03TravelExpenses> ccbxList = ccbxPage.getContent();
                instanceIds = ccbxList.stream().map(A03TravelExpenses::getBpmInstanceId).filter(x -> StringUtils.isNotBlank(x)).collect(Collectors.toList());
                page.setTotal(ccbxPage.getTotalElements());
                break;
            case WORK_OVERTIME:
                //加班备案
                Page<A03WorkOvertime> jbbaPage = a03WorkOvertimeRepository.findAll((Specification) retrieveFilter(criteria), pageable);
                //获取实例id
                List<A03WorkOvertime> jbbaList = jbbaPage.getContent();
                instanceIds = jbbaList.stream().map(A03WorkOvertime::getBpmInstanceId).filter(x -> StringUtils.isNotBlank(x)).collect(Collectors.toList());
                page.setTotal(jbbaPage.getTotalElements());
                break;
            default:
                return page;
        }
        if (instanceIds == null || instanceIds.size() < 1) {
            return page;
        }
        //根据实例ids和用户名查询对应Task进行分组
        List<Task> taskList = taskService.createTaskQuery().processInstanceIdIn(instanceIds).active().list();
        Map<String, List<Task>> map = taskList.stream().collect(Collectors.groupingBy(Task::getProcessInstanceId, Collectors.toList()));
        List<FlowTaskDto> flowTaskDtoList = instanceIds.stream().map(p -> {
            List<Task> taskList1 = map.get(p);
            if (taskList1 == null || taskList1.size() < 1) {
                return null;
            }
            Task task = taskList1.get(0);
            FlowTaskDto flowTaskDto = new FlowTaskDto();
            flowTaskDto.setCreateTime(task.getCreateTime());
            flowTaskDto.setTaskName(task.getName());
            flowTaskDto.setTaskId(task.getId());
            flowTaskDto.setProcInsId(p);
            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(task.getProcessDefinitionId())
                    .singleResult();
            if (Objects.nonNull(pd)) {
                flowTaskDto.setProcDefName(pd.getName());
                flowTaskDto.setProcDefVersion(pd.getVersion());
            }
            // 流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId())
                    .singleResult();
            if (StringUtils.isNotEmpty(historicProcessInstance.getStartUserId())) {
                UserDto startUser = sysUserService.findById(Long.parseLong(historicProcessInstance.getStartUserId()));
                FlowableUtils.setStartUserInfo(flowTaskDto, startUser);
            }
            return flowTaskDto;
        }).filter(x -> x != null).collect(Collectors.toList());
        page.setRecords(flowTaskDtoList);
        return page;
    }

    @Override
    public Map<String, Object> getXzList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        //封装待办查询条件
        BacklogListBO backlogListBO = new BacklogListBO();
        backlogListBO.setUserId(SecurityUtils.getCurrentUsername());
        backlogListBO.setAppId("OA");
        if (StringUtils.isNotBlank(criteria.getProcessDefinitionKey())) {
            //实例定义不为空
            backlogListBO.setModuleCode(criteria.getProcessDefinitionKey());
        } else {
            //实例定义为空则查询公文管理对应定义
            backlogListBO.setModuleCodes(DocumentConstant.PROCESS_DEFINITION_XZKEY_LIST);
        }
        List<BacklogBusinessSearchBO> businessSearchBOList = new ArrayList<>();
        if (StringUtils.isNotBlank(criteria.getBt())) {
            backlogListBO.setBt(criteria.getBt());
        }
        if (criteria.getTimeRange() != null && criteria.getTimeRange().size() > 1) {
            backlogListBO.setCreateStartTime(criteria.getTimeRange().get(0));
            backlogListBO.setCreateEndTime(criteria.getTimeRange().get(1));
        }
        backlogListBO.setBusinessSearchBOList(businessSearchBOList);
        backlogListBO.setStatus(criteria.getStatus());
        if (criteria.getStatus() == null) {
            //若不传默认查询所有，则为4
            backlogListBO.setStatus(4);
        }
        backlogListBO.setPage(pageable.getPageNumber());
        backlogListBO.setSize(pageable.getPageSize());

        //查询待办服务
        BacklogListPageDto backlogListPageDto = wdBacklogService.getBacklogList(backlogListBO);
        List<BacklogListDto> backlogListDtoList = backlogListPageDto.getContent();
        if (CollectionUtils.isEmpty(backlogListDtoList)) {
            return PageUtil.toPage(Page.empty());
        }

        //获取流程实例id
        Map<String, Date> bpmInstanceIdMap = new LinkedHashMap<>();
        for (BacklogListDto backlogListDto : backlogListDtoList) {
            bpmInstanceIdMap.put(backlogListDto.getBizId(), backlogListDto.getCreateDate());
        }
        criteria.setBpmInstanceId(Lists.newArrayList(bpmInstanceIdMap.keySet()));
        //查询业务视图表信息
        List<A07DocumentXzView> viewList = a07DocumentXzViewRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));

        String transactorNames = backlogListDtoList.stream().map(BacklogListDto::getCurrentHandler).collect(Collectors.joining(","));
        //用户名集合放入
        List<String> username = viewList.stream().map(A07DocumentXzView::getCjr).collect(Collectors.toList());
        //办理人放入用户名集合
        username.addAll(Arrays.asList(transactorNames.split(",")));

        //查询用户信息
        List<User> userList = userRepository.findByUsernameIn(username);
        Map<String, User> finalUserMap = userList.stream().collect(Collectors.toMap(User::getUsername, a -> a, (k1, k2) -> k1));

        List<A07DocumentGwDto> returnList = backlogListDtoList.stream().map(p -> {
            A07DocumentGwDto documentGwDto = new A07DocumentGwDto();
            for (A07DocumentXzView a07DocumentXzView : viewList) {
                if (a07DocumentXzView.getBpmInstanceId().equals(p.getBizId())) {
                    BeanUtils.copyProperties(a07DocumentXzView, documentGwDto);
                    if (StringUtils.isNotBlank(p.getCurrentHandler())) {
                        //当前办理人不为空根据username设置中文
                        String[] handlerArr = p.getCurrentHandler().split(",");
                        String handler = "";
                        for (int i = 0; i < handlerArr.length; i++) {
                            if (finalUserMap.get(handlerArr[i]) == null) {
                                continue;
                            }
                            handler += finalUserMap.get(handlerArr[i]).getNickName() + ",";
                        }
                        if (StringUtils.isNotBlank(handler)) {
                            handler = handler.substring(0, handler.length() - 1);
                        }
                        documentGwDto.setBlr(handler);
                    }
                    User cjrUser = finalUserMap.get(a07DocumentXzView.getCjr());
                    documentGwDto.setCjr(cjrUser == null ? null : cjrUser.getNickName());
                    WorkflowType workflowType = WorkflowType.getByValue(a07DocumentXzView.getModuleType());
                    documentGwDto.setModuleType(workflowType == null ? null : workflowType.getName());
                    documentGwDto.setBpmStatus(p.getHandleStatus());
                    documentGwDto.setUrgent(p.getUrgent());
                    if (Objects.nonNull(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()))) {
                        documentGwDto.setCreateTime(new Timestamp(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()).getTime()));
                    }
                    if (StringUtils.isBlank(documentGwDto.getBt())) {
                        documentGwDto.setBt(p.getTitle());
                    }
                    return documentGwDto;
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, Object> returnMap = new LinkedHashMap<>(2);
        returnMap.put("content", returnList);
        returnMap.put("totalElements", backlogListPageDto.getTotalElements());
        return returnMap;
    }

    @Override
    public Map<String, Object> getGwList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        //封装待办查询条件
        BacklogListBO backlogListBO = new BacklogListBO();
        backlogListBO.setUserId(SecurityUtils.getCurrentUsername());
        backlogListBO.setAppId("OA");
        if (StringUtils.isNotBlank(criteria.getProcessDefinitionKey())) {
            //实例定义不为空
            backlogListBO.setModuleCode(criteria.getProcessDefinitionKey());
        } else {
            //实例定义为空则查询公文管理对应定义
            backlogListBO.setModuleCodes(DocumentConstant.PROCESS_DEFINITION_KEY_LIST);
        }
        List<BacklogBusinessSearchBO> businessSearchBOList = new ArrayList<>();
        if (StringUtils.isNotBlank(criteria.getBt())) {
            backlogListBO.setBt(criteria.getBt());
        }
        if (criteria.getTimeRange() != null && criteria.getTimeRange().size() > 1) {
            backlogListBO.setCreateStartTime(criteria.getTimeRange().get(0));
            backlogListBO.setCreateEndTime(criteria.getTimeRange().get(1));
        }
        backlogListBO.setBusinessSearchBOList(businessSearchBOList);
        backlogListBO.setStatus(criteria.getStatus());
        if (criteria.getStatus() == null) {
            //若不传默认查询所有，则为4
            backlogListBO.setStatus(4);
        }
        backlogListBO.setPage(pageable.getPageNumber());
        backlogListBO.setSize(pageable.getPageSize());

        //查询待办服务
        BacklogListPageDto backlogListPageDto = wdBacklogService.getBacklogList(backlogListBO);
        List<BacklogListDto> backlogListDtoList = backlogListPageDto.getContent();
        if (CollectionUtils.isEmpty(backlogListDtoList)) {
            return PageUtil.toPage(Page.empty());
        }

        //获取流程实例id
        Map<String, Date> bpmInstanceIdMap = new LinkedHashMap<>();
        for (BacklogListDto backlogListDto : backlogListDtoList) {
            bpmInstanceIdMap.put(backlogListDto.getBizId(), backlogListDto.getCreateDate());
        }
        criteria.setBpmInstanceId(Lists.newArrayList(bpmInstanceIdMap.keySet()));
        //查询业务视图表信息
        List<A07DocumentGwView> viewList = a07DocumentGwViewRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));

        String transactorNames = backlogListDtoList.stream().map(BacklogListDto::getCurrentHandler).collect(Collectors.joining(","));
        //用户名集合放入
        List<String> username = viewList.stream().map(A07DocumentGwView::getCjr).collect(Collectors.toList());
        //办理人放入用户名集合
        username.addAll(Arrays.asList(transactorNames.split(",")));

        //查询用户信息
        List<User> userList = userRepository.findByUsernameIn(username);
        Map<String, User> finalUserMap = userList.stream().collect(Collectors.toMap(User::getUsername, a -> a, (k1, k2) -> k1));

        List<A07DocumentGwDto> returnList = backlogListDtoList.stream().map(p -> {
            A07DocumentGwDto documentGwDto = new A07DocumentGwDto();
            for (A07DocumentGwView a07DocumentGwView : viewList) {
                if (a07DocumentGwView.getBpmInstanceId().equals(p.getBizId())) {
                    BeanUtils.copyProperties(a07DocumentGwView, documentGwDto);
                    if (StringUtils.isNotBlank(p.getCurrentHandler())) {
                        //当前办理人不为空根据username设置中文
                        String[] handlerArr = p.getCurrentHandler().split(",");
                        String handler = "";
                        for (int i = 0; i < handlerArr.length; i++) {
                            if (finalUserMap.get(handlerArr[i]) == null) {
                                continue;
                            }
                            handler += finalUserMap.get(handlerArr[i]).getNickName() + ",";
                        }
                        if (StringUtils.isNotBlank(handler)) {
                            handler = handler.substring(0, handler.length() - 1);
                        }
                        documentGwDto.setBlr(handler);
                    }
                    User cjrUser = finalUserMap.get(a07DocumentGwView.getCjr());
                    documentGwDto.setCjr(cjrUser == null ? null : cjrUser.getNickName());
                    WorkflowType workflowType = WorkflowType.getByValue(a07DocumentGwView.getModuleType());
                    documentGwDto.setModuleType(workflowType == null ? null : workflowType.getName());
                    documentGwDto.setBpmStatus(p.getHandleStatus());
                    documentGwDto.setUrgent(p.getUrgent());
                    /*documentGwDto.setExpiredStatus(p.getExpiredStatus());*/
                    if (Objects.nonNull(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()))) {
                        documentGwDto.setCreateTime(new Timestamp(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()).getTime()));
                    }
                    if (StringUtils.isBlank(documentGwDto.getBt())) {
                        documentGwDto.setBt(p.getTitle());
                    }
                    return documentGwDto;
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, Object> returnMap = new LinkedHashMap<>(2);
        returnMap.put("content", returnList);
        returnMap.put("totalElements", backlogListPageDto.getTotalElements());
        return returnMap;
    }

    @Override
    public Map<String, Object> getFwList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        //去除标题左右空格
        if (StringUtils.isNotEmpty(criteria.getBt())) {
            criteria.setBt(criteria.getBt().trim());
        }
        //封装待办查询条件
        BacklogListBO backlogListBO = new BacklogListBO();
        backlogListBO.setUserId(SecurityUtils.getCurrentUsername());
        backlogListBO.setAppId("OA");
        backlogListBO.setReversedBy("TRANSACTION_DATE");
        if (StringUtils.isNotBlank(criteria.getProcessDefinitionKey())) {
            //实例定义不为空
            backlogListBO.setModuleCode(criteria.getProcessDefinitionKey());
        } else {
            //实例定义为空则查询公文管理对应定义
            backlogListBO.setModuleCodes(DocumentConstant.PROCESS_DEFINITION_KEY_FW_LIST);
        }
        List<BacklogBusinessSearchBO> businessSearchBOList = new ArrayList<>();
        if (StringUtils.isNotEmpty(criteria.getYear())) {
            BacklogBusinessSearchBO businessSearchBO = new BacklogBusinessSearchBO();
            businessSearchBO.setType("contains");
            businessSearchBO.setKey("gwwh");
            businessSearchBO.setValue("\"nh\":\"" + criteria.getYear() + "\"");
            businessSearchBOList.add(businessSearchBO);
        }
        if (StringUtils.isNotEmpty(criteria.getDz())) {
            BacklogBusinessSearchBO businessSearchBO = new BacklogBusinessSearchBO();
            businessSearchBO.setType("contains");
            businessSearchBO.setKey("gwwh");
            businessSearchBO.setValue("\"dz\":\"" + criteria.getDz() + "\"");
            businessSearchBOList.add(businessSearchBO);
        }
        if (StringUtils.isNotEmpty(criteria.getGwxh())) {
            BacklogBusinessSearchBO businessSearchBO = new BacklogBusinessSearchBO();
            businessSearchBO.setType("contains");
            businessSearchBO.setKey("gwwh");
            businessSearchBO.setValue("\"xh\":\"" + criteria.getGwxh() + "\"");
            businessSearchBOList.add(businessSearchBO);
        }

        if (StringUtils.isNotBlank(criteria.getBt())) {
            if (criteria.getBt().contains(" ")) {
                backlogListBO.setBts(Arrays.asList(criteria.getBt().split(" ")).stream().filter(s -> {
                    return StringUtils.isNotEmpty(s);
                }).collect(Collectors.toList()));
            } else {
                backlogListBO.setBt(criteria.getBt());
            }
        }
        if (criteria.getTimeRange() != null && criteria.getTimeRange().size() > 1) {
            backlogListBO.setCreateStartTime(criteria.getTimeRange().get(0));
            backlogListBO.setCreateEndTime(criteria.getTimeRange().get(1));
        }
        backlogListBO.setBusinessSearchBOList(businessSearchBOList);
        backlogListBO.setStatus(criteria.getStatus());
        if (criteria.getStatus() == null) {
            //若不传默认查询所有，则为4
            backlogListBO.setStatus(4);
        }
        backlogListBO.setPage(pageable.getPageNumber());
        backlogListBO.setSize(pageable.getPageSize());


        // 已办查询流程状态
        if (criteria.getStatus() == 1 && StringUtils.isNotEmpty(criteria.getBpmStatus())) {
            StringBuilder sql = new StringBuilder("SELECT DISTINCT biz_id FROM wd_backlog LEFT JOIN wd_backlog_already_record ON wd_backlog.id=backlog_id WHERE 1=1 ");
            sql.append(" AND wd_backlog.enabled=true AND wd_backlog.app_id=:appId ");
            sql.append(" AND (wd_backlog.user_id=:name OR wd_backlog_already_record.transactor_id=:name) ");
            sql.append(" AND module_id IN (SELECT id FROM wd_backlog_module WHERE module_code IN :moduleCode) ");
            if (StringUtils.isNotEmpty(backlogListBO.getCreateStartTime())) {
                sql.append(" AND to_char(wd_backlog.CREATE_DATE, 'YYYY-mm-dd') BETWEEN :createStartTime AND :createEndTime ");
            }

            Query nativeQuery = em.createNativeQuery(sql.toString());
            nativeQuery.setParameter("appId", backlogListBO.getAppId());
            nativeQuery.setParameter("name", SecurityUtils.getCurrentUsername());
            nativeQuery.setParameter("moduleCode", DocumentConstant.PROCESS_DEFINITION_KEY_FW_LIST);
            if (StringUtils.isNotEmpty(backlogListBO.getCreateStartTime())) {
                nativeQuery.setParameter("createStartTime", backlogListBO.getCreateStartTime());
                nativeQuery.setParameter("createEndTime", backlogListBO.getCreateEndTime());
            }
            List resultList = nativeQuery.getResultList();
            criteria.setBpmInstanceId(resultList);

            Page<A07DocumentGwView> all = a07DocumentGwViewRepository.findAll((root, query, criteriaBuilder) -> {
                Predicate predicate = QueryHelp.getPredicate(root, criteria, criteriaBuilder);
                if (StringUtils.isNotEmpty(criteria.getLwxh())) {
                    predicate = criteriaBuilder.and(
                            predicate,
                            criteriaBuilder.like(root.get("lwwh"), "%" + criteria.getLwxh() + "%")
                    );
                }
                if (StringUtils.isNotEmpty(criteria.getYear())) {
                    predicate = criteriaBuilder.and(
                            predicate,
                            criteriaBuilder.equal(root.get("gwwh").get("nh"), criteria.getYear())
                    );
                }
                if (StringUtils.isNotEmpty(criteria.getDz())) {
                    predicate = criteriaBuilder.and(
                            predicate,
                            criteriaBuilder.like(root.get("gwwh").get("dz"), "%" + criteria.getDz() + "%")
                    );
                }
                if (StringUtils.isNotEmpty(criteria.getSwxh())) {
                    predicate = criteriaBuilder.and(
                            predicate,
                            criteriaBuilder.equal(root.get("gwwh").get("xh"), criteria.getSwxh())
                    );
                }
                //中间存在空格，代表分词查询
                if (StringUtils.isNotEmpty(criteria.getBt()) && criteria.getBt().contains(" ")) {
                    String[] bts = criteria.getBt().split(" ");
                    criteria.setBt(null);

                    ArrayList<Predicate> orList = new ArrayList<>();
                    Arrays.stream(bts).forEach(bt -> {
                        if (StringUtils.isNotEmpty(bt)) {
                            orList.add(criteriaBuilder.or(criteriaBuilder.like(root.get("bt"), "%" + bt + "%")));
                        }
                    });
                    Predicate or = criteriaBuilder.or(orList.toArray(new Predicate[orList.size()]));
                    predicate = criteriaBuilder.and(predicate, or);
                }
                if (StringUtils.isNotEmpty(criteria.getNgr())) {
                    List<String> usernames = userCustomRepository.findUsernameByNickNameLike("%" + criteria.getNgr() + "%");
                    if (!usernames.isEmpty()) {
                        predicate = criteriaBuilder.and(
                                predicate,
                                root.get("createBy").in(usernames)
                        );
                    } else {
                        predicate = criteriaBuilder.and(
                                predicate,
                                criteriaBuilder.equal(root.get("createBy"), criteria.getNgr())
                        );
                    }
                }
                return predicate;
            }, pageable);
            List<A07DocumentGwDto> collect = all.stream().map(a07DocumentGwView -> {
                A07DocumentGwDto documentGwDto = new A07DocumentGwDto();
                BeanUtils.copyProperties(a07DocumentGwView, documentGwDto);
                Map backlog = (Map) em.createNativeQuery("select id,user_id from wd_backlog where biz_id=?1")
                        .setParameter(1, a07DocumentGwView.getBpmInstanceId())
                        .unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).getSingleResult();
                String cjr = sysUserService.findByName(backlog.get("user_id").toString()).getNickName();
                documentGwDto.setCjr(cjr);
                List<String> usernames = em.createNativeQuery("select transactor_id from wd_backlog_transactor where backlog_id =?1 and cc!=1").setParameter(1, backlog.get("id")).getResultList();
                String blr = sysUserService.findByUserNames(usernames).stream().map(UserDto::getNickName).collect(Collectors.joining(","));
                documentGwDto.setBlr(blr);
                WorkflowType workflowType = WorkflowType.getByValue(a07DocumentGwView.getModuleType());
                documentGwDto.setModuleType(workflowType == null ? null : workflowType.getName());

                Map singleResult = (Map) em.createNativeQuery("select end_read_time, transaction_time from wd_backlog_already_record where backlog_id=?1 and transactor_id =?2 order by transaction_time desc limit 1")
                        .setParameter(1, backlog.get("id"))
                        .setParameter(2, SecurityUtils.getCurrentUsername())
                        .unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
                        .getSingleResult();
                documentGwDto.setEndReadTime((Date) singleResult.get("end_read_time"));
                documentGwDto.setTransactionTime((Date) singleResult.get("transaction_time"));
                // 流程状态
                ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(a07DocumentGwView.getBpmStatus());
                documentGwDto.setBpmStatus(procStatusEnum == null ? null : procStatusEnum.getName());

                return documentGwDto;
            }).collect(Collectors.toList());
            Map<String, Object> returnMap = new LinkedHashMap<>(2);
            returnMap.put("content", collect);
            returnMap.put("totalElements", all.getTotalElements());
            return returnMap;
        }


        //查询待办服务
        BacklogListPageDto backlogListPageDto = wdBacklogService.getBacklogList(backlogListBO);
        List<BacklogListDto> backlogListDtoList = backlogListPageDto.getContent();
        if (CollectionUtils.isEmpty(backlogListDtoList)) {
            return PageUtil.toPage(Page.empty());
        }

        //获取流程实例id
        Map<String, Date> bpmInstanceIdMap = new LinkedHashMap<>();
        for (BacklogListDto backlogListDto : backlogListDtoList) {
            bpmInstanceIdMap.put(backlogListDto.getBizId(), backlogListDto.getCreateDate());
        }
        criteria.setBpmInstanceId(Lists.newArrayList(bpmInstanceIdMap.keySet()));
        //查询业务视图表信息
        List<A07DocumentGwView> viewList = a07DocumentGwViewRepository.findAll((root, criteriaQuery, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.and();
            //中间存在空格，代表分词查询
            if (StringUtils.isNotEmpty(criteria.getBt()) && criteria.getBt().contains(" ")) {
                String[] bts = criteria.getBt().split(" ");
                criteria.setBt(null);

                ArrayList<Predicate> orList = new ArrayList<>();
                Arrays.stream(bts).forEach(bt -> {
                    if (StringUtils.isNotEmpty(bt)) {
                        orList.add(criteriaBuilder.or(criteriaBuilder.like(root.get("bt"), "%" + bt + "%")));
                    }
                });
                Predicate or = criteriaBuilder.or(orList.toArray(new Predicate[orList.size()]));
                predicate = criteriaBuilder.and(predicate, or);
            }
            if (StringUtils.isNotEmpty(criteria.getNgr())) {
                List<String> usernames = userCustomRepository.findUsernameByNickNameLike("%" + criteria.getNgr() + "%");
                if (!usernames.isEmpty()) {
                    predicate = criteriaBuilder.and(
                            predicate,
                            root.get("createBy").in(usernames)
                    );
                } else {
                    predicate = criteriaBuilder.and(
                            predicate,
                            criteriaBuilder.equal(root.get("createBy"), criteria.getNgr())
                    );
                }
            }
            predicate = criteriaBuilder.and(
                    predicate,
                    QueryHelp.getPredicate(root, criteria, criteriaBuilder));
            return predicate;
        });

        String transactorNames = backlogListDtoList.stream().map(BacklogListDto::getCurrentHandler).collect(Collectors.joining(","));
        //用户名集合放入
        List<String> username = viewList.stream().map(A07DocumentGwView::getCjr).collect(Collectors.toList());
        //办理人放入用户名集合
        username.addAll(Arrays.asList(transactorNames.split(",")));

        //查询用户信息
        List<User> userList = userRepository.findByUsernameIn(username);
        Map<String, User> finalUserMap = userList.stream().collect(Collectors.toMap(User::getUsername, a -> a, (k1, k2) -> k1));

        List<A07DocumentGwDto> returnList = backlogListDtoList.stream().map(p -> {
            A07DocumentGwDto documentGwDto = new A07DocumentGwDto();
            for (A07DocumentGwView a07DocumentGwView : viewList) {
                if (a07DocumentGwView.getBpmInstanceId().equals(p.getBizId())) {
                    BeanUtils.copyProperties(a07DocumentGwView, documentGwDto);
                    if (StringUtils.isNotBlank(p.getCurrentHandler())) {
                        //当前办理人不为空根据username设置中文
                        String[] handlerArr = p.getCurrentHandler().split(",");
                        String handler = "";
                        for (int i = 0; i < handlerArr.length; i++) {
                            if (finalUserMap.get(handlerArr[i]) == null) {
                                continue;
                            }
                            handler += finalUserMap.get(handlerArr[i]).getNickName() + ",";
                        }
                        if (StringUtils.isNotBlank(handler)) {
                            handler = handler.substring(0, handler.length() - 1);
                        }
                        documentGwDto.setBlr(handler);
                    }
                    User cjrUser = finalUserMap.get(a07DocumentGwView.getCjr());
                    documentGwDto.setCjr(cjrUser == null ? null : cjrUser.getNickName());
                    WorkflowType workflowType = WorkflowType.getByValue(a07DocumentGwView.getModuleType());
                    documentGwDto.setModuleType(workflowType == null ? null : workflowType.getName());
                    documentGwDto.setUrgent(p.getUrgent());
                    documentGwDto.setExpiredStatus(p.getExpiredStatus());
                    if (Objects.nonNull(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()))) {
                        documentGwDto.setCreateTime(new Timestamp(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()).getTime()));
                    }
                    if (StringUtils.isBlank(documentGwDto.getBt())) {
                        documentGwDto.setBt(p.getTitle());
                    }
                    // 流程状态
                    ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(a07DocumentGwView.getBpmStatus());
                    documentGwDto.setBpmStatus(procStatusEnum == null ? null : procStatusEnum.getName());

                    documentGwDto.setTransactionTime(p.getTransactionDate());
                    return documentGwDto;
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, Object> returnMap = new LinkedHashMap<>(2);
        returnMap.put("content", returnList);
        returnMap.put("totalElements", backlogListPageDto.getTotalElements());
        return returnMap;
    }


    @Override
    public Map<String, Object> getAllFwList(A07AllFwQueryCriteria criteria, Pageable pageable) {
        List<String> procInstIds = new ArrayList<>();
        if (StringUtils.isBlank(criteria.getBelongToDept()) && !SecurityUtils.getCurrentUsername().equals("superAdmin")) {
            criteria.setUsername(SecurityUtils.getCurrentUsername());
            procInstIds = historyService.createHistoricTaskInstanceQuery()
                    .taskAssignee(criteria.getUsername())
                    .list().stream().map(HistoricTaskInstance::getProcessInstanceId).collect(Collectors.toList());
            if (procInstIds.isEmpty()) {
                return PageUtil.toPage(Page.empty());
            }
        }
//        Page<A07AllFwDto> pageDto = documentFlowTaskService.queryAllFwList(criteria, pageable);
//        // 适配业务属性
//        pageDto.getContent().forEach(dto -> {
//            ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(dto.getBpmStatus());
//            dto.setBpmStatus(procStatusEnum == null ? null : procStatusEnum.getName());
//
//            WorkflowType workflowType = WorkflowType.getByValue(dto.getBpmProcessKey());
//            dto.setModuleType(workflowType == null ? null : workflowType.getName());
//
//            //是否已分发
//            A07DocumentGwlz gwlz = a07DocumentgwlzRepository.findFirstByDocPronInstIdAndGwzt(dto.getBpmInstanceId(), "1");
//            dto.setIsDistribute(Objects.isNull(gwlz) ? false : true);
//        });
//        return PageUtil.toPage(pageDto);

        List bpmProcessKeys = em.createNativeQuery("SELECT DISTINCT bpm_process_key FROM fw_all_view").getResultList();
        List<String> finalProcInstIds = procInstIds;
        Page<A07DocumentGwView> all = a07DocumentGwViewRepository.findAll((root, query, criteriaBuilder) -> {
            Predicate predicate = QueryHelp.getPredicate(root, criteria, criteriaBuilder);
            predicate = criteriaBuilder.and(
                    predicate,
                    criteriaBuilder.equal(root.get("gwType"), "1"),
                    root.get("bpmProcessKey").in(bpmProcessKeys)
            );
            if (!finalProcInstIds.isEmpty()) {
                predicate = criteriaBuilder.and(
                        predicate,
                        root.get("bpmInstanceId").in(finalProcInstIds)
                );
            }
            if (StringUtils.isNotBlank(criteria.getBt())) {
                List<Predicate> collect = Arrays.stream(criteria.getBt().split(" ")).map(bt ->
                        criteriaBuilder.like(root.get("bt"), "%" + bt + "%")
                ).collect(Collectors.toList());
                predicate = criteriaBuilder.and(
                        predicate,
                        criteriaBuilder.or(collect.toArray(new Predicate[collect.size()]))
                );
            }
            if (criteria.getTimeRange() != null && !criteria.getTimeRange().isEmpty()) {
                Timestamp beginTime = Timestamp.valueOf(criteria.getTimeRange().get(0) + " 00:00:00");
                Timestamp endTime = Timestamp.valueOf(criteria.getTimeRange().get(1) + " 23:59:59");
                predicate = criteriaBuilder.and(
                        predicate,
                        criteriaBuilder.between(root.get("createTime"), beginTime, endTime)
                );
            }
            if (StringUtils.isNotEmpty(criteria.getNgr())) {
                List<String> usernames = userCustomRepository.findUsernameByNickNameLike("%" + criteria.getNgr() + "%");
                if (!usernames.isEmpty()) {
                    predicate = criteriaBuilder.and(
                            predicate,
                            root.get("createBy").in(usernames)
                    );
                } else {
                    predicate = criteriaBuilder.and(
                            predicate,
                            criteriaBuilder.equal(root.get("createBy"), criteria.getNgr())
                    );
                }
            }
            return predicate;
        }, pageable);
        List<A07AllFwDto> collect = all.stream().map(a07DocumentGwView -> {
            A07AllFwDto.A07AllFwDtoBuilder builder = A07AllFwDto.builder()
                    .bt(a07DocumentGwView.getBt())
                    .createDate(a07DocumentGwView.getCreateTime());
            if (a07DocumentGwView.getGwwh() != null) {
                builder.gwwhStr(a07DocumentGwView.getGwwh().toString());
            }
            try {
                Object singleResult = em.createNativeQuery("select ngr from fw_all_view where id=?1 ").setParameter(1, a07DocumentGwView.getId()).getSingleResult();
                if (singleResult != null) {
                    builder.ngr(singleResult.toString());
                }
            } catch (Exception e) {
            }
            WorkflowType workflowType = WorkflowType.getByValue(a07DocumentGwView.getModuleType());
            if (workflowType != null) {
                builder.moduleType(workflowType.getName());
            }
            if (StringUtils.isNotBlank(a07DocumentGwView.getCreateBy())) {
                UserDto userDto = sysUserService.findByName(a07DocumentGwView.getCreateBy());
                if (userDto != null) {
                    builder.ngr(userDto.getNickName());
                }
            }
            TaskQuery taskQuery = taskService.createTaskQuery();
            if (StringUtils.isNotBlank(criteria.getUsername())) {
                taskQuery.taskAssignee(criteria.getUsername());
            }
            List<Task> taskList = taskQuery.processInstanceId(a07DocumentGwView.getBpmInstanceId()).list();
            if (!taskList.isEmpty()) {
                String taskName = taskList.stream().map(Task::getName).distinct().collect(Collectors.joining(","));
                String assigneeName = taskList.stream().map(task -> {
                    if (StringUtils.isNotBlank(task.getAssignee())) {
                        UserDto assignee = sysUserService.findByName(task.getAssignee());
                        if (assignee != null) {
                            return assignee.getNickName();
                        }
                        return null;
                    }
                    return null;
                }).filter(Objects::nonNull).collect(Collectors.joining(","));
                builder.taskName(taskName);
                builder.assigneeName(assigneeName);
            }
            A07AllFwDto a07AllFwDto = builder.build();
            a07AllFwDto.setBpmInstanceId(a07DocumentGwView.getBpmInstanceId());
            ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(a07DocumentGwView.getBpmStatus());
            if (procStatusEnum != null) {
                a07AllFwDto.setBpmStatus(procStatusEnum.getValue());
            }
            A07FileArchive byDocId = fileArchiveRepository.findByDocId(a07DocumentGwView.getId());
            if (byDocId != null) {
                a07AllFwDto.setFileStatus(byDocId.getStatus());
            }
            return a07AllFwDto;

        }).collect(Collectors.toList());
        return PageUtil.toPage(collect, all.getTotalElements());
    }


    @Override
    public Map<String, Object> getSwList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        //去除标题左右空格
        if (StringUtils.isNotEmpty(criteria.getBt())) {
            criteria.setBt(criteria.getBt().trim());
        }
        //封装待办查询条件
        BacklogListBO backlogListBO = new BacklogListBO();
        backlogListBO.setUserId(SecurityUtils.getCurrentUsername());
        backlogListBO.setAppId("OA");
        backlogListBO.setReversedBy("TRANSACTION_DATE");
        if (StringUtils.isNotBlank(criteria.getProcessDefinitionKey())) {
            //实例定义不为空
            backlogListBO.setModuleCode(criteria.getProcessDefinitionKey());
        } else {
            //实例定义为空则查询公文管理对应定义
            backlogListBO.setModuleCodes(DocumentConstant.PROCESS_DEFINITION_KEY_SW_LIST);
        }
        List<BacklogBusinessSearchBO> businessSearchBOList = new ArrayList<>();
        if (StringUtils.isNotEmpty(criteria.getYear())) {
            BacklogBusinessSearchBO businessSearchBO = new BacklogBusinessSearchBO();
            businessSearchBO.setType("contains");
            businessSearchBO.setKey("swbh");
            businessSearchBO.setValue("\"nh\":\"" + criteria.getYear() + "\"");
            businessSearchBOList.add(businessSearchBO);
        }
        if (StringUtils.isNotEmpty(criteria.getDz())) {
            BacklogBusinessSearchBO businessSearchBO = new BacklogBusinessSearchBO();
            businessSearchBO.setType("contains");
            businessSearchBO.setKey("lwbh");
            businessSearchBO.setValue("\"dz\":\"" + criteria.getDz() + "\"");
            businessSearchBOList.add(businessSearchBO);
        }
        if (StringUtils.isNotEmpty(criteria.getLwxh())) {
            BacklogBusinessSearchBO businessSearchBO = new BacklogBusinessSearchBO();
            businessSearchBO.setType("contains");
            businessSearchBO.setKey("lwbh");
            businessSearchBO.setValue(criteria.getLwxh());
            businessSearchBOList.add(businessSearchBO);
        }
        if (StringUtils.isNotEmpty(criteria.getSwxh())) {
            BacklogBusinessSearchBO businessSearchBO = new BacklogBusinessSearchBO();
            businessSearchBO.setType("contains");
            businessSearchBO.setKey("swbh");
            businessSearchBO.setValue("\"xh\":\"" + criteria.getSwxh() + "\"");
            businessSearchBOList.add(businessSearchBO);
        }


        if (StringUtils.isNotBlank(criteria.getBt())) {
            if (criteria.getBt().contains(" ")) {
                backlogListBO.setBts(Arrays.asList(criteria.getBt().split(" ")).stream().filter(s -> {
                    return StringUtils.isNotEmpty(s);
                }).collect(Collectors.toList()));
            } else {
                backlogListBO.setBt(criteria.getBt());
            }
        }
        if (criteria.getTimeRange() != null && criteria.getTimeRange().size() > 1) {
            backlogListBO.setCreateStartTime(criteria.getTimeRange().get(0));
            backlogListBO.setCreateEndTime(criteria.getTimeRange().get(1));
        }
        backlogListBO.setBusinessSearchBOList(businessSearchBOList);
        backlogListBO.setStatus(criteria.getStatus());
        if (criteria.getStatus() == null) {
            //若不传默认查询所有，则为4
            backlogListBO.setStatus(4);
        }
        backlogListBO.setPage(pageable.getPageNumber());
        backlogListBO.setSize(pageable.getPageSize());

        // 已办查询流程状态
        if (criteria.getStatus() == 1 && StringUtils.isNotEmpty(criteria.getBpmStatus())) {
            StringBuilder sql = new StringBuilder("SELECT DISTINCT biz_id FROM wd_backlog LEFT JOIN wd_backlog_already_record ON wd_backlog.id=backlog_id WHERE 1=1 ");
            sql.append(" AND wd_backlog.enabled=true AND wd_backlog.app_id=:appId ");
            sql.append(" AND (wd_backlog.user_id=:name OR wd_backlog_already_record.transactor_id=:name) ");
            sql.append(" AND module_id IN (SELECT id FROM wd_backlog_module WHERE module_code IN :moduleCode) ");
            if (StringUtils.isNotEmpty(backlogListBO.getCreateStartTime())) {
                sql.append(" AND to_char(wd_backlog.CREATE_DATE, 'YYYY-mm-dd') BETWEEN :createStartTime AND :createEndTime ");
            }

            Query nativeQuery = em.createNativeQuery(sql.toString());
            nativeQuery.setParameter("appId", backlogListBO.getAppId());
            nativeQuery.setParameter("name", SecurityUtils.getCurrentUsername());
            nativeQuery.setParameter("moduleCode", DocumentConstant.PROCESS_DEFINITION_KEY_SW_LIST);
            if (StringUtils.isNotEmpty(backlogListBO.getCreateStartTime())) {
                nativeQuery.setParameter("createStartTime", backlogListBO.getCreateStartTime());
                nativeQuery.setParameter("createEndTime", backlogListBO.getCreateEndTime());
            }
            List resultList = nativeQuery.getResultList();
            criteria.setBpmInstanceId(resultList);

            Page<A07DocumentGwView> all = a07DocumentGwViewRepository.findAll((root, query, criteriaBuilder) -> {
                Predicate predicate = QueryHelp.getPredicate(root, criteria, criteriaBuilder);
                if (StringUtils.isNotEmpty(criteria.getLwxh())) {
                    predicate = criteriaBuilder.and(
                            predicate,
                            criteriaBuilder.like(root.get("lwwh"), "%" + criteria.getLwxh() + "%")
                    );
                }
                if (StringUtils.isNotEmpty(criteria.getYear())) {
                    predicate = criteriaBuilder.and(
                            predicate,
                            criteriaBuilder.equal(root.get("gwwh").get("nh"), criteria.getYear())
                    );
                }
                if (StringUtils.isNotEmpty(criteria.getDz())) {
                    predicate = criteriaBuilder.and(
                            predicate,
                            criteriaBuilder.like(root.get("gwwh").get("dz"), "%" + criteria.getDz() + "%")
                    );
                }
                if (StringUtils.isNotEmpty(criteria.getSwxh())) {
                    predicate = criteriaBuilder.and(
                            predicate,
                            criteriaBuilder.equal(root.get("gwwh").get("xh"), criteria.getSwxh())
                    );
                }
                //中间存在空格，代表分词查询
                if (StringUtils.isNotEmpty(criteria.getBt()) && criteria.getBt().contains(" ")) {
                    String[] bts = criteria.getBt().split(" ");
                    criteria.setBt(null);

                    ArrayList<Predicate> orList = new ArrayList<>();
                    Arrays.stream(bts).forEach(bt -> {
                        if (StringUtils.isNotEmpty(bt)) {
                            orList.add(criteriaBuilder.or(criteriaBuilder.like(root.get("bt"), "%" + bt + "%")));
                        }
                    });
                    Predicate or = criteriaBuilder.or(orList.toArray(new Predicate[orList.size()]));
                    predicate = criteriaBuilder.and(predicate, or);
                }
                return predicate;
            }, pageable);
            List<A07DocumentGwDto> collect = all.stream().map(a07DocumentGwView -> {
                A07DocumentGwDto documentGwDto = new A07DocumentGwDto();
                BeanUtils.copyProperties(a07DocumentGwView, documentGwDto);
                Map backlog = (Map) em.createNativeQuery("select id,user_id from wd_backlog where biz_id=?1")
                        .setParameter(1, a07DocumentGwView.getBpmInstanceId())
                        .unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).getSingleResult();
                String cjr = sysUserService.findByName(backlog.get("user_id").toString()).getNickName();
                documentGwDto.setCjr(cjr);
                List<String> usernames = em.createNativeQuery("select transactor_id from wd_backlog_transactor where backlog_id =?1 and cc!=1").setParameter(1, backlog.get("id")).getResultList();
                String blr = sysUserService.findByUserNames(usernames).stream().map(UserDto::getNickName).collect(Collectors.joining(","));
                documentGwDto.setBlr(blr);
                WorkflowType workflowType = WorkflowType.getByValue(a07DocumentGwView.getModuleType());
                documentGwDto.setModuleType(workflowType == null ? null : workflowType.getName());

                Map singleResult = (Map) em.createNativeQuery("select end_read_time, transaction_time from wd_backlog_already_record where backlog_id=?1 and transactor_id =?2 order by transaction_time desc limit 1")
                        .setParameter(1, backlog.get("id"))
                        .setParameter(2, SecurityUtils.getCurrentUsername())
                        .unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
                        .getSingleResult();
                documentGwDto.setEndReadTime((Date) singleResult.get("end_read_time"));
                documentGwDto.setTransactionTime((Date) singleResult.get("transaction_time"));
                // 流程状态
                ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(a07DocumentGwView.getBpmStatus());
                documentGwDto.setBpmStatus(procStatusEnum == null ? null : procStatusEnum.getName());

                return documentGwDto;
            }).collect(Collectors.toList());
            Map<String, Object> returnMap = new LinkedHashMap<>(2);
            returnMap.put("content", collect);
            returnMap.put("totalElements", all.getTotalElements());
            return returnMap;
        }


        //查询待办服务
        BacklogListPageDto backlogListPageDto = wdBacklogService.getBacklogList(backlogListBO);
        List<BacklogListDto> backlogListDtoList = backlogListPageDto.getContent();
        if (CollectionUtils.isEmpty(backlogListDtoList)) {
            return PageUtil.toPage(Page.empty());
        }

        //获取流程实例id
        Map<String, Date> bpmInstanceIdMap = new LinkedHashMap<>();
        for (BacklogListDto backlogListDto : backlogListDtoList) {
            bpmInstanceIdMap.put(backlogListDto.getBizId(), backlogListDto.getCreateDate());
        }
        criteria.setBpmInstanceId(Lists.newArrayList(bpmInstanceIdMap.keySet()));
        //查询业务视图表信息
        List<A07DocumentGwView> viewList = a07DocumentGwViewRepository.findAll((root, criteriaQuery, criteriaBuilder) -> {
            //中间存在空格，代表分词查询
            if (StringUtils.isNotEmpty(criteria.getBt()) && criteria.getBt().contains(" ")) {
                String[] bts = criteria.getBt().split(" ");
                criteria.setBt(null);
                Predicate predicate = QueryHelp.getPredicate(root, criteria, criteriaBuilder);
                ArrayList<Predicate> orList = new ArrayList<>();
                Arrays.stream(bts).forEach(bt -> {
                    if (StringUtils.isNotEmpty(bt)) {
                        orList.add(criteriaBuilder.or(criteriaBuilder.like(root.get("bt"), "%" + bt + "%")));
                    }
                });
                Predicate or = criteriaBuilder.or(orList.toArray(new Predicate[orList.size()]));
                return criteriaBuilder.and(predicate, or);
            }
            return QueryHelp.getPredicate(root, criteria, criteriaBuilder);
        });

        String transactorNames = backlogListDtoList.stream().map(BacklogListDto::getCurrentHandler).collect(Collectors.joining(","));
        //用户名集合放入
        List<String> username = viewList.stream().map(A07DocumentGwView::getCjr).collect(Collectors.toList());
        //办理人放入用户名集合
        username.addAll(Arrays.asList(transactorNames.split(",")));

        //查询用户信息
        List<User> userList = userRepository.findByUsernameIn(username);
        Map<String, User> finalUserMap = userList.stream().collect(Collectors.toMap(User::getUsername, a -> a, (k1, k2) -> k1));

        List<A07DocumentGwDto> returnList = backlogListDtoList.stream().map(p -> {
            A07DocumentGwDto documentGwDto = new A07DocumentGwDto();
            for (A07DocumentGwView a07DocumentGwView : viewList) {
                if (a07DocumentGwView.getBpmInstanceId().equals(p.getBizId())) {
                    BeanUtils.copyProperties(a07DocumentGwView, documentGwDto);
                    if (StringUtils.isNotBlank(p.getCurrentHandler())) {
                        //当前办理人不为空根据username设置中文
                        String[] handlerArr = p.getCurrentHandler().split(",");
                        String handler = "";
                        for (int i = 0; i < handlerArr.length; i++) {
                            if (finalUserMap.get(handlerArr[i]) == null) {
                                continue;
                            }
                            handler += finalUserMap.get(handlerArr[i]).getNickName() + ",";
                        }
                        if (StringUtils.isNotBlank(handler)) {
                            handler = handler.substring(0, handler.length() - 1);
                        }
                        documentGwDto.setBlr(handler);
                    }
                    User cjrUser = finalUserMap.get(a07DocumentGwView.getCjr());
                    documentGwDto.setCjr(cjrUser == null ? null : cjrUser.getNickName());
                    WorkflowType workflowType = WorkflowType.getByValue(a07DocumentGwView.getModuleType());
                    documentGwDto.setModuleType(workflowType == null ? null : workflowType.getName());
                    documentGwDto.setUrgent(p.getUrgent());
                    /*documentGwDto.setExpiredStatus(p.getExpiredStatus());*/
                    if (Objects.nonNull(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()))) {
                        documentGwDto.setCreateTime(new Timestamp(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()).getTime()));
                    }
                    if (StringUtils.isBlank(documentGwDto.getBt())) {
                        documentGwDto.setBt(p.getTitle());
                    }
                    documentGwDto.setEndReadTime(p.getEndReadTime());
                    // 流程状态
                    ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(a07DocumentGwView.getBpmStatus());
                    documentGwDto.setBpmStatus(procStatusEnum == null ? null : procStatusEnum.getName());

                    documentGwDto.setTransactionTime(p.getTransactionDate());
                    return documentGwDto;
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, Object> returnMap = new LinkedHashMap<>(2);
        returnMap.put("content", returnList);
        returnMap.put("totalElements", backlogListPageDto.getTotalElements());
        return returnMap;
    }

    @Override
    public Map<String, Object> getAllSwList(A07AllSwQueryCriteria criteria, Pageable pageable) {
        List<String> procInstIds = new ArrayList<>();
        if (StringUtils.isBlank(criteria.getBelongToDept()) && !SecurityUtils.getCurrentUsername().equals("superAdmin")) {
            criteria.setUsername(SecurityUtils.getCurrentUsername());
            procInstIds = historyService.createHistoricTaskInstanceQuery()
                    .taskAssignee(criteria.getUsername())
                    .list().stream().map(HistoricTaskInstance::getProcessInstanceId).collect(Collectors.toList());
            if (procInstIds.isEmpty()) {
                return PageUtil.toPage(Page.empty());
            }
        }
        if (StringUtils.isNotEmpty(criteria.getCurrentAssignee())) {
            procInstIds = taskService.createTaskQuery()
                    .taskAssignee(criteria.getCurrentAssignee())
                    .list().stream().map(Task::getProcessInstanceId).collect(Collectors.toList());
            if (procInstIds.isEmpty()) {
                return PageUtil.toPage(Page.empty());
            }
        }
//        Page<A07AllSwDto> pageDto = documentFlowTaskService.queryAllSwList(criteria, pageable);
//        // 适配业务属性
//        pageDto.getContent().forEach(dto -> {
//            ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(dto.getBpmStatus());
//            dto.setBpmStatus(procStatusEnum == null ? null : procStatusEnum.getName());
//
//            WorkflowType workflowType = WorkflowType.getByValue(dto.getBpmProcessKey());
//            dto.setModuleType(workflowType == null ? null : workflowType.getName());
//
//        });
//        return PageUtil.toPage(pageDto.getContent(), pageDto.getTotalElements());
        List bpmProcessKeys = em.createNativeQuery("SELECT DISTINCT bpm_process_key FROM sw_all_view").getResultList();
        List<String> finalProcInstIds = procInstIds;
        Page<A07DocumentGwView> all = a07DocumentGwViewRepository.findAll((root, query, criteriaBuilder) -> {
            Predicate predicate = QueryHelp.getPredicate(root, criteria, criteriaBuilder);
            predicate = criteriaBuilder.and(
                    predicate,
                    criteriaBuilder.equal(root.get("gwType"), "0"),
                    root.get("bpmProcessKey").in(bpmProcessKeys)
            );
            if (!finalProcInstIds.isEmpty()) {
                predicate = criteriaBuilder.and(
                        predicate,
                        root.get("bpmInstanceId").in(finalProcInstIds)
                );
            }
            if (StringUtils.isNotBlank(criteria.getBt())) {
                List<Predicate> collect = Arrays.stream(criteria.getBt().split(" ")).map(bt ->
                        criteriaBuilder.like(root.get("bt"), "%" + bt + "%")
                ).collect(Collectors.toList());
                predicate = criteriaBuilder.and(
                        predicate,
                        criteriaBuilder.or(collect.toArray(new Predicate[collect.size()]))
                );
            }
            if (criteria.getTimeRange() != null && !criteria.getTimeRange().isEmpty()) {
                Timestamp beginTime = Timestamp.valueOf(criteria.getTimeRange().get(0) + " 00:00:00");
                Timestamp endTime = Timestamp.valueOf(criteria.getTimeRange().get(1) + " 23:59:59");
                predicate = criteriaBuilder.and(
                        predicate,
                        criteriaBuilder.between(root.get("createTime"), beginTime, endTime)
                );
            }
            return predicate;
        }, pageable);
        List<A07AllSwDto> collect = all.stream().map(a07DocumentGwView -> {
            A07AllSwDto.A07AllSwDtoBuilder builder = A07AllSwDto.builder()
                    .bt(a07DocumentGwView.getBt())
                    .createDate(a07DocumentGwView.getCreateTime())
                    .lwdw(a07DocumentGwView.getLwwh());
            if (a07DocumentGwView.getGwwh() != null) {
                builder.swbhStr(a07DocumentGwView.getGwwh().toString());
            }
            WorkflowType workflowType = WorkflowType.getByValue(a07DocumentGwView.getModuleType());
            if (workflowType != null) {
                builder.moduleType(workflowType.getName());
            }
            if (StringUtils.isNotBlank(a07DocumentGwView.getCreateBy())) {
                UserDto userDto = sysUserService.findByName(a07DocumentGwView.getCreateBy());
                if (userDto != null) {
                    builder.createBy(userDto.getNickName());
                }
            }
            TaskQuery taskQuery = taskService.createTaskQuery();
            if (StringUtils.isNotBlank(criteria.getUsername())) {
                taskQuery.taskAssignee(criteria.getUsername());
            }
            List<Task> taskList = taskQuery.processInstanceId(a07DocumentGwView.getBpmInstanceId()).list();
            if (!taskList.isEmpty()) {
                String taskName = taskList.stream().map(Task::getName).distinct().collect(Collectors.joining(","));
                String assigneeName = taskList.stream().map(task -> {
                    if (StringUtils.isNotBlank(task.getAssignee())) {
                        UserDto assignee = sysUserService.findByName(task.getAssignee());
                        if (assignee != null) {
                            return assignee.getNickName();
                        }
                        return null;
                    }
                    return null;
                }).filter(Objects::nonNull).collect(Collectors.joining(","));
                builder.taskName(taskName);
                builder.assigneeName(assigneeName);
            }
            A07AllSwDto a07AllSwDto = builder.build();
            a07AllSwDto.setBpmInstanceId(a07DocumentGwView.getBpmInstanceId());
            ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(a07DocumentGwView.getBpmStatus());
            if (procStatusEnum != null) {
                a07AllSwDto.setBpmStatus(procStatusEnum.getValue());
            }
            A07FileArchive byDocId = fileArchiveRepository.findByDocId(a07DocumentGwView.getId());
            if (byDocId != null) {
                a07AllSwDto.setFileStatus(byDocId.getStatus());
            }
            return a07AllSwDto;

        }).collect(Collectors.toList());
        return PageUtil.toPage(collect, all.getTotalElements());
    }

    @Override
    public A07DocumentLdps findByLdpsBpmInstanceId(String bpmInstanceId) {
        A07DocumentLdps a07DocumentLdps = a07DocumentLdpsRepository.findFirstByBpmInstanceId(bpmInstanceId);
        return a07DocumentLdps;
    }

    @Override
    public Map<String, Object> queryPageSw(A07DocumentSwQueryCriteria criteria, Pageable pageable) {
        Page<A07DocumentSw> page = a07DocumentSwRepository.findAll((root, criteriaQuery, cb) -> QueryHelp.getPredicate(root, criteria, cb), pageable);
        return PageUtil.toPage(page.map(a07DocumentSwMapper::toDto));
    }

    @Override
    public Map<String, Object> queryPageFw(A07DocumentFwQueryCriteria criteria, Pageable pageable) {
        Page<A07DocumentFw> page = a07DocumentFwRepository.findAll((root, criteriaQuery, cb) -> QueryHelp.getPredicate(root, criteria, cb), pageable);
        return PageUtil.toPage(page.map(a07DocumentFwMapper::toDto));
    }

    @Override
    public Map<String, Object> queryPageLdps(A07DocumentLdpsQueryCriteria criteria, Pageable pageable) {
        Page<A07DocumentLdps> page = a07DocumentLdpsRepository.findAll((root, criteriaQuery, cb) -> QueryHelp.getPredicate(root, criteria, cb), pageable);
        return PageUtil.toPage(page.map(a07DocumentLdpsMapper::toDto));
    }

    @Override
    public Map<String, Object> queryPageTncy(A07DocumentClqpQueryCriteria criteria, Pageable pageable) {
        Page<A07DocumentClqp> page = a07DocumentClqpRepository.findAll((root, criteriaQuery, cb) -> QueryHelp.getPredicate(root, criteria, cb), pageable);
        Map<String, Object> map = PageUtil.toPage(page.map(item -> {
            A07DocumentClqpDto dto = a07DocumentClqpMapper.toDto(item);
            UserDto user = sysUserService.findByName(dto.getCreateBy());
            dto.setCreateBy(user.getNickName());
            return dto;
        }));
        return map;
    }

    @Override
    public Map<String, Object> queryPageHq(A07DocumentHqQueryCriteria criteria, Pageable pageable) {
        Page<A07DocumentHq> page = a07DocumentHqRepository.findAll((root, criteriaQuery, cb) -> QueryHelp.getPredicate(root, criteria, cb), pageable);
        return PageUtil.toPage(page.map(a07DocumentHqMapper::toDto));
    }

    /**
     * 查询我的不同状态的流程实例id
     *
     * @param status
     * @param processDefinitionKey
     * @param pageable
     * @return
     */
    private Map<String, FlowTaskInfoDto> getProcessInstanceIdMap(Integer status, String processDefinitionKey, Pageable pageable, A07SearchDtoCriteria searchDto) {
        Map<String, List<FlowTaskInfoDto>> result;
        Map<String, FlowTaskInfoDto> returnResult = new HashMap<>();
        if (DocumentConstant.QUERY_STATUS_DB == status || DocumentConstant.QUERY_STATUS_DY == status) {
            //待办任务列表
            TaskQuery taskQuery = taskService.createTaskQuery()
                    .active()
                    .processDefinitionKey(processDefinitionKey)
                    .taskAssignee(SecurityUtils.getCurrentUsername()) // userName关联处理人
                    .includeProcessVariables()
                    .orderByTaskCreateTime().desc();
            if (StringUtils.isNotEmpty(searchDto.getBt())) {
                taskQuery.processVariableValueLikeIgnoreCase(ProcessConstants.BPM_FORM_TITLE, searchDto.getBt() + "%");
            }
            if (StringUtils.isNotEmpty(searchDto.getSwlx())) {
                if (DocumentConstant.QUERY_STATUS_DY == status) {
                    taskQuery.processVariableValueEquals(ProcessConstants.BPM_FORM_DOCUMENT_REVIEW, searchDto.getSwlx());
                } else {
                    taskQuery.processVariableValueNotEquals(ProcessConstants.BPM_FORM_DOCUMENT_REVIEW, searchDto.getSwlx());
                }
            }

            List<Task> taskList = taskQuery.listPage(pageable.getPageNumber() * pageable.getPageSize(), pageable.getPageSize());
            if (CollectionUtils.isEmpty(taskList)) {
                return returnResult;
            }
            result = taskList.stream().collect(Collectors.groupingBy(Task::getProcessInstanceId, Collectors.mapping(FlowTaskInfoDto::new, Collectors.toList())));
        } else {
            //根据查询条件改成已办未完结和已办已完结
            ResultJson resultJson = flowTaskService.finishedList(pageable.getPageNumber(), pageable.getPageSize(), processDefinitionKey, searchDto);
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<FlowTaskDto> page = (com.baomidou.mybatisplus.extension.plugins.pagination.Page<FlowTaskDto>) resultJson.getData();
            result = page.getRecords().stream().collect(Collectors.groupingBy(FlowTaskDto::getProcInsId, Collectors.mapping(FlowTaskInfoDto::new, Collectors.toList())));
        }
        for (Map.Entry<String, List<FlowTaskInfoDto>> m : result.entrySet()) {
            returnResult.put(m.getKey(), m.getValue() == null ? null : m.getValue().get(0));
        }
        return returnResult;

    }

    /**
     * 检索条件
     *
     * @param params
     * @return
     */
    private Specification<T> retrieveFilter(A07DocumentRetrieveQueryCriteria params) {
        return (root, query, cb) -> {
            //封装and查询条件
            ArrayList<Predicate> andList = new ArrayList<>();
            if (StringUtils.isNotBlank(params.getBt())) {
                //标题不为空
                andList.add(cb.like(root.get("bt"), "%" + params.getBt() + "%"));
            }
            if (StringUtils.isNotBlank(params.getBpmStatus())) {
                //流程状态不为空
                andList.add(cb.equal(root.get("bpmStatus"), params.getBpmStatus()));
            }
            if (params.getMyParticipate() != null && params.getMyParticipate()) {
                //我参与的
                andList.add(cb.isMember(cb.literal(SecurityUtils.getCurrentUsername()), root.<Collection<String>>get("participateUser")));//存入条件集合里
            }
            if (params.getTimeType() != null && params.getTimeType() != DocumentConstant.RETRIEVE_DATE_BX) {
                Date date = new Date();
                if (DocumentConstant.RETRIEVE_DATE_24H == params.getTimeType()) {
                    //24小时内
                    andList.add(cb.between(root.get("createTime").as(Timestamp.class), DateUtil.offsetDay(date, -1), date));
                } else if (DocumentConstant.RETRIEVE_DATE_3DAY == params.getTimeType()) {
                    //3天内
                    andList.add(cb.between(root.get("createTime").as(Timestamp.class), DateUtil.offsetDay(date, -3), date));
                } else if (DocumentConstant.RETRIEVE_DATE_1M == params.getTimeType()) {
                    //1个月内
                    andList.add(cb.between(root.get("createTime").as(Timestamp.class), DateUtil.offsetDay(date, -31), date));
                } else if (DocumentConstant.RETRIEVE_DATE_CUSTOM == params.getTimeType()) {
                    //自定义时间
                    andList.add(cb.between(root.get("createTime").as(Timestamp.class), DateUtil.parse(params.getStartTime(), "yyyy-MM-dd"), DateUtil.parse(params.getEndTime(), "yyyy-MM-dd")));
                }
            }
            if (StringUtils.isNotBlank(params.getDz()) || params.getNh() != null
                    || params.getXh() != null) {
                if (StringUtils.isNotBlank(params.getDz())) {
                    setWh(root, cb, params, andList, "dz");
                }
                if (params.getNh() != null) {
                    setWh(root, cb, params, andList, "nh");
                }
                if (params.getXh() != null) {
                    setWh(root, cb, params, andList, "xh");
                }
            }
            return cb.and(andList.toArray(new Predicate[andList.size()]));
        };
    }

    /**
     * @param root
     * @param cb
     * @param params
     * @param andList
     * @param name
     */
    private void setWh(Root<T> root, CriteriaBuilder cb, A07DocumentRetrieveQueryCriteria params, ArrayList<Predicate> andList, String name) {
        if (params.getGwglType() == null) {
            return;
        }
        ArrayList<Predicate> orList = new ArrayList<>();
        if (0 == params.getGwglType() || 1 == params.getGwglType() || 2 == params.getGwglType()) {
            //0:发文 1:会签 2:领导批示
            Join<T, ReferenceNumber> gwwh = root.join("gwwh", JoinType.LEFT);
            if ("dz".equals(name)) {
                //文号-代字不为空
                orList.add(cb.equal(gwwh.get("dz"), params.getDz()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            } else if ("nh".equals(name)) {
                //文号-年号不为空
                orList.add(cb.equal(gwwh.get("nh"), params.getNh()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            } else if ("xh".equals(name)) {
                //文号-年号不为空
                orList.add(cb.equal(gwwh.get("xh"), params.getXh()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }
        } else if (3 == params.getGwglType()) {
            //3:收文
            Join<T, ReferenceNumber> swbh = root.join("swbh", JoinType.LEFT);
            Join<T, ReferenceNumber> lwbh = root.join("lwbh", JoinType.LEFT);
            if ("dz".equals(name)) {
                //文号-代字不为空
                orList.add(cb.equal(swbh.get("dz"), params.getDz()));
                orList.add(cb.equal(lwbh.get("dz"), params.getDz()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            } else if ("nh".equals(name)) {
                //文号-年号不为空
                orList.add(cb.equal(swbh.get("nh"), params.getNh()));
                orList.add(cb.equal(lwbh.get("nh"), params.getNh()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            } else if ("xh".equals(name)) {
                //文号-年号不为空
                orList.add(cb.equal(swbh.get("xh"), params.getXh()));
                orList.add(cb.equal(lwbh.get("xh"), params.getXh()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }
        }
    }


}
