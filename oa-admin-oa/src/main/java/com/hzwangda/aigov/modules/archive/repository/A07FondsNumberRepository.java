package com.hzwangda.aigov.modules.archive.repository;

import com.hzwangda.aigov.modules.archive.entity.A07FondsNumber;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface A07FondsNumberRepository extends JpaRepository<A07FondsNumber, String> {

    @Query("select code from A07FondsNumber where belongToDept=?1")
    String findCodeByBelongToDept(String belongToDept);
}
