package com.hzwangda.aigov.modules.addresslist.service.impl;

import com.hzwangda.aigov.modules.addresslist.domain.dto.UnitAddressListGroupDto;
import com.hzwangda.aigov.modules.addresslist.domain.entity.UnitAddressList;
import com.hzwangda.aigov.modules.addresslist.domain.entity.UnitAddressListGroup;
import com.hzwangda.aigov.modules.addresslist.mapstruct.UnitAddressListGroupMapper;
import com.hzwangda.aigov.modules.addresslist.repository.UnitAddressListGroupRepository;
import com.hzwangda.aigov.modules.addresslist.repository.UnitAddressListRepository;
import com.hzwangda.aigov.modules.addresslist.service.UnitAddressListGroupService;
import com.wangda.oa.config.ElPermissionConfig;
import com.wangda.oa.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: zhangzhanlong
 * @date: 2022/8/5 13:23
 * @description: 单位通讯录分组
 */
@Service
@RequiredArgsConstructor
public class UnitAddressListGroupServiceImpl implements UnitAddressListGroupService {
    private final UnitAddressListGroupRepository groupRepository;
    private final UnitAddressListRepository addressListRepository;
    private final UnitAddressListGroupMapper groupMapper;
    private final ElPermissionConfig elPermissionConfig;

    @Override
    public Object queryList(String groupName) {
        Specification<UnitAddressListGroup> sp = (root, criteriaQuery, cb) -> {
            Predicate pe = null;
            if (!elPermissionConfig.check("unitAddress:admin")) {
                pe = cb.equal(root.get("belongToDept"), SecurityUtils.getCurrentBelongToDept());
            }
            if(StringUtils.isNotBlank(groupName)) {
                if (pe!=null) {
                    pe = cb.and(pe, cb.like(root.get("groupName"), "%" + groupName + "%"));
                } else {
                    pe = cb.like(root.get("groupName"), "%" + groupName + "%");
                }
            }
            criteriaQuery.orderBy(
                    cb.asc(root.get("createTime")),
                    cb.asc(root.get("sort"))
            );
            return pe;
        };
        List<UnitAddressListGroup> groupList = groupRepository.findAll(sp);
        List<UnitAddressListGroupDto> groupListDto = groupList.stream().map(groupMapper::toDto).collect(Collectors.toList());
        return groupListDto;
    }

    @Override
    public Object queryOne(Long id) {
        UnitAddressListGroup group = groupRepository.findById(id).orElseGet(UnitAddressListGroup::new);
        return group;
    }

    @Override
    public Object save(UnitAddressListGroup group) {
        if(group.getId() == null) {
            group.setGroupUsername(SecurityUtils.getCurrentUsername());
        }
        UnitAddressListGroup save = groupRepository.save(group);
        return save;
    }

    @Override
    public Object del(Long id) {
        groupRepository.deleteById(id);
        List<UnitAddressList> addressLists = addressListRepository.findByGroupId(id);
        addressListRepository.deleteAll(addressLists);
        return "删除成功";
    }
}
