package com.hzwangda.aigov.modules.collaboration.domain.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @program: oa-mgt-server
 * @description: 工作协同      删除用
 * @author: liux
 * @create: 2021-08-11 18:09
 */
@Entity
@Getter
@Setter
@Table(name = "a10_collaboration_info")
@ApiModel(value = "工作协同")
public class A10CollaborationInfoCopy extends BaseEntity {

    @Id
    @NotNull(groups = {Update.class})
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A10CollaborationInfo.fj'")
    private List<StorageBiz> fj;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "info_id")
    @ApiModelProperty(value = "操作记录")
    private List<A10CollaborationHandleLog> handleLogs;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "info_id")
    @ApiModelProperty(value = "接收用户")
    private List<A10CollaborationAssignee> assignees;

    @OneToOne(mappedBy = "a10CollaborationInfo")
    private A10CollaborationLink link;

    public void copy(A10CollaborationInfoCopy a10CollaborationInfo) {
        BeanUtil.copyProperties(a10CollaborationInfo, this, CopyOptions.create().setIgnoreNullValue(true));
    }

    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }
}
