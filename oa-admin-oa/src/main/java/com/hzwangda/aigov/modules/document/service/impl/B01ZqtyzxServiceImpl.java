package com.hzwangda.aigov.modules.document.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.StyleSet;
import com.hzwangda.aigov.modules.document.dto.B01ZqtyzxQueryCriteria;
import com.hzwangda.aigov.modules.document.entity.B01Zqtyzx;
import com.hzwangda.aigov.modules.document.repository.B01ZqtyzxRepository;
import com.hzwangda.aigov.modules.document.service.B01ZqtyzxService;
import com.wangda.oa.modules.system.domain.DictDetail;
import com.wangda.oa.modules.system.repository.DictDetailRepository;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class B01ZqtyzxServiceImpl implements B01ZqtyzxService {
    private final B01ZqtyzxRepository zqtyzxRepository;
    private final DictDetailRepository dictDetailRepository;

    @Override
    public Map<String, Object> getAllZqtyzxList(B01ZqtyzxQueryCriteria criteria, Pageable pageable) {
        if(StringUtils.isBlank(criteria.getBelongToDept()) && !SecurityUtils.getCurrentUsername().equals("superAdmin")) {
            criteria.setCreateBy(SecurityUtils.getCurrentUsername());
        }
        Specification<B01Zqtyzx> sp = (root, query, cb) -> {
            Predicate predicate = QueryHelp.getPredicate(root, criteria, cb);
            if(CollUtil.isNotEmpty(criteria.getTimeRange())) {
                predicate = cb.and(predicate, cb.between(root.get("dcrq"), DateUtil.parse(criteria.getTimeRange().get(0)), DateUtil.parseDate(criteria.getTimeRange().get(1))));
            }
            return predicate;
        };
        Page<B01Zqtyzx> all = zqtyzxRepository.findAll(sp, pageable);
        return PageUtil.toPage(all);
    }

    @Override
    public Object queryAllBq() {
        List<Map<String, Object>> list = new ArrayList<>();
        List<DictDetail> npArchive = dictDetailRepository.findByDictName("lhrmyy_bq");
        npArchive.forEach(dictDetail -> {
            Map<String, Object> result = new HashMap<>();
            result.put("label", dictDetail.getLabel());
            result.put("value", dictDetail.getValue());
            list.add(result);
        });
        return list;
    }

    @Override
    public Object queryAllFzr() {
        List<Map<String, Object>> list = new ArrayList<>();
        List<DictDetail> npArchive = dictDetailRepository.findByDictName("lhrmyy_fzr");
        npArchive.forEach(dictDetail -> {
            Map<String, Object> result = new HashMap<>();
            result.put("label", dictDetail.getLabel());
            result.put("value", dictDetail.getValue());
            list.add(result);
        });
        return list;
    }

    @Override
    public Object queryAllKz() {
        List<Map<String, Object>> list = new ArrayList<>();
        List<DictDetail> npArchive = dictDetailRepository.findByDictName("lhrmyy_kz");
        npArchive.forEach(dictDetail -> {
            Map<String, Object> result = new HashMap<>();
            result.put("label", dictDetail.getLabel());
            result.put("value", dictDetail.getValue());
            list.add(result);
        });
        return list;
    }

    @Override
    public void exportZqtyzx(HttpServletResponse response, B01ZqtyzxQueryCriteria criteria) {
        if(StringUtils.isBlank(criteria.getBelongToDept()) && !SecurityUtils.getCurrentUsername().equals("superAdmin")) {
            criteria.setCreateBy(SecurityUtils.getCurrentUsername());
        }
        Specification<B01Zqtyzx> sp = (root, query, cb) -> {
            Predicate predicate = QueryHelp.getPredicate(root, criteria, cb);
            if(CollUtil.isNotEmpty(criteria.getTimeRange())) {
                predicate = cb.and(predicate, cb.between(root.get("dcrq"), DateUtil.parse(criteria.getTimeRange().get(0)), DateUtil.offsetDay(DateUtil.parseDate(criteria.getTimeRange().get(1)), 1)));
            }
            query.orderBy(cb.desc(root.get("kz")));
            return predicate;
        };
        List<B01Zqtyzx> all = zqtyzxRepository.findAll(sp);

        int a = 1;
        // 数据分组
        Map<String, Map<Date, List<B01Zqtyzx>>> collect = all.stream()
                .collect(Collectors.groupingBy(B01Zqtyzx::getBq,
                        Collectors.groupingBy(B01Zqtyzx::getDcrq)));
        // 增加序号数据处理
        Map<String, Map<String, List<Map<String, Object>>>> groupedByBqAndDcrq = new LinkedHashMap<>();
        for(Map.Entry<String, Map<Date, List<B01Zqtyzx>>> mapEntry : collect.entrySet()) {
            String bq = mapEntry.getKey();
            Map<Date, List<B01Zqtyzx>> innerMap = mapEntry.getValue();
            Map<String, List<Map<String, Object>>> resultMap = new TreeMap<>();
            for(Map.Entry<Date, List<B01Zqtyzx>> innerEntry : innerMap.entrySet()) {
                String dcrq = DateUtil.formatDate(innerEntry.getKey());
                List<B01Zqtyzx> list = innerEntry.getValue();
                List<Map<String, Object>> resultList = new ArrayList<>();
                for(B01Zqtyzx zqtyzx : list) {
                    Map<String, Object> responseMap = new LinkedHashMap<>();
                    responseMap.put("序号", String.valueOf(a++));
                    responseMap.put("科组", zqtyzx.getKz());
                    responseMap.put("住院号", zqtyzx.getZyh());
                    responseMap.put("存在问题", zqtyzx.getCzwt());
                    responseMap.put("负责人", zqtyzx.getFzr());
                    responseMap.put("同意书份数", zqtyzx.getTysfs());
                    resultList.add(responseMap);
                }
                a = 1;
                resultMap.put(dcrq, resultList);
            }
            groupedByBqAndDcrq.put(bq, resultMap);
        }
        // 处理汇总数据
        Map<String, List<Map<String, Object>>> allMap = new TreeMap<>();
        List<Map<String, Object>> resultList = new ArrayList<>();
        all.stream().collect(Collectors.groupingBy(B01Zqtyzx::getKz)).forEach((kz, list) -> {
           String value="";
            for(B01Zqtyzx zqtyzx : list){
                if(StringUtils.isNotEmpty(zqtyzx.getCzwt()) ){
                    value+= zqtyzx.getZyh() + zqtyzx.getCzwt() + "\n";
                }
            }
            Map<String, Object> responseMap = new LinkedHashMap<>();
            responseMap.put("科组", kz);
            responseMap.put("检查内容", value);
            responseMap.put("检查病例数", "");
            responseMap.put("检查同意书数量", "");
            responseMap.put("及时率", "");
            responseMap.put("完整率", "");
            responseMap.put("单项否决病历份数", "");
            resultList.add(responseMap);
        });
        allMap.put("all", resultList);
        groupedByBqAndDcrq.put("全部", allMap);

        try {
            downloadExcel(groupedByBqAndDcrq, response);
        }catch(IOException e) {
            throw new RuntimeException(e);
        }
    }

    public void downloadExcel(Map<String, Map<String, List<Map<String, Object>>>> map, HttpServletResponse response) throws IOException {
        String tempPath = System.getProperty("java.io.tmpdir") + File.separator + IdUtil.fastSimpleUUID() + ".xlsx";
        File file = new File(tempPath);
        BigExcelWriter writer = ExcelUtil.getBigWriter(file);
        for(Map.Entry<String, Map<String, List<Map<String, Object>>>> entry : map.entrySet()) {
            String key = entry.getKey();
            if(key.equals("全部")) {
                // 处理全部汇总数据
                getAll(writer, entry);
            }else {
                // 处理根据病区科组sheet
                getBqKz(writer, entry);
            }
        }
        //删除sheet1，因为sheet1为空
        writer.getWorkbook().removeSheetAt(0);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=file.xlsx");
        ServletOutputStream out = response.getOutputStream();
        // 终止后删除临时文件
        file.deleteOnExit();
        writer.flush(out, true);
        //此处记得关闭输出Servlet流
        IoUtil.close(out);
    }

    private void getAll(BigExcelWriter writer, Map.Entry<String, Map<String, List<Map<String, Object>>>> entry) {
        String key = entry.getKey();
        writer.setSheet(key);// 设置sheet名称
        // 在每个列表上方增加一行单独的文字
        List<Object> headerRow = Arrays.asList("科室知情同意执行情况汇总"); // 根据需要修改
        writer.writeRow(headerRow, true); // true 表示输出标题
        // 获取当前工作表对象
        SXSSFSheet currentSheet = (SXSSFSheet) writer.getSheet();
        // 合并前五列的单元格
        int startRow = writer.getRowCount() - 1;
        int endRow = writer.getRowCount() - 1;
        int startCol = 0;
        int endCol = 6;
        currentSheet.addMergedRegion(new CellRangeAddress(startRow, endRow, startCol, endCol));
        List<Integer> list = new ArrayList<>();
        Map<String, List<Map<String, Object>>> mapDate = entry.getValue();
        for(Map.Entry<String, List<Map<String, Object>>> mapEntry : mapDate.entrySet()) {
            List<Map<String, Object>> value = mapEntry.getValue();
            // 一次性写出内容，使用默认样式，强制输出标题
            writer.write(value, true);
            list.add(writer.getRowCount());
        }
        // 定义单元格样式
        //设置边框
        StyleSet style = writer.getStyleSet();
        style.setBorder(BorderStyle.THIN, IndexedColors.BLACK);
        Cell cell = writer.getCell(0, 0);
        CellStyle cellStyle = writer.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cell.setCellStyle(cellStyle);

        //上面需要强转SXSSFSheet  不然没有trackAllColumnsForAutoSizing方法
        currentSheet.trackAllColumnsForAutoSizing();
        //列宽自适应
        writer.autoSizeColumnAll();

    }

    private void getBqKz(BigExcelWriter writer, Map.Entry<String, Map<String, List<Map<String, Object>>>> entry) {
        String key = entry.getKey();
        writer.setSheet(key);// 设置sheet名称
        Map<String, List<Map<String, Object>>> mapDate = entry.getValue();
        for(Map.Entry<String, List<Map<String, Object>>> mapEntry : mapDate.entrySet()) {
            String dcrq = mapEntry.getKey();
            List<Map<String, Object>> value = mapEntry.getValue();
            // 获取当前工作表对象
            SXSSFSheet currentSheet = (SXSSFSheet) writer.getSheet();
            // 在每个列表上方增加一行单独的文字
            List<Object> headerRow = Arrays.asList(key + "患者知情同意制度执行情况督查反馈表"); // 根据需要修改
            writer.writeRow(headerRow, true); // true 表示输出标题
            List<Object> twoRow = Arrays.asList("督查人：", "费硕莹", "", "", "督查日期：", dcrq); // 根据需要修改
            writer.writeRow(twoRow, true); // true 表示输出标题

            // 合并前五列的单元格
            int startRow = writer.getRowCount() - 2;
            int endRow = writer.getRowCount() - 2;
            int startCol = 0;
            int endCol = 5;
            currentSheet.addMergedRegion(new CellRangeAddress(startRow, endRow, startCol, endCol));
            // 定义单元格样式
            StyleSet style = writer.getStyleSet();
            style.setBorder(BorderStyle.THIN, IndexedColors.BLACK);
            // 一次性写出内容，使用默认样式，强制输出标题
            writer.write(value, true);
            // 在两个列表之间插入五行空行
            writer.passRows(5); // 添加一行空行
            //上面需要强转SXSSFSheet  不然没有trackAllColumnsForAutoSizing方法
            currentSheet.trackAllColumnsForAutoSizing();
            //列宽自适应
            writer.autoSizeColumnAll();
        }
    }

}
