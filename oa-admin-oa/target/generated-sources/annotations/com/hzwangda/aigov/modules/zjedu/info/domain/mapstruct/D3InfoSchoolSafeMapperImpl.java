package com.hzwangda.aigov.modules.zjedu.info.domain.mapstruct;

import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto.SimpleUserDtoBuilder;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class D3InfoSchoolSafeMapperImpl implements D3InfoSchoolSafeMapper {

    @Override
    public SimpleUserDto toDto(User entity) {
        if ( entity == null ) {
            return null;
        }

        SimpleUserDtoBuilder simpleUserDto = SimpleUserDto.builder();

        simpleUserDto.id( entity.getId() );
        simpleUserDto.nickName( entity.getNickName() );

        return simpleUserDto.build();
    }

    @Override
    public List<SimpleUserDto> toDto(List<User> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SimpleUserDto> list = new ArrayList<SimpleUserDto>( entityList.size() );
        for ( User user : entityList ) {
            list.add( toDto( user ) );
        }

        return list;
    }

    @Override
    public User toEntity(SimpleUserDto dto) {
        if ( dto == null ) {
            return null;
        }

        User user = new User();

        user.setId( dto.getId() );
        user.setNickName( dto.getNickName() );

        return user;
    }

    @Override
    public List<User> toEntity(List<SimpleUserDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<User> list = new ArrayList<User>( dtoList.size() );
        for ( SimpleUserDto simpleUserDto : dtoList ) {
            list.add( toEntity( simpleUserDto ) );
        }

        return list;
    }
}
