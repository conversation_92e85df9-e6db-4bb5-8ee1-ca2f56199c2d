package com.hzwangda.aigov.modules.document.service.impl;

import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hzwangda.aigov.modules.document.constant.DocumentConstant;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwDto;
import com.hzwangda.aigov.modules.document.dto.UnitDocCriteria;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwView;
import com.hzwangda.aigov.modules.document.entity.qfb.Z01QfbSw;
import com.hzwangda.aigov.modules.document.repository.A07DocumentGwViewRepository;
import com.hzwangda.aigov.modules.document.repository.qfb.Z01QfbSwRepository;
import com.hzwangda.aigov.modules.document.service.UnitDocService;
import com.hzwangda.aigov.modules.workflow.enums.workflow.WorkflowType;
import com.wangda.oa.backlog.bo.BacklogBusinessSearchBO;
import com.wangda.oa.backlog.bo.BacklogListBO;
import com.wangda.oa.backlog.dto.BacklogListDto;
import com.wangda.oa.backlog.dto.BacklogListPageDto;
import com.wangda.oa.backlog.service.WdBacklogService;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.system.service.DeptUserBindService;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.persistence.criteria.Predicate;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: zhangzhanlong
 * @date: 2022/12/20 15:00
 * @description:
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UnitDocServiceImpl implements UnitDocService {

    private final A07DocumentGwViewRepository a07DocumentGwViewRepository;

    private final UserRepository userRepository;

    private final WdBacklogService wdBacklogService;
    private final DeptUserBindService deptUserBindService;

    private final HistoryService historyService;
    private final Z01QfbSwRepository z01QfbSwRepository;


    @Override
    public Map<String, Object> getUnitDocList(UnitDocCriteria criteria, Pageable pageable) {
        //封装待办查询条件
        BacklogListBO backlogListBO = new BacklogListBO();
        String currentUserBind = deptUserBindService.findByCurrentUserBind();
        backlogListBO.setUserId(currentUserBind);
        backlogListBO.setAppId("OA");
        if (StringUtils.isNotBlank(criteria.getProcessDefinitionKey())) {
            //实例定义不为空
            backlogListBO.setModuleCode(criteria.getProcessDefinitionKey());
        } else {
            //实例定义为空则查询公文管理对应定义
            backlogListBO.setModuleCodes(DocumentConstant.PROCESS_DEFINITION_KEY_SW_LIST);
        }
        List<BacklogBusinessSearchBO> businessSearchBOList = new ArrayList<>();
        if (StringUtils.isNotEmpty(criteria.getYear())) {
            BacklogBusinessSearchBO businessSearchBO = new BacklogBusinessSearchBO();
            businessSearchBO.setType("contains");
            businessSearchBO.setKey("swbh");
            businessSearchBO.setValue("\"nh\":\"" + criteria.getYear() + "\"");
            businessSearchBOList.add(businessSearchBO);
        }
        if (StringUtils.isNotEmpty(criteria.getDz())) {
            BacklogBusinessSearchBO businessSearchBO = new BacklogBusinessSearchBO();
            businessSearchBO.setType("contains");
            businessSearchBO.setKey("lwbh");
            businessSearchBO.setValue("\"dz\":\"" + criteria.getDz() + "\"");
            businessSearchBOList.add(businessSearchBO);
        }
        if (StringUtils.isNotEmpty(criteria.getLwxh())) {
            BacklogBusinessSearchBO businessSearchBO = new BacklogBusinessSearchBO();
            businessSearchBO.setType("contains");
            businessSearchBO.setKey("lwbh");
            businessSearchBO.setValue(criteria.getLwxh());
            businessSearchBOList.add(businessSearchBO);
        }
        if (StringUtils.isNotEmpty(criteria.getSwxh())) {
            BacklogBusinessSearchBO businessSearchBO = new BacklogBusinessSearchBO();
            businessSearchBO.setType("contains");
            businessSearchBO.setKey("swbh");
            businessSearchBO.setValue("\"xh\":\"" + criteria.getSwxh() + "\"");
            businessSearchBOList.add(businessSearchBO);
        }


        if (StringUtils.isNotBlank(criteria.getBt())) {
            backlogListBO.setBt(criteria.getBt());
        }
        if (criteria.getTimeRange() != null && criteria.getTimeRange().size() > 1) {
            backlogListBO.setCreateStartTime(criteria.getTimeRange().get(0));
            backlogListBO.setCreateEndTime(criteria.getTimeRange().get(1));
        }
        backlogListBO.setBusinessSearchBOList(businessSearchBOList);
        backlogListBO.setStatus(criteria.getStatus());
        if (criteria.getStatus() == null) {
            //若不传默认查询所有，则为4
            backlogListBO.setStatus(4);
        }
        backlogListBO.setPage(pageable.getPageNumber());
        backlogListBO.setSize(pageable.getPageSize());

        //查询待办服务
        BacklogListPageDto backlogListPageDto = wdBacklogService.getBacklogList(backlogListBO);
        List<BacklogListDto> backlogListDtoList = backlogListPageDto.getContent();
        if (CollectionUtils.isEmpty(backlogListDtoList)) {
            return PageUtil.toPage(Page.empty());
        }

        //获取流程实例id
        Map<String, Date> bpmInstanceIdMap = new LinkedHashMap<>();
        for (BacklogListDto backlogListDto : backlogListDtoList) {
            bpmInstanceIdMap.put(backlogListDto.getBizId(), backlogListDto.getCreateDate());
        }
        criteria.setBpmInstanceId(Lists.newArrayList(bpmInstanceIdMap.keySet()));
        //查询业务视图表信息
        List<A07DocumentGwView> viewList = a07DocumentGwViewRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));

        String transactorNames = backlogListDtoList.stream().map(BacklogListDto::getCurrentHandler).collect(Collectors.joining(","));
        //用户名集合放入
        List<String> username = viewList.stream().map(A07DocumentGwView::getCjr).collect(Collectors.toList());
        //办理人放入用户名集合
        username.addAll(Arrays.asList(transactorNames.split(",")));

        //查询用户信息
        List<User> userList = userRepository.findByUsernameIn(username);
        Map<String, User> finalUserMap = userList.stream().collect(Collectors.toMap(User::getUsername, a -> a, (k1, k2) -> k1));

        List<A07DocumentGwDto> returnList = backlogListDtoList.stream().map(p -> {
            A07DocumentGwDto documentGwDto = new A07DocumentGwDto();
            for (A07DocumentGwView a07DocumentGwView : viewList) {
                if (a07DocumentGwView.getBpmInstanceId().equals(p.getBizId())) {
                    BeanUtils.copyProperties(a07DocumentGwView, documentGwDto);
                    if (StringUtils.isNotBlank(p.getCurrentHandler())) {
                        //当前办理人不为空根据username设置中文
                        String[] handlerArr = p.getCurrentHandler().split(",");
                        String handler = "";
                        for (int i = 0; i < handlerArr.length; i++) {
                            if (finalUserMap.get(handlerArr[i]) == null) {
                                continue;
                            }
                            handler += finalUserMap.get(handlerArr[i]).getNickName() + ",";
                        }
                        if (StringUtils.isNotBlank(handler)) {
                            handler = handler.substring(0, handler.length() - 1);
                        }
                        documentGwDto.setBlr(handler);
                    }
                    User cjrUser = finalUserMap.get(a07DocumentGwView.getCjr());
                    documentGwDto.setCjr(cjrUser == null ? null : cjrUser.getNickName());
                    WorkflowType workflowType = WorkflowType.getByValue(a07DocumentGwView.getModuleType());
                    documentGwDto.setModuleType(workflowType == null ? null : workflowType.getName());
                    documentGwDto.setBpmStatus(p.getHandleStatus());
                    documentGwDto.setUrgent(p.getUrgent());
                    /*documentGwDto.setExpiredStatus(p.getExpiredStatus());*/
                    if (Objects.nonNull(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()))) {
                        documentGwDto.setCreateTime(new Timestamp(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()).getTime()));
                    }
                    if (StringUtils.isBlank(documentGwDto.getBt())) {
                        documentGwDto.setBt(p.getTitle());
                    }
                    documentGwDto.setEndReadTime(p.getEndReadTime());
                    return documentGwDto;
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, Object> returnMap = new LinkedHashMap<>(2);
        returnMap.put("content", returnList);
        returnMap.put("totalElements", backlogListPageDto.getTotalElements());
        return returnMap;
    }

    /**
     * 转办的区府办收文
     *
     * @param unitDocCriteria
     * @param pageable
     * @return
     */
    @Override
    public Page transferDoc(UnitDocCriteria unitDocCriteria, Pageable pageable) {

        Page<Z01QfbSw> all = z01QfbSwRepository.findAll((root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.equal(root.get("sfzb"), "是");
            if (StringUtils.isNotEmpty(unitDocCriteria.getYear())) {
                DateTime parse = DateUtil.parse(unitDocCriteria.getYear(), "yyyy");
                predicate = criteriaBuilder.and(
                        predicate,
                        criteriaBuilder.between(root.get("swrq"), DateUtil.beginOfYear(parse), DateUtil.endOfYear(parse))
                );
            }
            if (StringUtils.isNotEmpty(unitDocCriteria.getBt())) {
                predicate = criteriaBuilder.and(
                        predicate,
                        criteriaBuilder.like(root.get("bpmSubject"), "%" + unitDocCriteria.getBt() + "%")
                );
            }
            return predicate;
        }, pageable);

        List<JSONObject> collect = all.stream().map(z01QfbSw -> {
            List<HistoricTaskInstance> list = historyService.createHistoricTaskInstanceQuery()
                    .processInstanceId(z01QfbSw.getBpmInstanceId())
                    .taskName("部门镇处理")
                    .list();
            JSONObject json = new JSONObject();
            json.put("bt", z01QfbSw.getBpmSubject());
            json.put("bpmInstanceId", z01QfbSw.getBpmInstanceId());
            json.put("swbh", z01QfbSw.getSwbh().toString());
            json.put("lwwh", z01QfbSw.getLwwh());
            json.put("lwdw", z01QfbSw.getLwdw());
            Set<String> assignees = list.stream().filter(historicTaskInstance -> historicTaskInstance.getEndTime() == null).map(HistoricTaskInstance::getAssignee).collect(Collectors.toSet());
            List<String> unfinished = userRepository.findByEnabledIsTrueAndUsernameIn(assignees).stream().map(User::getNickName).collect(Collectors.toList());
            json.put("unfinished", unfinished);
            assignees = list.stream().filter(historicTaskInstance -> historicTaskInstance.getEndTime() != null).map(HistoricTaskInstance::getAssignee).collect(Collectors.toSet());
            List<String> finished = userRepository.findByEnabledIsTrueAndUsernameIn(assignees).stream().map(User::getNickName).collect(Collectors.toList());
            json.put("finished", finished);
            json.put("zbdh", z01QfbSw.getZbdh().toString());
            Date zbdrq = z01QfbSw.getZbdrq();
            json.put("zbrq", zbdrq);
            if (unfinished.size() > 0 && zbdrq != null) {
                Date dealine = zbdrq;
                String fksjbz = z01QfbSw.getFksjbz();
                if (StringUtils.isNotEmpty(fksjbz)) {
                    String days = "";
                    DateField unit = null;
                    for (int i = 0; i < fksjbz.length(); i++) {
                        char c = fksjbz.charAt(i);
                        List<Character> chars = Arrays.asList('零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十');
                        if (c >= 48 && c <= 57 || chars.contains(c)) {
                            days += c;
                        }
                        if (c == '日' || c == '天') {
                            unit = DateField.DAY_OF_YEAR;
                        } else if (c == '周') {
                            unit = DateField.WEEK_OF_YEAR;
                        } else if (c == '月') {
                            unit = DateField.MONTH;
                        }
                    }
                    if (days.length() > 0 && unit != null) {
                        Integer offset = null;
                        try {
                            offset = Integer.valueOf(days);
                        } catch (NumberFormatException e) {
                            try {
                                offset = NumberChineseFormatter.chineseToNumber(days);
                            } catch (Exception exception) {
                                log.error(exception.getMessage());
                            }
                        }
                        if (offset != null) {
                            dealine = DateUtil.offset(dealine, unit, offset);
                        }
                    }
                }
                Date date = new Date();
                if (dealine.getTime() < date.getTime()) {
                    long l = DateUtil.betweenDay(dealine, date, true);
                    if (l > 0) {
                        json.put("status", "已过" + l + "天");
                        return json;
                    }
                }
            }
            if (!finished.isEmpty()) {
                json.put("status", "已处理");
            }

            return json;
        }).collect(Collectors.toList());
        return new PageImpl(collect, pageable, all.getTotalElements());
    }

}
