/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.bpm.controller;

import com.hzwangda.aigov.bpm.domain.criteria.BpmButtonQueryCriteria;
import com.hzwangda.aigov.bpm.domain.entity.BpmButton;
import com.hzwangda.aigov.bpm.service.BpmButtonService;
import com.wangda.oa.annotation.Log;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2021-05-14
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "BpmButton管理")
@RequestMapping("/api/bpm/bpmButton")
public class BpmButtonController {

    private final BpmButtonService bpmButtonService;

    @GetMapping("/list")
    @Log("查询BpmButton")
    @ApiOperation("查询BpmButton")
    public ResponseEntity<Object> query(BpmButtonQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(bpmButtonService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增或修改BpmButton")
    @ApiOperation("新增或修改BpmButton")
    public ResponseEntity<Object> save(@Validated @RequestBody BpmButton resources) {
        return new ResponseEntity<>(bpmButtonService.createOrUpdate(resources), HttpStatus.CREATED);
    }

    @PostMapping("/delete")
    @Log("删除BpmButton")
    @ApiOperation("删除BpmButton")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        bpmButtonService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
