package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A08GzxtDto;
import com.hzwangda.aigov.modules.document.entity.A08Gzxt;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A08GzxtMapperImpl implements A08GzxtMapper {

    @Autowired
    private A07DocumentMapperUtil a07DocumentMapperUtil;

    @Override
    public List<A08GzxtDto> toDto(List<A08Gzxt> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A08GzxtDto> list = new ArrayList<A08GzxtDto>( entityList.size() );
        for ( A08Gzxt a08Gzxt : entityList ) {
            list.add( toDto( a08Gzxt ) );
        }

        return list;
    }

    @Override
    public A08Gzxt toEntity(A08GzxtDto dto) {
        if ( dto == null ) {
            return null;
        }

        A08Gzxt a08Gzxt = new A08Gzxt();

        a08Gzxt.setBelongToDept( dto.getBelongToDept() );
        a08Gzxt.setBpmInstanceId( dto.getBpmInstanceId() );
        a08Gzxt.setBpmProcessKey( dto.getBpmProcessKey() );
        a08Gzxt.setBpmStatus( dto.getBpmStatus() );
        a08Gzxt.setBpmSubject( dto.getBpmSubject() );
        a08Gzxt.setCreateBy( dto.getCreateBy() );
        a08Gzxt.setCreateTime( dto.getCreateTime() );
        a08Gzxt.setId( dto.getId() );
        List<String> list = dto.getParticipateUser();
        if ( list != null ) {
            a08Gzxt.setParticipateUser( new ArrayList<String>( list ) );
        }
        a08Gzxt.setUpdateBy( dto.getUpdateBy() );
        a08Gzxt.setUpdateTime( dto.getUpdateTime() );
        a08Gzxt.setVersion( dto.getVersion() );
        a08Gzxt.setBt( dto.getBt() );
        a08Gzxt.setFj( a07DocumentMapperUtil.toConvertToId( dto.getFj() ) );
        a08Gzxt.setJsry( dto.getJsry() );
        a08Gzxt.setNr( dto.getNr() );
        a08Gzxt.setYjLz( dto.getYjLz() );

        return a08Gzxt;
    }

    @Override
    public List<A08Gzxt> toEntity(List<A08GzxtDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A08Gzxt> list = new ArrayList<A08Gzxt>( dtoList.size() );
        for ( A08GzxtDto a08GzxtDto : dtoList ) {
            list.add( toEntity( a08GzxtDto ) );
        }

        return list;
    }

    @Override
    public A08GzxtDto toDto(A08Gzxt entity) {
        if ( entity == null ) {
            return null;
        }

        A08GzxtDto a08GzxtDto = new A08GzxtDto();

        a08GzxtDto.setBelongToDept( entity.getBelongToDept() );
        a08GzxtDto.setBpmInstanceId( entity.getBpmInstanceId() );
        a08GzxtDto.setBpmProcessKey( entity.getBpmProcessKey() );
        a08GzxtDto.setBpmStatus( entity.getBpmStatus() );
        a08GzxtDto.setBpmSubject( entity.getBpmSubject() );
        a08GzxtDto.setCreateBy( entity.getCreateBy() );
        a08GzxtDto.setCreateTime( entity.getCreateTime() );
        a08GzxtDto.setId( entity.getId() );
        List<String> list = entity.getParticipateUser();
        if ( list != null ) {
            a08GzxtDto.setParticipateUser( new ArrayList<String>( list ) );
        }
        a08GzxtDto.setUpdateBy( entity.getUpdateBy() );
        a08GzxtDto.setUpdateTime( entity.getUpdateTime() );
        a08GzxtDto.setVersion( entity.getVersion() );
        a08GzxtDto.setBt( entity.getBt() );
        a08GzxtDto.setJsry( entity.getJsry() );
        a08GzxtDto.setNr( entity.getNr() );
        a08GzxtDto.setYjLz( entity.getYjLz() );

        a08GzxtDto.setFj( a07DocumentMapperUtil.toFj(entity.getFj()) );

        splitTime( entity, a08GzxtDto );

        return a08GzxtDto;
    }
}
