package com.hzwangda.aigov.modules.zjedu.meeting.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35MeetingRoomDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35MeetingRoom;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35MeetingRoomApply;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class C35MeetingRoomMapperImpl implements C35MeetingRoomMapper {

    @Override
    public C35MeetingRoomDto toDto(C35MeetingRoom entity) {
        if ( entity == null ) {
            return null;
        }

        C35MeetingRoomDto c35MeetingRoomDto = new C35MeetingRoomDto();

        c35MeetingRoomDto.setCreateBy( entity.getCreateBy() );
        c35MeetingRoomDto.setCreateTime( entity.getCreateTime() );
        c35MeetingRoomDto.setUpdateTime( entity.getUpdateTime() );
        c35MeetingRoomDto.setUpdatedBy( entity.getUpdatedBy() );
        c35MeetingRoomDto.setAddress( entity.getAddress() );
        c35MeetingRoomDto.setId( entity.getId() );
        c35MeetingRoomDto.setIsExamine( entity.getIsExamine() );
        Set<C35MeetingRoomApply> set = entity.getMeetingRoomApplySet();
        if ( set != null ) {
            c35MeetingRoomDto.setMeetingRoomApplySet( new HashSet<C35MeetingRoomApply>( set ) );
        }
        c35MeetingRoomDto.setName( entity.getName() );
        c35MeetingRoomDto.setRoomImage( entity.getRoomImage() );
        c35MeetingRoomDto.setRoomManager( entity.getRoomManager() );
        c35MeetingRoomDto.setRoomNumber( entity.getRoomNumber() );
        c35MeetingRoomDto.setRoomType( entity.getRoomType() );
        c35MeetingRoomDto.setStatus( entity.getStatus() );

        return c35MeetingRoomDto;
    }

    @Override
    public List<C35MeetingRoomDto> toDto(List<C35MeetingRoom> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<C35MeetingRoomDto> list = new ArrayList<C35MeetingRoomDto>( entityList.size() );
        for ( C35MeetingRoom c35MeetingRoom : entityList ) {
            list.add( toDto( c35MeetingRoom ) );
        }

        return list;
    }

    @Override
    public C35MeetingRoom toEntity(C35MeetingRoomDto dto) {
        if ( dto == null ) {
            return null;
        }

        C35MeetingRoom c35MeetingRoom = new C35MeetingRoom();

        c35MeetingRoom.setCreateBy( dto.getCreateBy() );
        c35MeetingRoom.setCreateTime( dto.getCreateTime() );
        c35MeetingRoom.setUpdateTime( dto.getUpdateTime() );
        c35MeetingRoom.setUpdatedBy( dto.getUpdatedBy() );
        c35MeetingRoom.setAddress( dto.getAddress() );
        c35MeetingRoom.setId( dto.getId() );
        c35MeetingRoom.setIsExamine( dto.getIsExamine() );
        Set<C35MeetingRoomApply> set = dto.getMeetingRoomApplySet();
        if ( set != null ) {
            c35MeetingRoom.setMeetingRoomApplySet( new HashSet<C35MeetingRoomApply>( set ) );
        }
        c35MeetingRoom.setName( dto.getName() );
        c35MeetingRoom.setRoomImage( dto.getRoomImage() );
        c35MeetingRoom.setRoomManager( dto.getRoomManager() );
        c35MeetingRoom.setRoomNumber( dto.getRoomNumber() );
        c35MeetingRoom.setRoomType( dto.getRoomType() );
        c35MeetingRoom.setStatus( dto.getStatus() );

        return c35MeetingRoom;
    }

    @Override
    public List<C35MeetingRoom> toEntity(List<C35MeetingRoomDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<C35MeetingRoom> list = new ArrayList<C35MeetingRoom>( dtoList.size() );
        for ( C35MeetingRoomDto c35MeetingRoomDto : dtoList ) {
            list.add( toEntity( c35MeetingRoomDto ) );
        }

        return list;
    }
}
