package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07DocExchangeListSwDto;
import com.hzwangda.aigov.modules.document.entity.A07DocExchangeSw;
import com.wangda.oa.base.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", uses = A07DocExchangeSwMapperUtil.class, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface A07DocExchangeSwMapper extends BaseMapper<A07DocExchangeListSwDto, A07DocExchangeSw> {


    List<A07DocExchangeListSwDto> toListDto(List<A07DocExchangeSw> list);

    @Override
    A07DocExchangeListSwDto toDto(A07DocExchangeSw entity);


}
