package com.hzwangda.aigov.modules.addresslist.repository;


import com.hzwangda.aigov.modules.addresslist.domain.entity.PersonAddressList;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PersonAddressListRepository extends JpaRepository<PersonAddressList, Long>, JpaSpecificationExecutor<PersonAddressList> {
    List<PersonAddressList> findByGroupId(Long id);
}
