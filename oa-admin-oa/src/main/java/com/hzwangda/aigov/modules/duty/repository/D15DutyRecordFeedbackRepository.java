package com.hzwangda.aigov.modules.duty.repository;

import com.hzwangda.aigov.modules.duty.domain.entity.D15DutyRecordFeedBack;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface D15DutyRecordFeedbackRepository extends JpaRepository<D15DutyRecordFeedBack, Long>, JpaSpecificationExecutor<D15DutyRecordFeedBack> {

    List<D15DutyRecordFeedBack> findByNumberOrderByCreateDateDesc(String id);
}
