/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.modules.document.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-07-08
 **/
@Data
public class A07DocumentRetrieveNewQueryCriteria {

    @ApiModelProperty(value = "状态(0:不限,1:24小时内,2:近3天,3:近1个月,10:自定义时间)")
    private Integer timeType;

    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "代字")
    private String dz;

    @ApiModelProperty(value = "年号")
    private Integer nh;

    @ApiModelProperty(value = "序号")
    private Integer xh;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "流程状态(INPROGRESS:流转中,TG:办结)")
    private String bpmStatus;

    @ApiModelProperty(value = "true:本人参与")
    private Boolean myParticipate;

    @ApiModelProperty(value = "公文管理使用。类型。前端无需传值。0:发文,1:会签,2:领导批示,3:收文,其他类型可不传，因为无文号")
    private Integer gwglType;

    @ApiModelProperty(value = "服务事项使用。类型。businessTrip:公差备案,jobEntry:入职管理,jobLeave:调离管理,leaveApproval:因私请假,officeSupplies:物品领用,rentalCars:用车审批,sealApproval:用印审批,travelExpenses:出差报销,workOvertime:加班备案")
    private String fwsxType;
}
