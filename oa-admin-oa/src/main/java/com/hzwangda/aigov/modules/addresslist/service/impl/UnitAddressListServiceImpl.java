package com.hzwangda.aigov.modules.addresslist.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.hzwangda.aigov.modules.addresslist.domain.criteria.UnitAddressListQueryCriteria;
import com.hzwangda.aigov.modules.addresslist.domain.dto.AddressListDto;
import com.hzwangda.aigov.modules.addresslist.domain.dto.UnitAddressListDto;
import com.hzwangda.aigov.modules.addresslist.domain.entity.UnitAddressList;
import com.hzwangda.aigov.modules.addresslist.domain.entity.UnitAddressListGroup;
import com.hzwangda.aigov.modules.addresslist.mapstruct.UnitAddressListMapper;
import com.hzwangda.aigov.modules.addresslist.repository.UnitAddressListGroupRepository;
import com.hzwangda.aigov.modules.addresslist.repository.UnitAddressListRepository;
import com.hzwangda.aigov.modules.addresslist.service.UnitAddressListService;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.config.ElPermissionConfig;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.extension.bo.UserListBO;
import com.wangda.oa.modules.extension.dto.org.UserListDto;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;

/**
 * @author: zhangzhanlong
 * @date: 2022/8/5 13:23
 * @description: 单位通讯录
 */
@Service
@RequiredArgsConstructor
public class UnitAddressListServiceImpl implements UnitAddressListService {
    private final UnitAddressListGroupRepository addressListGroupRepository;
    private final UnitAddressListRepository addressListRepository;
    private final UnitAddressListMapper addressListMapper;
    private final ElPermissionConfig elPermissionConfig;

    @Override
    public Object queryList(UnitAddressListQueryCriteria criteria, Pageable pageable) {
        Specification<UnitAddressList> sp = (root, criteriaQuery, cb) -> {
            Predicate pe = QueryHelp.getPredicate(root, criteria, cb);
            if (!elPermissionConfig.check("unitAddress:admin")) {
                pe = cb.and(pe, cb.equal(root.get("belongToDept"), SecurityUtils.getCurrentBelongToDept()));
            }
            criteriaQuery.orderBy(
                    cb.asc(root.get("sort"))
            );
            return pe;
        };
        Page<UnitAddressList> addressLists = addressListRepository.findAll(sp, pageable);
        return PageUtil.toPage(addressLists.map(addressListMapper::toDto));
    }

    @Override
    public Object queryOne(Long id) {
        UnitAddressList addressList = addressListRepository.findById(id).orElseGet(UnitAddressList::new);
        return addressList;
    }

    @Override
    public Object create(UnitAddressListDto addressListDto) {
        UnitAddressList addressList = addressListMapper.toEntity(addressListDto);
        UnitAddressList save = addressListRepository.save(addressList);
        return save;
    }

    @Override
    public Object update(UnitAddressListDto addressListDto) {
        if(addressListDto.getId() == null) {
            throw new BadRequestException("id为空！");
        }
        UnitAddressList addressList = addressListMapper.toEntity(addressListDto);
        UnitAddressList save = addressListRepository.save(addressList);
        return save;
    }

    @Override
    public Object del(Long id) {
        addressListRepository.deleteById(id);
        return "删除成功";
    }

    @Override
    public Object queryModule(UnitAddressListQueryCriteria criteria) {
        Specification<UnitAddressListGroup> spGroup = (root, criteriaQuery, cb) -> {
            Predicate pe = cb.and(cb.equal(root.get("belongToDept"), SecurityUtils.getDeptCode()));
            criteriaQuery.orderBy(
                    cb.asc(root.get("sort"))
            );
            return pe;
        };

        Specification<UnitAddressList> sp = (root, criteriaQuery, cb) -> {
            Predicate pe = QueryHelp.getPredicate(root, criteria, cb);

            pe = cb.and(pe, cb.equal(root.get("belongToDept"), SecurityUtils.getDeptCode()));
            criteriaQuery.orderBy(
                    cb.asc(root.get("sort"))
            );
            return pe;
        };
        List<UnitAddressListGroup> addressListGroups = addressListGroupRepository.findAll(spGroup);
        List<UnitAddressList> addressLists = addressListRepository.findAll(sp);
        if(CollectionUtil.isEmpty(addressListGroups)) {
            return ResultJson.generateResult();
        }
        return ResultJson.generateResult(build(addressListGroups, addressListGroups.get(0).getId(), addressLists));
    }

    /**
     * 递归查子部门
     * @param groupList
     * @param groupId
     * @param personAddressLists
     * @return
     */
    private List<AddressListDto> build(List<UnitAddressListGroup> groupList, Long groupId, List<UnitAddressList> personAddressLists) {
        List<AddressListDto> list = new ArrayList<>();
        for(UnitAddressListGroup group : groupList) {
            //传入的pid为空则查询pid为空符合,传入的pid不为空则需要pid不为空且相同符合
            Boolean flag = (groupId == null && group.getId() == null) ||
                    (groupId != null && group.getId() != null && group.getId().longValue() == groupId.longValue());

            AddressListDto orgListDto = new AddressListDto();
            orgListDto.setId(group.getId());
            orgListDto.setDeptName(group.getGroupName());
            orgListDto.setNickName(group.getGroupName());
            orgListDto.setSort(group.getSort());
            orgListDto.setType(1);
            orgListDto.setExtId(group.getId().toString());

            Iterator<UnitAddressList> addressListDtoIterator = personAddressLists.iterator();
            while(addressListDtoIterator.hasNext()) {
                UnitAddressList addressListDto = addressListDtoIterator.next();
                if(addressListDto.getGroupId().longValue() == group.getId().longValue()) {
                    AddressListDto listDto = new AddressListDto();
                    listDto.setId(addressListDto.getId());
                    listDto.setDeptName(group.getGroupName());
                    listDto.setNickName(addressListDto.getNickName());
                    listDto.setUserName(addressListDto.getUsername());
                    listDto.setType(0);
                    listDto.setExtId(group.getGroupName() + addressListDto.getId());
                    listDto.setSort(addressListDto.getSort());
                    listDto.setPhone(addressListDto.getPhone());
                    //人员赋值
                    if(orgListDto.getChildren() == null) {
                        orgListDto.setChildren(new ArrayList<>());
                    }
                    orgListDto.getChildren().add(listDto);

                }
            }
            list.add(orgListDto);
        }
        list.sort(Comparator.comparing(AddressListDto::getSort, Comparator.nullsLast(Integer::compareTo)));
        return list;
    }

    @Override
    public ResultJson<List<UserListDto>> getUserList(UserListBO bo) {
        List<UserListDto> userListDtoList = new ArrayList<>();
        Specification<UnitAddressList> sp = (root, criteriaQuery, cb) -> {


            Predicate pe = cb.and(cb.equal(root.get("belongToDept"), SecurityUtils.getDeptCode()));
            if(StringUtils.isNotEmpty(bo.getName())) {
                pe = cb.and(pe, cb.like(root.get("belongToDept"), "%" + bo.getName() + "%"));
            }

            criteriaQuery.orderBy(
                    cb.asc(root.get("sort"))
            );
            return pe;
        };
        List<UnitAddressList> addressLists = addressListRepository.findAll(sp);
        if(!CollectionUtils.isEmpty(addressLists)) {
            for(UnitAddressList user : addressLists) {
                UserListDto userListDto = new UserListDto();
                userListDto.setId(user.getId().toString());
                userListDto.setUserName(user.getUsername());
                userListDto.setNickName(user.getNickName());
                userListDto.setExtId(user.getGroupName() + user.getId());
                userListDto.setType(0);
                userListDtoList.add(userListDto);
            }
        }
        return ResultJson.generateResult(userListDtoList);
    }

}
