package com.hzwangda.aigov.modules.document.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * 信息公开申请
 *
 * <AUTHOR>
 * @date 2021/6/15 上午10:18
 */
@Data
public class A07DocumentXxgksqDto extends BaseBpmDomain implements Serializable {

    @ApiModelProperty(value = "申请人身份")
    private String sqrsf;

    @ApiModelProperty(value = "姓名")
    private String gmXm;

    @ApiModelProperty(value = "工作单位")
    private String gmGzdw;

    @ApiModelProperty(value = "证件名称")
    private String gmZjmc;

    @ApiModelProperty(value = "证件号码")
    private String gmZjhm;

    @ApiModelProperty(value = "通讯地址")
    private String gmTxdz;

    @ApiModelProperty(value = "邮政编码")
    private String gmYzbm;

    @ApiModelProperty(value = "联系电话")
    private String gmLxdh;

    @ApiModelProperty(value = "电子邮箱")
    private String gmDzyx;

    @ApiModelProperty(value = "名称")
    private String frMc;

    @ApiModelProperty(value = "组织机构代码")
    private String frZzjgdm;

    @ApiModelProperty(value = "营业执照信息")
    private String frYyzzxx;

    @ApiModelProperty(value = "法定代表人或负责人")
    private String frFddbrhfzr;

    @ApiModelProperty(value = "联系人姓名")
    private String frLxrxm;

    @ApiModelProperty(value = "联系电话")
    private String frLxdh;

    @ApiModelProperty(value = "电子邮箱")
    private String frDzyx;

    @ApiModelProperty(value = "受理时间")
    @JSONField(format = "yyyy-MM-dd")
    private Date slsj;

    @ApiModelProperty(value = "反馈日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date fkrq;

    @ApiModelProperty(value = "所需信息内容描述")
    private String sxxxnrms;

    @ApiModelProperty(value = "所需信息用途描述")
    private String sxxxytms;

    @ApiModelProperty(value = "是否减免费用")
    private String sfjmfy;

    @ApiModelProperty(value = "所需信息指定提供方式")
    private Set<String> sxxxzdtgfs;

    @ApiModelProperty(value = "获取信息方式")
    private String hqxxfs;

    @ApiModelProperty(value = "接受其他方式")
    private String jsqtfs;

    @ApiModelProperty(value = "公开类型")
    private String gklx;

    @ApiModelProperty(value = "主任拟办意见-json格式")
    private String yjXrnbyj;

    @ApiModelProperty(value = "法规处意见-json格式")
    private String yjFgc;

    @ApiModelProperty(value = "处室办理意见-json格式")
    private String yjCsbl;

    @ApiModelProperty(value = "主任复核意见-json格式")
    private String yjZrfh;

    @ApiModelProperty(value = "流程任务相关信息")
    private FlowTaskInfoDto flowTaskInfoDto;
}
