package com.hzwangda.aigov.modules.document.controller;


import cn.hutool.Hutool;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.document.dto.tzb.A07LdpsReceiveDto;
import com.hzwangda.aigov.modules.document.service.A07TzbLdpsService;
import com.taiyu.typrecenter.util.Md5Util;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.annotation.rest.AnonymousPostMapping;
import io.swagger.annotations.Api;
import liquibase.util.MD5Util;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @author: cy
 * @date: 2023/03/02 16:40
 * @description: 南浔区统战部直通车相关接口  1。文件接收至系统生成对应批示件 2。提供对外查询办件情况
 */

@RestController
@RequestMapping("/api/ldps")
@CrossOrigin
@Api(tags = "南浔区统战部直通车")
public class A07TzbLdpsController {

    @Resource
    private A07TzbLdpsService a07TzbLdpsService;


    @AnonymousPostMapping("/receive")
        public ResultJson<Object> receive(@RequestHeader("token") String token, @RequestParam String dataJson, HttpServletRequest request){
        return a07TzbLdpsService.receive(token,dataJson,request);
    }

    @AnonymousPostMapping("/query")
    public ResultJson<Object> query(@RequestHeader("token") String token, @RequestParam String dataJson, HttpServletRequest request){

        return a07TzbLdpsService.query(token,dataJson, request);
    }

}
