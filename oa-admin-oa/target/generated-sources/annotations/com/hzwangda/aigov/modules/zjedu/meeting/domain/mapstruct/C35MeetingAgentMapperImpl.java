package com.hzwangda.aigov.modules.zjedu.meeting.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35MeetingAgentDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35MeetingAgent;
import com.wangda.oa.modules.system.service.mapstruct.UserMapperUtil;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class C35MeetingAgentMapperImpl implements C35MeetingAgentMapper {

    @Autowired
    private UserMapperUtil userMapperUtil;

    @Override
    public C35MeetingAgentDto toDto(C35MeetingAgent entity) {
        if ( entity == null ) {
            return null;
        }

        C35MeetingAgentDto c35MeetingAgentDto = new C35MeetingAgentDto();

        c35MeetingAgentDto.setAgent( userMapperUtil.toUser( entity.getAgent() ) );
        c35MeetingAgentDto.setId( entity.getId() );
        c35MeetingAgentDto.setMeeting( entity.getMeeting() );
        c35MeetingAgentDto.setOwner( entity.getOwner() );

        return c35MeetingAgentDto;
    }

    @Override
    public List<C35MeetingAgentDto> toDto(List<C35MeetingAgent> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<C35MeetingAgentDto> list = new ArrayList<C35MeetingAgentDto>( entityList.size() );
        for ( C35MeetingAgent c35MeetingAgent : entityList ) {
            list.add( toDto( c35MeetingAgent ) );
        }

        return list;
    }

    @Override
    public C35MeetingAgent toEntity(C35MeetingAgentDto dto) {
        if ( dto == null ) {
            return null;
        }

        C35MeetingAgent c35MeetingAgent = new C35MeetingAgent();

        c35MeetingAgent.setAgent( userMapperUtil.toName( dto.getAgent() ) );
        c35MeetingAgent.setId( dto.getId() );
        c35MeetingAgent.setMeeting( dto.getMeeting() );
        c35MeetingAgent.setOwner( dto.getOwner() );

        return c35MeetingAgent;
    }

    @Override
    public List<C35MeetingAgent> toEntity(List<C35MeetingAgentDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<C35MeetingAgent> list = new ArrayList<C35MeetingAgent>( dtoList.size() );
        for ( C35MeetingAgentDto c35MeetingAgentDto : dtoList ) {
            list.add( toEntity( c35MeetingAgentDto ) );
        }

        return list;
    }
}
