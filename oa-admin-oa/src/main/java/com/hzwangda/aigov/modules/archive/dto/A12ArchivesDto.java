package com.hzwangda.aigov.modules.archive.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/9/7 下午8:02
 **/
@Data
public class A12ArchivesDto {

    private Long id;

    @ApiModelProperty(value = "目录id")
    private Long directoryId;

    @ApiModelProperty(value = "文件id")
    private Long localStorageId;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "生效日期")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date reportDate;

    @ApiModelProperty(value = "序号")
    private Integer sort;

    @ApiModelProperty(value = "标签")
    private String tag;

    @ApiModelProperty(value = "创建时间", hidden = true)
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Timestamp createTime;
}
