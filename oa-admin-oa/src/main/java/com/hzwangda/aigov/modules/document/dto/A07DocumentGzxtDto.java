package com.hzwangda.aigov.modules.document.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * 收文单
 *
 * <AUTHOR>
 * @date 2021/6/21下午8:29
 */
@Data
public class A07DocumentGzxtDto extends BaseBpmDomain {


    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "发文单位")
    private String fwdw;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "发文日期")
    private Timestamp fwrq;

    @ApiModelProperty(value = "公文状态")
    private String gwzt;

    @ApiModelProperty(value = "附件")
    private List<StorageBizDto> fj;
}
