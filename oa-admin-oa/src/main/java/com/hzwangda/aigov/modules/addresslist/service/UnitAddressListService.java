package com.hzwangda.aigov.modules.addresslist.service;

import com.hzwangda.aigov.modules.addresslist.domain.criteria.UnitAddressListQueryCriteria;
import com.hzwangda.aigov.modules.addresslist.domain.dto.UnitAddressListDto;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.extension.bo.UserListBO;
import com.wangda.oa.modules.extension.dto.org.UserListDto;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface UnitAddressListService {
    Object queryList(UnitAddressListQueryCriteria criteria, Pageable pageable);

    Object queryOne(Long id);

    Object create(UnitAddressListDto addressListDto);

    Object update(UnitAddressListDto addressListDto);

    Object del(Long id);

    Object queryModule(UnitAddressListQueryCriteria criteria);

    ResultJson<List<UserListDto>> getUserList(UserListBO bo);
}
