package com.hzwangda.aigov.modules.document.dto;

import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * @author: zhangzhanlong
 * @date: 2022/10/27 14:59
 * @description: 文种维护
 */
@Data
@ApiModel(value = "文种维护")
public class Z08DocumentSwTypeDto extends BaseBpmDomain implements Serializable {

    @ApiModelProperty(value = "文种（代字）")
    private String dz;

    @ApiModelProperty(value = "所属单位")
    private String deptName;

    @ApiModelProperty(value = "所属单位Id")
    private String deptId;

    @ApiModelProperty(value = "排序")
    @Column(name = "sort")
    private Integer sort;
}
