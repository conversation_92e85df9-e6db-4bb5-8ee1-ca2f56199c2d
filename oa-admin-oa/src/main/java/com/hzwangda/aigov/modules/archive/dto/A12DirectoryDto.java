package com.hzwangda.aigov.modules.archive.dto;

import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/8 下午3:54
 **/
@Data
public class A12DirectoryDto extends BaseEntity {
    List<A12DirectoryDto> child;
    @ApiModelProperty(value = "主键id")
    private Long id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "父id")
    private Long pid;
    @ApiModelProperty(value = "部门id(顶级处室使用)")
    private Long deptId;
    @ApiModelProperty(value = "序号")
    private Integer sort;
}
