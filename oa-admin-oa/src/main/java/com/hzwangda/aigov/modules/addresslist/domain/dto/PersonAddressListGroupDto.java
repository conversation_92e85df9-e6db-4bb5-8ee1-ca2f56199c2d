package com.hzwangda.aigov.modules.addresslist.domain.dto;

import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2022/8/5 10:47
 * @description: 个人通讯录分组
 */

@Data
public class PersonAddressListGroupDto extends BaseEntity {

    private Long id;

    @ApiModelProperty(value = "分类名称")
    private String groupName;

    @ApiModelProperty(value = "使用人")
    private String groupUsername;

    @ApiModelProperty(value = "排序")
    private Integer sort;

}
