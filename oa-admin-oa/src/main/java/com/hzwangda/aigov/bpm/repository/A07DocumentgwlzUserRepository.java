/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.bpm.repository;

import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlzUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-05-14
 **/
public interface A07DocumentgwlzUserRepository extends JpaRepository<A07DocumentGwlzUser, Long>, JpaSpecificationExecutor<A07DocumentGwlzUser> {

    /**
     * 根据公文id和签收状态查询记录
     *
     * @param gwid
     * @return
     */
    List<A07DocumentGwlzUser> findByGwidAndQszt(String gwid, Integer qszt);

    /**
     * 根据公文id、签收状态、用户查询记录
     *
     * @param gwid
     * @return
     */
    List<A07DocumentGwlzUser> findByGwidAndQsztAndUsername(String gwid, Integer qszt, String username);

    /**
     * 根据公文id、用户查询记录
     *
     * @param gwid
     * @return
     */
    List<A07DocumentGwlzUser> findByGwidAndQsr(String gwid,  String username);

    List<A07DocumentGwlzUser> findByGwidAndCyztAndQsr(String gwid, Integer cyzt, String username);

    /**
     * 删除公文交换用户数据
     *
     * @param gwid
     * @param username
     */
    @Transactional
    void deleteByUsernameAndGwid(String username, String gwid);

    /**
     * 根据公文id和状态查询数据记录数
     *
     * @param gwid
     * @param qszt
     * @return
     */
    int countByGwidAndQszt(String gwid, Integer qszt);


    A07DocumentGwlzUser findFirstByGwidAndQsr(String gwid, String qsr);

    List<A07DocumentGwlzUser> findByGwidAndQsztAndQsr(String gwid, Integer qszt, String qsr);

    /***
     * @Description: 根据老的公文ID，更新成新的公文ID
     * @Author: lizh
     * @Date: 2021/9/23 9:54
     **/
    @Modifying
    @Query("update A07DocumentGwlzUser a set a.gwid=?1 where a.gwid=?2")
    void updateGwidbyGwid(String newgwid, String oldgwid);

    /***
     * @Description:
     * @Author: lizh
     * @Date: 2021/9/23 10:14
     **/
    @Transactional(rollbackFor = Exception.class)
    List<A07DocumentGwlzUser> findByGwid(String gwid);

    @Modifying
    @Transactional(rollbackFor = Exception.class)
    void deleteBygwidIn(List<Long> docID);

    @Modifying
    @Query("delete from A07DocumentGwlzUser where gwid in (?1)")
    @Transactional(rollbackFor = Exception.class)
    void deleteByGwidIn(List<String> docID);
}
