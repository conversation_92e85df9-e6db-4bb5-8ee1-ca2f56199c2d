package com.hzwangda.aigov.modules.collaboration.domain.mapstruct;

import com.hzwangda.aigov.modules.collaboration.domain.dto.A10CollaborationInfo4ListDto;
import com.hzwangda.aigov.modules.collaboration.domain.dto.A10CollaborationInfo4ViewDto;
import com.hzwangda.aigov.modules.collaboration.domain.dto.A10CollaborationInfoDto;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationInfo;
import com.wangda.oa.base.BaseMapper;
import com.wangda.oa.modules.system.service.mapstruct.UserMapperUtil;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", uses = {UserMapperUtil.class, StoragesMapperUtil.class}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface A10CollaborationInfoMapper extends BaseMapper<A10CollaborationInfo4ViewDto, A10CollaborationInfo> {
    List<A10CollaborationInfo4ListDto> toList(List<A10CollaborationInfo> entityList);

    A10CollaborationInfo4ListDto toListDto(A10CollaborationInfo entityList);

    A10CollaborationInfoDto toInfoDto(A10CollaborationInfo entity);
}
