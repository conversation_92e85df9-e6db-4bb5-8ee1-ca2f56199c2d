package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.bpm.repository.A07DocumentgwlzUserRepository;
import com.hzwangda.aigov.docconvert.common.domain.entity.SysStorageConversion;
import com.hzwangda.aigov.docconvert.common.repository.SysStorageConversionRepository;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import com.wangda.oa.modules.workflow.domain.common.WFStorageBiz;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.service.dto.LocalStorageDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class Z08FileMapperUtil {

    @Autowired
    private StorageManageService localStorageService;

    @Autowired
    private A07DocumentgwlzUserRepository a07DocumentgwlzUserRepository;

    @Autowired
    private SysStorageConversionRepository sysStorageConversionRepository;

    public Boolean toComplete(Long id) {
        if(id == null) {
            return false;
        }
        int number = a07DocumentgwlzUserRepository.countByGwidAndQszt(String.valueOf(id), 0);
        if(number == 0) {
            return true;
        }
        return false;
    }

    public List<StorageBizDto> toFj(List<WFStorageBiz> fj) {
        if(CollectionUtils.isEmpty(fj)) {
            return new ArrayList<>();
        }
        List<StorageBizDto> list = fj.stream().map(p -> setInfo(p)).collect(Collectors.toList());
        return list;
    }

    public StorageBizDto toZw(WFStorageBiz zw) {
        if(zw == null) {
            return new StorageBizDto();
        }
        StorageBizDto storageBizDto = setInfo(zw);
        List<SysStorageConversion> list = sysStorageConversionRepository.findByOriginalStorageId(Long.valueOf(zw.getStorageId()));
        if(list != null && list.size() > 0) {
            List<Long> ids = list.stream().map(SysStorageConversion::getConversionStorageId).collect(Collectors.toList());
            List<LocalStorageDto> conversionList = new ArrayList() {{
                for(Long id : ids) {
                    add(localStorageService.findById(id));
                }
            }};
            storageBizDto.setConversionList(conversionList);
        }
        return storageBizDto;
    }

    private StorageBizDto setInfo(WFStorageBiz storageBiz) {
        StorageBizDto storageBizDto = new StorageBizDto();
        storageBizDto.setStorage(localStorageService.findById(Long.valueOf(storageBiz.getStorageId())));
        storageBizDto.setBizId(storageBiz.getBizId());
        storageBizDto.setBizType(storageBiz.getBizType());
        storageBizDto.setSorted(storageBiz.getSorted());
        return storageBizDto;
    }

    public List<WFStorageBiz> toConvertToId(List<StorageBizDto> localStorageSimpleDtoList) {
        List<WFStorageBiz> set = new ArrayList<>();
        if(CollectionUtils.isEmpty(localStorageSimpleDtoList)) {
            return set;
        }
        for(StorageBizDto storageBiz : localStorageSimpleDtoList) {
            WFStorageBiz storage = new WFStorageBiz();
            storage.setBizId(storageBiz.getBizId());
            storage.setStorageId(String.valueOf(storageBiz.getStorage().getId()));
            set.add(storage);
        }
        return set;
    }

}
