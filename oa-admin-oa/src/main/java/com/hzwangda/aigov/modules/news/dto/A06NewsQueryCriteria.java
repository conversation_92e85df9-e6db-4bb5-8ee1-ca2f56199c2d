/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.modules.news.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-06-04
 **/
@Data
public class A06NewsQueryCriteria {

    @Query
    @ApiModelProperty(value = "状态(INPROGRESS：进行中,TG:发布)")
    private String bpmStatus;

    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> releaseDate;

    @ApiModelProperty(value = "类型(0:查询正常动态信息,1:查询单位动态信息)")
    private int type;

    @ApiModelProperty(value = "创建人集合(无需传值)")
    @Query(type = Query.Type.IN)
    private List<String> createBy;


    @ApiModelProperty(value = "类型(0:电子公告,1:今日择报,2:工作交流,3:教育参阅)")
    @Query(type = Query.Type.EQUAL)
    private Integer infoType;

}
