package com.hzwangda.aigov.modules.document.repository;

import com.hzwangda.aigov.modules.document.entity.A07DocumentConfidential;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * Description: 机要件流转通知Repository
 *
 * @Date: 2021/8/24 15:39
 * @Author: maogy
 */
public interface A07DocumentConfidentialRepository extends JpaRepository<A07DocumentConfidential, Long>, JpaSpecificationExecutor<A07DocumentConfidential> {
    A07DocumentConfidential findFirstByBpmInstanceId(String procInstanceId);

}
