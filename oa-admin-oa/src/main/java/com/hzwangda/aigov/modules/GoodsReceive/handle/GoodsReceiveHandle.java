package com.hzwangda.aigov.modules.GoodsReceive.handle;

import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.GoodsReceive.domain.GoodsReceive;
import com.hzwangda.aigov.modules.GoodsReceive.repository.GoodsReceiveRepository;
import com.wangda.oa.modules.workflow.dto.BacklogListDto;
import com.wangda.oa.modules.workflow.dto.MyWorkDto;
import com.wangda.oa.modules.workflow.service.FlowFormHandle;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class GoodsReceiveHandle implements FlowFormHandle {

    @Resource
    private GoodsReceiveRepository goodsReceiveRepository;

    @Override
    public void handleFormForMyWork(BacklogListDto myWork, MyWorkDto myWorkDto) {

    }

    @Override
    public String handleSubjectRule(JSONObject formDataObj, String subjectRule) {
        return null;
    }

    @Override
    public Object handleFormRecord(String procInstanceId, String taskDefKey, JSONObject bpmFormData) {
        GoodsReceive goodsReceive = goodsReceiveRepository.findByBpmInstanceId(procInstanceId);
        return goodsReceive;
    }

    @Override
    public void deleteFormRecord(String instanceId) {
        goodsReceiveRepository.deleteByBpmInstanceId(instanceId);
    }
}
