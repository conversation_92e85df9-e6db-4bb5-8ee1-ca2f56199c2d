package com.hzwangda.aigov.modules.document.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Timestamp;

@Data
@ApiModel(value = "签收")
public class A07DocExchangeSignDto {

    @ApiModelProperty(value = "单位id")
    private String qsr;

    @ApiModelProperty(value = "单位名称")
    private String username;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "签收时间")
    private Timestamp qsrq;

    @Column(name = "qszt")
    @ApiModelProperty(value = "签收状态(0:未签收,1:已签收)")
    private Integer qszt;


    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "查阅时间")
    private Timestamp cyrq;

    @Column(name = "cyzt")
    @ApiModelProperty(value = "查阅状态(0:未查阅,1:已查阅)")
    private Integer cyzt;
}
