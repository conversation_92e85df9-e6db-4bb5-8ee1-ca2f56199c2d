package com.hzwangda.aigov.modules.document.repository;

import com.hzwangda.aigov.modules.document.entity.A07DocumentSwView;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface A07DocumentSwViewRepository extends JpaRepository<A07DocumentSwView, Long>, JpaSpecificationExecutor<A07DocumentSwView> {
    List<A07DocumentSwView> findByUpdateTimeGreaterThanEqual(Date lastModify);

    List<A07DocumentSwView> findByUpdateTimeBefore(Date lastModify);
}
