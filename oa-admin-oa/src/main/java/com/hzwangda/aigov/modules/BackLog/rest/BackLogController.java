package com.hzwangda.aigov.modules.BackLog.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.BackLog.service.BackLogService;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.annotation.Log;
import com.wangda.oa.backlog.bo.BacklogAddBO;
import com.wangda.oa.backlog.bo.BacklogListBO;
import com.wangda.oa.backlog.bo.CheckCollectBO;
import com.wangda.oa.backlog.bo.CollectBO;
import com.wangda.oa.backlog.dto.BacklogListPageDto;
import com.wangda.oa.backlog.service.WdBacklogService;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "统一待办")
@RestController
@RequestMapping("/api/backlog")
public class BackLogController {

    @Resource
    private WdBacklogService wdBacklogService;
    @Resource
    private BackLogService backLogService;

    @PostMapping("/addOrUpdateBacklog")
    public ResultJson addOrUpdateBacklog(@RequestBody @Validated BacklogAddBO bo) {
        return ResultJson.generateResult(wdBacklogService.addOrUpdateBacklog(bo));
    }

    @PostMapping("/getBacklogList")
    public ResultJson getBacklogList(@RequestBody @Validated BacklogListBO bo) {
        return ResultJson.generateResult(wdBacklogService.getBacklogList(bo));
    }

    @PostMapping("/getUnitBacklogList")
    public ResultJson getUnitBacklogList(@RequestBody @Validated BacklogListBO bo) {

        JSONObject result =new JSONObject();
        result.put("content",new int[0]);
        result.put("totalElements",0);
        String currentUsername = SecurityUtils.getBindDeptUserNameByNotify();
        if(StringUtils.isEmpty(currentUsername))  return ResultJson.generateResult(result);
        bo.setUserId(currentUsername);
        BacklogListPageDto backlogList = wdBacklogService.getBacklogList(bo);/*   backlogList.getContent().stream().forEach(backlogListDto -> {
            backlogListDto.setCreateDate(DateUtil.parse(DateUtil.format(backlogListDto.getCreateDate(),DatePattern.NORM_DATETIME_FORMAT), DatePattern.NORM_DATETIME_FORMAT));

        });*/
        JSONObject parseObject = JSONObject.parseObject(JSON.toJSONString(backlogList));
        //根据用户是否绑定了单位账号，判断是否展示单位代表列表

        //UserDto userDto = userService.findByName(currentUsername);
        //parseObject.put("canSeeUnitBacklogList", !Objects.isNull(userDto.getDeptId()));
        return ResultJson.generateResult(parseObject);
    }

    @PostMapping("/collect")
    public ResultJson collect(@RequestBody @Validated CollectBO bo) {

        return ResultJson.generateResult(wdBacklogService.collect(bo));
    }

    @PostMapping("/checkCollect")
    public ResultJson checkCollect(@RequestBody @Validated CheckCollectBO bo) {

        return ResultJson.generateResult(wdBacklogService.checkCollect(bo));
    }

    @PostMapping("/getModuleCountByWaitBacklog")
    public Object getModuleCountByWaitBacklog() {
        return wdBacklogService.getModuleCountByWaitBacklog();
    }

    @ApiOperation("列表查询")
    @Log("列表查询")
    @GetMapping("/getCountByWaitBacklog")
    public Object getCountByWaitBacklog() {
        return backLogService.getCountByWaitBacklog();
    }
}
