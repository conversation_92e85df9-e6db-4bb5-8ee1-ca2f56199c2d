package com.hzwangda.aigov.modules.appControl.controller;

import com.hzwangda.aigov.modules.appControl.domain.Z08SelfAppSpace;
import com.hzwangda.aigov.modules.appControl.dto.SelfAppSpaceCriteria;
import com.hzwangda.aigov.modules.appControl.service.SelfAppSpaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/13
 */
@Api(tags = "自选空间接口")
@RestController
@CrossOrigin
@RequestMapping("/api/selfAppSpace")
public class SelfAppSpaceController {

    @Resource
    private SelfAppSpaceService selfAppSpaceService;

    @ApiOperation(value = "新增或修改空间")
    @RequestMapping(value = "/addOrUpdate", method = RequestMethod.POST)
    public ResponseEntity<Void> addOrUpdate(@RequestBody Z08SelfAppSpace space) {
        selfAppSpaceService.addOrUpdate(space);
        return ResponseEntity.ok().build();
    }

    @ApiOperation(value = "获取空间信息")
    @GetMapping(value = "/getById/{id}")
    public ResponseEntity<Z08SelfAppSpace> getById(@ApiParam(value = "空间Id", required = true) @PathVariable Long id) {
        return ResponseEntity.ok(selfAppSpaceService.getById(id));
    }

    @ApiOperation(value = "获取空间信息")
    @GetMapping(value = "/getByKey/{key}")
    public ResponseEntity<Z08SelfAppSpace> getByKey(@ApiParam(value = "空间标识", required = true) @PathVariable String key) {
        return ResponseEntity.ok(selfAppSpaceService.getByKey(key));
    }

    @ApiOperation(value = "查询空间列表分页")
    @GetMapping(value = "/queryListPage")
    public ResponseEntity<Page<Z08SelfAppSpace>> queryListPage(SelfAppSpaceCriteria criteria, Pageable pageable) {
        return ResponseEntity.ok(selfAppSpaceService.queryListPage(criteria, pageable));
    }

    @ApiOperation(value = "查询空间列表")
    @GetMapping(value = "/queryList")
    public ResponseEntity<List<Z08SelfAppSpace>> queryList(SelfAppSpaceCriteria criteria) {
        return ResponseEntity.ok(selfAppSpaceService.queryList(criteria));
    }

    @ApiOperation(value = "删除空间")
    @PostMapping("/delete/{id}")
    public ResponseEntity<Boolean> delete(@ApiParam(value = "空间Id", required = true) @PathVariable Long id) {
        return ResponseEntity.ok(selfAppSpaceService.delete(id));
    }
}
