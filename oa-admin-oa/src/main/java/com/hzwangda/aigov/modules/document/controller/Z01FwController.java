package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.dto.A07DocumentGwQueryCriteria;
import com.hzwangda.aigov.modules.document.service.Z01FwService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: zhang<PERSON>han<PERSON>
 * @date: 2023/2/14 13:27
 * @description:
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "发文管理")
@RequestMapping("/api/z01/fw")
public class Z01FwController {

    private final Z01FwService fwService;

    @ApiOperation("发文分发管理列表")
    @GetMapping(value = "/getFwDistributeList")
    public ResponseEntity<Object> getFwDistributeList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(fwService.getFwDistributeList(criteria, pageable), HttpStatus.OK);
    }

}
