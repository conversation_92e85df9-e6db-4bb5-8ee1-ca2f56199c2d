package com.hzwangda.aigov.modules.archive.repository;

import com.hzwangda.aigov.modules.archive.entity.A12Directory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/7
 **/
public interface A12DirectoryRepository extends JpaRepository<A12Directory, Long>, JpaSpecificationExecutor<A12Directory> {

    /**
     * 根据部门id查询文件档案目录
     *
     * @param deptId
     * @return
     */
    List<A12Directory> findByDeptIdInOrderBySortAsc(List<Long> deptId);

    /**
     * 根据部门id查询文件档案目录
     *
     * @param deptId
     * @return
     */
    A12Directory findDistinctFirstByDeptId(Long deptId);

    /**
     * 根据父id查询是否存在记录
     *
     * @param pid
     * @return
     */
    Integer countByPid(Long pid);

    /**
     * 根据父id查询记录
     *
     * @param pid
     * @return
     */
    List<A12Directory> findByPidOrderBySortAsc(Long pid);
}
