package com.hzwangda.aigov.docconvert.common.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class StorageConversionDto {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "真实文件名")
    private String realName;

    @ApiModelProperty(value = "文件名")
    private String name;

    @ApiModelProperty(value = "后缀")
    private String suffix;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "大小")
    private String size;

    @ApiModelProperty(value = "URL")
    private String url;

    @ApiModelProperty(value = "storageId")
    private Long storageId;

    @ApiModelProperty(value = "path")
    private String path;


    @ApiModelProperty(value = "转换后的附件集合")
    private Map<String, StorageConversionInfoDto> conversionStorage;
}
