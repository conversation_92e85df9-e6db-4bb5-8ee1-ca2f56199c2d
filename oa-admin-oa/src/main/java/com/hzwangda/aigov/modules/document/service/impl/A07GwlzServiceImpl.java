package com.hzwangda.aigov.modules.document.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzwangda.aigov.bpm.repository.A07DocumentGwlzRepository;
import com.hzwangda.aigov.bpm.repository.A07DocumentgwlzUserRepository;
import com.hzwangda.aigov.docconvert.common.service.SysStorageConversionService;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwlzDto;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwlzQueryCriteria;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwlzUserDto;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07DocumentGwlzMapper;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlz;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlzUser;
import com.hzwangda.aigov.modules.document.service.A07GwlzService;
import com.hzwangda.aigov.modules.task.base.TaskConstants;
import com.hzwangda.aigov.modules.workflow.bo.GwlzUserListBO;
import com.hzwangda.aigov.oa.mapper.A07documentMapper;
import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.ValidationUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class A07GwlzServiceImpl implements A07GwlzService {

    private final A07DocumentgwlzUserRepository a07DocumentgwlzUserRepository;

    private final A07documentMapper a07documentMapper;

    private final A07DocumentGwlzRepository a07DocumentgwlzRepository;

    private final A07DocumentGwlzMapper a07DocumentGwlzMapper;

    private final SysStorageConversionService sysStorageConversionService;

    @Override
    public Map<String, Object> gwlzList(A07DocumentGwlzQueryCriteria criteria, Pageable pageable) {
        criteria.setCreatorId(SecurityUtils.getCurrentUserId());
        Page<A07DocumentGwlz> page = a07DocumentgwlzRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(a07DocumentGwlzMapper::toListDto));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public A07DocumentGwlz createOrUpdate(A07DocumentGwlz resources) {
        boolean addConversion = false;
        A07DocumentGwlz gwlz;
        if (resources.getId() == null) {
            if (resources.getZw() != null) {
                addConversion = true;
            }
            Long id = IdWorker.generateId();
            resources.setId(id);
            resources.setBizId(id);
            resources.setCreatorId(SecurityUtils.getBindDeptUserId());
            gwlz = a07DocumentgwlzRepository.save(resources);
        } else {
            A07DocumentGwlz a07DocumentGwlz = a07DocumentgwlzRepository.findById(resources.getId()).orElseGet(A07DocumentGwlz::new);
            if (resources.getZw() != null && a07DocumentGwlz.getZw() != null && !a07DocumentGwlz.getZw().getStorageId().equals(resources.getZw().getStorageId())) {
                //若正文原有的附件id和入参的附件id不一致则需要重新转换
                addConversion = true;
            }
            a07DocumentGwlz.copy(resources);
            gwlz = a07DocumentgwlzRepository.save(a07DocumentGwlz);
        }
        if (addConversion) {
            //正文存在则转换文件
            sysStorageConversionService.addSysStorageConversion(resources.getZw().getStorageId());
        }
        return gwlz;
    }

    @Override
    public Object getGwlzUserList(GwlzUserListBO bo) {
        Map map = new HashMap();
        bo.setUsername(SecurityUtils.getCurrentUsername());
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<A07DocumentGwlz> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(bo.getPageNo(), bo.getSize());
        IPage<A07DocumentGwlzDto> listDtoIPage = a07documentMapper.getUserGwList(page, bo);
        map.put("content", listDtoIPage.getRecords());
        map.put("totalElements", listDtoIPage.getTotal());
        return map;
    }

    @Override
    public A07DocumentGwlz getGwlzInfo(Long id) {
        A07DocumentGwlz a07DocumentGwlz = a07DocumentgwlzRepository.findById(id).orElseGet(A07DocumentGwlz::new);
        ValidationUtil.isNull(a07DocumentGwlz.getId(), "a07DocumentGwlz", "id", id);
        return a07DocumentGwlz;
    }

    @Override
    public A07DocumentGwlzDto getGwlzInfoDto(Long id) {
        A07DocumentGwlz a07DocumentGwlz = a07DocumentgwlzRepository.findById(id).orElseGet(A07DocumentGwlz::new);
        ValidationUtil.isNull(a07DocumentGwlz.getId(), "a07DocumentGwlz", "id", id);
        return a07DocumentGwlzMapper.toDto(a07DocumentGwlz);
    }

    @Override
    public A07DocumentGwlzUserDto getGwlzUserInfo(Long id) {
        A07DocumentGwlzUserDto documentGwlzUserDto = new A07DocumentGwlzUserDto();

        List<A07DocumentGwlzUser> waitList = a07DocumentgwlzUserRepository.findByGwidAndQszt(String.valueOf(id), 0);
        List<A07DocumentGwlzUser> completeList = a07DocumentgwlzUserRepository.findByGwidAndQszt(String.valueOf(id), 1);
        if (waitList != null && waitList.size() > 0) {
            documentGwlzUserDto.setWaitList(new HashSet<>(waitList));
        }
        if (completeList != null && completeList.size() > 0) {
            documentGwlzUserDto.setCompleteList(new HashSet<>(completeList));
        }
        return documentGwlzUserDto;
    }

    @Override
    public Boolean gwlzQs(Long id) {
        List<A07DocumentGwlzUser> list = a07DocumentgwlzUserRepository.findByGwidAndQsztAndUsername(String.valueOf(id), 0, SecurityUtils.getCurrentUsername());
        if (list != null && list.size() > 0) {
            list.stream().forEach(p -> {
                if(p.getCyzt()!= TaskConstants.NUMBER_ONE){
                    p.setQsrq(new Timestamp(System.currentTimeMillis()));
                    p.setQszt(1);
                }
            });
        }
        a07DocumentgwlzUserRepository.saveAll(list);
        return true;
    }

    @Override
    public Boolean gwlzCy(Long id) {
        List<A07DocumentGwlzUser> list = a07DocumentgwlzUserRepository.findByGwidAndCyztAndQsr(String.valueOf(id), 0, SecurityUtils.getBindDeptUserName());
        if (list != null && list.size() > 0) {
            list.stream().forEach(p -> {
                p.setCyrq(new Timestamp(System.currentTimeMillis()));
                p.setCyzt(1);
            });
        }
        a07DocumentgwlzUserRepository.saveAll(list);
        return true;
    }

    @Override
    public Boolean gwlzLwSc(Long id) {
        a07DocumentgwlzUserRepository.deleteByUsernameAndGwid(SecurityUtils.getCurrentUsername(), String.valueOf(id));
        return true;
    }

    @Override
    public Boolean gwlzSc(Long id) {
        a07DocumentgwlzRepository.deleteById(id);
        return true;
    }

    @Override
    public Boolean gwlzCh(Long id) {
        A07DocumentGwlz a07DocumentGwlz = a07DocumentgwlzRepository.findById(id).orElseGet(A07DocumentGwlz::new);
        ValidationUtil.isNull(a07DocumentGwlz.getId(), "a07DocumentGwlz", "id", id);
        a07DocumentGwlz.setGwzt("草稿");
        a07DocumentgwlzRepository.save(a07DocumentGwlz);
        return true;
    }
}
