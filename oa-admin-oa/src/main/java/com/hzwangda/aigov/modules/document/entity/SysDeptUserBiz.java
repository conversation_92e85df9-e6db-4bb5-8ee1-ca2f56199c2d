package com.hzwangda.aigov.modules.document.entity;

import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
 * 公文人员关系表
 *
 * <AUTHOR>
 * @date 2021/6/16 下午2:30
 */
@Data
@Entity
@Table(name = "sys_dept_user_biz")
public class SysDeptUserBiz extends BaseEntity implements Serializable {

    @Id
    @Column(name = "id")
    @NotNull(groups = Update.class)
    @ApiModelProperty(value = "ID", hidden = true)
    private Long id;

    @Column(name = "biz_id")
    @ApiModelProperty(value = "业务id")
    private Long bizId;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @Column(name = "o_id")
    @ApiModelProperty(value = "用户、部门id")
    private Long oId;

    @Column(name = "user_name")
    @ApiModelProperty(value = "用户名")
    private String userName;

    @Column(name = "nick_name")
    @ApiModelProperty(value = "用户姓名")
    private String nickName;

    @Column(name = "type")
    @ApiModelProperty(value = "类型(0:人,1:部门)")
    private Integer type;

    public Long getId() {
        if (Objects.nonNull(this.oId)) {
            return this.oId;
        } else {
            return this.id;
        }
    }

    @PrePersist
    public void preCreateEntity() {
        if (Objects.isNull(oId)) {
            this.oId = this.id;
        }
        this.id = null;
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }
}
