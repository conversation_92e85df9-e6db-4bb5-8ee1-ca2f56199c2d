package com.hzwangda.aigov.modules.favorite.service;

import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.favorite.domain.Favorite;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface FavoriteService {

    Long save(Favorite favorite);

    Long del(Favorite favorite);

    List<Favorite> list(Integer type, String accountid);

    Page<Favorite> page(Integer type, String accountid, Pageable pageable);

    /**
     * 检查是否收藏、关注
     * @param procInstanceId
     * @return
     */
    JSONObject check(String procInstanceId);
}
