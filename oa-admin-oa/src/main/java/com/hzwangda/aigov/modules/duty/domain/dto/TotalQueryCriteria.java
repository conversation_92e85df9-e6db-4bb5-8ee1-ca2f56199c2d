package com.hzwangda.aigov.modules.duty.domain.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class TotalQueryCriteria {
    @ApiModelProperty(value = "开始值班日期")
    @Query(type = Query.Type.BETWEEN)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private List<Date> dutyTime;

    @ApiModelProperty(value = "类别：周week/月month")
    private String type;
}
