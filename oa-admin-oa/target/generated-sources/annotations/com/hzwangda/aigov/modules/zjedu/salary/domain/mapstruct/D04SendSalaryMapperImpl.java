package com.hzwangda.aigov.modules.zjedu.salary.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.salary.domain.dto.D04SendSalaryDto;
import com.hzwangda.aigov.modules.zjedu.salary.domain.entity.D04Salary;
import com.hzwangda.aigov.modules.zjedu.salary.domain.entity.D04SendSalary;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class D04SendSalaryMapperImpl implements D04SendSalaryMapper {

    @Override
    public D04SendSalaryDto toDto(D04SendSalary entity) {
        if ( entity == null ) {
            return null;
        }

        D04SendSalaryDto d04SendSalaryDto = new D04SendSalaryDto();

        d04SendSalaryDto.setCreateBy( entity.getCreateBy() );
        d04SendSalaryDto.setCreateTime( entity.getCreateTime() );
        d04SendSalaryDto.setUpdateTime( entity.getUpdateTime() );
        d04SendSalaryDto.setUpdatedBy( entity.getUpdatedBy() );
        d04SendSalaryDto.setId( entity.getId() );
        d04SendSalaryDto.setMonth( entity.getMonth() );
        List<D04Salary> list = entity.getSalaryList();
        if ( list != null ) {
            d04SendSalaryDto.setSalaryList( new ArrayList<D04Salary>( list ) );
        }
        d04SendSalaryDto.setStatus( entity.getStatus() );
        d04SendSalaryDto.setTitle( entity.getTitle() );
        d04SendSalaryDto.setType( entity.getType() );

        return d04SendSalaryDto;
    }

    @Override
    public List<D04SendSalaryDto> toDto(List<D04SendSalary> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<D04SendSalaryDto> list = new ArrayList<D04SendSalaryDto>( entityList.size() );
        for ( D04SendSalary d04SendSalary : entityList ) {
            list.add( toDto( d04SendSalary ) );
        }

        return list;
    }

    @Override
    public D04SendSalary toEntity(D04SendSalaryDto dto) {
        if ( dto == null ) {
            return null;
        }

        D04SendSalary d04SendSalary = new D04SendSalary();

        d04SendSalary.setCreateBy( dto.getCreateBy() );
        d04SendSalary.setCreateTime( dto.getCreateTime() );
        d04SendSalary.setUpdateTime( dto.getUpdateTime() );
        d04SendSalary.setUpdatedBy( dto.getUpdatedBy() );
        d04SendSalary.setId( dto.getId() );
        d04SendSalary.setMonth( dto.getMonth() );
        List<D04Salary> list = dto.getSalaryList();
        if ( list != null ) {
            d04SendSalary.setSalaryList( new ArrayList<D04Salary>( list ) );
        }
        d04SendSalary.setStatus( dto.getStatus() );
        d04SendSalary.setTitle( dto.getTitle() );
        d04SendSalary.setType( dto.getType() );

        return d04SendSalary;
    }

    @Override
    public List<D04SendSalary> toEntity(List<D04SendSalaryDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<D04SendSalary> list = new ArrayList<D04SendSalary>( dtoList.size() );
        for ( D04SendSalaryDto d04SendSalaryDto : dtoList ) {
            list.add( toEntity( d04SendSalaryDto ) );
        }

        return list;
    }
}
