package com.hzwangda.aigov.modules.document.entity;

import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Immutable
@Subselect("select * from sw_view")
@Entity
@Data
public class A07DocumentSwView extends BaseBpmDomain implements Serializable {
    @Id
    private Long id;

    @Column(name = "bt")
    @ApiModelProperty(value = "标题")
    private String bt;

    @Column(name = "bpm_status")
    private String bpmStatus;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;
}
