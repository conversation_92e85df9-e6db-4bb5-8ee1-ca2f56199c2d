package com.hzwangda.aigov.modules.meetingRoomSubscribe.mapstruct;

import com.hzwangda.aigov.modules.meetingRoomSubscribe.domain.MeetingRoom;
import com.hzwangda.aigov.modules.meetingRoomSubscribe.domain.MeetingRoomManager;
import com.hzwangda.aigov.modules.meetingRoomSubscribe.domain.MeetingRoomSubscribe;
import com.hzwangda.aigov.modules.meetingRoomSubscribe.dto.MeetingRoomDto;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class MeetingRoomMapperImpl implements MeetingRoomMapper {

    @Override
    public List<MeetingRoomDto> toDto(List<MeetingRoom> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MeetingRoomDto> list = new ArrayList<MeetingRoomDto>( entityList.size() );
        for ( MeetingRoom meetingRoom : entityList ) {
            list.add( toDto( meetingRoom ) );
        }

        return list;
    }

    @Override
    public MeetingRoom toEntity(MeetingRoomDto dto) {
        if ( dto == null ) {
            return null;
        }

        MeetingRoom meetingRoom = new MeetingRoom();

        meetingRoom.setApprove( dto.getApprove() );
        meetingRoom.setBeginDate( dto.getBeginDate() );
        meetingRoom.setEndDate( dto.getEndDate() );
        meetingRoom.setEquipment( dto.getEquipment() );
        meetingRoom.setHysFormId( dto.getHysFormId() );
        meetingRoom.setId( dto.getId() );
        meetingRoom.setMeetingRoomAppointment( dto.getMeetingRoomAppointment() );
        meetingRoom.setMeetingRoomFormInstantiateId( dto.getMeetingRoomFormInstantiateId() );
        List<MeetingRoomManager> list = dto.getMeetingRoomManagers();
        if ( list != null ) {
            meetingRoom.setMeetingRoomManagers( new ArrayList<MeetingRoomManager>( list ) );
        }
        meetingRoom.setMeetingRoomSend( dto.getMeetingRoomSend() );
        List<MeetingRoomSubscribe> list1 = dto.getMeetingRoomSubscribes();
        if ( list1 != null ) {
            meetingRoom.setMeetingRoomSubscribes( new ArrayList<MeetingRoomSubscribe>( list1 ) );
        }
        meetingRoom.setMethod( dto.getMethod() );
        meetingRoom.setMinInterval( dto.getMinInterval() );
        meetingRoom.setMobileFormId( dto.getMobileFormId() );
        meetingRoom.setNumbers( dto.getNumbers() );
        meetingRoom.setRemark( dto.getRemark() );
        meetingRoom.setRoomAddress( dto.getRoomAddress() );
        meetingRoom.setRoomFamilyId( dto.getRoomFamilyId() );
        meetingRoom.setRoomName( dto.getRoomName() );
        meetingRoom.setRoomType( dto.getRoomType() );
        meetingRoom.setSort( dto.getSort() );
        meetingRoom.setStatus( dto.getStatus() );
        List<StorageBiz> list2 = dto.getFile();
        if ( list2 != null ) {
            meetingRoom.setFile( new ArrayList<StorageBiz>( list2 ) );
        }

        return meetingRoom;
    }

    @Override
    public List<MeetingRoom> toEntity(List<MeetingRoomDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MeetingRoom> list = new ArrayList<MeetingRoom>( dtoList.size() );
        for ( MeetingRoomDto meetingRoomDto : dtoList ) {
            list.add( toEntity( meetingRoomDto ) );
        }

        return list;
    }

    @Override
    public MeetingRoomDto toDto(MeetingRoom entity) {
        if ( entity == null ) {
            return null;
        }

        MeetingRoomDto meetingRoomDto = new MeetingRoomDto();

        meetingRoomDto.setApprove( entity.getApprove() );
        meetingRoomDto.setBeginDate( entity.getBeginDate() );
        meetingRoomDto.setEndDate( entity.getEndDate() );
        meetingRoomDto.setEquipment( entity.getEquipment() );
        List<StorageBiz> list = entity.getFile();
        if ( list != null ) {
            meetingRoomDto.setFile( new ArrayList<StorageBiz>( list ) );
        }
        meetingRoomDto.setHysFormId( entity.getHysFormId() );
        meetingRoomDto.setId( entity.getId() );
        meetingRoomDto.setMeetingRoomAppointment( entity.getMeetingRoomAppointment() );
        meetingRoomDto.setMeetingRoomFormInstantiateId( entity.getMeetingRoomFormInstantiateId() );
        List<MeetingRoomManager> list1 = entity.getMeetingRoomManagers();
        if ( list1 != null ) {
            meetingRoomDto.setMeetingRoomManagers( new ArrayList<MeetingRoomManager>( list1 ) );
        }
        meetingRoomDto.setMeetingRoomSend( entity.getMeetingRoomSend() );
        List<MeetingRoomSubscribe> list2 = entity.getMeetingRoomSubscribes();
        if ( list2 != null ) {
            meetingRoomDto.setMeetingRoomSubscribes( new ArrayList<MeetingRoomSubscribe>( list2 ) );
        }
        meetingRoomDto.setMethod( entity.getMethod() );
        meetingRoomDto.setMinInterval( entity.getMinInterval() );
        meetingRoomDto.setMobileFormId( entity.getMobileFormId() );
        meetingRoomDto.setNumbers( entity.getNumbers() );
        meetingRoomDto.setRemark( entity.getRemark() );
        meetingRoomDto.setRoomAddress( entity.getRoomAddress() );
        meetingRoomDto.setRoomFamilyId( entity.getRoomFamilyId() );
        meetingRoomDto.setRoomName( entity.getRoomName() );
        meetingRoomDto.setRoomType( entity.getRoomType() );
        meetingRoomDto.setSort( entity.getSort() );
        meetingRoomDto.setStatus( entity.getStatus() );

        return meetingRoomDto;
    }
}
