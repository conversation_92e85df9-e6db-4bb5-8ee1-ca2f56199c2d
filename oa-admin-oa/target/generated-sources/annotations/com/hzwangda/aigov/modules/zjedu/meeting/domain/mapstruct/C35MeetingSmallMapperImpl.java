package com.hzwangda.aigov.modules.zjedu.meeting.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35MeetingSmallDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35Meeting;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class C35MeetingSmallMapperImpl implements C35MeetingSmallMapper {

    @Override
    public C35MeetingSmallDto toDto(C35Meeting entity) {
        if ( entity == null ) {
            return null;
        }

        C35MeetingSmallDto c35MeetingSmallDto = new C35MeetingSmallDto();

        c35MeetingSmallDto.setCreateBy( entity.getCreateBy() );
        c35MeetingSmallDto.setCreateTime( entity.getCreateTime() );
        c35MeetingSmallDto.setUpdateTime( entity.getUpdateTime() );
        c35MeetingSmallDto.setUpdatedBy( entity.getUpdatedBy() );
        c35MeetingSmallDto.setAddress( entity.getAddress() );
        c35MeetingSmallDto.setEndTime( entity.getEndTime() );
        c35MeetingSmallDto.setId( entity.getId() );
        c35MeetingSmallDto.setStartTime( entity.getStartTime() );
        c35MeetingSmallDto.setStatus( entity.getStatus() );
        c35MeetingSmallDto.setSubject( entity.getSubject() );
        c35MeetingSmallDto.setType( entity.getType() );
        c35MeetingSmallDto.setUserIds( entity.getUserIds() );

        return c35MeetingSmallDto;
    }

    @Override
    public List<C35MeetingSmallDto> toDto(List<C35Meeting> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<C35MeetingSmallDto> list = new ArrayList<C35MeetingSmallDto>( entityList.size() );
        for ( C35Meeting c35Meeting : entityList ) {
            list.add( toDto( c35Meeting ) );
        }

        return list;
    }

    @Override
    public C35Meeting toEntity(C35MeetingSmallDto dto) {
        if ( dto == null ) {
            return null;
        }

        C35Meeting c35Meeting = new C35Meeting();

        c35Meeting.setCreateBy( dto.getCreateBy() );
        c35Meeting.setCreateTime( dto.getCreateTime() );
        c35Meeting.setUpdateTime( dto.getUpdateTime() );
        c35Meeting.setUpdatedBy( dto.getUpdatedBy() );
        c35Meeting.setAddress( dto.getAddress() );
        c35Meeting.setEndTime( dto.getEndTime() );
        c35Meeting.setId( dto.getId() );
        c35Meeting.setStartTime( dto.getStartTime() );
        c35Meeting.setStatus( dto.getStatus() );
        c35Meeting.setSubject( dto.getSubject() );
        c35Meeting.setType( dto.getType() );
        c35Meeting.setUserIds( dto.getUserIds() );

        return c35Meeting;
    }

    @Override
    public List<C35Meeting> toEntity(List<C35MeetingSmallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<C35Meeting> list = new ArrayList<C35Meeting>( dtoList.size() );
        for ( C35MeetingSmallDto c35MeetingSmallDto : dtoList ) {
            list.add( toEntity( c35MeetingSmallDto ) );
        }

        return list;
    }
}
