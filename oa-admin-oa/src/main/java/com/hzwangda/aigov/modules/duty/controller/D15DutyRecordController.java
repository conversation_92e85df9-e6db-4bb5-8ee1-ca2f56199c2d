package com.hzwangda.aigov.modules.duty.controller;

import com.hzwangda.aigov.modules.duty.domain.dto.D15DutyRecordCriteria;
import com.hzwangda.aigov.modules.duty.domain.dto.TotalQueryCriteria;
import com.hzwangda.aigov.modules.duty.domain.entity.D15DutyRecord;
import com.hzwangda.aigov.modules.duty.domain.entity.D15DutyRecordFeedBack;
import com.hzwangda.aigov.modules.duty.domain.entity.D15DutyRecordList;
import com.hzwangda.aigov.modules.duty.service.D15DutyRecordService;
import com.wangda.oa.annotation.AnonymousAccess;
import com.wangda.oa.annotation.rest.AnonymousGetMapping;
import com.wangda.oa.annotation.rest.AnonymousPostMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = "值班记录")
@RequestMapping("/d15DutyRecord")
public class D15DutyRecordController {

    @Resource
    private D15DutyRecordService d15DutyRecordService;

    @GetMapping("/queryList")
    @ApiOperation(value = "查询值班记录列表")
    public ResponseEntity<Page<D15DutyRecordList>> queryList(D15DutyRecordCriteria d15DutyRecordCriteria, Pageable pageable) {
        Page<D15DutyRecordList> d15DutyRecords = d15DutyRecordService.queryList(d15DutyRecordCriteria, pageable);
        return new ResponseEntity<>(d15DutyRecords, HttpStatus.OK);
    }

    @GetMapping("/queryById")
    @ApiOperation(value = "查询值班记录详情")
    @AnonymousAccess
    public ResponseEntity<D15DutyRecord> queryById(@RequestParam Long id, @RequestParam(required = false) Boolean ck) {
        D15DutyRecord d15DutyRecord = d15DutyRecordService.queryById(id, ck);
        return new ResponseEntity<>(d15DutyRecord, HttpStatus.OK);
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存值班记录")
    public ResponseEntity<Long> save(@RequestBody D15DutyRecord d15DutyRecord) {
        Long save = d15DutyRecordService.save(d15DutyRecord);
        return new ResponseEntity<>(save, HttpStatus.OK);
    }

    @PostMapping("/batchDel")
    @ApiOperation(value = "批量删除")
    public ResponseEntity<String> batchDel(@RequestBody Long[] ids) {
        String del = d15DutyRecordService.batchDel(ids);
        return new ResponseEntity<>(del, HttpStatus.OK);
    }

    @GetMapping("/getMaxNumber")
    @ApiOperation("获取最大编号")
    public ResponseEntity<String> getMaxNumber(@RequestParam String type) {
        return new ResponseEntity<>(d15DutyRecordService.getMaxNumber(type), HttpStatus.OK);
    }

    @PostMapping("/read")
    @ApiOperation(value = "阅读值班记录")
    public ResponseEntity<Long> read(@RequestBody Long id) {
        Long save = d15DutyRecordService.read(id);
        return new ResponseEntity<>(save, HttpStatus.OK);
    }

    @PostMapping("/mark")
    @ApiOperation(value = "标记完成")
    public ResponseEntity<String> mark(@RequestBody Long[] ids) {
        String del = d15DutyRecordService.mark(ids);
        return new ResponseEntity<>(del, HttpStatus.OK);
    }

    @GetMapping("/total")
    @ApiOperation(value = "统计")
    @AnonymousAccess
    public ResponseEntity<Map<String, Object>> total(TotalQueryCriteria criteria) throws ParseException {
        Map<String, Object> map = d15DutyRecordService.total(criteria);
        return new ResponseEntity<>(map, HttpStatus.OK);
    }

    @AnonymousGetMapping("/getFeedbackList")
    @ApiOperation(value = "反馈内容")
    public ResponseEntity<List<D15DutyRecordFeedBack>> getFeedbackList(@RequestParam String number) {
        return ResponseEntity.ok(d15DutyRecordService.findByNumber(number));
    }

    @AnonymousPostMapping("/saveFeedback")
    @ApiOperation(value = "保存反馈内容")
    public ResponseEntity<Long> saveFeedback(@RequestBody D15DutyRecordFeedBack feedBack) {
        return ResponseEntity.ok(d15DutyRecordService.saveFeedback(feedBack));
    }

    @AnonymousGetMapping("/existsByNumber")
    @ApiOperation(value = "编号是否存在")
    public ResponseEntity<Boolean> existsByNumber(@RequestParam String number) {
        return ResponseEntity.ok(d15DutyRecordService.existsByNumber(number));
    }
}
