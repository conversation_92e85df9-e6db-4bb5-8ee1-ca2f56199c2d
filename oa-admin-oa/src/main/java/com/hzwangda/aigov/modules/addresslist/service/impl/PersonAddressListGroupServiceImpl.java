package com.hzwangda.aigov.modules.addresslist.service.impl;

import com.hzwangda.aigov.modules.addresslist.domain.dto.PersonAddressListGroupDto;
import com.hzwangda.aigov.modules.addresslist.domain.entity.PersonAddressList;
import com.hzwangda.aigov.modules.addresslist.domain.entity.PersonAddressListGroup;
import com.hzwangda.aigov.modules.addresslist.mapstruct.PersonAddressListGroupMapper;
import com.hzwangda.aigov.modules.addresslist.repository.PersonAddressListGroupRepository;
import com.hzwangda.aigov.modules.addresslist.repository.PersonAddressListRepository;
import com.hzwangda.aigov.modules.addresslist.service.PersonAddressListGroupService;
import com.wangda.oa.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2022/8/5 13:23
 * @description: 个人通讯录分组
 */
@Service
@RequiredArgsConstructor
public class PersonAddressListGroupServiceImpl implements PersonAddressListGroupService {
    private final PersonAddressListGroupRepository groupRepository;
    private final PersonAddressListRepository addressListRepository;
    private final PersonAddressListGroupMapper groupMapper;

    @Override
    public Object queryList(String groupName) {
        Specification<PersonAddressListGroup> sp = (root, criteriaQuery, cb) -> {
            Predicate pe = cb.equal(root.get("groupUsername"), SecurityUtils.getCurrentUsername());
            if (StringUtils.isNotBlank(groupName)) {
                pe = cb.and(pe, cb.like(root.get("groupName"), "%" + groupName + "%"));
            }
            criteriaQuery.orderBy(
                    cb.asc(root.get("sort"))
            );
            return pe;
        };
        List<PersonAddressListGroup> groupList = groupRepository.findAll(sp);
        List<PersonAddressListGroupDto> groupListDto = groupList.stream().map(groupMapper::toDto).collect(Collectors.toList());
        return groupListDto;
    }

    @Override
    public Object queryOne(Long id) {
        PersonAddressListGroup group = groupRepository.findById(id).orElseGet(PersonAddressListGroup::new);
        return group;
    }

    @Override
    public Object save(PersonAddressListGroup group) {
        if (group.getId() == null) {
            group.setGroupUsername(SecurityUtils.getCurrentUsername());
        }
        PersonAddressListGroup save = groupRepository.save(group);
        return save;
    }

    @Override
    public Object del(Long id) {
        groupRepository.deleteById(id);
        List<PersonAddressList> addressLists = addressListRepository.findByGroupId(id);
        addressListRepository.deleteAll(addressLists);
        return "删除成功";
    }
}
