package com.hzwangda.aigov.modules.matter.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.matter.domain.*;
import com.hzwangda.aigov.modules.matter.repository.*;
import com.hzwangda.aigov.modules.matter.service.MatterService;
import com.hzwangda.aigov.modules.matter.service.dto.MatterDto;
import com.hzwangda.aigov.modules.matter.service.dto.MatterHandleQueryCriteria;
import com.hzwangda.aigov.modules.matter.service.dto.MatterQueryCriteria;
import com.hzwangda.aigov.modules.matter.service.dto.MatterSegmentQueryCriteria;
import com.hzwangda.aigov.modules.matter.service.mapstruct.MatterMapper;
import com.hzwangda.aigov.oa.util.UserLoginStatusUtils;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.boot.platform.utils.PageUtil;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.system.domain.Dept;
import com.wangda.oa.modules.system.repository.DeptRepository;
import com.wangda.oa.modules.system.repository.DeptRepositoryCustom;
import com.wangda.oa.modules.system.service.DeptService;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.*;
import com.wangda.oa.modules.system.service.mapstruct.DeptMapper;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.domain.form.FormTemplate;
import com.wangda.oa.modules.workflow.domain.workflow.vo.ProcessQueryVo;
import com.wangda.oa.modules.workflow.domain.workflow.vo.TaskQueryVo;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskDto;
import com.wangda.oa.modules.workflow.repository.form.WdFormTemplateRepository;
import com.wangda.oa.modules.workflow.service.workflow.FlowInstanceService;
import com.wangda.oa.modules.workflow.service.workflow.FlowTaskService;
import com.wangda.oa.modules.workflow.utils.FlowableUtils;
import com.wangda.oa.utils.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.impl.RepositoryServiceImpl;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 事项库主表 服务实现
 * <AUTHOR>
 * @date 2022/01/14 10:06
 **/
@Service
@RequiredArgsConstructor
public class MatterServiceImpl implements MatterService {


    private final MatterSegmentRepository matterSegmentRepository;
    private final FlowTaskService flowTaskService;
    private final MatterRepository matterRepository;
    private final MatterMapper matterMapper;
    private final DeptMapper deptMapper;
    private final MatterAppMaterialsRepository matterAppMaterialsRepository;
    private final MatterCommonProblemRepository commonProblemRepository;
    private final MatterEvaluateRepository evaluateRepository;
    private final MatterSegmentRepository segmentRepository;

    private final MatterCollectionRepository collectionRepository;

    private final MatterMainItemRepository matterMainItemRepository;

    private final UserService userService;

    private final DeptRepository deptRepository;

    private final MatterHandleStatisticsRepository matterHandleStatisticsRepository;
    private final MatterCollectionRepository matterCollectionRepository;
    private final WdFormTemplateRepository wdFormTemplateRepository;
    private final HistoryService historyService;
    @Autowired
    private DeptService deptService;
    private final DeptRepositoryCustom deptRepositoryCustom;

    private final FlowInstanceService flowInstanceService;

    /*流程部署服务*/
    private final RepositoryServiceImpl repositoryService;


    // 任务服务
    @Resource
    protected TaskService taskService;

    // 运行服务
    @Resource
    protected RuntimeService runtimeService;

    private final UserService sysUserService;


    public static Date getThisWeekMonday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 获得当前日期是一个星期的第几天
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
        if(1 == dayWeek) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        // 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        // 获得当前日期是一个星期的第几天
        int day = cal.get(Calendar.DAY_OF_WEEK);
        // 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);
        return cal.getTime();
    }

    @Override
    public Map<String, Object> queryAll(MatterQueryCriteria criteria, Pageable pageable) {
        //未删除
        criteria.setDelStatus(0);
        Long currentUserId = SecurityUtils.getCurrentUserId();
        UserDto user = userService.findById(currentUserId);
        DeptSmallDto dept = user.getDept();
        Set<RoleSmallDto> roles = user.getRoles();
        if(!roles.stream().anyMatch(role -> {
            return role.getId() == 1;
        })) {
            criteria.setDeptId(dept.getId());
        }


        criteria.setMatterStatus(Optional.ofNullable(criteria.getMatterStatus()).orElse(5));
        Page<Matter> page = matterRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(matterMapper::toDto));
    }

    @Override
    public List<MatterDto> queryAll(MatterQueryCriteria criteria) {
        return matterMapper.toDto(matterRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    public MatterDto findById(Long id) {
        Matter matter = matterRepository.findById(id).orElseGet(Matter::new);
        ValidationUtil.isNull(matter.getId(), "Matter", "id", id);
        return matterMapper.toDto(matter);
    }

//    private String dealWithPicture(String imgJson) {
//        if (StringUtils.isBlank(imgJson)) {
//            return null;
//        }
//        JSONArray jsonArray = JSON.parseArray(imgJson);
//        List<String> urlList = new ArrayList<>();
//        for (int i = 0; i < jsonArray.size(); i++) {
//            String url = ((JSONObject) jsonArray.get(i)).getString("url");
//            urlList.add(url);
//        }
//        String str = String.join(",", urlList);
//        return str;
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MatterDto create(Matter resources) {
        if(resources.getId() == null) {
            //删除状态1删除0未删除
            resources.setDelStatus(0);
            //事项状态暂时设置成5-在用,待审核功能开发之后换成3
            resources.setMatterStatus(5);
         /*   Long currentUserId = SecurityUtils.getCurrentUserId();*/
            DeptDto dept = deptService.findById(resources.getDeptId());

           // resources.setDeptCode(dept == null ? "" : String.valueOf(dept.getId()));
            resources.setDeptName(dept == null ? "" : dept.getName());
            resources.setDeptId(dept != null ? dept.getId() : null);

            //维护主项
            String mainItem = resources.getMainItem();
            if(StringUtils.isNotBlank(mainItem)) {
                MatterMainItem m = matterMainItemRepository.findFirstByItemName(mainItem);
                if(m == null) {
                    m = new MatterMainItem();
                    m.setItemName(mainItem);
                    m.setSort(999);
                    m.setDelStatus(0);
                    m.setIsAutomatic(1);
                    matterMainItemRepository.save(m);
                }
            }
            return matterMapper.toDto(matterRepository.save(resources));
        }else {
            Matter matter = matterRepository.findById(resources.getId()).orElseGet(Matter::new);
            ValidationUtil.isNull(matter.getId(), "Matter", "id", resources.getId());
            matter.copy(resources);
            return matterMapper.toDto(matterRepository.save(matter));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Matter resources) {
        Matter matter = matterRepository.findById(resources.getId()).orElseGet(Matter::new);
        ValidationUtil.isNull(matter.getId(), "Matter", "id", resources.getId());
        matter.copy(resources);
        matterRepository.save(matter);
    }

    @Override
    public void approval(MatterQueryCriteria criteria) {
        matterRepository.approval(criteria.getId(), criteria.getMatterStatus());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(Set<Long> ids) {
        //逻辑删除事项主表
        if(CollectionUtils.isNotEmpty(ids)) {
            for(Long id : ids) {
                matterRepository.logicDel(id);
                //同步逻辑删除其他子表
                matterAppMaterialsRepository.logicDel(id);
                commonProblemRepository.logicDel(id);
                evaluateRepository.logicDel(id);
                segmentRepository.logicDel(id);
            }
        }
    }

    @Override
    public void download(List<MatterDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for(MatterDto matter : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("createDate", matter.getCreateDate());
            map.put("creatorId", matter.getCreatorId());
            map.put("enabled", matter.getEnabled());
            map.put("modifiedDate", matter.getModifiedDate());
            map.put("modifiedId", matter.getModifiedId());
            map.put("version", matter.getVersion());
            map.put("advisoryTelephone", matter.getAdvisoryTelephone());
            map.put("applicants", matter.getApplicants());
            map.put("belongTheme", matter.getBelongTheme());
            map.put("businessOffice", matter.getBusinessOffice());
            map.put("chargeBasis", matter.getChargeBasis());
            map.put("decisionBodies", matter.getDecisionBodies());
            map.put("delStatus", matter.getDelStatus());
            map.put("deptCode", matter.getDeptCode());
            map.put("deptName", matter.getDeptName());
            map.put("finishingDay", matter.getFinishingDay());
            map.put("finishingTimeLimit", matter.getFinishingTimeLimit());
            map.put("handleOffice", matter.getHandleOffice());
            map.put("isAcrossLayers", matter.getIsAcrossLayers());
            map.put("isDd", matter.getIsDd());
            map.put("isInternet", matter.getIsInternet());
            map.put("isOne", matter.getIsOne());
            map.put("isUnion", matter.getIsUnion());
            map.put("isZero", matter.getIsZero());
            map.put("legalBasis", matter.getLegalBasis());
            map.put("mainItem", matter.getMainItem());
            map.put("matterIcon", matter.getMatterIcon());
            map.put("matterName", matter.getMatterName());
            map.put("matterRemark", matter.getMatterRemark());
            map.put("matterStatus", matter.getMatterStatus());
            map.put("modeOfService", matter.getModeOfService());
            map.put("officeAddress", matter.getOfficeAddress());
            map.put("officeHours", matter.getOfficeHours());
            map.put("proImages", matter.getProImages());
            map.put("proKey", matter.getProKey());
            map.put("runTimes", matter.getRunTimes());
            map.put("sort", matter.getSort());
            map.put("timeNum", matter.getTimeNum());
            map.put("timeUnit", matter.getTimeUnit());
            map.put("urlDd", matter.getUrlDd());
            map.put("urlPc", matter.getUrlPc());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }


    public List<Matter> getTop5Matter(){
        List<Matter> matterList=new ArrayList();
        ProcessQueryVo vo=new ProcessQueryVo();
        vo.setCategory("内跑流程");
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<FlowTaskDto> page= flowInstanceService.queryProcess(vo);

        List<FlowTaskDto> list=page.getRecords();
        List<String> result = list.stream().collect(Collectors.groupingBy(FlowTaskDto::getDeployId, Collectors.counting())).entrySet()
                .stream()
                //按办件量排序
                .sorted(Comparator.comparing(Map.Entry::getValue))
                .map(Map.Entry::getKey)
                .limit(15)
                .collect(Collectors.toList());

        result.stream().forEach(key->{
            ProcessDefinition def = repositoryService.createProcessDefinitionQuery().deploymentId(key).singleResult();
            matterList.add(matterRepository.findFirstByProKey(def.getKey()));
        });
        return matterList;
    }
    /**
     * 首页常办事项
     */
    @Override
    public Map<String, Object> regularMatter() {
        boolean b = UserLoginStatusUtils.isLogin();
        //当前登录用户的部门id
        List<Matter> top5matterList=getTop5Matter();
        if(b) {
            top5matterList.stream().forEach(matter->{
                List<MatterCollection> c = collectionRepository.findByUserCodeAndMatterId(SecurityUtils.getCurrentUsername(), matter.getId());
                Integer isCollection = CollectionUtils.isNotEmpty(c) ? 1 : 0;
                matter.setIsCollection(isCollection);
                matter.setIsHandle(1);
            });
        }else {
            top5matterList.stream().forEach(matter->{matter.setIsHandle(1);} );

        }
        if( top5matterList.size()>5){
            top5matterList= top5matterList.subList(0,5);
        }

        top5matterList= PageUtil.toPage(0, 5, top5matterList);
        return PageUtil.toPage(top5matterList, top5matterList.size());

    }

    @Override
    public Object matterQuery(MatterQueryCriteria criteria, Pageable pageable) {
        Map<String, Object> map = new LinkedHashMap(2);
        boolean b = UserLoginStatusUtils.isLogin();

        String depName;
        String nowUserDep = "";
        String currentUsername = "";
        DeptSmallDto dept = null;
        if(b) {
            currentUsername = SecurityUtils.getCurrentUsername();
            Long currentUserId = SecurityUtils.getCurrentUserId();
            dept = userService.findById(currentUserId).getDept();
            nowUserDep = dept.getName();
            if(StringUtils.isBlank(criteria.getDeptName())) {
                depName = dept.getName();
            }else {
                depName = criteria.getDeptName();
            }
        }else {
            //未登录状态默认显示第一个部门的事项数据防止前端未传部门名导致全事项主表扫描影响效率
            List<DeptDto> deptDtos = deptService.queryAll();
            if(StringUtils.isBlank(criteria.getDeptName())) {
                depName = deptDtos.get(0).getName();
            }else {
                depName = criteria.getDeptName();
            }
        }
        criteria.setDeptName(depName);
        List<String> matterName = new ArrayList<>();
        List<String> themeName = new ArrayList<>();

        //如果没有事项就无需进入主项循环子项
        if(StringUtils.isNotBlank(criteria.getDeptName())) {
            List<Matter> deptMatter = matterRepository.findByDelStatusAndMatterStatusAndDeptName(0, 5, criteria.getDeptName());
            if(CollectionUtils.isEmpty(deptMatter)) {
                map.put("content", new ArrayList<>());
                map.put("totalElements", 0);
                return map;
            }else {
                //把事项所属主项取出
                matterName = deptMatter.stream().map(matter -> matter.getMainItem()).distinct().collect(Collectors.toList());
            }
        }
        if(StringUtils.isNotBlank(criteria.getBelongTheme())) {
            List<Matter> themeMatter = matterRepository.findByDelStatusAndMatterStatusAndBelongTheme(0, 5, criteria.getBelongTheme());
            if(CollectionUtils.isEmpty(themeMatter)) {
                map.put("content", new ArrayList<>());
                map.put("totalElements", 0);
                return map;
            }else {
                //主题查事项
                themeName = themeMatter.stream().map(matter -> matter.getMainItem()).distinct().collect(Collectors.toList());
            }
        }
        List<MatterMainItem> mainItemList;
        if(CollectionUtils.isNotEmpty(matterName)) {
            mainItemList = matterMainItemRepository.findByItemNameIn(matterName);
        }else if(CollectionUtils.isNotEmpty(themeName)) {
            //主题->事项->主项
            mainItemList = matterMainItemRepository.findByItemNameIn(themeName);
        }else {
            mainItemList = matterMainItemRepository.findByDelStatus(0);
        }
        List<MatterMainItem> mainItems = new ArrayList<>();
        Long d1 = System.currentTimeMillis();
        if(CollectionUtils.isNotEmpty(mainItemList)) {
            for(MatterMainItem m : mainItemList) {
                //未删除
                criteria.setDelStatus(0);
                criteria.setMatterStatus(5);
                //按条件查询主项下的子项
                //1.全部事项 2.联办事项 3.在线办理事项
                Integer searchFlag = criteria.getSearchFlag();
                if(Objects.equals(2, searchFlag)) {
                    criteria.setIsUnion(1);
                }else if(Objects.equals(3, searchFlag)) {
                    criteria.setIsInternet(1);
                }
                criteria.setMainItem(m.getItemName());
                List<Matter> matterList = matterRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));
                if(b) {
                    for(Matter matter : matterList) {
                        //和当前登录用户的部门判断
                        Integer isHandle = (dept != null) && Objects.equals(matter.getDeptName(), nowUserDep) ? 1 : 0;
                        matter.setIsHandle(isHandle);
                        List<MatterCollection> c = collectionRepository.findByUserCodeAndMatterId(currentUsername, matter.getId());
                        Integer isCollection = CollectionUtils.isNotEmpty(c) ? 1 : 0;
                        matter.setIsCollection(isCollection);
                    }
                }else {
                    for(Matter matter : matterList) {
                        matter.setIsHandle(0);
                        matter.setIsCollection(0);
                    }
                }
                m.setMatterList(matterList);
                System.out.println("===>" + matterList);
                if(CollectionUtils.isNotEmpty(matterList)) {
                    mainItems.add(m);
                }
            }
        }
        Long d2 = System.currentTimeMillis();

        System.out.println("耗时:" + (d2 - d1) / 1000 + "秒");

        //分页是拿主项分页
        List list = PageUtil.toPage(pageable.getPageNumber(), pageable.getPageSize(), mainItems);
        map.put("content", list);
        map.put("totalElements", mainItems.size());
        return map;
    }

    @Override
    public Map<String, Object> queryMatter(String key, Pageable pageable) {
        MatterQueryCriteria criteria = new MatterQueryCriteria();
        criteria.setDelStatus(0);
        criteria.setMatterStatus(5);
        criteria.setMatterName(key);
        Page<Matter> page = matterRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return  PageUtil.toPage(page.map(matterMapper::toDto));
    }

    @Override
    public List<DeptDto> queryDept(String key, Pageable pageable) {
        List<DeptDto> deptDtos = deptMapper.toDto(deptRepositoryCustom.findByIsNpOrderByNpDeptSortAsc(true));



        if(StringUtils.isNotBlank(key)) {
            deptDtos = deptDtos.stream().filter(d -> d.getName().contains(key)).collect(Collectors.toList());
        }
        return PageUtil.toPage(pageable.getPageNumber(), pageable.getPageSize(), deptDtos);
    }

    @Override
    public Map<String, Object> pcMatterDetails(Long id) {
        Matter matter = matterRepository.findById(id).get();
        Map<String, Object> result = new HashMap<>(16);
        //头部信息
        Map<String, Object> titleMap = new HashMap<>(16);
        //跑几次
        titleMap.put("runTimes", matter.getRunTimes());
        //累计办件量 TODO
        titleMap.put("handlingVolume", 0);
        //平均分 TODO
        titleMap.put("avgScore", 5);
        //是否收藏
        boolean b = UserLoginStatusUtils.isLogin();
        if(b) {
            List<MatterCollection> c = matterCollectionRepository.findByUserCodeAndMatterId(SecurityUtils.getCurrentUsername(), id);
            Integer isCollection = CollectionUtils.isNotEmpty(c) ? 1 : 0;
            titleMap.put("isCollection", isCollection);
        }else {
            titleMap.put("isCollection", 0);
        }
        result.put("titleMap", titleMap);
        //基础信息+经办依据+办事流程+备注+办结时限(finishingTimeLimit)
        result.put("basicMap", matter);
        //申请材料
        List<MatterAppMaterials> materials = matterAppMaterialsRepository.findByMatterIdAndDelStatusEquals(matter.getId(), 0);
        result.put("materialsMap", materials);
        //常见问题
        List<MatterCommonProblem> commonProblem = commonProblemRepository.findByMatterId(id);
        result.put("commonProblemMap", commonProblem);
        return result;
    }

    @Override
    public List<DeptDto> deptList() {
        return deptService.queryNpAll();
    }

    @Override
    public Object queryMatterByDept(MatterQueryCriteria criteria) {
        criteria.setDelStatus(0);
        criteria.setMatterStatus(5);
        return matterRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));
    }

    @Override
    public Object queryMatterByTheme(MatterQueryCriteria criteria) {
        criteria.setDelStatus(0);
        criteria.setMatterStatus(5);
        List<Matter> all = matterRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));
        List<String> theme = all.stream().map(Matter::getBelongTheme).distinct().collect(Collectors.toList());
        List<Map> mapList = new ArrayList<>(16);
        if(CollectionUtils.isNotEmpty(theme)) {
            for(String t : theme) {
                Map m = new HashMap(16);
                //主题下的事项
                List<Matter> matterList = all.stream().filter(ma -> Objects.equals(ma.getBelongTheme(), t)).collect(Collectors.toList());
                m.put("theme", t);
                m.put("matterList", matterList);
                mapList.add(m);
            }
        }
        return mapList;
    }

    @Override
    public List<Dept> queryDeptNoPage(String key) {
        DeptQueryCriteria criteria = new DeptQueryCriteria();
        criteria.setEnabled(true);
        criteria.setName(key);
        return deptRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));

    }

    @Override
    public Object queryMatterHanleStatics(MatterHandleQueryCriteria criteria) {

        Map map = new HashMap();
        String range = criteria.getTimeRange();
        List<String> xData = new ArrayList();
        //若是all全量范围则只取年份比较
        String dateFormatStr = "all".equals(range) ? "yyyy" : "yyyyMMdd";
        List<MatterHandleStatistics> list = matterHandleStatisticsRepository.findAll((root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if("week".equals(range)) {
                DateTime monday = DateUtil.beginOfWeek(new Date());
                predicates.add(cb.between(root.get("startTime"), monday, new Date()));
                int test = DateUtil.dayOfWeek(new Date());
                for(int i = 0; i < DateUtil.dayOfWeek(new Date()) - 1; i++) {
                    xData.add(DateUtil.format(DateUtil.offsetDay(monday, i), "yyyyMMdd"));
                }
            }else if("month".equals(range)) {
                DateTime firstDayOfMonth = DateUtil.beginOfMonth(new Date());
                for(int i = 0; i < DateUtil.dayOfMonth(new Date()) - 1; i++) {
                    xData.add(DateUtil.format(DateUtil.offsetDay(firstDayOfMonth, i), "yyyyMMdd"));
                }
                predicates.add(cb.between(root.get("startTime"), firstDayOfMonth, new Date()));
            }else if("all".equals(range)) {
                for(int i = 2019; i <= DateUtil.year(new Date()); i++) {
                    xData.add(String.valueOf(i));
                }
            }else if("date".equals(range)) {
                Date startTime = DateUtil.parse(criteria.getStartTime());
                Date endTime = DateUtil.parse(criteria.getEndTime());
                long daySub = DateUtil.betweenDay(startTime, endTime, true);
                for(long i = 0; i <= daySub; i++) {
                    xData.add(DateUtil.format(DateUtil.offsetDay(startTime, (int) i), "yyyyMMdd"));
                }
                predicates.add(cb.between(root.get("startTime"), startTime, endTime));
            }
            return cb.and(predicates.toArray(new Predicate[predicates.size()]));
        });

        Long allYData[] = new Long[xData.size()];
        Long overdueYData[] = new Long[xData.size()];
        Long ontimeYData[] = new Long[xData.size()];
        for(int i = 0; i < xData.size(); i++) {
            String dateStr = xData.get(i);
            allYData[i] = list.stream().filter(matter -> dateStr.equals(DateUtil.format(matter.getStartTime(), dateFormatStr))).count();
            overdueYData[i] = list.stream().filter(matter -> dateStr.equals(DateUtil.format(matter.getEndTime(), dateFormatStr))).filter(matter -> !"INPROGRESS".equals(matter.getStatus())).filter(matter -> null != matter.getIsOverdue()).filter(matter -> 1 == matter.getIsOverdue()).count();
            ontimeYData[i] = allYData[i] - overdueYData[i];
        }
        Long overdueNum = list.stream().filter(matter -> !"INPROGRESS".equals(matter.getStatus())).filter(matter -> null != matter.getIsOverdue()).filter(matter -> 1 == matter.getIsOverdue()).count();
        int allNum = list.size();
        Long ontimeNum = allNum - overdueNum;
        map.put("allYData", allYData);
        map.put("overdueYData", overdueYData);
        map.put("ontimeYData", ontimeYData);
        map.put("overdueNum", overdueNum);
        map.put("ontimeNum", ontimeNum);
        map.put("allNum", allNum);
        map.put("xData", xData);
        map.put("list", list);
        return map;
    }

    @Override
    public Object queryMatterStatics(MatterHandleQueryCriteria criteria) {
        Map map = new HashMap();
        int mainItem = 0;
        int sonItem = 0;
        int onlineItem = 0;
        int ddItem = 0;
        int oneItem = 0;
        int zeroItem = 0;
        List<Matter> matters = matterRepository.findByDelStatusAndMatterStatus(0, 5);

        sonItem += matterRepository.countSonItem();
        onlineItem += matters.stream().filter(matter -> null != matter.getIsInternet()).mapToInt(matter -> matter.getIsInternet()).sum();
        ddItem += matters.stream().filter(matter -> null != matter.getIsDD()).mapToInt(matter -> matter.getIsDD()).sum();
        oneItem += matters.stream().filter(matter -> null != matter.getIsOne()).mapToInt(matter -> matter.getIsOne()).sum();
        zeroItem += matters.stream().filter(matter -> null != matter.getIsZero()).mapToInt(matter -> matter.getIsZero()).sum();
        mainItem += matterRepository.countMainItem();
        map.put("mainItem", mainItem);
        map.put("sonItem", sonItem);
        map.put("onlineItem", onlineItem);
        map.put("ddItem", ddItem);
        map.put("oneItem", oneItem);
        map.put("zeroItem", zeroItem);
        map.put("allItem", matters.size());

        JSONArray pieData = new JSONArray();
        JSONObject obj = new JSONObject();
        obj.put("name", "跑0次");
        obj.put("value", zeroItem);
        pieData.add(obj);
        obj = new JSONObject();
        obj.put("name", "跑1次");
        obj.put("value", oneItem);
        pieData.add(obj);
        obj = new JSONObject();
        obj.put("name", "跑多次");
        obj.put("value", matters.size() - zeroItem - oneItem);
        pieData.add(obj);

        map.put("pieData", pieData.toJSONString());
        return map;
    }

    @Override
    public Object queryDeptList(DeptQueryCriteria criteria) {
        Map map = new HashMap();

        List<Dept> depts = deptRepositoryCustom.findByIsNpOrderByNpDeptSortAsc(true);
      /*  List<Dept> depts = deptRepository.findAll((root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            Predicate predicate = QueryHelp.getPredicate(root, criteria, cb);
            predicates.add(predicate);

            //关联查询
            // Join<Dept,Matter> join =  root.join("matter", JoinType.INNER);
            // predicates.add(cb.equal(join.get("matterName"),"11呜呜呜呜"));
            //query.distinct(true);

            //子查询
            Subquery<Long> subquery = query.subquery(Long.class);
            Root<Matter> from = subquery.from(Matter.class);
            subquery.select(from.get("deptId"));
            subquery.where(cb.isNotNull(from.get("deptId")));

            predicates.add(root.get("id").in(subquery));
            query.orderBy(cb.asc(root.get("deptSort")));
            return cb.and(predicates.toArray(new Predicate[predicates.size()]));
        });*/
        map.put("dept", depts);
        int mainItem = 0;
        int sonItem = 0;
        int onlineItem = 0;
        int ddItem = 0;
        int oneItem = 0;
        int zeroItem = 0;
        for(Dept dept : depts) {
            sonItem += matterRepository.countSonItemByDeptId(dept.getId());
            onlineItem += dept.getMatter().stream().filter(matter -> null != matter.getIsInternet()).mapToInt(matter -> matter.getIsInternet()).sum();
            ddItem += dept.getMatter().stream().filter(matter -> null != matter.getIsDD()).mapToInt(matter -> matter.getIsDD()).sum();
            oneItem += dept.getMatter().stream().filter(matter -> null != matter.getIsOne()).mapToInt(matter -> matter.getIsOne()).sum();
            zeroItem += dept.getMatter().stream().filter(matter -> null != matter.getIsZero()).mapToInt(matter -> matter.getIsZero()).sum();
            mainItem += matterRepository.countMainItemByDeptId(dept.getId());
        }
        map.put("mainItem", mainItem);
        map.put("sonItem", sonItem);
        map.put("onlineItem", onlineItem);
        map.put("ddItem", ddItem);
        map.put("oneItem", oneItem);
        map.put("zeroItem", zeroItem);

        return map;
    }

    @Override
    public List<MatterDto> queryMatterNoPage(String key) {
        MatterQueryCriteria criteria = new MatterQueryCriteria();
        criteria.setDelStatus(0);
        criteria.setMatterStatus(5);
        criteria.setMatterName(key);

        return  matterMapper.toDto(matterRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    public Object regularDep() {
        List<Matter> matterList = getTop5Matter();
        List<DeptSmallDto> deptList=new ArrayList();

        List<Long> addDepts=new ArrayList<>();
        matterList.stream().forEach(matter -> {
            if(addDepts.contains(matter.getDeptId())||deptList.size()>5)return;
            DeptSmallDto dept=new DeptSmallDto();
            dept.setName(matter.getDeptName());
            dept.setId(matter.getDeptId());
            addDepts.add(matter.getDeptId());
            deptList.add(dept);
        });
        return PageUtil.toPage(deptList, deptList.size());
    }

    @Override
    public ResultJson getMatterForm(String id) {
        Map result = new HashMap();
        List<FormTemplate> list = wdFormTemplateRepository.findByClassNameLike("%Matter%");
        if(list.size() >= 1) {
            FormTemplate form = list.stream().filter(item -> item.getTitle().equals("事项库添加表单") || item.getId() == 1063963735932928L).findFirst().orElse(list.get(0));
            result.put("data", form.getFormJson());
        }else if(list.size() == 0) {
            throw new BadRequestException("事项库添加表单模板不存在！");
        }
        if(StringUtils.isNotBlank(id)) {
            Matter matter = matterRepository.findById(Long.valueOf(id)).get();
            result.put("value",  matterMapper.toDto(matter));
        }
        return ResultJson.generateResult(result);
    }

    @Override
    public Map<String, Object> getUserNameAndDept() {
        Map<String, Object> map = new HashMap<>(16);
        map.put("userName", "");
        map.put("deptName", "");
        try {
            Long currentUserId = SecurityUtils.getCurrentUserId();
            String nickName = userService.findById(currentUserId).getNickName();
            DeptSmallDto dept = userService.findById(currentUserId).getDept();
            map.put("userName", nickName);
            map.put("deptName", dept != null ? dept.getName() : "");
            return map;
        }catch(Exception e) {
            return map;
        }
    }


    @Override
    public ResultJson todoList(TaskQueryVo query) {
        ResultJson json = null;
        try {
            //query.setUsername(SecurityUtils.getCurrentUsername());

            json = ResultJson.generateResult(getApplyingTasks(query));
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<FlowTaskDto> page = (com.baomidou.mybatisplus.extension.plugins.pagination.Page) json.getData();

         /*  page.getRecords().stream().filter(flowTaskDto -> {
               flowTaskDto.get
           });*/
            page.getRecords().stream().forEach(record -> {
                MatterSegmentQueryCriteria criteria = new MatterSegmentQueryCriteria().builder()
                        .proKey(record.getProcDefKey()).proNode(record.getTaskDefKey()).build();
                List<MatterSegment> list = matterSegmentRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));
                if(CollectionUtils.isNotEmpty(list)) {
                    int day = Integer.parseInt(list.get(0).getWorkingDay());
                    record.setWorkingDay(day + "工作日");
                    long sub = DateUtil.offsetDay(record.getCreateTime(), day).getTime() - DateUtil.date().getTime();
                    if(sub > 0 && sub <= 24 * 3600 * 1000) {
                        record.setLight("yellow");
                    }else if(sub > 24 * 3600 * 1000) {
                        record.setLight("green");
                    }else {
                        record.setLight("red");
                    }
                    record.setFinishTime(DateUtil.offsetDay(record.getCreateTime(), day));
                }
            });
            json.setData(page);
        }catch(Exception e) {
            e.printStackTrace();
        }
        return json;
    }

    @Override
    public ResultJson myProcessList(TaskQueryVo query) {
     /*   ResultJson json = null;
        try {
            query.setUsername(SecurityUtils.getCurrentUsername());
            json = ResultJson.generateResult(flowTaskService.getApplyingTasks(query));
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<FlowTaskDto> page = (com.baomidou.mybatisplus.extension.plugins.pagination.Page) json.getData();
            page.getRecords().stream().forEach(record -> {
                MatterSegmentQueryCriteria criteria = new MatterSegmentQueryCriteria().builder()
                        .proKey(record.getProcDefKey()).proNode(record.getNowProcNode()).build();
                List<MatterSegment> list = matterSegmentRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));
                if(CollectionUtils.isNotEmpty(list)) {
                    int day = Integer.parseInt(list.get(0).getWorkingDay());
                    record.setWorkingDay(day + "工作日");
                    long sub = DateUtil.offsetDay(record.getCreateTime(), day).getTime() - DateUtil.date().getTime();
                    if(sub > 0 && sub <= 24 * 3600 * 1000) {
                        record.setLight("yellow");
                    }else if(sub > 24 * 3600 * 1000) {
                        record.setLight("green");
                    }else {
                        record.setLight("red");
                    }
                    record.setFinishTime(DateUtil.offsetDay(record.getCreateTime(), day));
                }
            });
            json.setData(page);
        }catch(Exception e) {
            e.printStackTrace();
        }*/
        ResultJson json = ResultJson.generateResult();
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<HistoricProcessInstance> page=new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>();
        try {
            query.setUsername(SecurityUtils.getCurrentUsername());
            HistoricProcessInstanceQuery historyQuery = historyService.createHistoricProcessInstanceQuery().startedBy(String.valueOf(SecurityUtils.getCurrentUserId())).processDefinitionCategory(query.getCategory());
            List<HistoricProcessInstance> historyList = historyQuery.listPage(query.getPage() * query.getSize(), query.getSize());
            page.setTotal(historyQuery.count());
            page.setRecords(historyList);
            json.setData(page);
        }catch(Exception e) {
            e.printStackTrace();
        }
        return json;
    }


    public com.baomidou.mybatisplus.extension.plugins.pagination.Page<FlowTaskDto> getApplyingTasks(TaskQueryVo query) throws Exception {

        TaskQuery taskQuery = taskService.createTaskQuery()
                .active().processVariableExists(ProcessConstants.BPM_BPM_STARTEDBY)
                .includeProcessVariables()
                .orderByTaskCreateTime().desc();

        taskQuery.taskAssigneeLike(SecurityUtils.getCurrentUsername());
        if(org.apache.commons.lang3.StringUtils.isNotEmpty(query.getName())) {
            taskQuery.processDefinitionNameLike(query.getName() + "%");
        }
        if(!org.springframework.util.CollectionUtils.isEmpty(query.getTimeRange())) {
            taskQuery.taskCreatedAfter(DateUtils.parseDate(query.getTimeRange().get(0), DatePattern.NORM_DATETIME_PATTERN));
            taskQuery.taskCreatedBefore(DateUtils.parseDate(query.getTimeRange().get(1), DatePattern.NORM_DATETIME_PATTERN));
        }
        if(org.apache.commons.lang3.StringUtils.isNotEmpty(query.getCategory())) {
            //流程类型不为空查询
            taskQuery.processCategoryIn(Collections.singleton(query.getCategory()));
        }

        if(org.apache.commons.lang3.StringUtils.isNotBlank(query.getProcDefKey())) {
            taskQuery.processDefinitionKey(query.getProcDefKey());
        }


        com.baomidou.mybatisplus.extension.plugins.pagination.Page<FlowTaskDto> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>();
        page.setTotal(taskQuery.count());

        List<Task> taskList = taskQuery.listPage(query.getPage() * query.getSize(), query.getSize());
        List<FlowTaskDto> flowList = new ArrayList<>();
        if(!org.springframework.util.CollectionUtils.isEmpty(taskList)) {
            Set<String> processDefinitionIds = taskList.stream().map(Task::getProcessDefinitionId).collect(Collectors.toSet());
            Map<String, ProcessDefinition> processDefinitionMap = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionIds(processDefinitionIds)
                    .list()
                    .stream().collect(Collectors.toMap(ProcessDefinition::getId, v -> v, (k1, k2) -> k1));

            // 流程发起人信息
            Set<String> processInstanceIds = taskList.stream().map(Task::getProcessInstanceId).collect(Collectors.toSet());
            Map<String, ProcessInstance> processInstanceMap = runtimeService.createProcessInstanceQuery()
                    .processInstanceIds(processInstanceIds)
                    .list()
                    .stream().collect(Collectors.toMap(ProcessInstance::getProcessInstanceId, v -> v, (k1, k2) -> k1));

            for(Task task : taskList) {
                FlowTaskDto flowTask = new FlowTaskDto();
                // 当前流程信息
                flowTask.setTaskId(task.getId());
                flowTask.setTaskDefKey(task.getTaskDefinitionKey());
                // 审批人员信息
                if(StringUtils.isNotEmpty(task.getAssignee())) {
                    UserDto sysUser = sysUserService.findByName(task.getAssignee());
                    FlowableUtils.setAssigneeUserInfo(flowTask, sysUser);
                }
                flowTask.setCreateTime(task.getCreateTime());
                flowTask.setProcDefId(task.getProcessDefinitionId());
                flowTask.setTaskName(task.getName());

                // 流程定义信息
                ProcessDefinition pd = processDefinitionMap.get(task.getProcessDefinitionId());
                if(Objects.nonNull(pd)) {
                    flowTask.setProcDefKey(pd.getKey());
                    flowTask.setProcDefName(pd.getName());
                    flowTask.setProcDefVersion(pd.getVersion());
                    flowTask.setProcInsId(task.getProcessInstanceId());
                }

                ProcessInstance processInstance = processInstanceMap.get(task.getProcessInstanceId());
               if(StringUtils.isNotEmpty(processInstance.getStartUserId())) {
                    UserDto startUser = sysUserService.findById(Long.parseLong(processInstance.getStartUserId()));

                    if(StringUtils.isNotEmpty(query.getUsername())){
                        // 发起人查询
                        if(!startUser.getNickName().equals(query.getUsername())) {
                            continue;
                        }
                    }

                    FlowableUtils.setStartUserInfo(flowTask, startUser);
                }

                Map variables = task.getProcessVariables();
                Object formTitle = variables.get(ProcessConstants.BPM_FORM_TITLE);
                if(Objects.isNull(formTitle)) {
                    formTitle = "";
                }
                flowTask.setFormTitle(formTitle.toString());
                flowList.add(flowTask);
            }
        }
        page.setRecords(flowList);
        return page;
    }
}
