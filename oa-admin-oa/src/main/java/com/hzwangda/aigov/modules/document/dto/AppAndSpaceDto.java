package com.hzwangda.aigov.modules.document.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
@ApiModel(value = "应用和空间返回表")
public class AppAndSpaceDto {

    @ApiModelProperty(value = "空间id")
    private Long spcaeId;

    @ApiModelProperty(value = "空间名称")
    private String spaceName;

    @ApiModelProperty(value = "应用id")
    private Long applicationId;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "管理员")
    private String manager;

    @ApiModelProperty(value = "业务管理员")
    private String businessManager;

    @ApiModelProperty(value = "新建人员")
    private String createManager;


}
