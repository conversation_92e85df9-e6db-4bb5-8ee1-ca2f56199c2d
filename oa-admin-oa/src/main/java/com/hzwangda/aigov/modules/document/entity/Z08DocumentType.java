package com.hzwangda.aigov.modules.document.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.wangda.oa.modules.workflow.domain.common.WFStorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2022/10/27 14:59
 * @description: 文种维护
 */
@Data
@Entity
@Table(name = "z08_document_type")
public class Z08DocumentType extends BaseBpmDomain implements Serializable {

    @ApiModelProperty(value = "文种（代字）")
    @Column(name = "dz")
    private String dz;

    @ApiModelProperty(value = "所属单位")
    @Column(name = "dept_name")
    private String deptName;

    @ApiModelProperty(value = "所属单位Id")
    @Column(name = "dept_id")
    private String deptId;


    @ApiModelProperty(value = "关联应用")
    @Column(name = "application_name")
    private String applicationName;

    @ApiModelProperty(value = "关联应用id")
    @Column(name = "application_id")
    private Long applicationId;


    @ApiModelProperty(value = "附件")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "fj", referencedColumnName = "storage_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='Z08DocumentType.fj'")
    private WFStorageBiz fj;

    @ApiModelProperty(value = "缩略图")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "thumbnail", referencedColumnName = "storage_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='Z08DocumentType.thumbnail'")
    private WFStorageBiz thumbnail;

    @ApiModelProperty(value = "排序")
    @Column(name = "sort")
    private Integer sort;

    @ApiModelProperty(value = "最大文号")
    @Transient
    private String maxXh;

    public void setFj(WFStorageBiz fj) {
        if(Objects.nonNull(fj)) {
            this.fj = fj;
        }
        if(Objects.nonNull(this.fj) && StringUtils.isNotBlank(this.fj.getStorageId())) {
            if(this.id != null) {
                this.fj.setBizId(this.id.toString());
            }
        }else {
            this.fj = null;
        }
    }

    public void setThumbnail(WFStorageBiz thumbnail) {
        if(Objects.nonNull(thumbnail)) {
            this.thumbnail = thumbnail;
        }
        if(Objects.nonNull(this.thumbnail) && StringUtils.isNotBlank(this.thumbnail.getStorageId())) {
            if(this.id != null) {
                this.thumbnail.setBizId(this.id.toString());
            }
        }else {
            this.thumbnail = null;
        }
    }

    @Override
    @PrePersist
    @PreUpdate
    public void preCreateEntity() {
        super.preCreateEntity();
        if(Objects.nonNull(this.fj) && StringUtils.isNotBlank(this.fj.getStorageId())) {
            this.fj.setBizId(this.id.toString());
            this.fj.setBizType("Z08DocumentType.fj");
        }else {
            this.fj = null;
        }
        if(Objects.nonNull(this.thumbnail) && StringUtils.isNotBlank(this.thumbnail.getStorageId())) {
            this.thumbnail.setBizId(this.id.toString());
            this.thumbnail.setBizType("Z08DocumentType.thumbnail");
        }else {
            this.thumbnail = null;
        }
    }

    public void copy(Z08DocumentType documentType) {
        BeanUtil.copyProperties(documentType, this, CopyOptions.create().setIgnoreNullValue(true));
    }

}
