package com.hzwangda.aigov.modules.archive.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FileArchiveCriteria {
    @Query(type = Query.Type.IN)
    private List<String> bpmProcessKey;

    @Query(blurry = "subject,responsiblePerson")
    @ApiModelProperty(value = "搜索内容")
    private String searchKeys;

    @ApiModelProperty(value = "年度")
    @Query(type = Query.Type.EQUAL)
    private String year;

    @ApiModelProperty(value = "档案状态 1预归档,4已归档，-1移交失败")
    @Query(type = Query.Type.EQUAL)
    private Integer status;

    @Query(propName = "createDate", type = Query.Type.GREATER_THAN)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginDate;

    @Query(propName = "createDate", type = Query.Type.LESS_THAN)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;


    @Query(type = Query.Type.IN)
    private List<String> belongToDept;


}
