package com.hzwangda.aigov.modules.duty.service.impl;

import cn.hutool.core.lang.Assert;
import com.hzwangda.aigov.modules.duty.domain.dto.D15DutyLogCriteria;
import com.hzwangda.aigov.modules.duty.domain.entity.D15DutyLog;
import com.hzwangda.aigov.modules.duty.repository.D15DutyLogRepository;
import com.hzwangda.aigov.modules.duty.service.D15DutyLogService;
import com.hzwangda.aigov.oa.repository.WdSysOptionRepository;
import com.hzwangda.aigov.oa.util.SMSFormatUtil;
import com.hzwangda.aigov.zwdd.service.ZwddService;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationBO;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationLinkBO;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.extension.domain.SysUserPlatform;
import com.wangda.oa.modules.extension.domain.WdSysOption;
import com.wangda.oa.modules.extension.repository.SysUserPlatformRepository;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.system.service.DictDetailService;
import com.wangda.oa.modules.system.service.dto.DictDetailDto;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class D15DutyLogServiceImpl implements D15DutyLogService {

    private final static String DICT_NAME = "duty_new";
    @Resource
    private D15DutyLogRepository d15DutyLogRepository;
    @Resource
    private UserRepository userRepository;
    @Resource
    private WdSysOptionRepository wdSysOptionRepository;
    @Resource
    private ZwddService zwddService;
    @Resource
    private SysUserPlatformRepository sysUserPlatformRepository;
    @Resource
    private ZwddProperties zwddProperties;
    @Resource
    private DictDetailService dictDetailService;

    @Override
    public Page<D15DutyLog> queryList(D15DutyLogCriteria criteria, Pageable pageable) {
        Page<D15DutyLog> all = d15DutyLogRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return all;
    }

    @Override
    public Long save(D15DutyLog d15DutyLog) {
        List<DictDetailDto> l = dictDetailService.getDictByName(DICT_NAME);
        if (l.size() > 0) {
            DictDetailDto dictDetail = l.get(0);
            String value = dictDetail.getValue();
            if (Boolean.valueOf(value)) {
                Long d15dutyId = d15DutyLog.getD15dutyId();
                d15DutyLog.setD05dutyId(d15dutyId);
                d15DutyLog.setD15dutyId(null);
            }
        }
        D15DutyLog save = d15DutyLogRepository.save(d15DutyLog);
        // 浙政钉消息
        String oldUsercode = d15DutyLog.getOldUsercode();
        if (StringUtils.isEmpty(oldUsercode)) {
            List<User> list = userRepository.findByNickNameAndEnabled(d15DutyLog.getOldUsername(), true);
            if (list.size() > 0) {
                oldUsercode = list.get(0).getUsername();
            }
        }
        WdSysOption firstByKey = wdSysOptionRepository.getFirstByKey("D15DutyLog.Ding");
        Assert.notNull(firstByKey, "消息模板没有配置!");
        Map map = new HashMap();
        map.put("username", d15DutyLog.getUsername());
        String message = SMSFormatUtil.processTemplate(firstByKey.getValue(), map);

        List<SysUserPlatform> sysUserPlatforms = sysUserPlatformRepository.findByUserNameAndType(oldUsercode, zwddProperties.getType());
        String receiverIds = sysUserPlatforms.stream().map(SysUserPlatform::getPlatformUserId).collect(Collectors.joining(","));
        WorkNotificationBO bo = new WorkNotificationBO();
        bo.setReceiverIds(receiverIds);
        WorkNotificationLinkBO linkBO = new WorkNotificationLinkBO();
        String serviceUrl = zwddProperties.getAppUrl() + "/#/duty/Confirm?id=" + save.getId();
        linkBO.setMessageUrl(serviceUrl);//todo 地址需要改
        linkBO.setText(message);
        linkBO.setTitle(message);
        bo.setType(1);
        bo.setLinkBO(linkBO);
        zwddService.workNotification(bo);
        return save.getId();
    }

    @Override
    public Long confirm(D15DutyLog d15DutyLog) {
        d15DutyLogRepository.save(d15DutyLog);
        String message = d15DutyLog.getOldUsername() + (d15DutyLog.getType() == 1 ? "同意" : "拒绝") + "了您的交班申请";
        String oldUsercode = d15DutyLog.getOldUsercode();
        List<SysUserPlatform> sysUserPlatforms = sysUserPlatformRepository.findByUserNameAndType(oldUsercode, zwddProperties.getType());
        String receiverIds = sysUserPlatforms.stream().map(SysUserPlatform::getPlatformUserId).collect(Collectors.joining(","));
        WorkNotificationBO bo = new WorkNotificationBO();
        bo.setReceiverIds(receiverIds);
        WorkNotificationLinkBO linkBO = new WorkNotificationLinkBO();
        linkBO.setText(message);
        linkBO.setTitle(message);//todo 标题？
        bo.setType(1);
        bo.setLinkBO(linkBO);
        zwddService.workNotification(bo);
        return d15DutyLog.getId();
    }

    @Override
    public D15DutyLog queryById(Long id) {
        return d15DutyLogRepository.findById(id).orElseThrow(() -> new BadRequestException("未找到对应数据"));
    }

    @Override
    public String delete(Long id) {
        d15DutyLogRepository.deleteById(id);
        return "ok";
    }
}
