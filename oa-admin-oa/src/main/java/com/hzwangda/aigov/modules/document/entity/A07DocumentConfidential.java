package com.hzwangda.aigov.modules.document.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.wangda.boot.platform.idWorker.IdWorker;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.PrePersist;
import javax.persistence.Table;
import java.util.Date;

/**
 * @description: 机要件流转通知
 * @author: maogy
 * @create: 2021-08-24 10:42
 **/
@Getter
@Setter
@Entity
@Table(name = "a07_document_confidential")
public class A07DocumentConfidential extends BaseBpmDomain {

    @ApiModelProperty(value = "来文单位")
    private String dept;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @ApiModelProperty(value = "来文日期")
    private Date fromDate;

    @ApiModelProperty(value = "内部编号")
    private String number;

    @ApiModelProperty(value = "办理情况(描述)")
    private String details;

    @ApiModelProperty(value = "办理期限")
    @JSONField(format = "yyyy-MM-dd")
    private Date endDate;

    @ApiModelProperty(value = "办件类型:办件、阅件、其他")
    private String fileType;

    public void copy(A07DocumentConfidential a07DocumentConfidential) {
        BeanUtil.copyProperties(a07DocumentConfidential, this, CopyOptions.create().setIgnoreNullValue(true));
    }

    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }


}
