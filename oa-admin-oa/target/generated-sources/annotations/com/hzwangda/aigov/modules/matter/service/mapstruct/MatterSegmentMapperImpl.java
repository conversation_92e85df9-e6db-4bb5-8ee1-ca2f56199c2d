package com.hzwangda.aigov.modules.matter.service.mapstruct;

import com.hzwangda.aigov.modules.matter.domain.MatterSegment;
import com.hzwangda.aigov.modules.matter.service.dto.MatterSegmentDto;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class MatterSegmentMapperImpl implements MatterSegmentMapper {

    @Override
    public MatterSegmentDto toDto(MatterSegment entity) {
        if ( entity == null ) {
            return null;
        }

        MatterSegmentDto matterSegmentDto = new MatterSegmentDto();

        matterSegmentDto.setCreateBy( entity.getCreateBy() );
        matterSegmentDto.setCommitmentPeriod( entity.getCommitmentPeriod() );
        if ( entity.getCreateDate() != null ) {
            matterSegmentDto.setCreateDate( new Timestamp( entity.getCreateDate().getTime() ) );
        }
        matterSegmentDto.setCreatorId( entity.getCreatorId() );
        matterSegmentDto.setEnabled( entity.getEnabled() );
        matterSegmentDto.setId( entity.getId() );
        matterSegmentDto.setJobName( entity.getJobName() );
        matterSegmentDto.setJobUserCode( entity.getJobUserCode() );
        matterSegmentDto.setJobUserName( entity.getJobUserName() );
        if ( entity.getMatterId() != null ) {
            matterSegmentDto.setMatterId( String.valueOf( entity.getMatterId() ) );
        }
        if ( entity.getModifiedDate() != null ) {
            matterSegmentDto.setModifiedDate( new Timestamp( entity.getModifiedDate().getTime() ) );
        }
        matterSegmentDto.setModifiedId( entity.getModifiedId() );
        matterSegmentDto.setProKey( entity.getProKey() );
        matterSegmentDto.setProNode( entity.getProNode() );
        matterSegmentDto.setVersion( entity.getVersion() );
        matterSegmentDto.setWorkingDay( entity.getWorkingDay() );

        return matterSegmentDto;
    }

    @Override
    public List<MatterSegmentDto> toDto(List<MatterSegment> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MatterSegmentDto> list = new ArrayList<MatterSegmentDto>( entityList.size() );
        for ( MatterSegment matterSegment : entityList ) {
            list.add( toDto( matterSegment ) );
        }

        return list;
    }

    @Override
    public MatterSegment toEntity(MatterSegmentDto dto) {
        if ( dto == null ) {
            return null;
        }

        MatterSegment matterSegment = new MatterSegment();

        matterSegment.setCreateBy( dto.getCreateBy() );
        matterSegment.setCreateDate( dto.getCreateDate() );
        matterSegment.setCreatorId( dto.getCreatorId() );
        matterSegment.setEnabled( dto.getEnabled() );
        matterSegment.setId( dto.getId() );
        matterSegment.setModifiedDate( dto.getModifiedDate() );
        matterSegment.setModifiedId( dto.getModifiedId() );
        matterSegment.setVersion( dto.getVersion() );
        matterSegment.setCommitmentPeriod( dto.getCommitmentPeriod() );
        matterSegment.setJobName( dto.getJobName() );
        matterSegment.setJobUserCode( dto.getJobUserCode() );
        matterSegment.setJobUserName( dto.getJobUserName() );
        if ( dto.getMatterId() != null ) {
            matterSegment.setMatterId( Long.parseLong( dto.getMatterId() ) );
        }
        matterSegment.setProKey( dto.getProKey() );
        matterSegment.setProNode( dto.getProNode() );
        matterSegment.setWorkingDay( dto.getWorkingDay() );

        return matterSegment;
    }

    @Override
    public List<MatterSegment> toEntity(List<MatterSegmentDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MatterSegment> list = new ArrayList<MatterSegment>( dtoList.size() );
        for ( MatterSegmentDto matterSegmentDto : dtoList ) {
            list.add( toEntity( matterSegmentDto ) );
        }

        return list;
    }
}
