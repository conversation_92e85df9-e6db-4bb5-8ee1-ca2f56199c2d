package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07DocExchangeSignDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlzUser;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A07DocExchangeSignMapperImpl implements A07DocExchangeSignMapper {

    @Override
    public A07DocExchangeSignDto toDto(A07DocumentGwlzUser entity) {
        if ( entity == null ) {
            return null;
        }

        A07DocExchangeSignDto a07DocExchangeSignDto = new A07DocExchangeSignDto();

        if ( entity.getCyrq() != null ) {
            a07DocExchangeSignDto.setCyrq( new Timestamp( entity.getCyrq().getTime() ) );
        }
        a07DocExchangeSignDto.setCyzt( entity.getCyzt() );
        a07DocExchangeSignDto.setQsr( entity.getQsr() );
        if ( entity.getQsrq() != null ) {
            a07DocExchangeSignDto.setQsrq( new Timestamp( entity.getQsrq().getTime() ) );
        }
        a07DocExchangeSignDto.setQszt( entity.getQszt() );
        a07DocExchangeSignDto.setUsername( entity.getUsername() );

        return a07DocExchangeSignDto;
    }

    @Override
    public List<A07DocExchangeSignDto> toDto(List<A07DocumentGwlzUser> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A07DocExchangeSignDto> list = new ArrayList<A07DocExchangeSignDto>( entityList.size() );
        for ( A07DocumentGwlzUser a07DocumentGwlzUser : entityList ) {
            list.add( toDto( a07DocumentGwlzUser ) );
        }

        return list;
    }

    @Override
    public A07DocumentGwlzUser toEntity(A07DocExchangeSignDto dto) {
        if ( dto == null ) {
            return null;
        }

        A07DocumentGwlzUser a07DocumentGwlzUser = new A07DocumentGwlzUser();

        a07DocumentGwlzUser.setCyrq( dto.getCyrq() );
        a07DocumentGwlzUser.setCyzt( dto.getCyzt() );
        a07DocumentGwlzUser.setQsr( dto.getQsr() );
        a07DocumentGwlzUser.setQsrq( dto.getQsrq() );
        a07DocumentGwlzUser.setQszt( dto.getQszt() );
        a07DocumentGwlzUser.setUsername( dto.getUsername() );

        return a07DocumentGwlzUser;
    }

    @Override
    public List<A07DocumentGwlzUser> toEntity(List<A07DocExchangeSignDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A07DocumentGwlzUser> list = new ArrayList<A07DocumentGwlzUser>( dtoList.size() );
        for ( A07DocExchangeSignDto a07DocExchangeSignDto : dtoList ) {
            list.add( toEntity( a07DocExchangeSignDto ) );
        }

        return list;
    }
}
