package com.hzwangda.aigov.modules.favorite.controller;

import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.GoodsReceive.dto.GoodsCriteria;
import com.hzwangda.aigov.modules.favorite.domain.Favorite;
import com.hzwangda.aigov.modules.favorite.service.FavoriteService;
import com.hzwangda.aigov.oa.task.DingUserAndDeptTask;
import com.wangda.oa.annotation.Log;
import com.wangda.oa.annotation.rest.AnonymousGetMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;

@RestController
@RequestMapping("/favorite")
@Api(tags = "收藏/关注")
public class FavoriteController {
    @Resource
    private FavoriteService favoriteService;
    Logger logger = LoggerFactory.getLogger(FavoriteController.class);

    @ApiOperation(value = "浙政钉组件关注收藏列表")
    @AnonymousGetMapping("/scgz/getList")
    public ResponseEntity queryAttentionAndCollectList(@RequestParam(required = false) String accountid) {
        logger.info("浙政钉组件关注收藏列表,当前访问人的accountid：{}",accountid);
        HashMap map=new HashMap();
        map.put("success",true);
        JSONObject content=new JSONObject();
        JSONObject cardData=new JSONObject();
        cardData.put("attentionList", favoriteService.list(0,accountid));
        cardData.put("collectList",favoriteService.list(1,accountid));
        cardData.put("moreUrl","https://ding.nanxun.gov.cn:806/newdd/#/followCollect");
        content.put("cardData",cardData);
        map.put("content",content);
        return ResponseEntity.ok(map);
    }

    @ApiOperation("收藏/关注")
    @PostMapping("/save")
    public ResponseEntity<Long> save(@RequestBody Favorite favorite) {
        return ResponseEntity.ok(favoriteService.save(favorite));
    }

    @ApiOperation("取消收藏/关注")
    @PostMapping("/del")
    public ResponseEntity<Long> del(@RequestBody Favorite favorite) {
        return ResponseEntity.ok(favoriteService.del(favorite));
    }

    @ApiOperation("收藏/关注列表")
    @GetMapping("/list")
    public ResponseEntity list(@RequestParam Integer type,@RequestParam(required = false) String accountid) {
        return ResponseEntity.ok(favoriteService.list(type,accountid));
    }

    @ApiOperation("收藏/关注列表[分页]")
    @GetMapping("/page")
    public ResponseEntity page(@RequestParam Integer type,@RequestParam(required = false) String accountid, Pageable pageable) {
        return ResponseEntity.ok(favoriteService.page(type,accountid, pageable));
    }

    @ApiOperation("检查是否收藏、关注")
    @GetMapping("/check")
    public ResponseEntity check(@RequestParam String procInstanceId) {
        return ResponseEntity.ok(favoriteService.check(procInstanceId));
    }
}
