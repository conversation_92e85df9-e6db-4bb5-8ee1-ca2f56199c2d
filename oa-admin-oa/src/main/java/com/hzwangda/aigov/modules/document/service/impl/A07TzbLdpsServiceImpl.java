package com.hzwangda.aigov.modules.document.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzwangda.aigov.modules.document.constant.TzbLdpsTrainProperties;
import com.hzwangda.aigov.modules.document.dto.tzb.A07LdpsQueryDto;
import com.hzwangda.aigov.modules.document.dto.tzb.A07LdpsReceiveDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentLdps;
import com.hzwangda.aigov.modules.document.repository.A07DocumentLdpsRepository;
import com.hzwangda.aigov.modules.document.service.A07TzbLdpsService;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.hzwangda.aigov.oa.util.OaUtil;
import com.taiyu.typrecenter.util.Md5Util;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.boot.platform.enums.ResultCodeEnum;
import com.wangda.oa.config.FileProperties;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.security.security.CASAuthToken;
import com.wangda.oa.modules.security.security.TokenProvider;
import com.wangda.oa.modules.security.service.OnlineUserService;
import com.wangda.oa.modules.security.service.dto.JwtUserDto;
import com.wangda.oa.modules.security.service.dto.SysAppLoginDto;
import com.wangda.oa.modules.system.utils.SystemConstant;
import com.wangda.oa.modules.workflow.bo.SubmitFormBO;
import com.wangda.oa.modules.workflow.domain.form.FormTemplate;
import com.wangda.oa.modules.workflow.dto.application.AppSpaceNameDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowProcessInfoDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskAssigneeDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskVariablesDto;
import com.wangda.oa.modules.workflow.repository.application.ApplicationRepository;
import com.wangda.oa.modules.workflow.repository.form.WdFormTemplateRepository;
import com.wangda.oa.modules.workflow.service.form.FormTemplateService;
import com.wangda.oa.modules.workflow.service.workflow.FlowDefinitionService;
import com.wangda.oa.modules.workflow.service.workflow.FlowInstanceService;
import com.wangda.oa.repository.LocalStorageRepository;
import com.wangda.oa.service.IStorageService;
import com.wangda.oa.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
@RequiredArgsConstructor
public class A07TzbLdpsServiceImpl implements A07TzbLdpsService {

    private final FileProperties fileProperties;
    private final TzbLdpsTrainProperties tzbLdpsTrain;
    private final A07DocumentLdpsRepository a07DocumentLdpsRepository;
    private final IStorageService storageService;
    private final FlowInstanceService flowInstanceService;
    private final LocalStorageRepository localStorageRepository;
    private final FlowDefinitionService flowDefinitionService;
    private final WdFormTemplateRepository wdFormTemplateRepository;
    private final ApplicationRepository applicationRepository;
    private final OnlineUserService onlineUserService;
    private final TokenProvider tokenProvider;
    private final AuthenticationManagerBuilder authenticationManagerBuilder;
    private final ZwddProperties zwddProperties;

    @Override
    public ResultJson<Object> receive(String token, String dataJson, HttpServletRequest request) {

        A07LdpsReceiveDto a07LdpsReceiveDto = JSONObject.parseObject(dataJson, A07LdpsReceiveDto.class);
        if(!tokenMatch(token, a07LdpsReceiveDto.getDate())) {
            return ResultJson.generateResult("token不正确");
        }
        // 创建基础临时存储目录
        String baseTempPath = fileProperties.getPath().getPath() + "tzbLdps" + File.separator + DateUtil.format(new Date(), DatePattern.SIMPLE_MONTH_PATTERN) + File.separator;
        FileUtil.mkdir(baseTempPath);
        List<StorageBiz> attachs = new ArrayList<>();
        AtomicInteger k = new AtomicInteger();
        a07LdpsReceiveDto.getAttachs().stream().forEach(attach -> {
            String tempFilePath = baseTempPath + attach.getAttName();
            HttpUtil.downloadFile(attach.getAttUrl().replaceAll("https", "http"), tempFilePath);
            File tempFile = new File(tempFilePath);
            LocalStorage storage = storageService.create(attach.getAttName(), tempFile);
            StorageBiz fj = new StorageBiz();
            fj.setStorageId(String.valueOf(storage.getId()));
            fj.setBizType("A07DocumentLdps.psj");
            fj.setSorted(k.getAndIncrement());
            attachs.add(fj);
            log.info("文件下载成功");
        });
        A07DocumentLdps ldps = A07DocumentLdps.builder()
                .ldpsnr(a07LdpsReceiveDto.getFormData().getContent())
                .bt(a07LdpsReceiveDto.getFormData().getTitle())
                .yjDwjnr(a07LdpsReceiveDto.getFormData().getPushUnit())
                .psj(attachs)
                .build();
        if(Objects.nonNull(a07LdpsReceiveDto.getZw())) {
            A07LdpsReceiveDto.Zw zw = a07LdpsReceiveDto.getZw();
            String tempFilePath = baseTempPath + zw.getFileName();
            long result = HttpUtil.downloadFile(zw.getFileUrl().replaceAll("https", "http"), tempFilePath);
            File tempFile = new File(tempFilePath);
            LocalStorage storage = storageService.create(zw.getFileName(), tempFile);
            StorageBiz zwBiz = new StorageBiz();
            zwBiz.setStorageId(String.valueOf(storage.getId()));
            zwBiz.setBizType("A07DocumentLdps.zwfj");
            zwBiz.setSorted(0);
            ldps.setZwfj(Arrays.asList(zwBiz));
        }

        Long formTemplateId=null;
        Long appid=null;
        List<FormTemplate> formList = wdFormTemplateRepository.findByClassNameLike("%A07DocumentLdps%");
        if(formList.size()>0){
            formTemplateId=formList.get(0).getId();
        }else{
            return ResultJson.generateResult("未建立批示件表单", ResultCodeEnum.DATA_NOT_FOUND);
        }

        List<AppSpaceNameDto> applicationList = applicationRepository.findByTemplateId(formTemplateId);
        if(applicationList.size()>0){
            appid=applicationList.get(0).getId();
        }else{
            return ResultJson.generateResult("未建立批示件相关应用", ResultCodeEnum.DATA_NOT_FOUND);
        }


        // 以超级管理员模拟登录
        CASAuthToken authenticationToken = new CASAuthToken("superAdmin");
        Authentication authentication = authenticationManagerBuilder.getObject().authenticate(authenticationToken);
        SecurityContextHolder.getContext().setAuthentication(authentication);
        // 生成令牌
        String oaToken = tokenProvider.createToken(authentication);
        final JwtUserDto jwtUserDto = (JwtUserDto) authentication.getPrincipal();
        // 保存在线信息
        onlineUserService.saveAndSetTime(jwtUserDto, oaToken, request, SystemConstant.DATA_TOKEN_OVER_DUETIME_ONE);

        //根据appid启动流程
        ResultJson resultJson = flowDefinitionService.startProcessInstanceByAppId(appid, null);
        JSONObject data = JSONObject.parseObject(String.valueOf(resultJson.getData()).replaceAll("=",":"));

        SubmitFormBO submitFormBO=new SubmitFormBO();
        String str = JSONObject.toJSONString(ldps);
        submitFormBO.setFormTemplateId(formTemplateId);
        submitFormBO.setFormDataJson(str);
        submitFormBO.setProcessInstanceId(data.getString("processInstanceId"));
        submitFormBO.setTaskId(data.getString("taskId"));

        //String.valueOf(a07DocumentLdpsRepository.save(ldps).getId())
        //提交并保存草稿
        Boolean sendFlag = OaUtil.sendFormSubmit(submitFormBO,oaToken);
        if(!sendFlag){
            return ResultJson.generateResult("同步批示件失败", ResultCodeEnum.DATA_IS_WRONG);
        }
        return ResultJson.generateResult("",data.getString("processInstanceId") );
    }

    @Override
    public ResultJson<Object> query(String token, String dataJson, HttpServletRequest request) {
        JSONObject data = JSONObject.parseObject(dataJson);

        if(!tokenMatch(token, data.getString("date"))) {
            return ResultJson.generateResult("token不正确");
        }
        List<A07LdpsQueryDto> dtos = new ArrayList<>();
        A07DocumentLdps ldps = a07DocumentLdpsRepository.findFirstByBpmInstanceId(data.getString("wfEntityId"));
        if(Objects.isNull(ldps)){
         return ResultJson.generateResult("未查询到相关批示件", ResultCodeEnum.DATA_NOT_FOUND);
        }
        List<String> attUrls = new ArrayList<>();
        List<String> attNames = new ArrayList<>();
        if(CollUtil.isNotEmpty(ldps.getZwfj())) {
            LocalStorage storage = localStorageRepository.findById(new Long(ldps.getZwfj().get(0).getStorageId())).orElseGet(LocalStorage::new);
            attUrls.add("http://10.21.122.27:8001/oa-service/api/localStorage/downloadFile/" + ldps.getZwfj().get(0).getStorageId());
            attNames.add(storage.getName());
        }
        if(Objects.nonNull(ldps.getPsj())) {
            ldps.getPsj().stream().forEach(storageBiz -> {
                LocalStorage storage = localStorageRepository.findById(new Long(storageBiz.getStorageId())).orElseGet(LocalStorage::new);
                attUrls.add("http://10.21.122.27:8001/oa-service/api/localStorage/downloadFile/" + storageBiz.getStorageId());
                attNames.add(storage.getName());
            });
        }
        if(StringUtils.isNotEmpty(ldps.getBpmInstanceId())) {
            // 以超级管理员模拟登录
            CASAuthToken authenticationToken = new CASAuthToken("superAdmin");
            Authentication authentication = authenticationManagerBuilder.getObject().authenticate(authenticationToken);
            SecurityContextHolder.getContext().setAuthentication(authentication);
            // 生成令牌
            String oaToken = tokenProvider.createToken(authentication);
            final JwtUserDto jwtUserDto = (JwtUserDto) authentication.getPrincipal();
            // 保存在线信息
            onlineUserService.saveAndSetTime(jwtUserDto, oaToken, request, SystemConstant.DATA_TOKEN_OVER_DUETIME_ONE);

            ResultJson recordsJson = flowInstanceService.flowRecord(ldps.getBpmInstanceId(), null);
            FlowTaskVariablesDto flowTaskVariablesDto=(FlowTaskVariablesDto)recordsJson.getData();
            List<FlowTaskDto> list = flowTaskVariablesDto.getBpmData();
            list.stream().forEach(flowTaskAssigneeDto -> {
                A07LdpsQueryDto dto = A07LdpsQueryDto.builder()
                        .createTime(DateUtil.format(flowTaskAssigneeDto.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN))
                        .status(Objects.isNull(flowTaskAssigneeDto.getFinishTime())?flowTaskAssigneeDto.getTaskDefKey().equals("usertask01")?"NOT_ENTER": "DOING" : "FINISH")
                        .userName(flowTaskAssigneeDto.getAssigneeInfo().getAssigneeName())
                        .unitName("湖州市南浔区统战部")
                        .attUrl(StringUtils.join(attUrls, ","))
                        .attName(StringUtils.join(attNames, ","))
                        .opinion(Objects.isNull(flowTaskAssigneeDto.getComment())?"":flowTaskAssigneeDto.getComment().getComment())
                        .build();
                dtos.add(dto);
            });
        }/*else {
            A07LdpsQueryDto dto = A07LdpsQueryDto.builder()
                    .createTime(DateUtil.format(ldps.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN))
                    //  .createTime(flowTaskAssigneeDto.)
                    .status("NOT_ENTER")
                    .userName("曹旭龙")
                    .unitName("湖州市南浔区统战部")
                    .attUrl(StringUtils.join(attUrls, ","))
                    .attName(StringUtils.join(attNames, ","))
                    .build();
            dtos.add(dto);
        }*/
        return ResultJson.generateResult(dtos);
    }


    protected boolean tokenMatch(String token, String date) {
        String localToken = Md5Util.getMD5String(tzbLdpsTrain.getCode() + tzbLdpsTrain.getSecretKey() + date);
        return token.equals(localToken);
    }
}
