package com.hzwangda.aigov.modules.appcenter.domain.criteria;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: zhangzhanlong
 * @date: 2022/11/4 15:16
 * @description:
 */
@Data
public class Z08AppCenterCriteria {
    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "类型空为查询全部，1自选服务")
    private Integer type;

    @ApiModelProperty(value = "查询类型")
    private List<String> value;
}
