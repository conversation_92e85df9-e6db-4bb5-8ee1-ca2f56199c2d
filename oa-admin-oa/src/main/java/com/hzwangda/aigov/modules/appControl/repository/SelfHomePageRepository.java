package com.hzwangda.aigov.modules.appControl.repository;

import com.hzwangda.aigov.modules.appControl.domain.Z08SelfHomePage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


public interface SelfHomePageRepository extends JpaRepository<Z08SelfHomePage, Long>, JpaSpecificationExecutor<Z08SelfHomePage> {

    /**
     * 根据名称模糊查询
     */
    List<Z08SelfHomePage> findByNameLike(String name);

    @Query(value = "select * from z08_self_home_page where app_status=:appStatus and system_type=:systemType", nativeQuery = true)
    List<Z08SelfHomePage> findByAppStatus(String appStatus,String systemType);


    List<Z08SelfHomePage> findByType(String type);
}
