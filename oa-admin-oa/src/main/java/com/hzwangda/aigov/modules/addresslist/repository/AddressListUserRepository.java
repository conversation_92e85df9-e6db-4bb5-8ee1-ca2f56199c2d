package com.hzwangda.aigov.modules.addresslist.repository;


import com.blinkfox.fenix.jpa.QueryFenix;
import com.wangda.oa.modules.extension.dto.UserDataDto;
import com.wangda.oa.modules.system.domain.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AddressListUserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {
    @QueryFenix(resultType = UserDataDto.class)
    List<UserDataDto> getUserListByDeptId(List<Long> collect, String name);
}
