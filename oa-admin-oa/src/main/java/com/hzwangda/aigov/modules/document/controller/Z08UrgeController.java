package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.service.Z08UrgeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2022/10/26 9:35
 * @description:
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "公文催办")
@RequestMapping("/api/aigov/urge")
@CrossOrigin
public class Z08UrgeController {
    private final Z08UrgeService urgeService;

    @ApiOperation("催办人员查询")
    @GetMapping(value = "/getUrgeUserList")
    public ResponseEntity<Map<String, Object>> getUrgeUserList(@RequestParam("procInstId") String procInstId) {
        return new ResponseEntity<>(urgeService.getUrgeUserList(procInstId), HttpStatus.OK);
    }
}
