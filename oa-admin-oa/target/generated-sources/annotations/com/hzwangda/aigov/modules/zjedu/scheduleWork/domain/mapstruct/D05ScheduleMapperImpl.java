package com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.dto.D05ScheduleDto;
import com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.entity.D05Schedule;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class D05ScheduleMapperImpl implements D05ScheduleMapper {

    @Override
    public D05ScheduleDto toDto(D05Schedule entity) {
        if ( entity == null ) {
            return null;
        }

        D05ScheduleDto d05ScheduleDto = new D05ScheduleDto();

        d05ScheduleDto.setCreateBy( entity.getCreateBy() );
        d05ScheduleDto.setCreateTime( entity.getCreateTime() );
        d05ScheduleDto.setUpdateTime( entity.getUpdateTime() );
        d05ScheduleDto.setUpdatedBy( entity.getUpdatedBy() );
        d05ScheduleDto.setDepartment( entity.getDepartment() );
        d05ScheduleDto.setEndTime( entity.getEndTime() );
        d05ScheduleDto.setId( entity.getId() );
        d05ScheduleDto.setIsNextDay( entity.getIsNextDay() );
        d05ScheduleDto.setScheduleOreder( entity.getScheduleOreder() );
        d05ScheduleDto.setStartTime( entity.getStartTime() );
        d05ScheduleDto.setUseDate( entity.getUseDate() );

        return d05ScheduleDto;
    }

    @Override
    public List<D05ScheduleDto> toDto(List<D05Schedule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<D05ScheduleDto> list = new ArrayList<D05ScheduleDto>( entityList.size() );
        for ( D05Schedule d05Schedule : entityList ) {
            list.add( toDto( d05Schedule ) );
        }

        return list;
    }

    @Override
    public D05Schedule toEntity(D05ScheduleDto dto) {
        if ( dto == null ) {
            return null;
        }

        D05Schedule d05Schedule = new D05Schedule();

        d05Schedule.setCreateBy( dto.getCreateBy() );
        d05Schedule.setCreateTime( dto.getCreateTime() );
        d05Schedule.setUpdateTime( dto.getUpdateTime() );
        d05Schedule.setUpdatedBy( dto.getUpdatedBy() );
        d05Schedule.setDepartment( dto.getDepartment() );
        d05Schedule.setEndTime( dto.getEndTime() );
        d05Schedule.setId( dto.getId() );
        d05Schedule.setIsNextDay( dto.getIsNextDay() );
        d05Schedule.setScheduleOreder( dto.getScheduleOreder() );
        d05Schedule.setStartTime( dto.getStartTime() );
        d05Schedule.setUseDate( dto.getUseDate() );

        return d05Schedule;
    }

    @Override
    public List<D05Schedule> toEntity(List<D05ScheduleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<D05Schedule> list = new ArrayList<D05Schedule>( dtoList.size() );
        for ( D05ScheduleDto d05ScheduleDto : dtoList ) {
            list.add( toEntity( d05ScheduleDto ) );
        }

        return list;
    }
}
