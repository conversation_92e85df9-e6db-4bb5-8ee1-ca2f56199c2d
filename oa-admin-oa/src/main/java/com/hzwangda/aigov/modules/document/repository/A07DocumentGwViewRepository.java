package com.hzwangda.aigov.modules.document.repository;

import com.hzwangda.aigov.modules.document.entity.A07DocumentGwView;
import com.wangda.oa.modules.workflow.domain.application.Application;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface A07DocumentGwViewRepository extends JpaRepository<A07DocumentGwView, Long>, JpaSpecificationExecutor<A07DocumentGwView> {

    @Query("select wa as name from AppSpace was inner join AppPermission wap on was.id=wap.spaceId inner join Application wa on wap.applicationId=wa.id where wap.spaceId in(:spaceId) and  wa.type=:gwType and wa.rev=(select max(rev) from Application where appKey=wa.appKey )")
    List<Application> getGwClassify(List<Long> spaceId, String gwType);

    A07DocumentGwView findByBpmInstanceId(String procInstId);
}
