package com.hzwangda.aigov.modules.document.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 领导批示办理单
 *
 * <AUTHOR>
 * @date 2021/3/19上午11:29
 */
@Data
public class A07DocumentLdpsDto extends BaseBpmDomain {

    @ApiModelProperty(value = "文号")
    private ReferenceNumber gwwh;

    @ApiModelProperty(value = "缓急")
    private String hj;

    @ApiModelProperty(value = "批示序号")
    private String psxh;

    @ApiModelProperty(value = "收文日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date swqr;

    @ApiModelProperty(value = "原件单位及内容")
    private String yjDwjnr;

    @ApiModelProperty(value = "需要反馈")
    private String xyfk;

    @ApiModelProperty(value = "反馈日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date fkrq;

    @ApiModelProperty(value = "领导批示内容")
    private String ldpsnr;

    @ApiModelProperty(value = "批示件")
    private List<StorageBizDto> psj;

    @ApiModelProperty(value = "办公室拟办意见")
    private String yjBgsnb;

    @ApiModelProperty(value = "厅领导批示意见")
    private String yjTldps;

    @ApiModelProperty(value = "办理情况")
    private String yjBlqk;

    @ApiModelProperty(value = "办理结果")
    private String bljg;

    @ApiModelProperty(value = "备注")
    private String bz;

    @ApiModelProperty(value = "联系人")
    private String lxr;

    @ApiModelProperty(value = "联系人电话")
    private String lxrdh;

    @ApiModelProperty(value = "流程任务相关信息")
    private FlowTaskInfoDto flowTaskInfoDto;
}
