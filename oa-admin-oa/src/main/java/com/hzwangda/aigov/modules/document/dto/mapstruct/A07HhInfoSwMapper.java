package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07HhInfoListSwDto;
import com.hzwangda.aigov.modules.document.entity.A07MeetingSea;
import com.wangda.oa.base.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", uses = A07DocumentMapperUtil.class, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface A07HhInfoSwMapper extends BaseMapper<A07HhInfoListSwDto, A07MeetingSea> {


}
