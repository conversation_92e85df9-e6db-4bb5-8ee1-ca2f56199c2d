package com.hzwangda.aigov.modules.document.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.hzwangda.aigov.modules.document.constant.DocumentConstant;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwDto;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwQueryCriteria;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwView;
import com.hzwangda.aigov.modules.document.repository.A07DocumentGwViewRepository;
import com.hzwangda.aigov.modules.document.service.A01ReadDocService;
import com.hzwangda.aigov.modules.workflow.enums.workflow.WorkflowType;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.workflow.domain.workflow.BpmTaskUserRead;
import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import com.wangda.oa.modules.workflow.repository.workflow.BpmTaskUserReadRepository;
import com.wangda.oa.modules.workflow.utils.FlowableUtils;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.persistence.criteria.Predicate;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class A01ReadDocServiceImpl implements A01ReadDocService {

    private final A07DocumentGwViewRepository gwViewRepository;

    private final UserRepository userRepository;

    private final BpmTaskUserReadRepository readRepository;

    @Override
    public Object getFwReadList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        Specification<BpmTaskUserRead> sp = (root, cq, cb) -> {
            Predicate pe = cb.equal(root.get("assignee"), SecurityUtils.getCurrentUsername());
            if(criteria.getStatus() != null) {
                if(criteria.getStatus() == 0) {
                    pe = cb.and(pe, cb.isNull(root.get("firstReadTime")));
                }else if(criteria.getStatus() == 1) {
                    pe = cb.and(pe, cb.isNotNull(root.get("firstReadTime")));
                }
            }
            if(StringUtils.isNotBlank(criteria.getBt())) {
                pe = cb.and(pe, cb.like(root.get("bt"), "%" + criteria.getBt() + "%"));
            }
            if(StringUtils.isNotBlank(criteria.getYear())) {
                pe = cb.and(pe, cb.like(root.get("createDate").as(String.class), "%" + criteria.getYear() + "%"));
            }
            if(StringUtils.isNotBlank(criteria.getProcessDefinitionKey())) {
                pe = cb.and(pe, cb.equal(root.get("processDefKey"), criteria.getProcessDefinitionKey()));
            }else {
                pe = cb.and(pe, root.get("processDefKey").in(DocumentConstant.PROCESS_DEFINITION_KEY_FW_LIST));
            }
            cq.orderBy(cb.desc(root.get("createDate")));
            return pe;
        };
        Page<BpmTaskUserRead> reads = readRepository.findAll(sp, pageable);
        List<BpmTaskUserRead> readList = reads.getContent();
        if(CollectionUtils.isEmpty(readList)) {
            return PageUtil.toPage(Page.empty());
        }
        Map<String, Date> bpmInstanceIdMap = new LinkedHashMap<>();
        for(BpmTaskUserRead read : readList) {
            bpmInstanceIdMap.put(read.getProcessInstanceId(), read.getCreateDate());
        }
        criteria.setBpmInstanceId(Lists.newArrayList(bpmInstanceIdMap.keySet()));
        //查询业务视图表信息
        List<A07DocumentGwView> viewList = gwViewRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));
        //用户名集合放入
        List<String> username = viewList.stream().map(A07DocumentGwView::getCjr).collect(Collectors.toList());
        //查询用户信息
        List<User> userList = userRepository.findByUsernameIn(username);
        Map<String, User> finalUserMap = userList.stream().collect(Collectors.toMap(User::getUsername, a -> a, (k1, k2) -> k1));

        List<A07DocumentGwDto> returnList = readList.stream().map(p -> {
            A07DocumentGwDto documentGwDto = new A07DocumentGwDto();
            for(A07DocumentGwView a07DocumentGwView : viewList) {
                if(a07DocumentGwView.getBpmInstanceId().equals(p.getProcessInstanceId())) {
                    BeanUtils.copyProperties(a07DocumentGwView, documentGwDto);
                    User cjrUser = finalUserMap.get(a07DocumentGwView.getCjr());
                    documentGwDto.setCjr(cjrUser == null ? null : cjrUser.getNickName());
                    WorkflowType workflowType = WorkflowType.getByValue(a07DocumentGwView.getModuleType());
                    documentGwDto.setModuleType(workflowType == null ? null : workflowType.getName());
                    if(Objects.nonNull(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()))) {
                        documentGwDto.setCreateTime(new Timestamp(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()).getTime()));
                    }
                    if(StringUtils.isBlank(documentGwDto.getBt())) {
                        documentGwDto.setBt(p.getBt());
                    }
                    // 流程状态
                    ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(a07DocumentGwView.getBpmStatus());
                    documentGwDto.setBpmStatus(procStatusEnum == null ? null : procStatusEnum.getName());
                    return documentGwDto;
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, Object> returnMap = new LinkedHashMap<>(2);
        returnMap.put("content", returnList);
        returnMap.put("totalElements", reads.getTotalElements());
        return returnMap;
    }

    @Override
    public Object getSwReadList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        Specification<BpmTaskUserRead> sp = (root, cq, cb) -> {
            Predicate pe = cb.equal(root.get("assignee"), SecurityUtils.getCurrentUsername());
            if(criteria.getStatus() != null) {
                if(criteria.getStatus() == 0) {
                    pe = cb.and(pe, cb.isNull(root.get("firstReadTime")));
                }else if(criteria.getStatus() == 1) {
                    pe = cb.and(pe, cb.isNotNull(root.get("firstReadTime")));
                }
            }
            if(StringUtils.isNotBlank(criteria.getBt())) {
                pe = cb.and(pe, cb.like(root.get("bt"), "%" + criteria.getBt() + "%"));
            }
            if(StringUtils.isNotBlank(criteria.getYear())) {
                pe = cb.and(pe, cb.like(root.get("createDate").as(String.class), "%" + criteria.getYear() + "%"));
            }
            if(StringUtils.isNotBlank(criteria.getProcessDefinitionKey())) {
                pe = cb.and(pe, cb.equal(root.get("processDefKey"), criteria.getProcessDefinitionKey()));
            }else {
                pe = cb.and(pe, root.get("processDefKey").in(DocumentConstant.PROCESS_DEFINITION_KEY_SW_LIST));
            }
            cq.orderBy(cb.desc(root.get("createDate")));
            return pe;
        };
        Page<BpmTaskUserRead> reads = readRepository.findAll(sp, pageable);
        List<BpmTaskUserRead> readList = reads.getContent();
        if(CollectionUtils.isEmpty(readList)) {
            return PageUtil.toPage(Page.empty());
        }
        Map<String, Date> bpmInstanceIdMap = new LinkedHashMap<>();
        for(BpmTaskUserRead read : readList) {
            bpmInstanceIdMap.put(read.getProcessInstanceId(), read.getCreateDate());
        }
        criteria.setBpmInstanceId(Lists.newArrayList(bpmInstanceIdMap.keySet()));
        //查询业务视图表信息
        List<A07DocumentGwView> viewList = gwViewRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));
        //用户名集合放入
        List<String> username = viewList.stream().map(A07DocumentGwView::getCjr).collect(Collectors.toList());
        //查询用户信息
        List<User> userList = userRepository.findByUsernameIn(username);
        Map<String, User> finalUserMap = userList.stream().collect(Collectors.toMap(User::getUsername, a -> a, (k1, k2) -> k1));

        List<A07DocumentGwDto> returnList = readList.stream().map(p -> {
            A07DocumentGwDto documentGwDto = new A07DocumentGwDto();
            for(A07DocumentGwView a07DocumentGwView : viewList) {
                if(a07DocumentGwView.getBpmInstanceId().equals(p.getProcessInstanceId())) {
                    BeanUtils.copyProperties(a07DocumentGwView, documentGwDto);
                    User cjrUser = finalUserMap.get(a07DocumentGwView.getCjr());
                    documentGwDto.setCjr(cjrUser == null ? null : cjrUser.getNickName());
                    WorkflowType workflowType = WorkflowType.getByValue(a07DocumentGwView.getModuleType());
                    documentGwDto.setModuleType(workflowType == null ? null : workflowType.getName());
                    if(Objects.nonNull(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()))) {
                        documentGwDto.setCreateTime(new Timestamp(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()).getTime()));
                    }
                    if(StringUtils.isBlank(documentGwDto.getBt())) {
                        documentGwDto.setBt(p.getBt());
                    }
                    // 流程状态
                    ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(a07DocumentGwView.getBpmStatus());
                    documentGwDto.setBpmStatus(procStatusEnum == null ? null : procStatusEnum.getName());
                    return documentGwDto;
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, Object> returnMap = new LinkedHashMap<>(2);
        returnMap.put("content", returnList);
        returnMap.put("totalElements", reads.getTotalElements());
        return returnMap;
    }

    @Override
    public Object getAllReadList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        Specification<BpmTaskUserRead> sp = (root, cq, cb) -> {
            Predicate pe = cb.equal(root.get("assignee"), SecurityUtils.getCurrentUsername());
            if(criteria.getStatus() != null) {
                if(criteria.getStatus() == 0) {
                    pe = cb.and(pe, cb.isNull(root.get("lastReadTime")));
                }else if(criteria.getStatus() == 1) {
                    pe = cb.and(pe, cb.isNull(root.get("lastReadTime")));
                }
            }
            if(StringUtils.isNotBlank(criteria.getBt())) {
                pe = cb.and(pe, cb.like(root.get("bt"), "%" + criteria.getBt() + "%"));
            }
            if(StringUtils.isNotBlank(criteria.getYear())) {
                pe = cb.and(pe, cb.like(root.get("createDate").as(String.class), "%" + criteria.getYear() + "%"));
            }
            if(StringUtils.isNotBlank(criteria.getProcessDefinitionKey())) {
                pe = cb.and(pe, cb.equal(root.get("processDefKey"), criteria.getProcessDefinitionKey()));
            }else {
//                pe = cb.and(pe, root.get("processDefKey").in(DocumentConstant.PROCESS_DEFINITION_KEY_LIST));
            }
            cq.orderBy(cb.desc(root.get("createDate")));
            return pe;
        };
        Page<BpmTaskUserRead> reads = readRepository.findAll(sp, pageable);
        List<BpmTaskUserRead> readList = reads.getContent();
        if(CollectionUtils.isEmpty(readList)) {
            return PageUtil.toPage(Page.empty());
        }
        Map<String, Date> bpmInstanceIdMap = new LinkedHashMap<>();
        for(BpmTaskUserRead read : readList) {
            bpmInstanceIdMap.put(read.getProcessInstanceId(), read.getCreateDate());
        }
        criteria.setBpmInstanceId(Lists.newArrayList(bpmInstanceIdMap.keySet()));
        //查询业务视图表信息
        List<A07DocumentGwView> viewList = gwViewRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));
        //用户名集合放入
        List<String> username = viewList.stream().map(A07DocumentGwView::getCjr).collect(Collectors.toList());
        //查询用户信息
        List<User> userList = userRepository.findByUsernameIn(username);
        Map<String, User> finalUserMap = userList.stream().collect(Collectors.toMap(User::getUsername, a -> a, (k1, k2) -> k1));

        List<A07DocumentGwDto> returnList = readList.stream().map(p -> {
            A07DocumentGwDto documentGwDto = new A07DocumentGwDto();
            for(A07DocumentGwView a07DocumentGwView : viewList) {
                if(a07DocumentGwView.getBpmInstanceId().equals(p.getProcessInstanceId())) {
                    BeanUtils.copyProperties(a07DocumentGwView, documentGwDto);
                    User cjrUser = finalUserMap.get(a07DocumentGwView.getCjr());
                    documentGwDto.setCjr(cjrUser == null ? null : cjrUser.getNickName());
                    WorkflowType workflowType = WorkflowType.getByValue(a07DocumentGwView.getModuleType());
                    documentGwDto.setModuleType(workflowType == null ? null : workflowType.getName());
                    if(Objects.nonNull(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()))) {
                        documentGwDto.setCreateTime(new Timestamp(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()).getTime()));
                    }
                    if(StringUtils.isBlank(documentGwDto.getBt())) {
                        documentGwDto.setBt(p.getBt());
                    }
                    // 流程状态
                    ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(a07DocumentGwView.getBpmStatus());
                    documentGwDto.setBpmStatus(procStatusEnum == null ? null : procStatusEnum.getName());
                    documentGwDto.setFirstReadTime(p.getFirstReadTime());
                    documentGwDto.setLastReadTime(p.getLastReadTime());
                    return documentGwDto;
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, Object> returnMap = new LinkedHashMap<>(2);
        returnMap.put("content", returnList);
        returnMap.put("totalElements", reads.getTotalElements());
        return returnMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String read(List<String> bpmInstanceIds) {
        readRepository.updateReadTimeByProcessInstanceIds(new Date(), bpmInstanceIds);
        return "办理成功";
    }

    @Override
    public List<Map<String, String>> getFlowDocRead(A07DocumentGwQueryCriteria criteria) {
        if(StringUtils.isBlank(criteria.getBpmInstId())) {
            throw new BadRequestException("流程实例id为空");
        }
        List<Map<String, String>> byProcInstId = readRepository.findByProcInstId(criteria.getBpmInstId());
        List<Map<String, String>> newList = new ArrayList<>();
        for(Map m : byProcInstId) {
            Map<String, String> aa = new HashMap<>(m);
            if(Objects.nonNull(m.get("createDate")) && Objects.nonNull(m.get("lastReadTime"))) {
                long createDate = DateUtil.parse(m.get("createDate").toString(), DatePattern.NORM_DATETIME_PATTERN).getTime();
                long lastReadTime = DateUtil.parse(m.get("lastReadTime").toString(), DatePattern.NORM_DATETIME_PATTERN).getTime();
                aa.put("duration", FlowableUtils.getDate(lastReadTime - createDate));
            }
            newList.add(aa);
        }
        if(criteria.getStatus() != null) {
            if(criteria.getStatus() == 0) {
                // 未查阅
                newList = newList.stream().filter(map -> Objects.isNull(map.get("firstReadTime"))).collect(Collectors.toList());
            }else if(criteria.getStatus() == 1) {
                // 已查阅
                newList = newList.stream().filter(a -> Objects.nonNull(a.get("firstReadTime"))).collect(Collectors.toList());
            }else if(criteria.getStatus() == 2) {
                // 未办理
                newList = newList.stream().filter(a -> Objects.isNull(a.get("lastReadTime"))).collect(Collectors.toList());
            }else if(criteria.getStatus() == 3) {
                // 已办理
                newList = newList.stream().filter(a -> Objects.nonNull(a.get("lastReadTime"))).collect(Collectors.toList());
            }
        }
        return newList;
    }

    @Override
    public Map<String, Integer> getFlowDocReadCount(A07DocumentGwQueryCriteria criteria) {
        List<Map<String, String>> byProcInstId = readRepository.findByProcInstId(criteria.getBpmInstId());
        Integer all = 0;
        Integer noRead = 0;
        Integer doneRead = 0;
        Integer noHandle = 0;
        Integer doneHandle = 0;
        for(Map m : byProcInstId) {
            all++;
            if(Objects.isNull(m.get("firstReadTime"))) {
                // 未查阅
                noRead++;
            }
            if(Objects.nonNull(m.get("firstReadTime"))) {
                // 已查阅
                doneRead++;
            }
            if(Objects.isNull(m.get("lastReadTime"))) {
                //未办理
                noHandle++;
            }
            if(Objects.nonNull(m.get("lastReadTime"))) {
                //已办理
                doneHandle++;
            }
        }
        Map<String, Integer> map = new HashMap<>();
        map.put("all", all);
        map.put("noRead", noRead);
        map.put("doneRead", doneRead);
        map.put("noHandle", noHandle);
        map.put("doneHandle", doneHandle);
        return map;
    }
}
