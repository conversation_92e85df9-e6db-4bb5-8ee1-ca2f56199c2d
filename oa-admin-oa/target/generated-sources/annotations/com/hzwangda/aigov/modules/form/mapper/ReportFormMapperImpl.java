package com.hzwangda.aigov.modules.form.mapper;

import com.hzwangda.aigov.modules.form.convert.IdToUserConvert;
import com.hzwangda.aigov.modules.form.domain.A23ReportForm;
import com.hzwangda.aigov.modules.form.dto.A23ReportFormDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class ReportFormMapperImpl implements ReportFormMapper {

    @Autowired
    private IdToUserConvert idToUserConvert;

    @Override
    public List<A23ReportFormDto> toDto(List<A23ReportForm> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A23ReportFormDto> list = new ArrayList<A23ReportFormDto>( entityList.size() );
        for ( A23ReportForm a23ReportForm : entityList ) {
            list.add( toDto( a23ReportForm ) );
        }

        return list;
    }

    @Override
    public A23ReportForm toEntity(A23ReportFormDto dto) {
        if ( dto == null ) {
            return null;
        }

        A23ReportForm a23ReportForm = new A23ReportForm();

        a23ReportForm.setCreateBy( dto.getCreateBy() );
        a23ReportForm.setCreateTime( dto.getCreateTime() );
        a23ReportForm.setUpdateTime( dto.getUpdateTime() );
        a23ReportForm.setUpdatedBy( dto.getUpdatedBy() );
        a23ReportForm.setId( dto.getId() );
        a23ReportForm.setRemark( dto.getRemark() );
        a23ReportForm.setTitle( dto.getTitle() );

        return a23ReportForm;
    }

    @Override
    public List<A23ReportForm> toEntity(List<A23ReportFormDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A23ReportForm> list = new ArrayList<A23ReportForm>( dtoList.size() );
        for ( A23ReportFormDto a23ReportFormDto : dtoList ) {
            list.add( toEntity( a23ReportFormDto ) );
        }

        return list;
    }

    @Override
    public A23ReportFormDto toDto(A23ReportForm entity) {
        if ( entity == null ) {
            return null;
        }

        A23ReportFormDto a23ReportFormDto = new A23ReportFormDto();

        a23ReportFormDto.setCreate( idToUserConvert.convert( entity.getCreateBy() ) );
        a23ReportFormDto.setCreateBy( entity.getCreateBy() );
        a23ReportFormDto.setCreateTime( entity.getCreateTime() );
        a23ReportFormDto.setUpdateTime( entity.getUpdateTime() );
        a23ReportFormDto.setUpdatedBy( entity.getUpdatedBy() );
        a23ReportFormDto.setId( entity.getId() );
        a23ReportFormDto.setRemark( entity.getRemark() );
        a23ReportFormDto.setTitle( entity.getTitle() );

        return a23ReportFormDto;
    }
}
