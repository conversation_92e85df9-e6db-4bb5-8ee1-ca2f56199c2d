package com.hzwangda.aigov.modules.document.controller;


import com.hzwangda.aigov.modules.collaboration.domain.criteria.SmsInfo;
import com.hzwangda.aigov.modules.document.dto.*;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlz;
import com.hzwangda.aigov.modules.document.service.A07DocExchangeService;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.annotation.AnonymousAccess;
import com.wangda.oa.annotation.Log;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/a07DocExchange")
@Api(tags = "公文交换")
@CrossOrigin
public class A07DocExchangeController {
    @Resource
    private A07DocExchangeService a07DocExchangeService;


    @AnonymousAccess
    @Log("公文交换-新建发文")
    @ApiOperation("获取公文表单")
    @GetMapping(value = "/openDoc")
    public ResultJson start(@RequestParam(required = false) String id) {
        return a07DocExchangeService.openDoc(id);
    }

    @Log("分页查询-发文")
    @ApiOperation("分页查询-发文")
    @GetMapping(value = "/getDocExchangeFwList")
    public ResponseEntity<Page<A07DocExchangeListFwDto>> getDocExchangeFwList(A07DocExchangeQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocExchangeService.getDocExchangeFwList(criteria, pageable), HttpStatus.OK);
    }


    @Log("分页查询-收文")
    @ApiOperation("分页查询-收文")
    @GetMapping(value = "/getDocExchangeSwList")
    public ResponseEntity<Page<A07DocExchangeListSwDto>> getDocExchangeSwList(A07DocExchangeQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocExchangeService.getDocExchangeSwList(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping("/saveGwlz")
    @Log("新增或修改公文流转")
    @ApiOperation("新增或修改gwlz")
    public ResponseEntity<Long> saveGwlz(@Validated @RequestBody A07DocExchangeSaveCriteria resources) {
        return new ResponseEntity<>(a07DocExchangeService.saveGwlz(resources.getA07DocumentGwlz(), resources.getType()), HttpStatus.OK);
    }

    @PostMapping("/edit")
    @Log("公文流转-编辑")
    @ApiOperation("公文流转-编辑")
    public ResponseEntity<Long> editGwlz(@Validated @RequestBody A07DocumentGwlz resources) {
        return new ResponseEntity<>(a07DocExchangeService.editGwlz(resources), HttpStatus.OK);
    }

    @Log("公文流转详情")
    @ApiOperation("公文流转详情")
    @GetMapping(value = "/getGwlzInfo")
    public ResponseEntity<A07DocumentGwlzDto> getGwlzInfo(@RequestParam Long id) {
        return new ResponseEntity<>(a07DocExchangeService.getGwlzInfo(id), HttpStatus.OK);
    }

    @Log("签收记录列表")
    @ApiOperation("签收记录列表")
    @GetMapping("/getSignRecords")
    public ResponseEntity<Object> getSignRecords(A07DocExchangeSignCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocExchangeService.getSignRecords(criteria, pageable), HttpStatus.OK);
    }

    @Log("批量删除")
    @ApiOperation("批量删除")
    @PostMapping("/batchDel")
    public ResponseEntity<String> batchDel(@NotNull @RequestBody Long[] id) {
        return new ResponseEntity<>(a07DocExchangeService.batchDel(id), HttpStatus.OK);
    }

    @Log("签收")
    @ApiOperation("签收")
    @PostMapping("/sign")
    public ResponseEntity<String> sign(@RequestBody Long id) {
        return new ResponseEntity<>(a07DocExchangeService.sign(id), HttpStatus.OK);
    }

    @Log("撤回")
    @ApiOperation("撤回")
    @PostMapping("/revoke")
    public ResponseEntity<String> revoke(@RequestBody Long id) {
        return new ResponseEntity<>(a07DocExchangeService.revoke(id), HttpStatus.OK);
    }

    @Log("退回")
    @ApiOperation("退回")
    @PostMapping("/back")
    public ResponseEntity<String> back(@RequestBody Long id) {
        return new ResponseEntity<>(a07DocExchangeService.back(id), HttpStatus.OK);
    }

    @Log("公文交换会议数据接口")
    @ApiOperation("公文交换会议数据接口")
    @GetMapping("/getHyGwList")
    public ResponseEntity<List<A07DocExchangeListSwDto>> getHyGwList(@RequestParam Integer size, String type) {
        return new ResponseEntity<>(a07DocExchangeService.getHyGwList(size, type), HttpStatus.OK);
    }

    @Log("获取填报部门组织树")
    @ApiOperation("获取填报部门组织树")
    @GetMapping("/getDeptTree")
    public ResultJson getDeptTree(@RequestParam Long id) {
        return a07DocExchangeService.getDeptTree(id);
    }

    @Log("获取填报数量")
    @ApiOperation("获取填报数量")
    @GetMapping("/getNum")
    public ResultJson getNum(@RequestParam Long id) {
        return a07DocExchangeService.getNum(id);
    }

    @Log("短信催收")
    @ApiOperation("短信催收")
    @PostMapping("/smsUrge")
    public ResponseEntity smsUrge(@RequestBody SmsInfo smsInfo) {
        return ResponseEntity.ok(a07DocExchangeService.smsUrge(smsInfo));
    }

    @Log("公文交换转收文")
    @ApiOperation("公文交换转收文")
    @PostMapping("/doTransferToDeptOA")
    public ResultJson doTransferToDeptOA(@RequestParam("id") Long id, @RequestParam("appId") Long appId) {
        return a07DocExchangeService.doTransferToDeptOA(id, appId);
    }

    @Log("公文交换转收文已转入打标")
    @ApiOperation("公文交换转收文已转入打标")
    @PostMapping("/markTransferToDeptOA")
    public ResultJson markTransferToDeptOA(@RequestParam("id") Long id) {
        return a07DocExchangeService.markTransferToDeptOA(id);
    }


    @ApiOperation("发文转公文交换")
    @PostMapping("/fwToGw")
    public ResponseEntity<Long> fwToGw(@RequestParam("bpmInstanceId") String bpmInstanceId) {
        return new ResponseEntity<>(a07DocExchangeService.fwToGw(bpmInstanceId), HttpStatus.OK);
    }

    @ApiOperation("获取短信内容")
    @GetMapping("/getSms")
    public ResponseEntity<Map> getSms(@RequestParam Long id) {
        return ResponseEntity.ok(a07DocExchangeService.getSms(id));
    }

    @ApiOperation("导出数据填报内容")
    @GetMapping("/exportAll")
    public void exportAll(@RequestParam Long id, HttpServletRequest request, HttpServletResponse response) throws IOException {
        a07DocExchangeService.exportAll(id, request, response);
    }

    @ApiOperation("导出公文交换已分发数据")
    @GetMapping("/exportFw")
    @AnonymousAccess
    public void exportFw(A07DocExchangeQueryCriteria queryCriteria, HttpServletRequest request, HttpServletResponse response) throws IOException {
        a07DocExchangeService.exportFw(queryCriteria, request, response);
    }
    @ApiOperation("导出正文压缩包")
    @GetMapping("/exportFwZwZip")
    @AnonymousAccess
    public void exportFwZwZip(A07DocExchangeQueryCriteria queryCriteria, HttpServletRequest request, HttpServletResponse response) throws IOException {
        a07DocExchangeService.exportFwZwZip(queryCriteria, request, response);
    }
}
