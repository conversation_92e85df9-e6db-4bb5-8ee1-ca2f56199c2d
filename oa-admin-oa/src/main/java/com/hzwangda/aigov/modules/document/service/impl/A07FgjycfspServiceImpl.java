package com.hzwangda.aigov.modules.document.service.impl;

import com.hzwangda.aigov.modules.document.dto.A07FgjycfQueryCriteria;
import com.hzwangda.aigov.modules.document.entity.A07DocumentFgjycfSp;
import com.hzwangda.aigov.modules.document.entity.A07DocumentFgjycfzxSp;
import com.hzwangda.aigov.modules.document.repository.A07DocumentFgjycfSpRepository;
import com.hzwangda.aigov.modules.document.repository.A07DocumentFgjycfzxSpRepository;
import com.hzwangda.aigov.modules.document.service.A07FgjycfspService;
import com.wangda.oa.utils.FileUtil;
import com.wangda.oa.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.apache.tools.ant.util.DateUtils;
import org.springframework.stereotype.Service;


import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class A07FgjycfspServiceImpl implements A07FgjycfspService {

    private final A07DocumentFgjycfSpRepository a07DocumentFgjycfSpRepository;
    private final A07DocumentFgjycfzxSpRepository a07DocumentFgjycfzxSpRepository;

    /**
     * 发改夜餐费局审批列表
     * @param criteria
     * @return
     */
    @Override
    public List<Map<String, Integer>> getList(A07FgjycfQueryCriteria criteria) throws ParseException {
        if(criteria.getTimeRange()==null){
            //一年前时间
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -365);
            SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String endDate = sd.format(calendar.getTime());
            Date d = sd.parse(endDate);
            Timestamp agoTime = new Timestamp(d.getTime());
            criteria.setJbStart(agoTime);
            criteria.setJbEnd(new Timestamp(System.currentTimeMillis()));
        }else{
            criteria.setJbStart(criteria.getTimeRange().get(0));
            criteria.setJbEnd(criteria.getTimeRange().get(1));
        }

        List<Map<String,Integer>> list;
        if(StringUtils.isEmpty(criteria.getJbry())){
            list = a07DocumentFgjycfSpRepository.findAllByBpmStatus(criteria.getJbStart(), criteria.getJbEnd());
        }else{
            list = a07DocumentFgjycfSpRepository.findAllByBpmStatusAndJbry(criteria.getJbry(), criteria.getJbStart(), criteria.getJbEnd());
        }
        return list;
    }

    /**
     * 发改夜餐费中心审批列表
     * @param criteria
     * @return
     */
    @Override
    public List<Map<String, Integer>> getListzx(A07FgjycfQueryCriteria criteria) throws ParseException {
        if(criteria.getTimeRange()==null){
            //一年前时间
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -365);
            SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String endDate = sd.format(calendar.getTime());
            Date d = sd.parse(endDate);
            Timestamp agoTime = new Timestamp(d.getTime());
            criteria.setJbStart(agoTime);
            criteria.setJbEnd(new Timestamp(System.currentTimeMillis()));
        }else{
            criteria.setJbStart(criteria.getTimeRange().get(0));
            criteria.setJbEnd(criteria.getTimeRange().get(1));
        }
        List<Map<String,Integer>> list;
        if(StringUtils.isEmpty(criteria.getJbry())){
            list = a07DocumentFgjycfzxSpRepository.findAllByBpmStatus(criteria.getJbStart(), criteria.getJbEnd());
        }else{
            list = a07DocumentFgjycfzxSpRepository.findAllByBpmStatusAndJbry(criteria.getJbry(), criteria.getJbStart(), criteria.getJbEnd());
        }
        return list;
    }

    @Override
    public void exportExcel(HttpServletResponse response, A07FgjycfQueryCriteria criteria) throws IOException, ParseException {
        List<Map<String, Object>> responseList = new ArrayList<>();
        if(criteria.getTimeRange()==null){
            //一年前时间
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -365);
            SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String endDate = sd.format(calendar.getTime());
            Date d = sd.parse(endDate);
            Timestamp agoTime = new Timestamp(d.getTime());
            criteria.setJbStart(agoTime);
            criteria.setJbEnd(new Timestamp(System.currentTimeMillis()));
        }else{
            criteria.setJbStart(criteria.getTimeRange().get(0));
            criteria.setJbEnd(criteria.getTimeRange().get(1));
        }
        List<A07DocumentFgjycfSp> list = a07DocumentFgjycfSpRepository.findByBpmStatus(criteria.getJbStart(), criteria.getJbEnd());
        for(A07DocumentFgjycfSp lists:list){
            Map<String, Object> responseMap = new LinkedHashMap<>();
            responseMap.put("姓名", lists.getJbry());
            responseMap.put("加(值)班日期", DateUtils.format(lists.getJbStart(),"yyyy-MM-dd"));
            responseMap.put("时间段", DateUtils.format(lists.getJbStart(),"HH:mm")+" ~ "+DateUtils.format(lists.getJbEnd(),"HH:mm"));
            responseMap.put("次数", "");
            responseMap.put("金额", lists.getMoney());
            responseMap.put("备注（加班事由）", lists.getJbyy());
            responseList.add(responseMap);
        }
        FileUtil.downloadExcel(responseList, response);
    }

    @Override
    public void exportExcelzx(HttpServletResponse response, A07FgjycfQueryCriteria criteria) throws IOException, ParseException {
        List<Map<String, Object>> responseList = new ArrayList<>();
        if(criteria.getTimeRange()==null){
            //一年前时间
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -365);
            SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String endDate = sd.format(calendar.getTime());
            Date d = sd.parse(endDate);
            Timestamp agoTime = new Timestamp(d.getTime());
            criteria.setJbStart(agoTime);
            criteria.setJbEnd(new Timestamp(System.currentTimeMillis()));
        }else{
            criteria.setJbStart(criteria.getTimeRange().get(0));
            criteria.setJbEnd(criteria.getTimeRange().get(1));
        }
        List<A07DocumentFgjycfzxSp> list = a07DocumentFgjycfzxSpRepository.findByBpmStatus(criteria.getJbStart(), criteria.getJbEnd());
        for(A07DocumentFgjycfzxSp lists:list){
            Map<String, Object> responseMap = new LinkedHashMap<>();
            responseMap.put("姓名", lists.getJbry());
            responseMap.put("加(值)班日期", DateUtils.format(lists.getJbStart(),"yyyy-MM-dd"));
            responseMap.put("时间段", DateUtils.format(lists.getJbStart(),"HH:mm")+" ~ "+DateUtils.format(lists.getJbEnd(),"HH:mm"));
            responseMap.put("次数", "");
            responseMap.put("金额", lists.getMoney());
            responseMap.put("备注（加班事由）", lists.getJbyy());
            responseList.add(responseMap);
        }
        FileUtil.downloadExcel(responseList, response);
    }
}
