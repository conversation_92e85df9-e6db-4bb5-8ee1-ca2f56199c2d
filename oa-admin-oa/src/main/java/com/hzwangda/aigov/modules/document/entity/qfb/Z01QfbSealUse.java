package com.hzwangda.aigov.modules.document.entity.qfb;

import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Where;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 区府办用印审批
 *
 * <AUTHOR>
 * @date 2023/03/28 上午9:58
 */
@Data
@Entity
@Table(name = "z01_qfb_seal_use")
public class Z01QfbSealUse extends BaseBpmDomain implements Serializable {

    @ApiModelProperty(value = "用印次数")
    private Integer counts;

    @ApiModelProperty(value = "用印部门")
    private String startDeptName;

    @ApiModelProperty(value = "正文")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "zw", referencedColumnName = "id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private StorageBiz zw;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A01DocumentTySealUse.fj'")
    private List<StorageBiz> fj;

    @Column(name = "bt")
    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "经办人")
    private String startNickName;

    @ApiModelProperty(value = "科室拟办意见")
    @Column(columnDefinition = "text")
    private String yjksnb;

    @ApiModelProperty(value = "科室负责人意见")
    @Column(columnDefinition = "text")
    private String yjksfzr;

    @ApiModelProperty(value = "领导签批")
    @Column(columnDefinition = "text")
    private String yjLdqp;

    @ApiModelProperty(value = "盖章文件")
    @Column(columnDefinition = "text")
    private String gzwj;

    public void setFj(List<StorageBiz> fj) {
        if (fj != null) {
            if (this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }

    @PrePersist
    @PreUpdate
    public void preCreateEntity() {
        super.preCreateEntity();
        if (!CollectionUtils.isEmpty(this.fj)) {
            for (StorageBiz storageBiz : this.fj) {
                if (Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                }
            }
        }
    }

    public void setZw(StorageBiz zw) {
        if (Objects.nonNull(zw)) {
            this.zw = zw;
        }
        if (Objects.nonNull(this.zw) && StringUtils.isNotBlank(this.zw.getStorageId())) {
            if (this.id != null) {
                this.zw.setBizId(this.id.toString());
            }
        } else {
            this.zw = null;
        }
    }
}
