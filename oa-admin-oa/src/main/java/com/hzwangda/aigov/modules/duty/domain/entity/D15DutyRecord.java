package com.hzwangda.aigov.modules.duty.domain.entity;

import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.wangda.boot.platform.base.BaseDomain;
import com.wangda.oa.domain.LocalStorage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Data
@Table(name = "d15_duty_record")
@ApiModel("值班记录")
@DynamicInsert
public class D15DutyRecord extends BaseDomain {

    @ApiModelProperty(value = "编号")
    private String number;

    @ApiModelProperty(value = "值班日期")
    @Temporal(TemporalType.DATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dutyTime;

    @ApiModelProperty(value = "内容")
    @Column(length = 2000)
    private String content;

    @ApiModelProperty(value = "咨询举报投诉1、重大突发情况2")
    private String type;

    @ApiModelProperty(value = "值班人员")
    private String username;

    @ApiModelProperty(value = "值班人员姓名")
    private String nickName;

    @ApiModelProperty(value = "签转")
    private String transferTo;

    @ApiModelProperty(value = "抄送")
    private String copyTo;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "recordId")
    @Where(clause = "type=1")
    @OrderBy("id desc")
    private List<D15DutyRecordUser> transferToUsers;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "recordId")
    @Where(clause = "type=2")
    private List<D15DutyRecordUser> copyToUsers;

    @ApiModelProperty(value = "处理情况")
    @OneToMany(targetEntity = D15DutyRecordDeal.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "recordId", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private List<D15DutyRecordDeal> d15DutyRecordDeals;

    @ApiModelProperty(value = "是否处理完成 0进行中 1已完成 2代签转 ")
    @Column(nullable = false, columnDefinition = "int default 0")
    private Integer finished;

    @ApiModelProperty(value = "删除标志")
    @Column(nullable = false, columnDefinition = "int default 0")
    private Integer delFlag;

    @ApiModelProperty(value = "是否转办")
    private Integer zfFlag;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='D15DutyRecord.fj'")
    private List<StorageBiz> attachments;

    @ApiModelProperty("来电人")
    @Column
    private String caller;

    @ApiModelProperty("来电人电话")
    @Column
    private String callerPhone;

    @ApiModelProperty("匿名")
    @Column
    private Boolean anonymity;

    @ApiModelProperty("区域地址")
    @Column
    private String area;


    @Transient
    private Boolean op;
    @Transient
    private List<LocalStorage> storages;
}
