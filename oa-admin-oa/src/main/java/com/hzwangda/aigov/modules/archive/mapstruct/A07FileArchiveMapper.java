package com.hzwangda.aigov.modules.archive.mapstruct;

import com.hzwangda.aigov.modules.archive.entity.A07FileArchive;
import com.hzwangda.aigov.modules.archive.entity.FileArchiveDto;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07DocumentMapperUtil;
import com.wangda.oa.base.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-06-09
 **/
@Mapper(componentModel = "spring", uses = A07DocumentMapperUtil.class, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface A07FileArchiveMapper extends BaseMapper<FileArchiveDto, A07FileArchive> {

    @Override
    @Mappings({
            @Mapping(target = "fj", expression = "java(a07DocumentMapperUtil.toFj(entity.getFj()))"),
    })
    FileArchiveDto toDto(A07FileArchive entity);
}
