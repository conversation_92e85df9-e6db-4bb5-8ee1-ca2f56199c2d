package com.hzwangda.aigov.modules.news.dto.mapstruct;

import com.hzwangda.aigov.modules.news.repository.A06NewsReadRepository;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class StorageMapperUtil {

    @Autowired
    private StorageManageService localStorageService;

    @Autowired
    private A06NewsReadRepository a06NewsReadRepository;

    public List<StorageBizDto> toConvertToLocalStorageSimpleDto(List<StorageBiz> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<StorageBizDto> list = ids.stream().map(p -> setInfo(p)).collect(Collectors.toList());
        return list;
    }

    public Integer toConvertToRead(Long id) {
        int number = a06NewsReadRepository.countByInfoIdAndUsername(id, SecurityUtils.getCurrentUsername());
        return number > 0 ? 1 : 0;
    }

    public StorageBizDto toConvertToGuide(StorageBiz guideId) {
        if (guideId == null) {
            return new StorageBizDto();
        }
        return setInfo(guideId);
    }

    public List<StorageBiz> toConvertToId(List<StorageBizDto> localStorageSimpleDtoList) {
        List<StorageBiz> set = new ArrayList<>();
        if (CollectionUtils.isEmpty(localStorageSimpleDtoList)) {
            return set;
        }
        for (StorageBizDto storageBiz : localStorageSimpleDtoList) {
            StorageBiz storage = new StorageBiz();
            storage.setBizId(storageBiz.getBizId());
            storage.setStorageId(String.valueOf(storageBiz.getStorage().getId()));
            set.add(storage);
        }
        return set;
    }

    private StorageBizDto setInfo(StorageBiz storageBiz) {
        StorageBizDto storageBizDto = new StorageBizDto();
        storageBizDto.setStorage(localStorageService.findById(Long.valueOf(storageBiz.getStorageId())));
        storageBizDto.setBizId(storageBiz.getBizId());
        storageBizDto.setBizType(storageBiz.getBizType());
        storageBizDto.setSorted(storageBiz.getSorted());
        return storageBizDto;
    }
}
