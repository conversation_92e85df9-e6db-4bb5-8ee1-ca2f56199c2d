package com.hzwangda.aigov.modules.duty.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.duty.domain.dto.D15DutyRecordCriteria;
import com.hzwangda.aigov.modules.duty.domain.dto.TotalQueryCriteria;
import com.hzwangda.aigov.modules.duty.domain.entity.*;
import com.hzwangda.aigov.modules.duty.repository.D15DutyRecordFeedbackRepository;
import com.hzwangda.aigov.modules.duty.repository.D15DutyRecordListRepository;
import com.hzwangda.aigov.modules.duty.repository.D15DutyRecordRepository;
import com.hzwangda.aigov.modules.duty.repository.D15DutyRecordUserRepository;
import com.hzwangda.aigov.modules.duty.service.D15DutyRecordService;
import com.hzwangda.aigov.modules.phonebook.domain.entity.A09PhoneBook;
import com.hzwangda.aigov.modules.phonebook.repository.A09PhoneBookRepository;
import com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.entity.D05Duty;
import com.hzwangda.aigov.modules.zjedu.scheduleWork.repository.D05DutyRepository;
import com.hzwangda.aigov.oa.bo.MasSendContentBO;
import com.hzwangda.aigov.oa.bo.MasUserBO;
import com.hzwangda.aigov.oa.constant.AuthorityConstant;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.hzwangda.aigov.oa.repository.WdSysOptionRepository;
import com.hzwangda.aigov.oa.service.MasBusinessService;
import com.hzwangda.aigov.oa.util.SMSFormatUtil;
import com.hzwangda.aigov.zwdd.service.ZwddService;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationBO;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationLinkBO;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.extension.domain.SysUserPlatform;
import com.wangda.oa.modules.extension.domain.WdSysOption;
import com.wangda.oa.modules.extension.repository.SysUserPlatformRepository;
import com.wangda.oa.modules.extension.service.ShortLinkService;
import com.wangda.oa.modules.system.domain.*;
import com.wangda.oa.modules.system.repository.DeptRepository;
import com.wangda.oa.modules.system.repository.DictDetailRepository;
import com.wangda.oa.modules.system.repository.MenuRepository;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.repository.LocalStorageRepository;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Tuple;
import javax.persistence.criteria.*;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class D15DutyRecordServiceImpl implements D15DutyRecordService {

    @Resource
    private D15DutyRecordRepository d15DutyRecordRepository;
    @Resource
    private D15DutyRecordListRepository d15DutyRecordListRepository;
    @Resource
    private D15DutyRecordUserRepository d15DutyRecordUserRepository;
    @Resource
    private ZwddService zwddService;
    @Resource
    private SysUserPlatformRepository sysUserPlatformRepository;
    @Resource
    private ZwddProperties zwddProperties;
    @Resource
    private D05DutyRepository d05DutyRepository;
    @Resource
    private UserRepository userRepository;
    @Resource
    private A09PhoneBookRepository a09PhoneBookRepository;
    @Resource
    private MasBusinessService masBusinessService;
    @Resource
    private DeptRepository deptRepository;
    @PersistenceContext
    private EntityManager entityManager;
    @Resource
    private DictDetailRepository dictDetailRepository;
    @Resource
    private D15DutyRecordFeedbackRepository d15DutyRecordFeedbackRepository;
    @Resource
    private MenuRepository menuRepository;
    @Resource
    private WdSysOptionRepository wdSysOptionRepository;
    @Resource
    private ShortLinkService shortLinkService;
    @Value("${url.serverUrl}")
    private String serviceUrl;
    @Resource
    private LocalStorageRepository localStorageRepository;


    @Override
    public Page<D15DutyRecordList> queryList(D15DutyRecordCriteria d15DutyRecordCriteria, Pageable pageable) {
        Page<D15DutyRecordList> all = d15DutyRecordListRepository.findAll(((root, criteriaQuery, criteriaBuilder) -> {
            Predicate predicate = QueryHelp.getPredicate(root, d15DutyRecordCriteria, criteriaBuilder);
            Join<D15DutyRecord, D15DutyRecordDeal> d15DutyRecordDeals = root.join("d15DutyRecordDeals", JoinType.LEFT);
            Join<D15DutyRecord, D15DutyRecordUser> recordUsers = root.join("recordUsers", JoinType.LEFT);
            Predicate username = criteriaBuilder.equal(recordUsers.get("username"), SecurityUtils.getCurrentUsername());
            Predicate rootUsername = criteriaBuilder.equal(root.get("username"), SecurityUtils.getCurrentUsername());
            List<Predicate> predicatesAnd = new ArrayList<>();
            predicatesAnd.add(criteriaBuilder.equal(root.get("delFlag"), 0));

            //查询是否是管理权限 true是  false否
            Boolean flag = infoAdmin();
            switch (d15DutyRecordCriteria.getType()) {
                case "todo":
                    Predicate readFlag = criteriaBuilder.equal(recordUsers.get("readFlag"), 0);
//                    Predicate content = criteriaBuilder.isNull(d15DutyRecordDeals.get("content"));
                    predicatesAnd.add(readFlag);
                    if (!flag) {
                        predicatesAnd.add(username);
                    }
                    break;
                case "unfinished":
                    Predicate finished = criteriaBuilder.equal(root.get("finished"), 0);
                    predicatesAnd.add(finished);
                    if (!flag) {
                        Predicate or = criteriaBuilder.or(username, rootUsername);
                        predicatesAnd.add(or);
                    }
                    break;
                case "finished":
                    finished = criteriaBuilder.equal(root.get("finished"), 1);
                    predicatesAnd.add(finished);
                    if (!flag) {
                        Predicate or = criteriaBuilder.or(username, rootUsername);
                        predicatesAnd.add(or);
                    }
                    break;
                case "all":
                    if (!flag) {
                        Predicate or = criteriaBuilder.or(username, rootUsername);
                        predicatesAnd.add(or);
                    }
                    break;
                default:
            }
            Predicate[] array = new Predicate[predicatesAnd.size()];
            Predicate and = criteriaBuilder.and(predicatesAnd.toArray(array));
            criteriaQuery.distinct(true);
            return criteriaBuilder.and(predicate, and);
        }), pageable);
        all.stream().forEach(d15DutyRecord -> {
            D15DutyRecordUser d15DutyRecordUser = d15DutyRecordUserRepository.findFirstByRecordIdAndUsernameOrderByReadFlag(d15DutyRecord.getId(), SecurityUtils.getCurrentUsername());
            if (d15DutyRecordUser != null) {
                d15DutyRecord.setReadFlag(d15DutyRecordUser.getReadFlag());
            }
        });
        return all;
    }

    private boolean infoAdmin() {
        List<String> elPermissions = SecurityUtils.getCurrentUser().getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList());
        if (Arrays.stream(new String[]{AuthorityConstant.AUTHORITY_DUTYRECORD_ADMIN, AuthorityConstant.AUTHORITY_ADMIN}).anyMatch(elPermissions::contains)) {
            return true;
        }
        return false;
    }

    @Override
    public D15DutyRecord queryById(Long id, Boolean ck) {
        D15DutyRecord d15DutyRecord = d15DutyRecordRepository.findById(id).orElseThrow(() -> new BadRequestException("找不到数据"));
        if (ck == null || ck) {
            // 没传参数的走这里
            d15DutyRecord.setOp(infoAdmin() || check(d15DutyRecord));
        }
        List<StorageBiz> attachments = d15DutyRecord.getAttachments();
        if (attachments != null && attachments.size() > 0) {
            List<LocalStorage> storages = attachments.stream().map(storageBiz ->
                    localStorageRepository.findById(Long.valueOf(storageBiz.getStorageId())).orElse(null)
            ).collect(Collectors.toList());
            d15DutyRecord.setStorages(storages);
        }
        return d15DutyRecord;
    }

    private boolean check(D15DutyRecord d15DutyRecord) {
        boolean flag = d15DutyRecord.getUsername().equals(SecurityUtils.getCurrentUsername());
        List<D15DutyRecordUser> transferToUsers = d15DutyRecord.getTransferToUsers();
        if (transferToUsers != null && transferToUsers.size() > 0) {
            D15DutyRecordUser d15DutyRecordUser = transferToUsers.get(0);
            flag |= d15DutyRecordUser.getUsername().equals(SecurityUtils.getCurrentUsername());
        }
        return flag;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(D15DutyRecord d15DutyRecord) {
        if (d15DutyRecord.getId() == null) {
            d15DutyRecord.setDutyTime(new Date());
            d15DutyRecord.setCreatorId(SecurityUtils.getCurrentUserId());
        }
        d15DutyRecord.setModifiedId(SecurityUtils.getCurrentUserId());
        // 消息通知
        Set<String> receiverIdSet = new HashSet<>();
        List<MasUserBO> userBOList = new ArrayList<>();

        List<D15DutyRecordUser> recordUsers = d15DutyRecord.getTransferToUsers();

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date date = calendar.getTime();


        if (recordUsers != null) {
            recordUsers.stream().forEach(d15DutyRecordUser -> {
                Integer msgFlag = d15DutyRecordUser.getMsgFlag();
                if (msgFlag == null || msgFlag == 0) {
                    String username = d15DutyRecordUser.getUsername();
                    String nickName = d15DutyRecordUser.getNickName();
                    String phone = "";

                    // 发送给当前单位的值班人员，值班人员点击链接查看内容，不可操作
                    D05Duty d05Duty = d05DutyRepository.findFirstByDutyDateAndDepartmentAndStartTimeBeforeAndEndTimeAfter(date, nickName, new Date(), new Date());
                    if (d05Duty != null) {
                        String userName = d05Duty.getUserName();
                        String tel = d05Duty.getTel();
                        List<User> list = userRepository.findByNickNameAndEnabled(userName, true);
                        if (list.size() > 0) {
                            username = list.get(0).getUsername();
                        }
                        // 短信通知
                        MasUserBO masUserBO = new MasUserBO();
                        masUserBO.setNickName(nickName);
                        masUserBO.setPhone(tel);
                        userBOList.add(masUserBO);


                    }
                    // 如果为空，查询办公室主任
                    else {
                        A09PhoneBook a09PhoneBook = a09PhoneBookRepository.findFirstByUnitName(nickName);
                        if (a09PhoneBook != null) {
                            String name = a09PhoneBook.getOfficeDirectorName();
                            String officeDirectorPhone = a09PhoneBook.getOfficeDirectorPhone();
                            List<User> list = userRepository.findByNickNameAndEnabled(name, true);
                            if (list.size() > 0) {
                                username = list.get(0).getUsername();
                            }
                            // 短信通知
                            MasUserBO masUserBO = new MasUserBO();
                            masUserBO.setNickName(nickName);
                            masUserBO.setPhone(officeDirectorPhone);
                            userBOList.add(masUserBO);
                        } else {
                            MasUserBO masUserBO = new MasUserBO();
                            masUserBO.setNickName(nickName);
                            List<User> list = userRepository.findByNickNameAndEnabled(nickName, true);
                            if (list.size() > 0) {
                                username = list.get(0).getUsername();
                                phone = list.get(0).getPhone();
                            }
                            masUserBO.setPhone(phone);
                            userBOList.add(masUserBO);
                        }
                    }

                    List<SysUserPlatform> users = sysUserPlatformRepository.findByUserNameAndType(username, zwddProperties.getType());
                    users.stream().forEach(sysUserPlatform -> {
                        String platformUserId = sysUserPlatform.getPlatformUserId();
                        if (StringUtils.isNotEmpty(platformUserId)) {
                            receiverIdSet.add(platformUserId);
                        }
                    });
                    d15DutyRecordUser.setMsgFlag(1);
                }
            });
        }
        List<D15DutyRecordUser> copyToUsers = d15DutyRecord.getCopyToUsers();
        if (copyToUsers != null) {
            copyToUsers.stream().forEach(d15DutyRecordUser -> {
                Integer msgFlag = d15DutyRecordUser.getMsgFlag();
                if (msgFlag == null || msgFlag == 0) {
                    String username = d15DutyRecordUser.getUsername();
                    List<SysUserPlatform> users = sysUserPlatformRepository.findByUserNameAndType(username, zwddProperties.getType());
                    users.stream().forEach(sysUserPlatform -> {
                        String platformUserId = sysUserPlatform.getPlatformUserId();
                        if (StringUtils.isNotEmpty(platformUserId)) {
                            receiverIdSet.add(platformUserId);
                        }
                    });
                }
            });
        }


        List<D15DutyRecordDeal> d15DutyRecordDeals = d15DutyRecord.getD15DutyRecordDeals();
        if (d15DutyRecordDeals != null) {
            d15DutyRecordDeals.stream().forEach(d15DutyRecordDeal -> {
                if (StringUtils.isNotEmpty(d15DutyRecordDeal.getContent()) && d15DutyRecordDeal.getTime() == null) {
                    d15DutyRecordDeal.setTime(new Date());
                }
            });

        }
        D15DutyRecord save = d15DutyRecordRepository.save(d15DutyRecord);

        String serviceUrl = zwddProperties.getAppUrl() + "/#/duty/dutyRecord/Content?id=" + save.getId();
        WorkNotificationLinkBO linkBO = WorkNotificationLinkBO.builder()
                .text("您收到一条教育厅值班消息！")
                .messageUrl(serviceUrl)
                .title("您收到一条教育厅值班消息！")
                .build();
        WorkNotificationBO bo = WorkNotificationBO.builder()
                .receiverIds(receiverIdSet.stream().collect(Collectors.joining(",")))
                .type(1)
                .linkBO(linkBO)
                .build();
        zwddService.workNotification(bo);

        String url = zwddProperties.getAppUrl() + "/#/duty/feedback?id=" + save.getId();
        url = this.serviceUrl + "/d/" + shortLinkService.create(url, 0);

        WdSysOption firstByKey = wdSysOptionRepository.getFirstByKey("D15DutyRecord.SMS");
        Assert.notNull(firstByKey, "值班短信模板没有配置!");
        String value = firstByKey.getValue();
        Map map = new HashMap();
        if (d15DutyRecord.getAnonymity()) {
            map.put("user", "匿名");
        } else {
            map.put("user", d15DutyRecord.getCaller() + " (" + d15DutyRecord.getCallerPhone() + ")");
        }
        map.put("content", d15DutyRecord.getContent());
        map.put("url", url);
        String message = SMSFormatUtil.processTemplate(value, map);

        MasSendContentBO masSendContentBO = new MasSendContentBO();
        masSendContentBO.setContent(message);
        masSendContentBO.setUserBOList(userBOList);
        masSendContentBO.setSendDate(new Date());
        masSendContentBO.setTiming(0);
        masBusinessService.sendSMContent(masSendContentBO, 0, 0);

        return save.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchDel(Long[] ids) {
        d15DutyRecordRepository.updateDelFlagByIdIn(ids);
        return "删除成功";
    }

    @Override
    public String getMaxNumber(String type) {
        List<D15DutyRecord> list = d15DutyRecordRepository.findByTypeOrderByCreateDateDesc(type);
        if (list.size() > 0) {
            String number = list.get(0).getNumber();

            String s = number.substring(number.length() - 4);
            Integer num = Integer.valueOf(s) + 1;

            return number.substring(0, number.lastIndexOf(s)) + StrUtil.padPre(num.toString(), 4, '0');
        }
        int year = LocalDate.now().getYear();
        return "JYT" + year + type + "0001";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long read(Long id) {
        d15DutyRecordUserRepository.readByRecordIdAndAndUsername(id, SecurityUtils.getCurrentUsername(), new Date());
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String mark(Long[] ids) {
        d15DutyRecordRepository.updateFinishedByIdIn(ids);
        return "标记完成";
    }

    @Override
    public Map<String, Object> total(TotalQueryCriteria criteria) throws ParseException {
        //市级教育局
        List<User> sjjyj = userRepository.findByDeptIdAndEnabledAndUserType(20001L, true, 2);
        //县级教育局单位
        List<Dept> depts = deptRepository.findByPidAndEnabled(20004L);

        String type = criteria.getType();
        if (StringUtils.isNotEmpty(type)) {
            Date date = new Date();
            String format = "yyyy-MM-dd";
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            if (type.equals("week")) {
                //获取周一的日期
                DateTime beginOfWeek = DateUtil.beginOfWeek(date);
                DateTime endOfWeek = DateUtil.endOfWeek(date);
                List<Date> dutyTime = new ArrayList<>();
                dutyTime.add(sdf.parse(DateUtil.format(beginOfWeek, format)));
                dutyTime.add(sdf.parse(DateUtil.format(endOfWeek, format)));
                criteria.setDutyTime(dutyTime);
            } else if (type.equals("month")) {
                DateTime beginOfMonth = DateUtil.beginOfMonth(date);
                DateTime endOfMonth = DateUtil.endOfMonth(date);
                List<Date> dutyTime = new ArrayList<>();
                dutyTime.add(sdf.parse(DateUtil.format(beginOfMonth, format)));
                dutyTime.add(sdf.parse(DateUtil.format(endOfMonth, format)));
                criteria.setDutyTime(dutyTime);
            }
        }

        //统计总数
        JSONObject total = new JSONObject();
        Specification<D15DutyRecord> sp = (root, query, cb) -> {
            Predicate queryPredicate = QueryHelp.getPredicate(root, criteria, cb);
            return queryPredicate;
        };
        List<D15DutyRecord> all = d15DutyRecordRepository.findAll(sp);
        total.put("all", all.size());
        long finished = all.stream().filter(d15DutyRecord -> d15DutyRecord.getFinished() == 1).count();
        total.put("finished", finished);
        total.put("unfinished", all.size() - finished);
        if (all.size() == 0) {
            total.put("rate", 0);
        } else {
            double rate = ((double) finished) / ((double) all.size());
            if (rate == 0) {
                total.put("rate", 0);
            } else {
                DecimalFormat percentInstance = (DecimalFormat) NumberFormat.getPercentInstance();
                percentInstance.applyPattern("00%");
                percentInstance.setMaximumFractionDigits(2);
                String format = percentInstance.format(rate);
                total.put("rate", format);
            }
        }


        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();
        Root<D15DutyRecord> root = criteriaQuery.from(D15DutyRecord.class);
        criteriaQuery.multiselect(root.get("type"), criteriaBuilder.count(root));
        //返回的列表格式
        List<JSONObject> collect = depts.stream().map(dept -> {
            JSONObject jsonObject = new JSONObject();
            String name = dept.getName();
            jsonObject.put("name", name);


            //先预设0
            List<DictDetail> duty_record_type = dictDetailRepository.findByDictName("duty_record_type");
            if (duty_record_type.size() > 0) {
                duty_record_type.stream().forEach(dictDetail -> {
                    jsonObject.put("xj_" + dictDetail.getValue(), 0);
                    jsonObject.put("sj_" + dictDetail.getValue(), 0);
                    jsonObject.put("total_" + dictDetail.getValue(), 0);
                });
            }
            jsonObject.put("total", 0L);


            Long id = dept.getId();
            // 县级单位账号
            List<User> users = userRepository.findByDeptIdAndEnabledAndUserType(id, true, 2);
            List<String> usernames = users.stream().map(user -> user.getUsername()).collect(Collectors.toList());
            List<D15DutyRecordUser> dutyRecordUsers = d15DutyRecordUserRepository.findAllByUsernameInAndReadFlag(usernames, 1);
            List<Long> recordIds = dutyRecordUsers.stream().map(d15DutyRecordUser -> d15DutyRecordUser.getRecordId()).collect(Collectors.toList());
            if (recordIds != null && recordIds.size() > 0) {

                criteriaQuery.where(
                        toPredicate(criteria, recordIds, root, criteriaQuery, criteriaBuilder)
                );

                List<Tuple> resultList = entityManager.createQuery(criteriaQuery).getResultList();
                resultList.stream().forEach(tuple -> {
                    String label = (String) tuple.get(0);
                    Long value = (Long) tuple.get(1);
                    jsonObject.put("xj_" + label, value);
                });

            }
            // 市级单位账号
            List<User> curSjUser = sjjyj.stream().filter(user -> user.getNickName().contains(name)).collect(Collectors.toList());
            List<String> sjUsernames = curSjUser.stream().map(user -> user.getUsername()).collect(Collectors.toList());
            List<D15DutyRecordUser> sjDutyRecordUsers = d15DutyRecordUserRepository.findAllByUsernameInAndReadFlag(sjUsernames, 1);
            List<Long> sjRecordIds = sjDutyRecordUsers.stream().map(d15DutyRecordUser -> d15DutyRecordUser.getRecordId()).collect(Collectors.toList());
            if (sjRecordIds != null && sjRecordIds.size() > 0) {
                criteriaQuery.where(
                        toPredicate(criteria, sjRecordIds, root, criteriaQuery, criteriaBuilder)
                );

                List<Tuple> resultList = entityManager.createQuery(criteriaQuery).getResultList();
                resultList.stream().forEach(tuple -> {
                    String label = (String) tuple.get(0);
                    Long value = (Long) tuple.get(1);
                    jsonObject.put("sj_" + label, value);


                    // 当前类型的总数
                    long val = value + jsonObject.getLongValue("xj_" + label);
                    jsonObject.put("total_" + label, val);
                    // 所有类型的总和
                    jsonObject.merge("total", val, (a, b) -> ((Long) a) + ((Long) b));
                });
            }

            return jsonObject;
        }).sorted((a, b) -> {
            long total1 = a.getLongValue("total");
            long total2 = b.getLongValue("total");
            return (int) (total2 - total1);
        }).collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();
        result.put("total", total);
        result.put("dsList", collect);

        return result;
    }

    private Predicate toPredicate(TotalQueryCriteria criteria, List<Long> recordIds, Root root, CriteriaQuery<?> query, CriteriaBuilder cb) {

        Predicate queryPredicate = QueryHelp.getPredicate(root, criteria, cb);
        List<Predicate> andPredicateList = new ArrayList<>();
        andPredicateList.add(cb.and(root.get("id").in(recordIds)));
//                    andPredicateList.add(cb.equal(root.get("finished"),1));
        Predicate defaultPredicate = cb.and(andPredicateList.toArray(new Predicate[andPredicateList.size()]));
        return cb.and(queryPredicate, defaultPredicate);

    }

    @Override
    public List<D15DutyRecordFeedBack> findByNumber(String number) {
        return d15DutyRecordFeedbackRepository.findByNumberOrderByCreateDateDesc(number);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveFeedback(D15DutyRecordFeedBack d15DutyRecordFeedBack) {
        D15DutyRecordFeedBack save = d15DutyRecordFeedbackRepository.save(d15DutyRecordFeedBack);

        D15DutyRecord d15DutyRecord = d15DutyRecordRepository.findByNumber(d15DutyRecordFeedBack.getNumber());
        Set<String> receiverIdSet = new HashSet<>();
        //创建人
        List<SysUserPlatform> users = sysUserPlatformRepository.findByUserNameAndType(d15DutyRecord.getUsername(), zwddProperties.getType());
        users.stream().forEach(sysUserPlatform -> {
            String platformUserId = sysUserPlatform.getPlatformUserId();
            if (StringUtils.isNotEmpty(platformUserId)) {
                receiverIdSet.add(platformUserId);
            }
        });
        //配置权限的人
        Specification<Menu> sp = (root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get("permission"), AuthorityConstant.AUTHORITY_DUTYRECORD_ADMIN);
        List<Menu> all = menuRepository.findAll(sp);
        all.stream().forEach(menu -> {
            Set<Role> roles = menu.getRoles();
            if (roles != null) {
                roles.stream().forEach(role -> {
                    Set<User> userSet = role.getUsers();
                    if (userSet != null) {
                        userSet.stream().forEach(user -> {
                            List<SysUserPlatform> platforms = sysUserPlatformRepository.findByUserNameAndType(user.getUsername(), zwddProperties.getType());
                            platforms.stream().forEach(sysUserPlatform -> {
                                String platformUserId = sysUserPlatform.getPlatformUserId();
                                if (StringUtils.isNotEmpty(platformUserId)) {
                                    receiverIdSet.add(platformUserId);
                                }
                            });
                        });
                    }
                });
            }
        });

        String serviceUrl = zwddProperties.getAppUrl() + "/#/duty/dutyRecord/Content?id=" + d15DutyRecord.getId();

        WorkNotificationLinkBO linkBO = WorkNotificationLinkBO.builder()
                .text("您收到一条值班反馈消息！")
                .messageUrl(serviceUrl)
                .title("您收到一条值班反馈消息！")
                .build();
        WorkNotificationBO bo = WorkNotificationBO.builder()
                .receiverIds(receiverIdSet.stream().collect(Collectors.joining(",")))
                .type(1)
                .linkBO(linkBO)
                .build();
        zwddService.workNotification(bo);

        //反馈登记人 短信通知
        MasSendContentBO masSendContentBO = new MasSendContentBO();
        WdSysOption firstByKey = wdSysOptionRepository.getFirstByKey("D15DutyRecordFeedback.SMS");
        Assert.notNull(firstByKey, "值班反馈模板没有配置!");
        String value = firstByKey.getValue();
        Map map = new HashMap();
        serviceUrl = this.serviceUrl + "/d/" + shortLinkService.create(serviceUrl, 0);
        map.put("url", serviceUrl);
        String message = SMSFormatUtil.processTemplate(value, map);
        masSendContentBO.setContent(message);

        List<MasUserBO> userBOList = new ArrayList<>();
        MasUserBO masUserBO = new MasUserBO();
        masUserBO.setNickName(d15DutyRecordFeedBack.getUsername());
        masUserBO.setPhone(d15DutyRecordFeedBack.getPhone());
        userBOList.add(masUserBO);
        masSendContentBO.setUserBOList(userBOList);

        masSendContentBO.setSendDate(new Date());
        masSendContentBO.setTiming(0);
        masBusinessService.sendSMContent(masSendContentBO, 0, 0);

        return save.getId();
    }

    @Override
    public Boolean existsByNumber(String number) {
        return d15DutyRecordRepository.existsD15DutyRecordByNumber(number);
    }
}
