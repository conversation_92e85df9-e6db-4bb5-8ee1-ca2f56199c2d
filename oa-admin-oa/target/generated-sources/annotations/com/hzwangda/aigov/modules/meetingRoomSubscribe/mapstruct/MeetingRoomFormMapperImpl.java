package com.hzwangda.aigov.modules.meetingRoomSubscribe.mapstruct;

import com.hzwangda.aigov.modules.form.convert.IdToUserConvert;
import com.hzwangda.aigov.modules.meetingRoomSubscribe.domain.MeetingRoomForm;
import com.hzwangda.aigov.modules.meetingRoomSubscribe.dto.MeetingRoomFormDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class MeetingRoomFormMapperImpl implements MeetingRoomFormMapper {

    @Autowired
    private IdToUserConvert idToUserConvert;

    @Override
    public List<MeetingRoomFormDto> toDto(List<MeetingRoomForm> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MeetingRoomFormDto> list = new ArrayList<MeetingRoomFormDto>( entityList.size() );
        for ( MeetingRoomForm meetingRoomForm : entityList ) {
            list.add( toDto( meetingRoomForm ) );
        }

        return list;
    }

    @Override
    public MeetingRoomForm toEntity(MeetingRoomFormDto dto) {
        if ( dto == null ) {
            return null;
        }

        MeetingRoomForm meetingRoomForm = new MeetingRoomForm();

        meetingRoomForm.setCreateBy( dto.getCreateBy() );
        meetingRoomForm.setCreateTime( dto.getCreateTime() );
        meetingRoomForm.setUpdateTime( dto.getUpdateTime() );
        meetingRoomForm.setUpdatedBy( dto.getUpdatedBy() );
        meetingRoomForm.setId( dto.getId() );
        meetingRoomForm.setRemark( dto.getRemark() );
        meetingRoomForm.setTitle( dto.getTitle() );

        return meetingRoomForm;
    }

    @Override
    public List<MeetingRoomForm> toEntity(List<MeetingRoomFormDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MeetingRoomForm> list = new ArrayList<MeetingRoomForm>( dtoList.size() );
        for ( MeetingRoomFormDto meetingRoomFormDto : dtoList ) {
            list.add( toEntity( meetingRoomFormDto ) );
        }

        return list;
    }

    @Override
    public MeetingRoomFormDto toDto(MeetingRoomForm entity) {
        if ( entity == null ) {
            return null;
        }

        MeetingRoomFormDto meetingRoomFormDto = new MeetingRoomFormDto();

        meetingRoomFormDto.setCreate( idToUserConvert.convert( entity.getCreateBy() ) );
        meetingRoomFormDto.setCreateBy( entity.getCreateBy() );
        meetingRoomFormDto.setCreateTime( entity.getCreateTime() );
        meetingRoomFormDto.setUpdateTime( entity.getUpdateTime() );
        meetingRoomFormDto.setUpdatedBy( entity.getUpdatedBy() );
        meetingRoomFormDto.setId( entity.getId() );
        meetingRoomFormDto.setRemark( entity.getRemark() );
        meetingRoomFormDto.setTitle( entity.getTitle() );

        return meetingRoomFormDto;
    }
}
