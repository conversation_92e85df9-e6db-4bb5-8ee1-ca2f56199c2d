package com.hzwangda.aigov.modules.document.entity.qfb;

import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Where;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 通用请假审批
 *
 * <AUTHOR>
 * @date 2023/02/09 上午10:43
 */
@Data
@Entity
@Table(name = "z01_qfb_leave")
public class Z01QfbLeave extends BaseBpmDomain implements Serializable {

    @ApiModelProperty(value = "所在科室")
    private String startDeptName;

    @ApiModelProperty(value = "申请人")
    private String startNickName;

    @ApiModelProperty(value = "请假类别")
    private String type;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @ApiModelProperty(value = "联系电话")
    private String contactNumber;

    @ApiModelProperty(value = "请假时间")
    private String qjsj;

    @Column(name = "qjsy")
    @ApiModelProperty(value = "请假事由")
    private String qjsy;

    @ApiModelProperty(value = "科室负责人审核意见")
    @Column(columnDefinition = "text")
    private String ksfzryj;

    @ApiModelProperty(value = "分管领导审核意见")
    @Column(columnDefinition = "text")
    private String fgldyj;

    @ApiModelProperty(value = "领导签批意见")
    @Column(columnDefinition = "text")
    private String ldqpyj;

    @ApiModelProperty(value = "备注")
    @Column(columnDefinition = "text")
    private String bz;

    @ApiModelProperty(value = "正文")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "zw", referencedColumnName = "id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private StorageBiz zw;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A01DocumentTyLeave.fj'")
    private List<StorageBiz> fj;

    public void setFj(List<StorageBiz> fj) {
        if (fj != null) {
            if (this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }

    @PrePersist
    @PreUpdate
    public void preCreateEntity() {
        super.preCreateEntity();
        if (!CollectionUtils.isEmpty(this.fj)) {
            for (StorageBiz storageBiz : this.fj) {
                if (Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                }
            }
        }
    }

    public void setZw(StorageBiz zw) {
        if (Objects.nonNull(zw)) {
            this.zw = zw;
        }
        if (Objects.nonNull(this.zw) && StringUtils.isNotBlank(this.zw.getStorageId())) {
            if (this.id != null) {
                this.zw.setBizId(this.id.toString());
            }
        } else {
            this.zw = null;
        }
    }
}
