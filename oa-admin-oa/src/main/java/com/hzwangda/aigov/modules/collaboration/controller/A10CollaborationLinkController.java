package com.hzwangda.aigov.modules.collaboration.controller;

import com.hzwangda.aigov.modules.collaboration.domain.criteria.ReferralInfo;
import com.hzwangda.aigov.modules.collaboration.service.A10CollaborationLinkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/collaborationLink")
@Api(tags = "转协同")
public class A10CollaborationLinkController {

    @Resource
    private A10CollaborationLinkService a10CollaborationLinkService;

    @ApiOperation("/转协同")
    @PostMapping("/referral")
    public ResponseEntity referral(@RequestBody ReferralInfo referralInfo) throws NoSuchFieldException, ClassNotFoundException, IllegalAccessException {
        return ResponseEntity.ok(a10CollaborationLinkService.referral(referralInfo));
    }

    @ApiOperation("/获取关联表单数据")
    @GetMapping("/findById")
    public ResponseEntity findById(@RequestParam String className, @RequestParam Long id) throws ClassNotFoundException {
        return ResponseEntity.ok(a10CollaborationLinkService.findById(className, id));
    }

}
