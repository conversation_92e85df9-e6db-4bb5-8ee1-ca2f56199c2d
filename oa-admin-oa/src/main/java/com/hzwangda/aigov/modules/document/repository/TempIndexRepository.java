package com.hzwangda.aigov.modules.document.repository;

import com.hzwangda.aigov.modules.document.entity.TempIndex;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface TempIndexRepository extends JpaRepository<TempIndex, Long>, JpaSpecificationExecutor<TempIndex> {

    List<TempIndex> findByFwIdIsNotNull();

    List<TempIndex> findBySwIdIsNotNull();

    List<TempIndex> findByFwIdNotInAndFwIdIsNotNull(List<String> ids);

    List<TempIndex> findBySwIdNotInAndSwIdIsNotNull(List<String> ids);
}
