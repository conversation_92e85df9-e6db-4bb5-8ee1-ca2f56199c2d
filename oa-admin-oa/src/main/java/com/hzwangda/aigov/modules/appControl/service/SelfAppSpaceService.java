package com.hzwangda.aigov.modules.appControl.service;


import com.hzwangda.aigov.modules.appControl.domain.Z08SelfAppSpace;
import com.hzwangda.aigov.modules.appControl.dto.SelfAppSpaceCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/03
 * @description
 */
public interface SelfAppSpaceService {
    /**
     * 新增或修改空间
     * @param space 空间
     * @return void
     */
    void addOrUpdate(Z08SelfAppSpace space);

    /**
     * 获取空间信息
     * @param id 空间id
     * @return Space 空间信息
     */
    Z08SelfAppSpace getById(Long id);


    /**
     * 获取空间信息
     * @param key 空间key
     * @return Space 空间信息
     */
    Z08SelfAppSpace getByKey(String key);

    /**
     * 查询空间列表
     * @param criteria 查询条件
     * @param pageable 分页
     * @return Page
     */
    Page<Z08SelfAppSpace> queryListPage(SelfAppSpaceCriteria criteria, Pageable pageable);

    /**
     * 查询空间列表不分页
     * @param criteria 查询条件
     * @return Page
     */
    List<Z08SelfAppSpace> queryList(SelfAppSpaceCriteria criteria);

    /**
     * 删除空间
     * @param id 空间id
     * @return boolean
     */
    boolean delete(Long id);

    boolean validatePermission(String userRule, String username);
}
