package com.hzwangda.aigov.modules.matter.service.mapstruct;

import com.hzwangda.aigov.modules.matter.domain.Matter;
import com.hzwangda.aigov.modules.matter.service.dto.MatterDto;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class MatterMapperImpl implements MatterMapper {

    @Override
    public MatterDto toDto(Matter entity) {
        if ( entity == null ) {
            return null;
        }

        MatterDto matterDto = new MatterDto();

        matterDto.setCreateBy( entity.getCreateBy() );
        matterDto.setAdvisoryTelephone( entity.getAdvisoryTelephone() );
        matterDto.setApplicants( entity.getApplicants() );
        matterDto.setBelongTheme( entity.getBelongTheme() );
        matterDto.setBusinessOffice( entity.getBusinessOffice() );
        matterDto.setChargeBasis( entity.getChargeBasis() );
        if ( entity.getCreateDate() != null ) {
            matterDto.setCreateDate( new Timestamp( entity.getCreateDate().getTime() ) );
        }
        matterDto.setCreatorId( entity.getCreatorId() );
        matterDto.setDecisionBodies( entity.getDecisionBodies() );
        matterDto.setDelStatus( entity.getDelStatus() );
        matterDto.setDeptCode( entity.getDeptCode() );
        matterDto.setDeptName( entity.getDeptName() );
        matterDto.setEnabled( entity.getEnabled() );
        matterDto.setFinishingDay( entity.getFinishingDay() );
        matterDto.setFinishingTimeLimit( entity.getFinishingTimeLimit() );
        matterDto.setHandleOffice( entity.getHandleOffice() );
        matterDto.setId( entity.getId() );
        if ( entity.getIsAcrossLayers() != null ) {
            matterDto.setIsAcrossLayers( String.valueOf( entity.getIsAcrossLayers() ) );
        }
        if ( entity.getIsInternet() != null ) {
            matterDto.setIsInternet( String.valueOf( entity.getIsInternet() ) );
        }
        if ( entity.getIsOne() != null ) {
            matterDto.setIsOne( String.valueOf( entity.getIsOne() ) );
        }
        if ( entity.getIsUnion() != null ) {
            matterDto.setIsUnion( String.valueOf( entity.getIsUnion() ) );
        }
        if ( entity.getIsZero() != null ) {
            matterDto.setIsZero( String.valueOf( entity.getIsZero() ) );
        }
        matterDto.setLegalBasis( entity.getLegalBasis() );
        matterDto.setMainItem( entity.getMainItem() );
        matterDto.setMatterIcon( entity.getMatterIcon() );
        matterDto.setMatterName( entity.getMatterName() );
        matterDto.setMatterRemark( entity.getMatterRemark() );
        matterDto.setMatterStatus( entity.getMatterStatus() );
        matterDto.setModeOfService( entity.getModeOfService() );
        if ( entity.getModifiedDate() != null ) {
            matterDto.setModifiedDate( new Timestamp( entity.getModifiedDate().getTime() ) );
        }
        matterDto.setModifiedId( entity.getModifiedId() );
        matterDto.setOfficeAddress( entity.getOfficeAddress() );
        matterDto.setOfficeHours( entity.getOfficeHours() );
        matterDto.setProImages( entity.getProImages() );
        matterDto.setProKey( entity.getProKey() );
        matterDto.setRunTimes( entity.getRunTimes() );
        matterDto.setSort( entity.getSort() );
        matterDto.setTimeNum( entity.getTimeNum() );
        matterDto.setTimeUnit( entity.getTimeUnit() );
        matterDto.setUrlDd( entity.getUrlDd() );
        matterDto.setUrlPc( entity.getUrlPc() );
        matterDto.setVersion( entity.getVersion() );

        return matterDto;
    }

    @Override
    public List<MatterDto> toDto(List<Matter> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MatterDto> list = new ArrayList<MatterDto>( entityList.size() );
        for ( Matter matter : entityList ) {
            list.add( toDto( matter ) );
        }

        return list;
    }

    @Override
    public Matter toEntity(MatterDto dto) {
        if ( dto == null ) {
            return null;
        }

        Matter matter = new Matter();

        matter.setCreateBy( dto.getCreateBy() );
        matter.setCreateDate( dto.getCreateDate() );
        matter.setCreatorId( dto.getCreatorId() );
        matter.setEnabled( dto.getEnabled() );
        matter.setId( dto.getId() );
        matter.setModifiedDate( dto.getModifiedDate() );
        matter.setModifiedId( dto.getModifiedId() );
        matter.setVersion( dto.getVersion() );
        matter.setAdvisoryTelephone( dto.getAdvisoryTelephone() );
        matter.setApplicants( dto.getApplicants() );
        matter.setBelongTheme( dto.getBelongTheme() );
        matter.setBusinessOffice( dto.getBusinessOffice() );
        matter.setChargeBasis( dto.getChargeBasis() );
        matter.setDecisionBodies( dto.getDecisionBodies() );
        matter.setDelStatus( dto.getDelStatus() );
        matter.setDeptCode( dto.getDeptCode() );
        matter.setDeptName( dto.getDeptName() );
        matter.setFinishingDay( dto.getFinishingDay() );
        matter.setFinishingTimeLimit( dto.getFinishingTimeLimit() );
        matter.setHandleOffice( dto.getHandleOffice() );
        if ( dto.getIsAcrossLayers() != null ) {
            matter.setIsAcrossLayers( Integer.parseInt( dto.getIsAcrossLayers() ) );
        }
        if ( dto.getIsInternet() != null ) {
            matter.setIsInternet( Integer.parseInt( dto.getIsInternet() ) );
        }
        if ( dto.getIsOne() != null ) {
            matter.setIsOne( Integer.parseInt( dto.getIsOne() ) );
        }
        if ( dto.getIsUnion() != null ) {
            matter.setIsUnion( Integer.parseInt( dto.getIsUnion() ) );
        }
        if ( dto.getIsZero() != null ) {
            matter.setIsZero( Integer.parseInt( dto.getIsZero() ) );
        }
        matter.setLegalBasis( dto.getLegalBasis() );
        matter.setMainItem( dto.getMainItem() );
        matter.setMatterIcon( dto.getMatterIcon() );
        matter.setMatterName( dto.getMatterName() );
        matter.setMatterRemark( dto.getMatterRemark() );
        matter.setMatterStatus( dto.getMatterStatus() );
        matter.setModeOfService( dto.getModeOfService() );
        matter.setOfficeAddress( dto.getOfficeAddress() );
        matter.setOfficeHours( dto.getOfficeHours() );
        matter.setProImages( dto.getProImages() );
        matter.setProKey( dto.getProKey() );
        matter.setRunTimes( dto.getRunTimes() );
        matter.setSort( dto.getSort() );
        matter.setTimeNum( dto.getTimeNum() );
        matter.setTimeUnit( dto.getTimeUnit() );
        matter.setUrlDd( dto.getUrlDd() );
        matter.setUrlPc( dto.getUrlPc() );

        return matter;
    }

    @Override
    public List<Matter> toEntity(List<MatterDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Matter> list = new ArrayList<Matter>( dtoList.size() );
        for ( MatterDto matterDto : dtoList ) {
            list.add( toEntity( matterDto ) );
        }

        return list;
    }
}
