/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.bpm.repository;

import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlzFeedback;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-05-14
 **/
public interface A07DocumentGwlzFeedbackRepository extends JpaRepository<A07DocumentGwlzFeedback, Long>, JpaSpecificationExecutor<A07DocumentGwlzFeedback> {
    /***
     * @Description: 根据老的公文id，更新成新的公文ID
     * @Author: lizh
     * @Date: 2021/9/23 9:58
     **/
    @Modifying
    @Query("update A07DocumentGwlzFeedback a set a.docID=?1 where a.docID=?2")
    void updatedocIDBydocID(Long newID, Long oldID);

    /***
     * @Description:
     * @Author: lizh
     * @Date: 2021/9/23 10:14
     **/
    @Transactional(rollbackFor = Exception.class)
    void deleteBydocIDIn(List<Long> docID);
}
