package com.hzwangda.aigov.modules.schedule.domain.mapsturct;

import com.wangda.oa.modules.extension.domain.SysDeptUserPosition;
import com.wangda.oa.modules.extension.dto.LeadershipListDto;
import com.wangda.oa.modules.system.service.dto.UserDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class SysDeptUserPositionMapperImpl implements SysDeptUserPositionMapper {

    @Override
    public LeadershipListDto toDto(SysDeptUserPosition entity) {
        if ( entity == null ) {
            return null;
        }

        LeadershipListDto leadershipListDto = new LeadershipListDto();

        leadershipListDto.setUserId( entity.getUserId() );
        leadershipListDto.setUserName( entity.getUserName() );

        convertUserName( entity, leadershipListDto );

        return leadershipListDto;
    }

    @Override
    public List<LeadershipListDto> toDto(List<SysDeptUserPosition> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<LeadershipListDto> list = new ArrayList<LeadershipListDto>( entityList.size() );
        for ( SysDeptUserPosition sysDeptUserPosition : entityList ) {
            list.add( toDto( sysDeptUserPosition ) );
        }

        return list;
    }

    @Override
    public SysDeptUserPosition toEntity(LeadershipListDto dto) {
        if ( dto == null ) {
            return null;
        }

        SysDeptUserPosition sysDeptUserPosition = new SysDeptUserPosition();

        sysDeptUserPosition.setUserId( dto.getUserId() );
        sysDeptUserPosition.setUserName( dto.getUserName() );

        return sysDeptUserPosition;
    }

    @Override
    public List<SysDeptUserPosition> toEntity(List<LeadershipListDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<SysDeptUserPosition> list = new ArrayList<SysDeptUserPosition>( dtoList.size() );
        for ( LeadershipListDto leadershipListDto : dtoList ) {
            list.add( toEntity( leadershipListDto ) );
        }

        return list;
    }

    @Override
    public LeadershipListDto fromUserDto(UserDto entity) {
        if ( entity == null ) {
            return null;
        }

        LeadershipListDto leadershipListDto = new LeadershipListDto();

        leadershipListDto.setUserId( entity.getId() );
        leadershipListDto.setUserName( entity.getUsername() );
        leadershipListDto.setNickName( entity.getNickName() );

        return leadershipListDto;
    }

    @Override
    public List<LeadershipListDto> fromUserDto(List<UserDto> entity) {
        if ( entity == null ) {
            return null;
        }

        List<LeadershipListDto> list = new ArrayList<LeadershipListDto>( entity.size() );
        for ( UserDto userDto : entity ) {
            list.add( fromUserDto( userDto ) );
        }

        return list;
    }
}
