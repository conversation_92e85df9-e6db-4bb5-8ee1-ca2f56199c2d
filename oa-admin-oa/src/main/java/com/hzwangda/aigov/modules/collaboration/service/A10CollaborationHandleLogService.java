package com.hzwangda.aigov.modules.collaboration.service;

import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationAssignee;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationHandleLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface A10CollaborationHandleLogService {

    /**
     * 工作协同-工作协同-发送、签收、转交、评论
     *
     * @param log
     */
    Long doHandle(A10CollaborationHandleLog log);

    /**
     * 所有待签收协同
     *
     * @param infoId
     * @param pageable
     * @return
     */
    Page<A10CollaborationHandleLog> queryHandleLog(Long infoId, Pageable pageable);

    /**
     * 查询未签收
     *
     * @param infoId
     * @return
     */
    List<A10CollaborationAssignee> queryUnsignedAssignees(Long infoId);

    Page<A10CollaborationAssignee> queryAssignees(Long infoId, String status, String keyword, Pageable pageable);
}
