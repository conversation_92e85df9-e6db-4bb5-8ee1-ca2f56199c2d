package com.hzwangda.aigov.modules.document.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class MaxWhCriteria {
    @NotBlank(message = "代字不能为空")
    private String dz;
    @NotNull(message = "年号不能为空")
    private Integer nh;
    @NotNull(message = "文号不能为空")
    private Integer wh;
    @NotBlank(message = "单位id不能为空")
    private String belongToDept;
}
