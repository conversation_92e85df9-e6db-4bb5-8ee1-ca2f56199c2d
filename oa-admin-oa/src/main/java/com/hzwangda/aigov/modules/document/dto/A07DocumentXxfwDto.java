package com.hzwangda.aigov.modules.document.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 收文单
 *
 * <AUTHOR>
 * @date 2021/9/1上午11:29
 */
@Data
public class A07DocumentXxfwDto extends BaseDomain {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "标题")
    private String bt;
    @ApiModelProperty(value = "流程实例id")
    private String bpmInstanceId;
    @ApiModelProperty(name = "状态")
    private String bpmStatus;
    @ApiModelProperty(name = "创建人")
    private String cjr;
    @ApiModelProperty(value = "类型")
    private String moduleType;

    @ApiModelProperty(value = "发布时间")
    @JSONField(format = "yyyy-MM-dd")
    private Date releaseDate;
    @ApiModelProperty(value = "发布部门")
    private String deptName;
    @ApiModelProperty(value = "办理人")
    private String blr;
    @ApiModelProperty(value = "taskid")
    private String taskId;

    @ApiModelProperty(value = "发送数量")
    private Integer sendNum;
    @ApiModelProperty(value = "签收数量")
    private Integer receiveNum;
    @ApiModelProperty(value = "是否撤回")
    private Boolean revoke;
    @ApiModelProperty(value = "是否不可见")
    private Boolean invisible;
}
