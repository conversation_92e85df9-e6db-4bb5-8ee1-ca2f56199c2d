package com.hzwangda.aigov.modules.document.entity;

import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;
import org.hibernate.annotations.Where;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Immutable
@Subselect("select * from fw_view")
@Entity
@Data
public class A07DocumentFwView extends BaseBpmDomain implements Serializable {
    @Id
    private Long id;

    @ApiModelProperty(value = "文号")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "wh_id", referencedColumnName = "id")
    private ReferenceNumber gwwh;

    @Column(name = "hj")
    @ApiModelProperty(value = "缓急(普通,急)")
    private String hj;

    @Column(name = "bt")
    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "正文")
    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "zw", referencedColumnName = "id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private StorageBiz zw;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type like '%.fj%'")
    private List<StorageBiz> fj;

    @ApiModelProperty(value = "主送单位")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "zsdw", referencedColumnName = "id")
    private SysDeptUserMain zsdw;

    @ApiModelProperty(value = "抄送单位")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "csdw", referencedColumnName = "id")
    private SysDeptUserMain csdw;

    @Column(name = "zbbm")
    @ApiModelProperty(value = "主办部门")
    private String zbbm;

    @Column(name = "ngr")
    @ApiModelProperty(value = "拟稿人")
    private String ngr;

    @Column(name = "ngdw")
    @ApiModelProperty(value = "拟稿单位")
    private String ngdw;

    @Column(name = "gklx")
    @ApiModelProperty(value = "公开类型(主动公开,依申请公开,不予公开)")
    private String gklx;

    @Column(name = "bz", columnDefinition = "text")
    @ApiModelProperty(value = "备注")
    private String bz;

    @ApiModelProperty(value = "拟稿日期")
    private Timestamp ngrq;

    @ApiModelProperty(value = "联系电话")
    private String lxdh;

    public void setZw(StorageBiz zw) {
        if(Objects.nonNull(zw)) {
            this.zw = zw;
        }
        if(Objects.nonNull(this.zw) && StringUtils.isNotBlank(this.zw.getStorageId())) {
            if(this.id != null) {
                this.zw.setBizId(this.id.toString());
            }
        }else {
            this.zw = null;
        }
    }

    public void setFj(List<StorageBiz> fj) {
        if(fj != null) {
            if(this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }


    @Override
    @PrePersist
    @PreUpdate
    public void preCreateEntity() {
        super.preCreateEntity();
        if(!CollectionUtils.isEmpty(this.fj)) {
            for(StorageBiz storageBiz : this.fj) {
                if(Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                }
            }
        }
        if(Objects.nonNull(this.zw) && StringUtils.isNotBlank(this.zw.getStorageId())) {
            this.zw.setBizId(this.id.toString());
        }else {
            this.zw = null;
        }
    }
}
