package com.hzwangda.aigov.modules.document.service;

import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.collaboration.domain.criteria.SmsInfo;
import com.hzwangda.aigov.modules.document.dto.*;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlz;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.extension.dto.org.OrgListDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface A07DocExchangeService {


    ResultJson openDoc(String docid);

    Page<A07DocExchangeListFwDto> getDocExchangeFwList(A07DocExchangeQueryCriteria criteria, Pageable pageable);

    Page<A07DocExchangeListSwDto> getDocExchangeSwList(A07DocExchangeQueryCriteria criteria, Pageable pageable);

    Long saveGwlz(A07DocumentGwlz resources, Integer type);

    Long editGwlz(A07DocumentGwlz resources);

    A07DocumentGwlzDto getGwlzInfo(Long id);

    Map<String, Object> getSignRecords(A07DocExchangeSignCriteria criteria, Pageable pageable);

    String batchDel(Long[] id);

    String sign(Long id);

    String revoke(Long id);

    String back(Long id);

    List<A07DocExchangeListSwDto> getHyGwList(Integer num, String type);

    /**
     * 获取主送、抄送单位的树结构
     * @param infoId
     * @return
     */
    ResultJson<List<OrgListDto>> getDeptTree(Long infoId);

    /**
     * 获取填报数量
     * @param infoId
     * @return
     */
    ResultJson<Map<String, Integer>> getNum(Long infoId);

    ResultJson<String> smsUrge(Set<Long> ids);

    /**
     * 公文交换转收文
     * @param id
     * @param appId
     * @return
     */
    ResultJson<JSONObject> doTransferToDeptOA(Long id,Long appId);


    ResultJson<JSONObject> markTransferToDeptOA(Long id);

    Long fwToGw(String procInstId);

    Map getSms(Long id);

    String smsUrge(SmsInfo smsInfo);

    void exportAll(Long id, HttpServletRequest request, HttpServletResponse response) throws IOException;


    void exportFw(A07DocExchangeQueryCriteria queryCriteria, HttpServletRequest request, HttpServletResponse response);

    void exportFwZwZip(A07DocExchangeQueryCriteria queryCriteria, HttpServletRequest request, HttpServletResponse response);
}
