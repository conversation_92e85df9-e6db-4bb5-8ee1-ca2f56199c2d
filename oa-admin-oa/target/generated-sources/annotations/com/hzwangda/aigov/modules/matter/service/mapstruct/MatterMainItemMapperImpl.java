package com.hzwangda.aigov.modules.matter.service.mapstruct;

import com.hzwangda.aigov.modules.matter.domain.MatterMainItem;
import com.hzwangda.aigov.modules.matter.service.dto.MatterMainItemDto;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class MatterMainItemMapperImpl implements MatterMainItemMapper {

    @Override
    public MatterMainItemDto toDto(MatterMainItem entity) {
        if ( entity == null ) {
            return null;
        }

        MatterMainItemDto matterMainItemDto = new MatterMainItemDto();

        matterMainItemDto.setCreateBy( entity.getCreateBy() );
        if ( entity.getCreateDate() != null ) {
            matterMainItemDto.setCreateDate( new Timestamp( entity.getCreateDate().getTime() ) );
        }
        matterMainItemDto.setCreatorId( entity.getCreatorId() );
        matterMainItemDto.setDelStatus( entity.getDelStatus() );
        matterMainItemDto.setEnabled( entity.getEnabled() );
        matterMainItemDto.setId( entity.getId() );
        matterMainItemDto.setIsAutomatic( entity.getIsAutomatic() );
        matterMainItemDto.setIsManual( entity.getIsManual() );
        matterMainItemDto.setItemName( entity.getItemName() );
        if ( entity.getModifiedDate() != null ) {
            matterMainItemDto.setModifiedDate( new Timestamp( entity.getModifiedDate().getTime() ) );
        }
        matterMainItemDto.setModifiedId( entity.getModifiedId() );
        matterMainItemDto.setSort( entity.getSort() );
        matterMainItemDto.setVersion( entity.getVersion() );

        return matterMainItemDto;
    }

    @Override
    public List<MatterMainItemDto> toDto(List<MatterMainItem> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MatterMainItemDto> list = new ArrayList<MatterMainItemDto>( entityList.size() );
        for ( MatterMainItem matterMainItem : entityList ) {
            list.add( toDto( matterMainItem ) );
        }

        return list;
    }

    @Override
    public MatterMainItem toEntity(MatterMainItemDto dto) {
        if ( dto == null ) {
            return null;
        }

        MatterMainItem matterMainItem = new MatterMainItem();

        matterMainItem.setCreateBy( dto.getCreateBy() );
        matterMainItem.setCreateDate( dto.getCreateDate() );
        matterMainItem.setCreatorId( dto.getCreatorId() );
        matterMainItem.setEnabled( dto.getEnabled() );
        matterMainItem.setId( dto.getId() );
        matterMainItem.setModifiedDate( dto.getModifiedDate() );
        matterMainItem.setModifiedId( dto.getModifiedId() );
        matterMainItem.setVersion( dto.getVersion() );
        matterMainItem.setDelStatus( dto.getDelStatus() );
        matterMainItem.setIsAutomatic( dto.getIsAutomatic() );
        matterMainItem.setIsManual( dto.getIsManual() );
        matterMainItem.setItemName( dto.getItemName() );
        matterMainItem.setSort( dto.getSort() );

        return matterMainItem;
    }

    @Override
    public List<MatterMainItem> toEntity(List<MatterMainItemDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MatterMainItem> list = new ArrayList<MatterMainItem>( dtoList.size() );
        for ( MatterMainItemDto matterMainItemDto : dtoList ) {
            list.add( toEntity( matterMainItemDto ) );
        }

        return list;
    }
}
