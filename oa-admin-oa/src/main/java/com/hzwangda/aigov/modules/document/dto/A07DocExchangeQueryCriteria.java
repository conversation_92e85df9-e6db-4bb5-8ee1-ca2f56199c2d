package com.hzwangda.aigov.modules.document.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Set;

@ApiModel
@Data
public class A07DocExchangeQueryCriteria {

    @Query(propName = "nh", joinName = "gwwh")
    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "类型")
    private String gwzl;

    @ApiModelProperty(value = "公文状态")
    @Query(propName = "gwzt", type = Query.Type.IN)
    private Set<String> gwzt;

    @Query(propName = "bt", type = Query.Type.INNER_LIKE)
    @ApiModelProperty(value = "搜索内容")
    private String searchKeys;

    @ApiModelProperty(value = "状态: 0:待,1:已")
    private Integer status;

    @Query(type = Query.Type.BETWEEN)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private List<Date> fwrq;

    private String deptCode;

}
