package com.hzwangda.aigov.modules.collaboration.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
 * @program: oa-mgt-server
 * @description:
 * @author: liux
 * @create: 2021-08-12 14:57
 */
@Data
@ApiModel(value = "返回工作协同-列表")
public class A10CollaborationInfo4ListDto {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "主题")
    private String subject;

    @ApiModelProperty(value = "紧急程度(是否加急，1：是，0：否)")
    private String urgent;

    @ApiModelProperty(value = "状态[草稿(draft),已发送(sent)]")
    private String status;

    @ApiModelProperty(value = "发送时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date sendTime;

    @ApiModelProperty(value = "创建人")
    private SimpleUserDto createBy;

    @ApiModelProperty(value = "到达时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date arriveTime;

    @ApiModelProperty(value = "创建时间", hidden = true)
    @JSONField(format = "yyyy-MM-dd HH:mm")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Timestamp createTime;
}
