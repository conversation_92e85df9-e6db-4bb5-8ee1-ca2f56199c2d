package com.hzwangda.aigov.modules.taskmanagement.domain.mapstruct;

import com.hzwangda.aigov.modules.taskmanagement.domain.dto.TmPlanFinishDto;
import com.hzwangda.aigov.modules.taskmanagement.domain.entity.TmPlanFinish;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class TmPlanFinishMapperImpl implements TmPlanFinishMapper {

    @Override
    public TmPlanFinishDto toDto(TmPlanFinish entity) {
        if ( entity == null ) {
            return null;
        }

        TmPlanFinishDto tmPlanFinishDto = new TmPlanFinishDto();

        tmPlanFinishDto.setCreateBy( entity.getCreateBy() );
        tmPlanFinishDto.setCreateTime( entity.getCreateTime() );
        tmPlanFinishDto.setUpdateTime( entity.getUpdateTime() );
        tmPlanFinishDto.setUpdatedBy( entity.getUpdatedBy() );
        tmPlanFinishDto.setFirstQuarter( entity.getFirstQuarter() );
        tmPlanFinishDto.setFourthQuarter( entity.getFourthQuarter() );
        tmPlanFinishDto.setId( entity.getId() );
        tmPlanFinishDto.setSecondQuarter( entity.getSecondQuarter() );
        tmPlanFinishDto.setTask( entity.getTask() );
        tmPlanFinishDto.setThirdQuarter( entity.getThirdQuarter() );

        return tmPlanFinishDto;
    }

    @Override
    public List<TmPlanFinishDto> toDto(List<TmPlanFinish> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmPlanFinishDto> list = new ArrayList<TmPlanFinishDto>( entityList.size() );
        for ( TmPlanFinish tmPlanFinish : entityList ) {
            list.add( toDto( tmPlanFinish ) );
        }

        return list;
    }

    @Override
    public TmPlanFinish toEntity(TmPlanFinishDto dto) {
        if ( dto == null ) {
            return null;
        }

        TmPlanFinish tmPlanFinish = new TmPlanFinish();

        tmPlanFinish.setCreateBy( dto.getCreateBy() );
        tmPlanFinish.setCreateTime( dto.getCreateTime() );
        tmPlanFinish.setUpdateTime( dto.getUpdateTime() );
        tmPlanFinish.setUpdatedBy( dto.getUpdatedBy() );
        tmPlanFinish.setFirstQuarter( dto.getFirstQuarter() );
        tmPlanFinish.setFourthQuarter( dto.getFourthQuarter() );
        tmPlanFinish.setId( dto.getId() );
        tmPlanFinish.setSecondQuarter( dto.getSecondQuarter() );
        tmPlanFinish.setTask( dto.getTask() );
        tmPlanFinish.setThirdQuarter( dto.getThirdQuarter() );

        return tmPlanFinish;
    }

    @Override
    public List<TmPlanFinish> toEntity(List<TmPlanFinishDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TmPlanFinish> list = new ArrayList<TmPlanFinish>( dtoList.size() );
        for ( TmPlanFinishDto tmPlanFinishDto : dtoList ) {
            list.add( toEntity( tmPlanFinishDto ) );
        }

        return list;
    }
}
