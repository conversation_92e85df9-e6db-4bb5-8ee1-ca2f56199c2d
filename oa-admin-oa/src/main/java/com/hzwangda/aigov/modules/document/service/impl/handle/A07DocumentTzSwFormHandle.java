package com.hzwangda.aigov.modules.document.service.impl.handle;

import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.document.entity.qfb.Z01QfbTzSw;
import com.hzwangda.aigov.modules.document.repository.qfb.Z01QfbTzSwRepository;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.workflow.dto.BacklogListDto;
import com.wangda.oa.modules.workflow.dto.MyWorkDto;
import com.wangda.oa.modules.workflow.enums.MyWorkStatusEnum;
import com.wangda.oa.modules.workflow.service.FlowFormHandle;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskInfo;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/13
 * @description 区府办通知收文
 */
@Component
@Slf4j
public class A07DocumentTzSwFormHandle implements FlowFormHandle {

    @Autowired
    private Z01QfbTzSwRepository z01QfbTzSwRepository;

    @Resource
    private UserService userService;

    @Autowired
    private HistoryService historyService;

    @Resource
    private TaskService taskService;

    @Override
    public void handleFormForMyWork(BacklogListDto myWork, MyWorkDto myWorkDto) {
        myWorkDto.setId(myWork.getId());
        myWorkDto.setProcessInstanceId(myWork.getBizId());
        myWorkDto.setTitle(myWork.getTitle());
        myWorkDto.setType(myWork.getModuleName());
        myWorkDto.setCreateTime(myWork.getCreateDate());
        myWorkDto.setPcUrl(myWork.getPcUrl());
        myWorkDto.setLogo(myWork.getLogo());
        if(Objects.nonNull(myWork.getUrgent())) {
            // 0:非急件,1:急件
            if(myWork.getUrgent().equals(1)) {
                myWorkDto.setHj("急");
            }else {
                myWorkDto.setHj("普通");
            }
        }

        Z01QfbTzSw z01QfbTzSw = z01QfbTzSwRepository.findFirstByBpmInstanceId(myWork.getBizId());
        if(Objects.nonNull(z01QfbTzSw)) {
            myWorkDto.setTitle(z01QfbTzSw.getBt());
            myWorkDto.setHj("普通");
            List<HistoricTaskInstance> historicTaskInstanceList = historyService.createHistoricTaskInstanceQuery().processInstanceId(myWork.getBizId()).taskAssignee(SecurityUtils.getCurrentUsername()).orderByHistoricTaskInstanceStartTime().desc().list();
            HistoricTaskInstance historicTaskInstance = historicTaskInstanceList.get(0);
            myWorkDto.setCreateTime(historicTaskInstance.getCreateTime());
            if(Objects.isNull(historicTaskInstance.getEndTime())) {
                myWorkDto.setStatus(MyWorkStatusEnum.BLZ);
            }else {
                myWorkDto.setStatus(MyWorkStatusEnum.YBL);
            }
        }
    }

    @Override
    public String handleSubjectRule(JSONObject formDataObj, String subjectRule) {
        return null;
    }

    @Override
    public Object handleFormRecord(String procInstanceId, String taskDefKey, JSONObject bpmFormData) {
        // 如果表单数据有值且不需要处理，直接返回
        if(Objects.nonNull(bpmFormData)) {
            List<Task> list = taskService.createTaskQuery()
                    .active()
                    .taskDefinitionKey("usertask03")
                    .processInstanceId(procInstanceId)
                    .list();
            List<String> usernames = list.stream().map(TaskInfo::getAssignee).collect(Collectors.toList());
            List<UserDto> users = userService.findByUserNames(usernames);
            String collect = users.stream().map(UserDto::getName).collect(Collectors.joining("， "));
            if (StringUtils.isNotEmpty(collect)) {
                bpmFormData.put("units", "已送： " + collect);
            } else {
                bpmFormData.put("units", null);
            }
            return bpmFormData;
        }
        Z01QfbTzSw document = z01QfbTzSwRepository.findFirstByBpmInstanceId(procInstanceId);
        // 初始化默认值
        if(Objects.isNull(document)) {
            document = new Z01QfbTzSw();
            UserDto userInfoDto = userService.findById(SecurityUtils.getCurrentUserId());
            document.setQsr(userInfoDto.getNickName());
            document.setHj("普通");
            document.setLwrq(new Date());
            document.setSwrq(new Date());
        }
        return JSONObject.toJSON(document);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFormRecord(String instanceId) {
        z01QfbTzSwRepository.deleteByBpmInstanceId(instanceId);
    }
}
