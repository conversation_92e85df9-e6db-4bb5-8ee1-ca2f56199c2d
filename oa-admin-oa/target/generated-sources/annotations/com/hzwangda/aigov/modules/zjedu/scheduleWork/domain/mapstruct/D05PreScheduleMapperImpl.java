package com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.dto.D05PreScheduleDto;
import com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.entity.D05PreSchedule;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class D05PreScheduleMapperImpl implements D05PreScheduleMapper {

    @Override
    public D05PreScheduleDto toDto(D05PreSchedule entity) {
        if ( entity == null ) {
            return null;
        }

        D05PreScheduleDto d05PreScheduleDto = new D05PreScheduleDto();

        d05PreScheduleDto.setCreateBy( entity.getCreateBy() );
        d05PreScheduleDto.setCreateTime( entity.getCreateTime() );
        d05PreScheduleDto.setUpdateTime( entity.getUpdateTime() );
        d05PreScheduleDto.setUpdatedBy( entity.getUpdatedBy() );
        d05PreScheduleDto.setDepartment( entity.getDepartment() );
        d05PreScheduleDto.setDeptUserName( entity.getDeptUserName() );
        d05PreScheduleDto.setEndTime( entity.getEndTime() );
        d05PreScheduleDto.setId( entity.getId() );
        d05PreScheduleDto.setIsNextDay( entity.getIsNextDay() );
        d05PreScheduleDto.setIsOver( entity.getIsOver() );
        d05PreScheduleDto.setScheduleOreder( entity.getScheduleOreder() );
        d05PreScheduleDto.setScheduleRule( entity.getScheduleRule() );
        d05PreScheduleDto.setScheduleType( entity.getScheduleType() );
        d05PreScheduleDto.setScheduleUser( entity.getScheduleUser() );
        d05PreScheduleDto.setScheduleUserName( entity.getScheduleUserName() );
        d05PreScheduleDto.setStartTime( entity.getStartTime() );
        d05PreScheduleDto.setUseDate( entity.getUseDate() );

        return d05PreScheduleDto;
    }

    @Override
    public List<D05PreScheduleDto> toDto(List<D05PreSchedule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<D05PreScheduleDto> list = new ArrayList<D05PreScheduleDto>( entityList.size() );
        for ( D05PreSchedule d05PreSchedule : entityList ) {
            list.add( toDto( d05PreSchedule ) );
        }

        return list;
    }

    @Override
    public D05PreSchedule toEntity(D05PreScheduleDto dto) {
        if ( dto == null ) {
            return null;
        }

        D05PreSchedule d05PreSchedule = new D05PreSchedule();

        d05PreSchedule.setCreateBy( dto.getCreateBy() );
        d05PreSchedule.setCreateTime( dto.getCreateTime() );
        d05PreSchedule.setUpdateTime( dto.getUpdateTime() );
        d05PreSchedule.setUpdatedBy( dto.getUpdatedBy() );
        d05PreSchedule.setDepartment( dto.getDepartment() );
        d05PreSchedule.setDeptUserName( dto.getDeptUserName() );
        d05PreSchedule.setEndTime( dto.getEndTime() );
        d05PreSchedule.setId( dto.getId() );
        d05PreSchedule.setIsNextDay( dto.getIsNextDay() );
        d05PreSchedule.setIsOver( dto.getIsOver() );
        d05PreSchedule.setScheduleOreder( dto.getScheduleOreder() );
        d05PreSchedule.setScheduleRule( dto.getScheduleRule() );
        d05PreSchedule.setScheduleType( dto.getScheduleType() );
        d05PreSchedule.setScheduleUser( dto.getScheduleUser() );
        d05PreSchedule.setScheduleUserName( dto.getScheduleUserName() );
        d05PreSchedule.setStartTime( dto.getStartTime() );
        d05PreSchedule.setUseDate( dto.getUseDate() );

        return d05PreSchedule;
    }

    @Override
    public List<D05PreSchedule> toEntity(List<D05PreScheduleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<D05PreSchedule> list = new ArrayList<D05PreSchedule>( dtoList.size() );
        for ( D05PreScheduleDto d05PreScheduleDto : dtoList ) {
            list.add( toEntity( d05PreScheduleDto ) );
        }

        return list;
    }
}
