package com.hzwangda.aigov.modules.duty.repository;

import com.hzwangda.aigov.modules.duty.domain.entity.D15DutyRecordUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;


public interface D15DutyRecordUserRepository extends JpaRepository<D15DutyRecordUser, Long>, JpaSpecificationExecutor<D15DutyRecordUser> {

    @Modifying
    @Query("update D15DutyRecordUser set readFlag=1, readTime=?3 where recordId=?1 and username=?2 and readFlag=0")
    void readByRecordIdAndAndUsername(Long recordId, String username, Date readTime);

    D15DutyRecordUser findFirstByRecordIdAndUsernameOrderByReadFlag(Long id, String currentUsername);

    List<D15DutyRecordUser> findAllByUsernameInAndReadFlag(List<String> usernames, Integer readFlag);
}
