package com.hzwangda.aigov.modules.zjedu.meeting.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35MeetingOutlayDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35MeetingOutlay;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class C35MeetingOutlayMapperImpl implements C35MeetingOutlayMapper {

    @Override
    public C35MeetingOutlayDto toDto(C35MeetingOutlay entity) {
        if ( entity == null ) {
            return null;
        }

        C35MeetingOutlayDto c35MeetingOutlayDto = new C35MeetingOutlayDto();

        c35MeetingOutlayDto.setBelongToDept( entity.getBelongToDept() );
        c35MeetingOutlayDto.setBpmInstanceId( entity.getBpmInstanceId() );
        c35MeetingOutlayDto.setBpmProcessKey( entity.getBpmProcessKey() );
        c35MeetingOutlayDto.setBpmStatus( entity.getBpmStatus() );
        c35MeetingOutlayDto.setBpmSubject( entity.getBpmSubject() );
        c35MeetingOutlayDto.setCreateBy( entity.getCreateBy() );
        c35MeetingOutlayDto.setCreateTime( entity.getCreateTime() );
        List<String> list = entity.getParticipateUser();
        if ( list != null ) {
            c35MeetingOutlayDto.setParticipateUser( new ArrayList<String>( list ) );
        }
        c35MeetingOutlayDto.setUpdateBy( entity.getUpdateBy() );
        c35MeetingOutlayDto.setUpdateTime( entity.getUpdateTime() );
        c35MeetingOutlayDto.setVersion( entity.getVersion() );
        c35MeetingOutlayDto.setApplyDate( entity.getApplyDate() );
        c35MeetingOutlayDto.setApplyDept( entity.getApplyDept() );
        c35MeetingOutlayDto.setBalance( entity.getBalance() );
        c35MeetingOutlayDto.setDeclareWay( entity.getDeclareWay() );
        c35MeetingOutlayDto.setDeptLeader( entity.getDeptLeader() );
        c35MeetingOutlayDto.setDeptManager( entity.getDeptManager() );
        c35MeetingOutlayDto.setDescribetion( entity.getDescribetion() );
        c35MeetingOutlayDto.setFinanceLeader( entity.getFinanceLeader() );
        c35MeetingOutlayDto.setId( entity.getId() );
        c35MeetingOutlayDto.setIsNative( entity.getIsNative() );
        c35MeetingOutlayDto.setMealFee( entity.getMealFee() );
        c35MeetingOutlayDto.setMeeting( entity.getMeeting() );
        c35MeetingOutlayDto.setMeetingDay( entity.getMeetingDay() );
        c35MeetingOutlayDto.setPrintingFee( entity.getPrintingFee() );
        c35MeetingOutlayDto.setPurchaseLeader( entity.getPurchaseLeader() );
        c35MeetingOutlayDto.setRepresentAmount( entity.getRepresentAmount() );
        c35MeetingOutlayDto.setSpaceFee( entity.getSpaceFee() );
        c35MeetingOutlayDto.setStayFee( entity.getStayFee() );
        c35MeetingOutlayDto.setSumFee( entity.getSumFee() );
        c35MeetingOutlayDto.setTotalOutlay( entity.getTotalOutlay() );
        c35MeetingOutlayDto.setTrafficFee( entity.getTrafficFee() );
        c35MeetingOutlayDto.setWorkerAmount( entity.getWorkerAmount() );

        return c35MeetingOutlayDto;
    }

    @Override
    public List<C35MeetingOutlayDto> toDto(List<C35MeetingOutlay> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<C35MeetingOutlayDto> list = new ArrayList<C35MeetingOutlayDto>( entityList.size() );
        for ( C35MeetingOutlay c35MeetingOutlay : entityList ) {
            list.add( toDto( c35MeetingOutlay ) );
        }

        return list;
    }

    @Override
    public C35MeetingOutlay toEntity(C35MeetingOutlayDto dto) {
        if ( dto == null ) {
            return null;
        }

        C35MeetingOutlay c35MeetingOutlay = new C35MeetingOutlay();

        c35MeetingOutlay.setBelongToDept( dto.getBelongToDept() );
        c35MeetingOutlay.setBpmInstanceId( dto.getBpmInstanceId() );
        c35MeetingOutlay.setBpmProcessKey( dto.getBpmProcessKey() );
        c35MeetingOutlay.setBpmStatus( dto.getBpmStatus() );
        c35MeetingOutlay.setBpmSubject( dto.getBpmSubject() );
        c35MeetingOutlay.setCreateBy( dto.getCreateBy() );
        c35MeetingOutlay.setCreateTime( dto.getCreateTime() );
        c35MeetingOutlay.setId( dto.getId() );
        List<String> list = dto.getParticipateUser();
        if ( list != null ) {
            c35MeetingOutlay.setParticipateUser( new ArrayList<String>( list ) );
        }
        c35MeetingOutlay.setUpdateBy( dto.getUpdateBy() );
        c35MeetingOutlay.setUpdateTime( dto.getUpdateTime() );
        c35MeetingOutlay.setVersion( dto.getVersion() );
        c35MeetingOutlay.setApplyDate( dto.getApplyDate() );
        c35MeetingOutlay.setApplyDept( dto.getApplyDept() );
        c35MeetingOutlay.setBalance( dto.getBalance() );
        c35MeetingOutlay.setDeclareWay( dto.getDeclareWay() );
        c35MeetingOutlay.setDeptLeader( dto.getDeptLeader() );
        c35MeetingOutlay.setDeptManager( dto.getDeptManager() );
        c35MeetingOutlay.setDescribetion( dto.getDescribetion() );
        c35MeetingOutlay.setFinanceLeader( dto.getFinanceLeader() );
        c35MeetingOutlay.setIsNative( dto.getIsNative() );
        c35MeetingOutlay.setMealFee( dto.getMealFee() );
        c35MeetingOutlay.setMeeting( dto.getMeeting() );
        c35MeetingOutlay.setMeetingDay( dto.getMeetingDay() );
        c35MeetingOutlay.setPrintingFee( dto.getPrintingFee() );
        c35MeetingOutlay.setPurchaseLeader( dto.getPurchaseLeader() );
        c35MeetingOutlay.setRepresentAmount( dto.getRepresentAmount() );
        c35MeetingOutlay.setSpaceFee( dto.getSpaceFee() );
        c35MeetingOutlay.setStayFee( dto.getStayFee() );
        c35MeetingOutlay.setSumFee( dto.getSumFee() );
        c35MeetingOutlay.setTotalOutlay( dto.getTotalOutlay() );
        c35MeetingOutlay.setTrafficFee( dto.getTrafficFee() );
        c35MeetingOutlay.setWorkerAmount( dto.getWorkerAmount() );

        return c35MeetingOutlay;
    }

    @Override
    public List<C35MeetingOutlay> toEntity(List<C35MeetingOutlayDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<C35MeetingOutlay> list = new ArrayList<C35MeetingOutlay>( dtoList.size() );
        for ( C35MeetingOutlayDto c35MeetingOutlayDto : dtoList ) {
            list.add( toEntity( c35MeetingOutlayDto ) );
        }

        return list;
    }
}
