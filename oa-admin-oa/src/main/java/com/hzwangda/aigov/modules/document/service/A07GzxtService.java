package com.hzwangda.aigov.modules.document.service;

import com.hzwangda.aigov.modules.document.dto.A08GzxtDto;
import com.hzwangda.aigov.modules.document.dto.A08GzxtQueryCriteria;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGzxt;
import com.hzwangda.aigov.modules.workflow.bo.GzxtUserListBO;
import com.hzwangda.aigov.modules.workflow.dto.MyConferenceListDto;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface A07GzxtService {
    /**
     * 协同列表
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> getXtList(A08GzxtQueryCriteria criteria, Pageable pageable);

    /**
     * 协同详情
     *
     * @param id
     * @return
     */
    A08GzxtDto getXtInfo(Long id);

    /**
     * 工作协同操作
     *
     * @param resources
     * @return
     */
    A07DocumentGzxt createOrUpdateGzxt(A07DocumentGzxt resources);

    /**
     * 用户工作协同列表
     *
     * @return
     */
    MyConferenceListDto getGzxtUserList(GzxtUserListBO gzxtUserListBO);

    /**
     * 删除协同
     *
     * @param ids
     * @return
     */
    Boolean deleteXt(List<Long> ids);
}
