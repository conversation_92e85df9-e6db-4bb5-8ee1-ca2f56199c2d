package com.hzwangda.aigov.modules.addresslist.domain.entity;

import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

/**
 * @author: zhangzhanlong
 * @date: 2022/8/5 10:47
 * @description: 单位通讯录
 */
@Entity
@Data
@Table(name = "z08_unit_address_list")
public class UnitAddressList extends BaseEntity {
    @Id
    private Long id;

    @ApiModelProperty(value = "分类Id")
    @Column(name = "group_id")
    @NotNull
    private Long groupId;

    @ApiModelProperty(value = "分类名称")
    @Column(name = "group_name")
    private String groupName;

    @ApiModelProperty(value = "姓名")
    @Column(name = "nick_name")
    @NotNull
    private String nickName;

    @ApiModelProperty(value = "用户名")
    @Column(name = "username")
    private String username;

    @ApiModelProperty(value = "单位/部门Id")
    @Column(name = "unit_id")
    private Long unitId;

    @ApiModelProperty(value = "单位/部门名称")
    @Column(name = "unit_name")
    private String unitName;

    @ApiModelProperty(value = "职位")
    @Column(name = "job_name")
    private String jobName;

    @ApiModelProperty(value = "手机号")
    @Column(name = "phone")
    private String phone;

    @ApiModelProperty(value = "办电")
    @Column(name = "office_tel")
    private String officeTel;

    @ApiModelProperty(value = "备注")
    @Column(name = "remark", length = 2000)
    private String remark;

    @ApiModelProperty(value = "排序")
    @Column(name = "sort")
    private Integer sort;


    @Override
    @PrePersist
    @PreUpdate
    public void preCreateEntity() {
        super.preCreateEntity();
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }
}
