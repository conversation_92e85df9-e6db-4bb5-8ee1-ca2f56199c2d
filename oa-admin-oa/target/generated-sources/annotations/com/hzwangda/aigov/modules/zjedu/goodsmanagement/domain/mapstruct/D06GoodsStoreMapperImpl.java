package com.hzwangda.aigov.modules.zjedu.goodsmanagement.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.goodsmanagement.domain.dto.D06GoodsStoreDto;
import com.hzwangda.aigov.modules.zjedu.goodsmanagement.domain.entity.D06GoodsStore;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class D06GoodsStoreMapperImpl implements D06GoodsStoreMapper {

    @Override
    public D06GoodsStoreDto toDto(D06GoodsStore entity) {
        if ( entity == null ) {
            return null;
        }

        D06GoodsStoreDto d06GoodsStoreDto = new D06GoodsStoreDto();

        d06GoodsStoreDto.setCreateBy( entity.getCreateBy() );
        d06GoodsStoreDto.setCreateTime( entity.getCreateTime() );
        d06GoodsStoreDto.setUpdateTime( entity.getUpdateTime() );
        d06GoodsStoreDto.setDescription( entity.getDescription() );
        d06GoodsStoreDto.setId( entity.getId() );

        return d06GoodsStoreDto;
    }

    @Override
    public List<D06GoodsStoreDto> toDto(List<D06GoodsStore> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<D06GoodsStoreDto> list = new ArrayList<D06GoodsStoreDto>( entityList.size() );
        for ( D06GoodsStore d06GoodsStore : entityList ) {
            list.add( toDto( d06GoodsStore ) );
        }

        return list;
    }

    @Override
    public D06GoodsStore toEntity(D06GoodsStoreDto dto) {
        if ( dto == null ) {
            return null;
        }

        D06GoodsStore d06GoodsStore = new D06GoodsStore();

        d06GoodsStore.setCreateBy( dto.getCreateBy() );
        d06GoodsStore.setCreateTime( dto.getCreateTime() );
        d06GoodsStore.setId( dto.getId() );
        d06GoodsStore.setUpdateTime( dto.getUpdateTime() );
        d06GoodsStore.setDescription( dto.getDescription() );

        return d06GoodsStore;
    }

    @Override
    public List<D06GoodsStore> toEntity(List<D06GoodsStoreDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<D06GoodsStore> list = new ArrayList<D06GoodsStore>( dtoList.size() );
        for ( D06GoodsStoreDto d06GoodsStoreDto : dtoList ) {
            list.add( toEntity( d06GoodsStoreDto ) );
        }

        return list;
    }
}
