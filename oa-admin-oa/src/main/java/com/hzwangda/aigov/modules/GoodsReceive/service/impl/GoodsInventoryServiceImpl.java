package com.hzwangda.aigov.modules.GoodsReceive.service.impl;

import com.hzwangda.aigov.modules.GoodsReceive.domain.GoodsInventory;
import com.hzwangda.aigov.modules.GoodsReceive.dto.GoodsInventoryCriteria;
import com.hzwangda.aigov.modules.GoodsReceive.repository.GoodsInventoryRepository;
import com.hzwangda.aigov.modules.GoodsReceive.service.GoodsInventoryService;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import java.util.List;

@Service
public class GoodsInventoryServiceImpl implements GoodsInventoryService {

    @Resource
    private GoodsInventoryRepository goodsInventoryRepository;

    @Override
    public List<GoodsInventory> findAllByPid(GoodsInventoryCriteria goodsInventoryCriteria) {
        Specification<GoodsInventory> sp = (root, query, criteriaBuilder) -> {
            return QueryHelp.getPredicate(root, goodsInventoryCriteria, criteriaBuilder);
        };
        return goodsInventoryRepository.findAll(sp);
    }

    @Override
    public List<GoodsInventory> findAllByPidIsNull(GoodsInventoryCriteria goodsInventoryCriteria) {
        Specification<GoodsInventory> sp = (root, query, criteriaBuilder) -> {
            Predicate predicate = QueryHelp.getPredicate(root, goodsInventoryCriteria, criteriaBuilder);
            predicate = criteriaBuilder.and(
                    predicate,
                    root.get("pid").isNull()
            );
            return predicate;
        };
        return goodsInventoryRepository.findAll(sp);
    }

    @Override
    public List<GoodsInventory> findAll(GoodsInventoryCriteria goodsInventoryCriteria) {
        return goodsInventoryRepository.findAll((root, query, criteriaBuilder) -> {
            return QueryHelp.getPredicate(root, goodsInventoryCriteria, criteriaBuilder);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(GoodsInventory goodsInventory) {
        return goodsInventoryRepository.save(goodsInventory).getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object delByIds(Long[] ids) {
        return goodsInventoryRepository.deleteAllByIdIn(ids);
    }

    @Override
    public List<GoodsInventory> findChildrenByName(String name, Boolean leaf, GoodsInventoryCriteria goodsInventoryCriteria) {
        Specification<GoodsInventory> sp = (root, criteriaQuery, criteriaBuilder) -> {
            Predicate predicate;
            if (leaf != null) {
                predicate = criteriaBuilder.equal(root.get("leaf"), leaf);
            } else {
                predicate = criteriaBuilder.isNull(root.get("leaf"));
            }
            if (StringUtils.isNotEmpty(name)) {
                Subquery<Long> subquery = criteriaQuery.subquery(Long.class);
                Root<GoodsInventory> from = subquery.from(GoodsInventory.class);
                subquery.select(from.get("id"));
                subquery.where(criteriaBuilder.equal(from.get("name"), name));
                predicate = root.get("pid").in(subquery);
            }

            predicate = criteriaBuilder.and(
                    QueryHelp.getPredicate(root, goodsInventoryCriteria, criteriaBuilder),
                    predicate);

            return predicate;
        };
        return goodsInventoryRepository.findAll(sp);
    }
}
