package com.hzwangda.aigov.modules.document.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2022/10/27 14:59
 * @description: 文种维护
 */
@Data
@Entity
@Table(name = "z08_document_sw_type")
public class Z08DocumentSwType extends BaseBpmDomain implements Serializable {

    @ApiModelProperty(value = "文种（代字）")
    @Column(name = "dz")
    private String dz;

    @ApiModelProperty(value = "所属单位")
    @Column(name = "dept_name")
    private String deptName;

    @ApiModelProperty(value = "所属单位Id")
    @Column(name = "dept_id")
    private String deptId;

    @ApiModelProperty(value = "排序")
    @Column(name = "sort")
    private Integer sort;

    @ApiModelProperty(value = "最大文号")
    @Transient
    private String maxXh;
    public void copy(Z08DocumentSwType documentType) {
        BeanUtil.copyProperties(documentType, this, CopyOptions.create().setIgnoreNullValue(true));
    }

}
