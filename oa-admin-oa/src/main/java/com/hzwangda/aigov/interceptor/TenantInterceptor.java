package com.hzwangda.aigov.interceptor;

import com.hzwangda.aigov.aspect.TenantIgnore;
import com.hzwangda.aigov.config.TenantProperties;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.SpringContextHolder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.dreamlu.mica.core.utils.ThreadLocalUtil;
import net.sf.jsqlparser.expression.BinaryExpression;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.Parenthesis;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.conditional.OrExpression;
import net.sf.jsqlparser.expression.operators.relational.*;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.Statements;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.statement.update.Update;
import org.hibernate.resource.jdbc.spi.StatementInspector;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;

@Slf4j
@Data
@Component
public class TenantInterceptor implements StatementInspector {


    /**
     * 需要租户解析的表
     */
    private Set<String> tenantTables;

    /**
     * 租户字段名
     */
    private String tenantColumn = "belong_to_dept";

    @Override
    public synchronized String inspect(String sql) {
        try {
            Stack<Method> stack = ((Stack<Method>) ThreadLocalUtil.get("method"));
            if(stack != null && !stack.empty()) {
                TenantIgnore annotation = stack.peek().getAnnotation(TenantIgnore.class);
                if(annotation == null) {

                    log.info("当前线程：{}", Thread.currentThread());

                    /**
                     * 初始化需要进行租户解析的租户表
                     */
                    if(tenantTables == null) {
                        TenantProperties tenantProperties = SpringContextHolder.getBean(TenantProperties.class);
                        tenantTables = tenantProperties.getTenantTables();
                    }
                    /**
                     * 非租户用户不解析
                     */
                    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                    if(Objects.isNull(authentication)) {
                        return sql;
                    }else {
                        if(authentication.getPrincipal().equals("anonymousUser") || "superAdmin".equals(SecurityUtils.getCurrentUsername())) {
                            return sql;
                        }
                    }
                    /**
                     * 切换到全部不解析
                     */
                 /*   Map deptCodeMap = SecurityUtils.getRedisCurrentDeptCode();
                    if(!((Boolean) deptCodeMap.get("available"))) {
                        return sql;
                    }*/

                    log.info("租户解析开始，原始sql：{}", sql);
                    Statements statements = CCJSqlParserUtil.parseStatements(sql);
                    StringBuilder stringBuilder = new StringBuilder();
                    statements.getStatements().stream().forEach(statement -> {
                        stringBuilder.append(this.processParser(statement));
                        stringBuilder.append(";");
                    });
                    log.info("租户解析完成，解析后sql：{}", stringBuilder.toString());
                    return stringBuilder.toString();
                }
            }
        }catch(Exception e) {
            log.error("租户解析失败，解析sql异常{}", e.getMessage());
            e.printStackTrace();
        }finally {
        }
        return sql;
    }

    private String processParser(Statement statement) {
        if(statement instanceof Insert) {
//            this.processInsert(((Insert) statement));
        }else if(statement instanceof Select) {
            this.processSelect(((Select) statement).getSelectBody());
        }else if(statement instanceof Update) {
//            this.processUpdate(((Update) statement));
        }else if(statement instanceof Delete) {
//            this.processDelete(((Delete) statement));
        }
        return statement.toString();
    }

    private void processDelete(Delete statement) {
        if(tenantTables.contains(statement.getTable().getFullyQualifiedName())) {
            // statement.setWhere(this.andExpression(statement.getTable(), statement.getWhere()));
        }
    }

    private void processUpdate(Update statement) {
        Table table = statement.getTable();
        if(tenantTables.contains(table.getFullyQualifiedName())) {
            //   statement.setWhere(this.andExpression(table, statement.getWhere()));
        }
    }

    private void processSelect(SelectBody selectBody) {
        if(selectBody instanceof PlainSelect) {
            this.processPlainSelect(((PlainSelect) selectBody));
        }else if(selectBody instanceof WithItem) {
            WithItem withItem = (WithItem) selectBody;
            if(withItem.getSelectBody() != null) {
                this.processSelect(withItem.getSelectBody());
            }
        }else {
            SetOperationList body = (SetOperationList) selectBody;
            if(body.getSelects() != null && !body.getSelects().isEmpty()) {
                body.getSelects().forEach(this::processSelect);
            }
        }
    }

    private void processInsert(Insert statement) {
        /**
         * 获取租户id
         */
        String tenantId = SecurityUtils.getCurrentDeptCode();
        if(tenantTables.contains(statement.getTable().getFullyQualifiedName())) {
            statement.getColumns().add(new Column(tenantColumn));
            if(statement.getSelect() != null) {
                this.processPlainSelect((PlainSelect) statement.getSelect().getSelectBody(), true);
            }else if(statement.getItemsList() != null) {
                ItemsList itemsList = statement.getItemsList();
                if(itemsList instanceof MultiExpressionList) {
                    ((MultiExpressionList) itemsList).getExprList().stream().forEach(expressionList -> {
                        expressionList.getExpressions().add(new StringValue(tenantId));
                    });
                }else {
                    ((ExpressionList) itemsList).getExpressions().add(new StringValue(tenantId));
                }
            }else {
                throw new RuntimeException("Failed to process multiple-table update, please exclude the tableName or statementId");
            }
        }
    }

    /**
     * where语句处理
     * @param table
     * @param where
     * @return
     */
    private Expression andExpression(Table table, Expression where) {
        /**
         * 获取租户id
         */
        String tenantId = SecurityUtils.getCurrentDeptCode();
        EqualsTo equalsTo = new EqualsTo();
        equalsTo.setLeftExpression(this.getAliasColumn(table));
        equalsTo.setRightExpression(new StringValue(tenantId));
        if(null != where) {
            if(where instanceof OrExpression) {
                return new AndExpression(equalsTo, new Parenthesis(where));
            }
            return new AndExpression(equalsTo, where);
        }
        return equalsTo;
    }

    /**
     * 租户字段别名设置
     * @param table
     * @return
     */
    private Expression getAliasColumn(Table table) {
        StringBuilder stringBuilder = new StringBuilder();
        if(null == table.getAlias()) {
            stringBuilder.append(table.getName());
        }else {
            stringBuilder.append(table.getAlias().getName());
        }
        stringBuilder.append(".");
        stringBuilder.append(tenantColumn);
        return new Column(stringBuilder.toString());
    }


    /**
     * 处理plainSelect
     * @param selectBody
     */
    private void processPlainSelect(PlainSelect selectBody) {
        processPlainSelect(selectBody, false);
    }

    private void processPlainSelect(PlainSelect selectBody, boolean addColumn) {
        FromItem fromItem = selectBody.getFromItem();
        if(fromItem instanceof Table) {
            Table table = (Table) fromItem;
            if(tenantTables.contains(table.getFullyQualifiedName())) {
                selectBody.setWhere(buildExpression(selectBody.getWhere(), table));
                if(addColumn) {
                    selectBody.getSelectItems().add(new SelectExpressionItem(new Column(tenantColumn)));
                }
            }
        }else {
            this.processFromItem(fromItem);
            List<Join> joins = selectBody.getJoins();
            if(joins != null && !joins.isEmpty()) {
                joins.stream().forEach(join -> {
                    this.processJoin(join);
                    this.processFromItem(join.getRightItem());
                });
            }
        }
    }

    /**
     * 处理join语句
     * @param join
     */
    private void processJoin(Join join) {
        if(join.getRightItem() instanceof Table) {
            Table table = (Table) join.getRightItem();
            if(tenantTables.contains(table.getFullyQualifiedName())) {
                join.setOnExpression(buildExpression(join.getOnExpression(), table));
            }
        }
    }

    /**
     * 处理子查询
     * @param fromItem
     */
    private void processFromItem(FromItem fromItem) {
        if(fromItem instanceof SubJoin) {
            SubJoin subJoin = (SubJoin) fromItem;
            if(subJoin.getJoinList() != null) {
                subJoin.getJoinList().stream().forEach(this::processJoin);
            }
            if(subJoin.getLeft() != null) {
                this.processFromItem(subJoin.getLeft());
            }
        }else if(fromItem instanceof SubSelect) {
            SubSelect subSelect = (SubSelect) fromItem;
            if(subSelect.getSelectBody() != null) {
                this.processSelect(subSelect.getSelectBody());
            }
        }else if(fromItem instanceof ValuesList) {

        }else if(fromItem instanceof LateralSubSelect) {
            LateralSubSelect lateralSubSelect = (LateralSubSelect) fromItem;
            if(lateralSubSelect.getSubSelect().getSelectBody() != null) {
                this.processSelect(lateralSubSelect.getSubSelect().getSelectBody());
            }
        }

    }

    private Expression buildExpression(Expression where, Table table) {
        /**
         * 获取租户id
         */
        String tenantId = SecurityUtils.getCurrentBelongToDept();
        Expression expression = new StringValue(tenantId);
        Expression appendExpression;
        if(!(expression instanceof SupportsOldOracleJoinSyntax)) {
            appendExpression = new EqualsTo();
            ((EqualsTo) appendExpression).setLeftExpression(this.getAliasColumn(table));
            ((EqualsTo) appendExpression).setRightExpression(expression);
        }else {
            appendExpression = this.processTableAlias4CustomizedTenantIdExpression(expression, table);
        }
        if(where == null) {
            return appendExpression;
        }
        if(where instanceof BinaryExpression) {
            BinaryExpression binaryExpression = (BinaryExpression) where;
            doExpression(binaryExpression.getLeftExpression());
            doExpression(binaryExpression.getRightExpression());
        }else if(where instanceof InExpression) {
            InExpression inExpression = (InExpression) where;
            ItemsList rightItemsList = inExpression.getRightItemsList();
            if(rightItemsList instanceof SubSelect) {
                this.processSelect(((SubSelect) rightItemsList).getSelectBody());
            }
        }
        if(where instanceof OrExpression) {
            return new AndExpression(new Parenthesis(where), appendExpression);
        }
        return new AndExpression(where, appendExpression);
    }


    private Expression processTableAlias4CustomizedTenantIdExpression(Expression expression, Table table) {
        return expression;
    }

    private void doExpression(Expression rightExpression) {
        if(rightExpression instanceof FromItem) {
            this.processFromItem(((FromItem) rightExpression));
        }else if(rightExpression instanceof InExpression) {
            InExpression inExpression = (InExpression) rightExpression;
            ItemsList rightItemsList = inExpression.getRightItemsList();
            if(rightExpression instanceof SubSelect) {
                this.processSelect(((SubSelect) rightExpression).getSelectBody());
            }
        }
    }
}
