package com.hzwangda.aigov.modules.archive.repository;

import com.hzwangda.aigov.modules.archive.entity.A12Archives;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * <AUTHOR>
 * @Date 2021/9/7
 **/
public interface A12ArchivesRepository extends JpaRepository<A12Archives, Long>, JpaSpecificationExecutor<A12Archives> {

    /**
     * 根据目录id查询是否存在文件
     *
     * @param directoryId
     * @return
     */
    Integer countByDirectoryId(Long directoryId);

    /**
     * 根据目录id和存储id查询
     *
     * @param directoryId
     * @param storageId
     * @return
     */
    A12Archives findFirstByDirectoryIdAndLocalStorageId(Long directoryId, Long storageId);
}
