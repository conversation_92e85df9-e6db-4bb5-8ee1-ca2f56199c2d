package com.hzwangda.aigov.modules.meetingRoomSubscribe.mapstruct;

import com.hzwangda.aigov.modules.form.convert.IdToDeptConvert;
import com.hzwangda.aigov.modules.form.convert.IdToUserConvert;
import com.hzwangda.aigov.modules.meetingRoomSubscribe.domain.MeetingRoomData;
import com.hzwangda.aigov.modules.meetingRoomSubscribe.domain.MeetingRoomFormInstantiate;
import com.hzwangda.aigov.modules.meetingRoomSubscribe.dto.MeetingRoomFormDataDto;
import com.hzwangda.aigov.modules.meetingRoomSubscribe.dto.MeetingRoomFormInstantiateDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class MeetingRoomDataMapperImpl implements MeetingRoomDataMapper {

    @Autowired
    private IdToUserConvert idToUserConvert;
    @Autowired
    private IdToDeptConvert idToDeptConvert;

    @Override
    public List<MeetingRoomFormDataDto> toDto(List<MeetingRoomData> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MeetingRoomFormDataDto> list = new ArrayList<MeetingRoomFormDataDto>( entityList.size() );
        for ( MeetingRoomData meetingRoomData : entityList ) {
            list.add( toDto( meetingRoomData ) );
        }

        return list;
    }

    @Override
    public List<MeetingRoomData> toEntity(List<MeetingRoomFormDataDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MeetingRoomData> list = new ArrayList<MeetingRoomData>( dtoList.size() );
        for ( MeetingRoomFormDataDto meetingRoomFormDataDto : dtoList ) {
            list.add( toEntity( meetingRoomFormDataDto ) );
        }

        return list;
    }

    @Override
    public MeetingRoomFormDataDto toDto(MeetingRoomData entity) {
        if ( entity == null ) {
            return null;
        }

        MeetingRoomFormDataDto meetingRoomFormDataDto = new MeetingRoomFormDataDto();

        meetingRoomFormDataDto.setDept( idToDeptConvert.convert( entity.getDeptId() ) );
        meetingRoomFormDataDto.setCreate( idToUserConvert.convert( entity.getCreateBy() ) );
        meetingRoomFormDataDto.setCreateBy( entity.getCreateBy() );
        meetingRoomFormDataDto.setCreateTime( entity.getCreateTime() );
        meetingRoomFormDataDto.setUpdateTime( entity.getUpdateTime() );
        meetingRoomFormDataDto.setUpdatedBy( entity.getUpdatedBy() );
        meetingRoomFormDataDto.setId( entity.getId() );
        meetingRoomFormDataDto.setInstantiate( meetingRoomFormInstantiateToMeetingRoomFormInstantiateDto( entity.getInstantiate() ) );

        return meetingRoomFormDataDto;
    }

    @Override
    public MeetingRoomData toEntity(MeetingRoomFormDataDto dto) {
        if ( dto == null ) {
            return null;
        }

        MeetingRoomData meetingRoomData = new MeetingRoomData();

        meetingRoomData.setDeptId( idToDeptConvert.convert( dto.getDept() ) );
        meetingRoomData.setCreateBy( idToUserConvert.convert( dto.getCreate() ) );
        meetingRoomData.setCreateTime( dto.getCreateTime() );
        meetingRoomData.setUpdateTime( dto.getUpdateTime() );
        meetingRoomData.setUpdatedBy( dto.getUpdatedBy() );
        meetingRoomData.setId( dto.getId() );
        meetingRoomData.setInstantiate( meetingRoomFormInstantiateDtoToMeetingRoomFormInstantiate( dto.getInstantiate() ) );

        return meetingRoomData;
    }

    protected MeetingRoomFormInstantiateDto meetingRoomFormInstantiateToMeetingRoomFormInstantiateDto(MeetingRoomFormInstantiate meetingRoomFormInstantiate) {
        if ( meetingRoomFormInstantiate == null ) {
            return null;
        }

        MeetingRoomFormInstantiateDto meetingRoomFormInstantiateDto = new MeetingRoomFormInstantiateDto();

        meetingRoomFormInstantiateDto.setBizType( meetingRoomFormInstantiate.getBizType() );
        meetingRoomFormInstantiateDto.setEndTime( meetingRoomFormInstantiate.getEndTime() );
        meetingRoomFormInstantiateDto.setId( meetingRoomFormInstantiate.getId() );
        meetingRoomFormInstantiateDto.setStartTime( meetingRoomFormInstantiate.getStartTime() );
        meetingRoomFormInstantiateDto.setTitle( meetingRoomFormInstantiate.getTitle() );

        return meetingRoomFormInstantiateDto;
    }

    protected MeetingRoomFormInstantiate meetingRoomFormInstantiateDtoToMeetingRoomFormInstantiate(MeetingRoomFormInstantiateDto meetingRoomFormInstantiateDto) {
        if ( meetingRoomFormInstantiateDto == null ) {
            return null;
        }

        MeetingRoomFormInstantiate meetingRoomFormInstantiate = new MeetingRoomFormInstantiate();

        meetingRoomFormInstantiate.setBizType( meetingRoomFormInstantiateDto.getBizType() );
        meetingRoomFormInstantiate.setEndTime( meetingRoomFormInstantiateDto.getEndTime() );
        meetingRoomFormInstantiate.setId( meetingRoomFormInstantiateDto.getId() );
        meetingRoomFormInstantiate.setStartTime( meetingRoomFormInstantiateDto.getStartTime() );
        meetingRoomFormInstantiate.setTitle( meetingRoomFormInstantiateDto.getTitle() );

        return meetingRoomFormInstantiate;
    }
}
