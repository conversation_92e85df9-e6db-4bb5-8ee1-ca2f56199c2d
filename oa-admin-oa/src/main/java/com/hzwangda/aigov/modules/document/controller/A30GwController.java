package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.dto.A07DocumentGwQueryCriteria;
import com.hzwangda.aigov.modules.document.service.A30GwService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "文件归档")
@RequestMapping("/api/aigov/document/gw")
@CrossOrigin
public class A30GwController {

    private final A30GwService a30GwService;

    @ApiOperation("待归档列表")
    @GetMapping(value = "/getGwWaitArchiveList")
    public ResponseEntity<Object> getGwWaitArchiveList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a30GwService.getGwWaitArchiveList(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("预归档整理")
    @GetMapping(value = "/getGwPutInOrder")
    public ResponseEntity<Object> getGwPutInOrder(@RequestParam("id") Long id, @RequestParam("type") String type) {
        return new ResponseEntity<>(a30GwService.getGwPutInOrder(id, type), HttpStatus.OK);
    }


    @ApiOperation("传阅意见上表单")
    @PostMapping(value = "/remarkToFormField")
    public ResponseEntity<Object> remarkToFormField(@RequestParam(value = "remark", required = false) String remark, @RequestParam("bpmProcInstId") String bpmProcInstId, @RequestParam(value = "users", required = false) String users) {
        a30GwService.remarkToFormField(remark, bpmProcInstId, users);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("流转记录-转阅列表操作记录查询")
    @GetMapping(value = "/getFlowDocReadOperateTable")
    public ResponseEntity<List<Map<String, String>>> getFlowDocReadOperateTable(@RequestParam("bpmProcInstId") String bpmProcInstId) {

        return new ResponseEntity<>(a30GwService.getFlowDocReadOperateTable(bpmProcInstId), HttpStatus.OK);
    }

    @ApiOperation("流转记录-转阅列表查询")
    @GetMapping(value = "/getFlowDocReadTable")
    public ResponseEntity<List<Map<String, String>>> getFlowDocReadTable(@RequestParam("bpmProcInstId") String bpmProcInstId) {

        return new ResponseEntity<>(a30GwService.getFlowDocReadTable(bpmProcInstId), HttpStatus.OK);
    }

    @ApiOperation("转阅人重复查询")
    @GetMapping(value = "/getRepetitionReadUsers")
    public ResponseEntity<String> getRepetitionReadUsers(@RequestParam("bpmProcInstId") String bpmProcInstId, @RequestParam("users") String users) {

        return new ResponseEntity<>(a30GwService.getRepetitionReadUsers(bpmProcInstId, users), HttpStatus.OK);
    }
}
