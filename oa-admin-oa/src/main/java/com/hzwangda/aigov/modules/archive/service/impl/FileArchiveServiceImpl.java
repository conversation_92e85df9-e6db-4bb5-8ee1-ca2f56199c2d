package com.hzwangda.aigov.modules.archive.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.file.visitor.DelVisitor;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bstek.ureport.export.ExportConfigure;
import com.bstek.ureport.export.ExportConfigureImpl;
import com.bstek.ureport.export.ExportManager;
import com.hzwangda.aigov.docconvert.common.domain.entity.SysStorageConversion;
import com.hzwangda.aigov.docconvert.common.repository.SysStorageConversionRepository;
import com.hzwangda.aigov.modules.archive.entity.A07FileArchive;
import com.hzwangda.aigov.modules.archive.entity.FileArchiveCriteria;
import com.hzwangda.aigov.modules.archive.entity.FileArchiveDto;
import com.hzwangda.aigov.modules.archive.mapstruct.A07FileArchiveMapper;
import com.hzwangda.aigov.modules.archive.repository.FileArchiveRepository;
import com.hzwangda.aigov.modules.archive.service.FileArchiveService;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07DocumentMapperUtil;
import com.hzwangda.aigov.modules.document.entity.*;
import com.hzwangda.aigov.modules.document.repository.A07DocumentFwViewRepository;
import com.hzwangda.aigov.modules.document.repository.SwAllViewRepository;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.hzwangda.aigov.oa.domain.WdArchiveInfo;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import com.hzwangda.aigov.oa.repository.ArchiveInfoRepository;
import com.taiyu.typrecenter.util.Md5Util;
import com.wangda.oa.config.ElPermissionConfig;
import com.wangda.oa.config.FileProperties;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.extension.dto.UserDataDto;
import com.wangda.oa.modules.extension.mapper.WdSysOptionCustomMapper;
import com.wangda.oa.modules.security.service.dto.JwtUserDto;
import com.wangda.oa.modules.system.domain.DictDetail;
import com.wangda.oa.modules.system.domain.Role;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.DictDetailRepository;
import com.wangda.oa.modules.system.repository.RoleRepositoryCustom;
import com.wangda.oa.modules.system.repository.UserRepositoryCustom;
import com.wangda.oa.modules.workflow.domain.application.Application;
import com.wangda.oa.modules.workflow.domain.form.PrintTemplate;
import com.wangda.oa.modules.workflow.domain.workflow.vo.ProcessQueryVo;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskDto;
import com.wangda.oa.modules.workflow.repository.form.PrintTemplateRepository;
import com.wangda.oa.modules.workflow.service.application.ApplicationService;
import com.wangda.oa.repository.LocalStorageRepository;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.file.DeletingPathVisitor;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.XMLWriter;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FileArchiveServiceImpl implements FileArchiveService {

    @Resource
    private FileArchiveRepository fileArchiveRepository;
    @Resource
    private A07FileArchiveMapper fileArchiveMapper;
    @Resource
    private LocalStorageRepository localStorageRepository;
    @Resource
    private StorageManageService localStorageService;
    @Resource
    private FileProperties properties;
    @Resource
    private ExportManager exportManager;
    @Resource
    private A07DocumentFwViewRepository a07DocumentFwViewRepository;
    @Resource
    private SwAllViewRepository swAllViewRepository;
    @Resource
    private SysStorageConversionRepository sysStorageConversionRepository;
//    @Value("classpath:archive/archiveData.json")
//    private org.springframework.core.io.Resource archiveData;
//    @Resource
//    private FlowInstanceService flowInstanceService;

    @Value("${archive.url}")
    private String url;
    @Value(("${archive.apiKey}"))
    private String apiKey;
    @Value("${archive.secretKey}")
    private String secretKey;
    @Value("${url.serverUrl}")
    private String hostUrl;

    @Value("${archive.x-apiKey}")
    private String x_apiKey;
    @Value("${archive.x-secretKey}")
    private String x_secretKey;
    @Value("${archive.uri}")
    private String uri;


    @Resource
    private UserRepositoryCustom userRepositoryCustom;
    @Resource
    private A07DocumentMapperUtil a07DocumentMapperUtil;

    /**
     * 归档文件夹
     */
    private String dirPath = "archiveFile";

    private static byte[] aesKey;
    @Resource
    private ResourceLoader resourceLoader;
    @Resource
    private PrintTemplateRepository printTemplateRepository;

    @Resource
    private ElPermissionConfig elPermissionConfig;
    @Resource
    private RoleRepositoryCustom roleRepositoryCustom;
    @Resource
    private WdSysOptionCustomMapper wdSysOptionCustomMapper;
    @Resource
    private HistoryService historyService;

    @Resource
    private DictDetailRepository dictDetailRepository;
    @Resource
    private ArchiveInfoRepository archiveInfoRepository;
    @Resource
    private ApplicationService applicationService;

    @PostConstruct
    public void init() throws IOException {
        org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:aes.key");
        aesKey = IOUtils.toByteArray(resource.getInputStream());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(A07FileArchive fileArchive) throws IOException {
        if (fileArchive.getId() == null) {
            List<StorageBiz> fj = fileArchive.getFj();
            List<StorageBiz> collect = fj.stream().map(storageBiz ->
                            localStorageRepository.findById(Long.valueOf(storageBiz.getStorageId()))
                    ).filter(Optional::isPresent)
                    .map(optional -> {
                        LocalStorage localStorage = optional.get();
                        LocalStorage newStorage = new LocalStorage();
                        BeanUtil.copyProperties(localStorage, newStorage, "id");
                        newStorage = localStorageRepository.save(newStorage);
                        StorageBiz storageBiz = new StorageBiz();
                        storageBiz.setStorageId(newStorage.getId().toString());
                        storageBiz.setBizType("A07FileArchive.fj");
                        return storageBiz;
                    }).collect(Collectors.toList());
            fileArchive.setFj(collect);


            // 件号
            Integer max = fileArchiveRepository.findMaxNoByLimitTimeAndYear(fileArchive.getLimitTime(), fileArchive.getYear());
            if (max == null) {
                max = 1;
            } else {
                max += 1;
            }
            fileArchive.setNo(max);
            String num = StrUtil.padPre(String.valueOf(max), 4, '0');
            fileArchive.setFileNumber(fileArchive.getFondsNumber() + "-WS·" + fileArchive.getYear() + "-" + fileArchive.getLimitTime() + "-" + num);
        }

        A07FileArchive save = fileArchiveRepository.save(fileArchive);

        createArchiveFile(save);
        return save.getId();
    }

    private void createArchiveFile(A07FileArchive save) throws IOException {
        // 1、确定存储路径
        Path path = Paths.get(properties.getPath().getPath(), dirPath, save.getFileNumber());
        if (Files.exists(path)) {
            // 删除重新生成
            Files.walkFileTree(path, EnumSet.of(FileVisitOption.FOLLOW_LINKS), Integer.MAX_VALUE, new DelVisitor());
        }
        Files.createDirectories(path);

        // 材料信息
        List<JSONObject> fileList = new ArrayList<>();
        // 归档信息包
        createDescXml(path, fileList);
        // 年度-保管期限-档号文件夹
        Path dir = createYearDirs(path, save.getYear(), save.getLimitTime(), save.getFileNumber());

        // 2、表单生成
        String file = null, fileName = save.getFileNumber() + "-01";
        if ("发文".equals(save.getDocType())) {
            Optional<A07DocumentFwView> byId = a07DocumentFwViewRepository.findById(save.getDocId());
            if (byId.isPresent()) {
                A07DocumentFwView a07DocumentFwView = byId.get();
                String bpmProcessKey = a07DocumentFwView.getBpmProcessKey();
                //todo 从应用中找打印模板id
                PrintTemplate printTemplate = findPrintTemplate(bpmProcessKey);
                String printUrl = printTemplate.getPrintUrl();
                if (StringUtils.isNotEmpty(printUrl)) {
                    CharSequence u = UrlBuilder.of(printUrl, StandardCharsets.UTF_8).getQuery().get("_u");
                    file = u.toString();
                }
            }
        } else if ("内跑".equals(save.getDocType())) {
            PrintTemplate printTemplate = findPrintTemplate(save.getBpmProcessKey());
            String printUrl = printTemplate.getPrintUrl();
            if (StringUtils.isNotEmpty(printUrl)) {
                CharSequence u = UrlBuilder.of(printUrl, StandardCharsets.UTF_8).getQuery().get("_u");
                file = u.toString();
            }
        } else {
            Optional<SwAllView> byId = swAllViewRepository.findById(save.getDocId());
            if (byId.isPresent()) {
                SwAllView a07DocumentSwView = byId.get();
                String bpmProcessKey = a07DocumentSwView.getBpmProcessKey();
                PrintTemplate printTemplate = findPrintTemplate(bpmProcessKey);
                String printUrl = printTemplate.getPrintUrl();
                if (StringUtils.isNotEmpty(printUrl)) {
                    CharSequence u = UrlBuilder.of(printUrl, StandardCharsets.UTF_8).getQuery().get("_u");
                    file = u.toString();
                }
            }

        }
        int i = 1;
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("procInstId", save.getProcInstId());
        OutputStream os = new FileOutputStream(dir + File.separator + fileName + ".pdf");
        try {
            ExportConfigure exportConfigure = new ExportConfigureImpl(file, parameters, os);
            exportManager.exportPdf(exportConfigure);
        } catch (Exception e) {
            os.close();
        }

        JSONObject json = new JSONObject();
        json.put("name", fileName + ".pdf");
        json.put("type", "文件处理单");
        Path p = Paths.get(dir + File.separator + fileName + ".pdf");
        BasicFileAttributes attributes = Files.readAttributes(p, BasicFileAttributes.class);
        long creationMillis = attributes.creationTime().toMillis();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String c = dateFormat.format(creationMillis);
        json.put("createTime", c);
        long modifiedMillis = attributes.lastModifiedTime().toMillis();
        String modified = dateFormat.format(modifiedMillis);
        json.put("modifiedTime", modified);
        json.put("suffix", "pdf");
        json.put("size", FileUtil.getSize(attributes.size()));
        json.put("md5", SecureUtil.md5(p.toFile()));
        fileList.add(json);


        // 3、正文、附件拷贝
        if ("发文".equals(save.getDocType())) {
            Optional<A07DocumentFwView> byId = a07DocumentFwViewRepository.findById(save.getDocId());
            if (byId.isPresent()) {
                A07DocumentFwView a07DocumentFw = byId.get();
                StorageBiz zw = a07DocumentFw.getZw();
                if (zw!=null) {
                    LocalStorage localStorage = findConversionStorage(Long.valueOf(zw.getStorageId()), "pdf");
                    if (localStorage == null) {
                        localStorage = localStorageRepository.findById(Long.valueOf(zw.getStorageId())).get();
                    }
                    Path zwPath = Paths.get(properties.getPath().getPath(), localStorage.getPath());
                    String number;
                    if (i < 9) {
                        number = "-0" + (++i);
                    } else {
                        number = String.valueOf(++i);
                    }
                    String newName = save.getFileNumber() + number + "." + localStorage.getSuffix();
                    Path copy = Files.copy(zwPath, dir.resolve(newName), StandardCopyOption.REPLACE_EXISTING);

                    JSONObject zwJson = new JSONObject();
                    zwJson.put("name", newName);
                    zwJson.put("type", "正文");
                    BasicFileAttributes zwAttributes = Files.readAttributes(copy, BasicFileAttributes.class);
                    long zwC = zwAttributes.creationTime().toMillis();
                    String zwCreate = dateFormat.format(zwC);
                    zwJson.put("createTime", zwCreate);
                    long zwM = zwAttributes.lastModifiedTime().toMillis();
                    String zwModified = dateFormat.format(zwM);
                    zwJson.put("modifiedTime", zwModified);
                    zwJson.put("suffix", localStorage.getSuffix());
                    zwJson.put("size", FileUtil.getSize(localStorage.getSize()));
                    zwJson.put("md5", SecureUtil.md5(copy.toFile()));
                    fileList.add(zwJson);
                }

            }
        } else {

        }
        // 附件材料
        List<StorageBiz> fj = save.getFj();
        for (StorageBiz storageBiz : fj) {
            Optional<LocalStorage> byId = localStorageRepository.findById(Long.valueOf(storageBiz.getStorageId()));
            if (byId.isPresent()) {
                LocalStorage localStorage = findConversionStorage(byId.get().getId(), "pdf");
                if (localStorage == null) {
                    localStorage = byId.get();
                }
                Path source = Paths.get(properties.getPath().getPath(), localStorage.getPath());
                String number;
                if (i < 9) {
                    number = "-0" + (++i);
                } else {
                    number = String.valueOf(++i);
                }
                String newName = save.getFileNumber() + number + "." + localStorage.getSuffix();
                Path copy = Files.copy(source, dir.resolve(newName), StandardCopyOption.REPLACE_EXISTING);

                JSONObject fjJson = new JSONObject();
                fjJson.put("name", newName);
                fjJson.put("type", "附件");
                BasicFileAttributes zwAttributes = Files.readAttributes(copy, BasicFileAttributes.class);
                long zwC = zwAttributes.creationTime().toMillis();
                String zwCreate = dateFormat.format(zwC);
                fjJson.put("createTime", zwCreate);
                long zwM = zwAttributes.lastModifiedTime().toMillis();
                String zwModified = dateFormat.format(zwM);
                fjJson.put("modifiedTime", zwModified);
                fjJson.put("suffix", localStorage.getSuffix());
                fjJson.put("size", FileUtil.getSize(localStorage.getSize()));
                fjJson.put("md5", SecureUtil.md5(copy.toFile()));
                fileList.add(fjJson);
            }
        }
        // 4、元数据.xml
        createMetaDataXml(dir, save, fileList);

    }

    private PrintTemplate findPrintTemplate(String bpmProcessKey) {
        PrintTemplate printTemplate = printTemplateRepository.findByTemplateKey(bpmProcessKey);
        if (printTemplate != null) {
            return printTemplate;
        }
        List<Application> applications = applicationService.findByProcDefKey(bpmProcessKey);
        if (applications.isEmpty()) {
            throw new BadRequestException("未找到流程相关应用配置");
        }
        Application application = applications.get(0);
        Long printTemplateId = application.getPrintTemplateId();
        if (printTemplateId == null) {
            throw new BadRequestException("未找到表单打印模板！");
        }
        printTemplate = printTemplateRepository.findById(printTemplateId).orElseThrow(() -> new BadRequestException("未找到表单打印模板！"));
        return printTemplate;
    }

    private LocalStorage findConversionStorage(Long originalStorageId, String conversionType) {
        List<SysStorageConversion> all = sysStorageConversionRepository.findAll((root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.and(
                    criteriaBuilder.equal(root.get("originalStorageId"), originalStorageId),
                    criteriaBuilder.equal(root.get("conversionType"), conversionType)
            );
            return predicate;
        }, Sort.by(Sort.Direction.DESC, "createDate"));
        if (all.size() > 0) {
            SysStorageConversion sysStorageConversion = all.get(0);
            LocalStorage storage = localStorageRepository.findById(sysStorageConversion.getConversionStorageId()).get();
            return storage;
        }
        return null;
    }

    @Override
    public Page<A07FileArchive> queryList(FileArchiveCriteria criteria, Pageable pageable) {
        List<String> belongs = criteria.getBelongToDept();
        if (!elPermissionConfig.check()) {
            if (belongs == null || belongs.isEmpty()) {
                Role firstByValue = roleRepositoryCustom.findFirstByValue("archive:admin");
                if (Objects.isNull(firstByValue)) {
                    return Page.empty();
                }
                List<Long> deptIds = roleRepositoryCustom.findByUserIdAndRoleId(SecurityUtils.getCurrentUserId(), firstByValue.getId());
                if (CollUtil.isEmpty(deptIds)) {
                    return Page.empty();
                }
                List<UserDataDto> userInfoDtoList = wdSysOptionCustomMapper.getUserListByUserIdAndEnabled(deptIds);
                belongs = userInfoDtoList.stream().map(UserDataDto::getUsername).collect(Collectors.toList());
            }
        }
        Specification<A07FileArchive> sp = (root, criteriaQuery, criteriaBuilder) -> {
            Predicate predicate = QueryHelp.getPredicate(root, criteria, criteriaBuilder);
            if (criteria.getStatus() == null) {
                predicate = criteriaBuilder.and(
                        predicate,
                        criteriaBuilder.not(root.get("status").in(2, 3))
                );
            }
            return predicate;
        };
        return fileArchiveRepository.findAll(sp, pageable);
    }

    @Override
    public FileArchiveDto queryOne(Long id) {
        A07FileArchive fileArchive = fileArchiveRepository.findById(id).orElseGet(A07FileArchive::new);
        FileArchiveDto fileArchiveDto = fileArchiveMapper.toDto(fileArchive);
        return fileArchiveDto;
    }

    @Override
    public Object upload(String fileNumber) throws IOException {

        Path path = Paths.get(properties.getPath().getPath(), dirPath, fileNumber);
        if (!Files.exists(path)) {
            throw new BadRequestException("归档文件" + fileNumber + "不存在!");
        }
        A07FileArchive archive = fileArchiveRepository.findFirstByFileNumber(fileNumber);

        File zip = ZipUtil.zip(FileUtil.file(properties.getPath().getPath(), dirPath, fileNumber + ".zip"), false, path.toFile());

        long timestamp = System.currentTimeMillis();
        int nonce = new SecureRandom().nextInt(9000) + 1000;
        String sign = SecureUtil.md5(apiKey + "-" + secretKey + "-" + timestamp + "-" + nonce);
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("x-apiKey", apiKey);
        httpPost.setHeader("x-sign", sign);
        httpPost.setHeader("x-nonce", String.valueOf(nonce));
        httpPost.setHeader("x-timestamp", String.valueOf(timestamp));
        httpPost.setHeader("Content-Type", "application/json;charset=utf8");
        JSONObject body = new JSONObject();
        String name = zip.getName();
        body.put("dataName", name);
        log.info("dataName:{}", name);
        byte[] encrypt = SecureUtil.aes(aesKey).encrypt(name);

        String dataPath = hostUrl + "/api/archive/download?name=" + URLEncoder.encode(Base64.encode(encrypt), "UTF-8");
        log.info("dataPath:{}", dataPath);
        body.put("dataPath", dataPath);
        // 保存压缩包下载路径
        archive.setZipPath(dataPath);

        // 全宗号
        body.put("deptCode", archive.getFondsNumber());
        body.put("fileNum", archive.getDocType());
        String md5 = SecureUtil.md5(zip);
        body.put("md5", md5);
        body.put("arg", "1");

        log.info("推送接口参数:{}", body.toJSONString());
        StringEntity stringEntity = new StringEntity(body.toJSONString(), "UTF-8");
        httpPost.setEntity(stringEntity);

        CloseableHttpResponse httpResponse = null;
        try {
            httpResponse = httpClient.execute(httpPost);
            log.info("推送接口响应状态:{}", httpResponse.getStatusLine());
            HttpEntity entity = httpResponse.getEntity();
            String s = EntityUtils.toString(entity);
            log.info("推送接口响应内容:{}", s);
            JSONObject response = JSONObject.parseObject(s);
            Integer code = response.getInteger("code");
            if (code == 10000) {
                archive.setStatus("4");
                archive.setSender(((JwtUserDto) SecurityUtils.getCurrentUser()).getUser().getNickName());
                archive.setSendTime(DateUtil.date());
                archive.setResultJson(s);
                fileArchiveRepository.save(archive);

                return "移交成功";
            } else {
                if (!"4".equals(archive.getStatus())) {
                    archive.setStatus("-1");
                }
                archive.setResultJson(s);
                fileArchiveRepository.save(archive);
                return "移交失败," + response.getString("msg");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                httpResponse.close();
                httpClient.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "连接档案系统失败";
    }


    /**
     * 归档信息包
     * 说明文件
     */
    public void createDescXml(Path parentPath, List<JSONObject> fileList) throws IOException {
        String file = parentPath.resolve("说明文件.xml").toString();
        Document document = DocumentHelper.createDocument();
        Element packageinfo = document.addElement("packageinfo");
        packageinfo.addAttribute("name", "信息包元数据");
        Element elements = packageinfo.addElement("elements");
        Element element = elements.addElement("element");
        Element packageId = element.addElement("package_id");

        packageId.addAttribute("name", parentPath.getFileName().toString());
        Element packageType = element.addElement("package_type");
        packageType.addAttribute("name", "zip");
        Element author = element.addElement("author");
        author.addAttribute("name", ((JwtUserDto) SecurityUtils.getCurrentUser()).getUser().getNickName());
        Element createTime = element.addElement("create_time");
        createTime.addAttribute("name", DateUtil.now());
        Element environ = element.addElement("environ");
        environ.addAttribute("name", System.getProperty("os.name"));
        Element storage = element.addElement("storage");
        storage.addAttribute("name", parentPath.toString());
        Element remark = element.addElement("remark");
        remark.addAttribute("name", "");
        OutputFormat outputFormat = OutputFormat.createPrettyPrint();
        outputFormat.setEncoding("UTF-8");
        XMLWriter writer = new XMLWriter(new FileOutputStream(file), outputFormat);
        writer.setEscapeText(false);
        writer.write(document);
        writer.close();

        JSONObject json = new JSONObject();
        json.put("name", "说明文件.xml");
        json.put("type", "附件");
        Path p = Paths.get(file);
        BasicFileAttributes attributes = Files.readAttributes(p, BasicFileAttributes.class);
        long creationMillis = attributes.creationTime().toMillis();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String c = dateFormat.format(creationMillis);
        json.put("createTime", c);
        long modifiedMillis = attributes.lastModifiedTime().toMillis();
        String modified = dateFormat.format(modifiedMillis);
        json.put("modifiedTime", modified);
        json.put("suffix", "xml");
        json.put("md5", SecureUtil.md5(new File(file)));

        fileList.add(json);

    }

    /**
     * 年度-保管期限-档号文件夹
     *
     * @param parentPath
     */
    private Path createYearDirs(Path parentPath, String year, String retentionPeriod, String fileNumber) throws IOException {
        Path resolve = parentPath.resolve(year).resolve(retentionPeriod).resolve(fileNumber);
        if (Files.notExists(resolve)) {
            Files.createDirectories(resolve);
        }
        return resolve;
    }


    /**
     * 元数据.xml(档号-xx.ofd)(xx指两位数件内顺序号)
     *
     * @param dir
     * @param archive
     */
    public void createMetaDataXml(Path dir, A07FileArchive archive, List<JSONObject> fileList) throws IOException {
        Path archivalCode = dir.getFileName();
        String file = dir.resolve("元数据.xml").toString();

        Document document = DocumentHelper.createDocument();
        Element description = document.addElement("description");
        description.addAttribute("name", "电子档案元数据");

        if ("内跑".equals(archive.getDocType())) {
            Element type = description.addElement("TYPE");
            type.addAttribute("name", "类别");
            safeSetText(type, "内跑");
            Element bs = description.addElement("BS");
            bs.addAttribute("name", "标识");
            safeSetText(bs, archive.getBpmProcessKey() + "_" + archive.getProcInstId());
            Element title = description.addElement("TITLE");
            title.addAttribute("name", "标题");
            safeSetText(title, archive.getSubject());
            Element reason = description.addElement("REASON");
            reason.addAttribute("name", "申请事由");
            safeSetText(reason, archive.getApplicationReason());
            Element fj = description.addElement("FJ");
            fj.addAttribute("name", "附件说明");
            String collect = fileList.stream().map(jsonObject -> jsonObject.getString("name")).collect(Collectors.joining(";"));
            safeSetText(fj, collect);

            Element dh = description.addElement("DH");
            dh.addAttribute("name", "档号");
            safeSetText(dh, archive.getFileNumber());
            Element nd = dh.addElement("ND");
            nd.addAttribute("name", "年度");
            safeSetText(nd, String.valueOf(archive.getYear()));
            Element bgqx = dh.addElement("BGQX");
            bgqx.addAttribute("name", "保管期限");
            safeSetText(bgqx, archive.getLimitTime());
            Element jh = dh.addElement("JH");
            jh.addAttribute("name", "件号");
            // 件号
            String no = String.valueOf(archive.getNo());
            if (no.length() < 4) {
                for (int i = 0; i < 4 - no.length(); i++) {
                    no = "0" + no;
                }
            }
            safeSetText(jh, no);
            Element szdxbs = description.addElement("SZDXBS");
            szdxbs.addAttribute("name", "数字对象标识");
            safeSetText(szdxbs, String.valueOf(archive.getId()));
        } else {

            Element gwbs = description.addElement("GWBS");
            gwbs.addAttribute("name", "公文标识");
            safeSetText(gwbs, String.valueOf(archive.getDocId()));
            Element wz = description.addElement("WZ");
            wz.addAttribute("name", "文种");
            if (StringUtils.isNotEmpty(archive.getWz())) {
                safeSetText(wz, archive.getWz());
            } else {
                String subject = archive.getSubject();
                String[] arr = new String[]{"决议", "决定", "命令", "令", "公报", "公告", "通告", "意见", "通知", "通报",
                        "报告", "请示", "批复", "议案", "函", "纪要"};
                String text = "其他";
                for (String s : arr) {
                    if (subject.contains(s)) {
                        text = s;
                    }
                }
                safeSetText(wz, text);
            }
            Element sfwlb = description.addElement("SFWLB");
            sfwlb.addAttribute("name", "收发文类别");
            String docType = archive.getDocType();
            safeSetText(sfwlb, docType.equals("OutDoc") ? "发文" : "收文");
            Element fh = description.addElement("FH");
            fh.addAttribute("name", "份号");
            Element mjbmqx = description.addElement("MJBMQX");
            mjbmqx.addAttribute("name", "密级和保密期限");
            safeSetText(mjbmqx, archive.getSecretLevel() + "★" + archive.getLimitTime());
            Element jzcd = description.addElement("JZCD");
            jzcd.addAttribute("name", "紧急程度");
            Element fwjgbz = description.addElement("FWJGBZ");
            fwjgbz.addAttribute("name", "发文机关标志");
            safeSetText(fwjgbz, archive.getFwjg());
            Element fwzh = description.addElement("FWZH");
            fwzh.addAttribute("name", "发文字号");
            Element qfr = description.addElement("QFR");
            qfr.addAttribute("name", "签发人");
            safeSetText(qfr, archive.getQfr());
            Element title = description.addElement("TITLE");
            title.addAttribute("name", "标题");
            safeSetText(title, archive.getSubject());
            Element zsjg = description.addElement("ZSJG");
            zsjg.addAttribute("name", "主送机关");
            Element fj = description.addElement("FJ");
            fj.addAttribute("name", "附件说明");
            Element fwjg = description.addElement("FWJG");
            fwjg.addAttribute("name", "发文机关或签发人署名");
            Element cwrq = description.addElement("CWRQ");
            cwrq.addAttribute("name", "成文日期");
            safeSetText(cwrq, DateUtil.formatDate(archive.getCwrq()));
            Element fz = description.addElement("FZ");
            fz.addAttribute("name", "附注");
            Element csjg = description.addElement("CSJG");
            csjg.addAttribute("name", "抄送机关");
            Element yfjg = description.addElement("YFJG");
            yfjg.addAttribute("name", "印发机关");
            safeSetText(yfjg, archive.getYfjg());
            Element yfrq = description.addElement("YFRQ");
            yfrq.addAttribute("name", "印发日期");
            safeSetText(yfrq, DateUtil.formatDate(archive.getYfrq()));
            Element fbcc = description.addElement("FBCC");
            fbcc.addAttribute("name", "发布层次");
            if (StringUtils.isNotEmpty(archive.getFbcc())) {
                safeSetText(fbcc, archive.getFbcc());
            }
            Element dh = description.addElement("DH");
            dh.addAttribute("name", "档号");
            safeSetText(dh, archive.getFileNumber());
            Element nd = dh.addElement("ND");
            nd.addAttribute("name", "年度");
            safeSetText(nd, String.valueOf(archive.getYear()));
            Element bgqx = dh.addElement("BGQX");
            bgqx.addAttribute("name", "保管期限");
            safeSetText(bgqx, archive.getLimitTime());
            Element jgwt = dh.addElement("JGWT");
            jgwt.addAttribute("name", "机构或问题");
            safeSetText(jgwt, "无");
            Element jh = dh.addElement("JH");
            jh.addAttribute("name", "件号");
            // 件号
            String no = String.valueOf(archive.getNo());
            if (no.length() < 4) {
                for (int i = 0; i < 4 - no.length(); i++) {
                    no = "0" + no;
                }
            }
            safeSetText(jh, no);
            Element szdxbs = description.addElement("SZDXBS");
            szdxbs.addAttribute("name", "数字对象标识");
            safeSetText(szdxbs, String.valueOf(archive.getId()));
            Element hjxx = description.addElement("HJXX");
            hjxx.addAttribute("name", "环境信息");
            Element rjxx = hjxx.addElement("RJXX");
            rjxx.addAttribute("name", "软件环境");
            Element yjxx = hjxx.addElement("YJXX");
            yjxx.addAttribute("name", "硬件环境");
            if (docType.equals("OutDoc")) {
                Optional<A07DocumentFwView> optional = a07DocumentFwViewRepository.findById(archive.getDocId());
                if (optional.isPresent()) {
                    A07DocumentFwView a07DocumentFw = optional.get();
                    ReferenceNumber gwwh = a07DocumentFw.getGwwh();
                    safeSetText(fwzh, gwwh.toString());

                    SysDeptUserMain zsdw = a07DocumentFw.getZsdw();
                    if (zsdw != null) {
                        safeSetText(zsjg, zsdw.getMs());
                    }
                    SysDeptUserMain csdw = a07DocumentFw.getCsdw();
                    if (csdw != null) {
                        safeSetText(csjg, csdw.getMs());
                    }
                }
            } else {
                Optional<SwAllView> optional = swAllViewRepository.findById(archive.getDocId());
                if (optional.isPresent()) {
                    SwAllView a07DocumentSw = optional.get();
                    String dispatchNo = a07DocumentSw.getLwwh();
                    if (StringUtils.isNotEmpty(dispatchNo)) {
                        safeSetText(fwzh, dispatchNo);
                    }
                }
            }
        }
        Element elements = description.addElement("elements");
        elements.addAttribute("name", "处理人员");

        // 流转记录
        List<HistoricTaskInstance> list = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(archive.getProcInstId())
                .processDefinitionKey(archive.getBpmProcessKey())
                .finished()
                .orderByHistoricTaskInstanceEndTime()
                .asc()
                .list();
        list.stream().forEach(historicTaskInstance -> {
            String name = historicTaskInstance.getName();
            String assignee = historicTaskInstance.getAssignee();
            User user = userRepositoryCustom.findByUsername(assignee);
            Date endTime = historicTaskInstance.getEndTime();

            Element element = elements.addElement("element");
            element.addAttribute("name", "处理人员实体");
            Element disposalType = element.addElement("disposal_type");
            disposalType.addAttribute("name", "处理类型");
            safeSetText(disposalType, name);
            Element disposalActor = element.addElement("disposal_actor");
            disposalActor.addAttribute("name", "处理者");
            safeSetText(disposalActor, user.getNickName());
            Element disposalDepartment = element.addElement("disposal_department");
            disposalDepartment.addAttribute("name", "处理部门");
            safeSetText(disposalDepartment, user.getDept().getName());
            Element disposalTime = element.addElement("disposal_time");
            disposalTime.addAttribute("name", "处理时间");
            safeSetText(disposalTime, DateUtil.formatDate(endTime));
            Element disposalResult = element.addElement("disposal_result");
            disposalResult.addAttribute("name", "处理结果");
        });


        // 材料收取清单
        Element files = description.addElement("files");
        files.addAttribute("name", "材料收取清单");
        JSONObject metaJson = new JSONObject();
        metaJson.put("name", "元数据.xml");
        metaJson.put("type", "附件");
        metaJson.put("createTime", DateUtil.now());
        metaJson.put("modifiedTime", DateUtil.now());
        metaJson.put("suffix", "xml");
        fileList.add(0, metaJson);
        for (JSONObject jsonObject : fileList) {
            Element fileinfo = files.addElement("fileinfo");
            fileinfo.addAttribute("title", "材料信息");
            Element clmc = fileinfo.addElement("CLMC");
            clmc.addAttribute("title", "材料名称");
            safeSetText(clmc, jsonObject.getString("name"));
            Element cllx = fileinfo.addElement("CLLX");
            cllx.addAttribute("title", "材料类型");
            safeSetText(cllx, jsonObject.getString("type"));
            Element sqfs = fileinfo.addElement("SQFS");
            sqfs.addAttribute("title", "收取方式");
            safeSetText(sqfs, "电子收取");
            Element wbss = fileinfo.addElement("WBSS");
            wbss.addAttribute("title", "未收取说明");
            Element detailinfo = fileinfo.addElement("detailinfo");
            detailinfo.addAttribute("title", "计算机文件详细信息");
            Element wjm = detailinfo.addElement("WJM");
            wjm.addAttribute("title", "计算机文件名");
            safeSetText(wjm, jsonObject.getString("name"));
            Element cjsj = detailinfo.addElement("CJSJ");
            cjsj.addAttribute("title", "计算机文件创建时间");
            safeSetText(cjsj, jsonObject.getString("createTime"));
            Element xgsj = detailinfo.addElement("XGSJ");
            xgsj.addAttribute("title", "计算机文件修改时间");
            safeSetText(xgsj, jsonObject.getString("modifiedTime"));
            Element wjdx = detailinfo.addElement("WJDX");
            wjdx.addAttribute("title", "计算机文件大小");
            if (StringUtils.isNotEmpty(jsonObject.getString("size"))) {
                safeSetText(wjdx, jsonObject.getString("size"));
            }
            Element gsxx = detailinfo.addElement("GSXX");
            gsxx.addAttribute("title", "计算机文件格式信息");
            safeSetText(gsxx, jsonObject.getString("suffix"));
            Element wjszzy = detailinfo.addElement("WJSZZY");
            wjszzy.addAttribute("title", "文件数字摘要值");
            if (StringUtils.isNotEmpty(jsonObject.getString("md5"))) {
                safeSetText(wjszzy, jsonObject.getString("md5"));
            }
        }


        OutputFormat outputFormat = OutputFormat.createPrettyPrint();
        outputFormat.setEncoding("UTF-8");
        XMLWriter writer = new XMLWriter(new FileOutputStream(file), outputFormat);
        writer.setEscapeText(false);
        writer.write(document);
        writer.close();
    }


    @Override
    public void download(String name, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String rootPath = properties.getPath().getPath() + File.separator + dirPath;
        byte[] decrypt = SecureUtil.aes(aesKey).decrypt(Base64.decode(name));
        String zipName = new String(decrypt);
        Path path = Paths.get(rootPath, zipName);
        if (Files.notExists(path)) {
            throw new BadRequestException("文件不存在!");
        }
        response.setCharacterEncoding(request.getCharacterEncoding());
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(zipName, request.getCharacterEncoding()));
        ServletOutputStream outputStream = response.getOutputStream();
        IOUtils.copy(new FileInputStream(path.toFile()), outputStream);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object mark(Long[] ids, String status) {
        for (Long id : ids) {
            A07FileArchive a07FileArchive = fileArchiveRepository.findByDocId(id);
            if (a07FileArchive == null) {
                a07FileArchive = new A07FileArchive();
                a07FileArchive.setDocId(id);
                a07FileArchive.setYear(String.valueOf(DateUtil.year(new Date())));
            }

            a07FileArchive.setStatus(status);

            fileArchiveRepository.save(a07FileArchive);
        }

        return "标记成功";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object cancelMark(Long[] ids) {
        fileArchiveRepository.deleteAllByDocIdIn(ids);
        return "取消标记";
    }

    @Override
    public Object getAccessToken(String usercode) {
        long timestamp = System.currentTimeMillis();
        int nonce = new SecureRandom().nextInt(9000) + 1000;
        String sign = SecureUtil.md5(x_apiKey + "-" + x_secretKey + "-" + timestamp + "-" + nonce);

        HttpRequest get = HttpUtil.createGet(uri);
        get.form("usercode", usercode);
        get.header("x-apiKey", x_apiKey);
        get.header("x-sign", sign);
        get.header("x-nonce", String.valueOf(nonce));
        get.header("x-timestamp", String.valueOf(timestamp));
        HttpResponse response1 = get.execute();
        String body = response1.body();
        response1.close();
        return JSONObject.parseObject(body);
    }


    /**
     * 获取内跑归档数据
     *
     * @param processInstanceId
     * @return
     */
    @Override
    public Object getNpDataByProcessInstanceId(String processInstanceId) {
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceBusinessStatus("TG")
                .variableExists("startedBy")
                .includeProcessVariables()
                .processInstanceId(processInstanceId)
                .singleResult();
        String processDefinitionKey = historicProcessInstance.getProcessDefinitionKey();
        Map<String, Object> processVariables = historicProcessInstance.getProcessVariables();

        JSONObject json = handleVariables(processVariables);
        json.put("bpmProcessKey", processDefinitionKey);

        List<WdArchiveInfo> list = archiveInfoRepository.findAll((root, query, criteriaBuilder) -> {
            return criteriaBuilder.and(
                    criteriaBuilder.equal(root.get("belongToDept"), historicProcessInstance.getTenantId()),
                    root.get("archiveNum").isNotNull()
            );
        });
        String code = null;
        if (list.size() > 0) {
            code = list.get(0).getArchiveNum();
        }
        json.put("fondsNumber", code);
        return json;
    }

    private JSONObject handleVariables(Map<String, Object> processVariables) {
        JSONObject json = new JSONObject();
        // 标题
        Object bpmFormTitle = processVariables.get("bpmFormTitle");
        json.put("subject", bpmFormTitle);

        JSONObject formData = (JSONObject) processVariables.get("formData");
        // 申请事由
        Object applicationReason = formData.get("applicationReason");
        json.put("applicationReason", applicationReason);

        // 申请表、附件
        List<StorageBizDto> attachList = new ArrayList<>();

        formData.entrySet().stream()
                .filter(entry -> filter(entry))
                .forEach(m -> {
                    Object value = m.getValue();
                    if (value instanceof StorageBiz) {
                        List<StorageBiz> list = new ArrayList<>();
                        list.add(((StorageBiz) value));
                        attachList.addAll(a07DocumentMapperUtil.toFj(list));

                    } else if (value instanceof List) {
                        List<StorageBiz> storageBizs = JSONObject.parseArray(JSON.toJSONString(value), StorageBiz.class);
                        attachList.addAll(a07DocumentMapperUtil.toFj(storageBizs));
                    }
                });
        json.put("fj", attachList);

        json.put("docType", "内跑");
        return json;

    }

    public boolean filter(Map.Entry<String, Object> entry) {
        String key = entry.getKey().toLowerCase();
        Object value = entry.getValue();
        return (
                key.contains("applicationform")
                        || key.contains("document")
                        || key.contains("attach")
        )
                && (
                value instanceof StorageBiz || value instanceof List
        )
                ;
    }

    @Override
    public Page getNpDatas(String fileStatus, String bt, String belongToDept, Pageable pageable) {
        List<DictDetail> npArchive = dictDetailRepository.findByDictName("np_archive");
        Assert.notEmpty(npArchive, "请在字典管理中配置字典名称为np_archive的流程定义数据!");
        List<String> collect = npArchive.stream().map(DictDetail::getValue).collect(Collectors.toList());
        HistoricProcessInstanceQuery historicProcessInstanceQuery = historyService.createHistoricProcessInstanceQuery();
        if (StringUtils.isNotEmpty(belongToDept)) {
            historicProcessInstanceQuery.processInstanceTenantId(belongToDept);
        }
        if (StringUtils.isNotEmpty(bt)) {
            historicProcessInstanceQuery.processInstanceNameLike("%" + bt + "%");
        }
        List<HistoricProcessInstance> list = historicProcessInstanceQuery
                .variableExists("startedBy")
                .processDefinitionKeyIn(collect)
                .finished()
                .orderByProcessInstanceStartTime()
                .desc()
                .list();
        List<A07FileArchive> a07FileArchives = fileArchiveRepository.findAll((root, query, criteriaBuilder) -> {
            return criteriaBuilder.and(
                    root.get("bpmProcessKey").in(collect),
                    criteriaBuilder.equal(root.get("status"), 1)
            );
        });
        if (!a07FileArchives.isEmpty()) {
            List<String> procInstIds = a07FileArchives.stream().map(A07FileArchive::getProcInstId).collect(Collectors.toList());
            list = list.stream()
                    .filter(historicProcessInstance -> !procInstIds.contains(historicProcessInstance.getId()))
                    .collect(Collectors.toList());
        }
        List<JSONObject> result = list.stream().map(historicProcessInstance -> {
            JSONObject json = new JSONObject();
            json.put("bpmInstanceId", historicProcessInstance.getId());
            json.put("bt", historicProcessInstance.getName());
            json.put("createTime", historicProcessInstance.getStartTime());
            a07FileArchives.stream().filter(a07FileArchive -> a07FileArchive.getProcInstId().equals(historicProcessInstance.getId()))
                    .findFirst().ifPresent(a07FileArchive -> {
                        json.put("fileStatus", a07FileArchive.getStatus());
                    });
            return json;
        }).skip(pageable.getOffset()).limit(pageable.getPageSize()).collect(Collectors.toList());
        return new PageImpl<>(result, pageable, list.size());
    }

    private void safeSetText(Element element, String text) {
        if (text == null) {
            element.setText(""); // 设置默认值为空字符串
        } else {
            element.setText(text);
        }
    }

}
