package com.hzwangda.aigov.modules.document.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwView;
import com.hzwangda.aigov.modules.document.entity.SysDocumentDz;
import com.hzwangda.aigov.modules.document.entity.Z08DocumentType;
import com.hzwangda.aigov.modules.document.repository.A07DocumentGwViewRepository;
import com.hzwangda.aigov.modules.document.repository.SysDocumentDzRepository;
import com.hzwangda.aigov.modules.document.repository.Z08DocumentTypeRepository;
import com.hzwangda.aigov.modules.document.service.A07GwService;
import com.hzwangda.aigov.oa.repository.WdSysOptionRepository;
import com.hzwangda.aigov.oa.util.SMSFormatUtil;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.extension.domain.WdSysOption;
import com.wangda.oa.modules.system.service.DeptService;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.RedisDto;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.workflow.bo.QueryFormBO;
import com.wangda.oa.modules.workflow.domain.application.AppPermission;
import com.wangda.oa.modules.workflow.domain.application.Application;
import com.wangda.oa.modules.workflow.domain.form.PrintTemplate;
import com.wangda.oa.modules.workflow.domain.space.AppSpace;
import com.wangda.oa.modules.workflow.dto.application.ApplicationDto;
import com.wangda.oa.modules.workflow.dto.space.SpaceCriteria;
import com.wangda.oa.modules.workflow.service.IUserRuleService;
import com.wangda.oa.modules.workflow.service.application.AppPermissionService;
import com.wangda.oa.modules.workflow.service.application.ApplicationService;
import com.wangda.oa.modules.workflow.service.form.FormTemplateService;
import com.wangda.oa.modules.workflow.service.form.PrintTemplateService;
import com.wangda.oa.modules.workflow.service.space.SpaceService;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.criteria.Predicate;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class A07GwServiceImpl implements A07GwService {

    @Value("${url.serverUrl}")
    private String hostUrl;

    private final UserService userService;
    private final DeptService deptService;
    private final SysDocumentDzRepository dzRepository;
    private final IUserRuleService iUserRuleService;
    private final SpaceService spaceService;
    private final ApplicationService applicationService;
    private final AppPermissionService appPermissionService;
    private final A07DocumentGwViewRepository gwViewRepository;
    private final RepositoryService repositoryService;
    private final WdSysOptionRepository wdSysOptionRepository;
    private final Z08DocumentTypeRepository documentTypeRepository;
    private final HistoryService historyService;
    private final PrintTemplateService printTemplateService;
    private final FormTemplateService formTemplateService;
    @PersistenceContext
    private EntityManager em;

    @Override
    public Object getGwDefault() {
        Map<String, Object> restMap = new HashMap<>();
        UserDto byName = userService.findByName(SecurityUtils.getCurrentUsername());
        restMap.put("nickName", byName.getNickName());
        restMap.put("phone", byName.getPhone());
        RedisDto convert = Convert.convert(RedisDto.class, deptService.queryCurrentUnit());
        String unitName = SecurityUtils.getUnitName(Long.valueOf(convert.getDeptId()));
        restMap.put("deptName", unitName);
        restMap.put("newDate", DateUtil.today());
        restMap.put("thisYear", DateUtil.thisYear());
        return restMap;
    }

    @Override
    public Object getDeptHeaderTemplateList(String procInstId) {
        List<String> deptNames = new ArrayList();
        List<Map<String, Object>> list = (List<Map<String, Object>>) deptService.queryUnitCutDept();
        for (Map m : list) {
            String isMainJob = m.get("isMainJob").toString();
            if (!isMainJob.equals("2")) {
                if (ObjectUtil.isNotEmpty(m.get("belongToDept"))) {
                    deptNames.add(m.get("belongToDept").toString());
                }
            }
        }

        Specification<Z08DocumentType> sp = (root, cq, cb) -> {
            Predicate p = cb.isNull(root.get("deptId"));
            p = cb.or(p, cb.in(root.get("deptId")).value(deptNames));
            if (StringUtils.isNotEmpty(procInstId)) {
                HistoricProcessInstance instance = historyService.createHistoricProcessInstanceQuery().processInstanceId(procInstId).singleResult();
                p = cb.and(p, cb.equal(root.get("applicationId"), instance.getBusinessKey()));
            }
            return p;
        };
        List<Z08DocumentType> deptTemplateList = documentTypeRepository.findAll(sp);
        Map<String, Object> map = new HashMap<>(1);
        map.put("content", deptTemplateList);
        return map;
    }

    @Override
    public List<String> getDeptDzList(String dzType) {
        List<String> deptNames = new ArrayList();
        List<Map<String, Object>> list = (List<Map<String, Object>>) deptService.queryUnitCutDept();
        for (Map m : list) {
            String isMainJob = m.get("isMainJob").toString();
            if (!isMainJob.equals("2")) {
                deptNames.add(m.get("deptCode").toString());
            }
        }
        List<SysDocumentDz> dzList;
        if (StringUtils.isNotEmpty(dzType)) {
            dzList = dzRepository.findByDzTypeAndBelongToDeptIn(dzType, deptNames);
        } else {
            dzList = dzRepository.findByBelongToDeptIn(deptNames);
        }
        List<String> collect = dzList.stream().map(SysDocumentDz::getDz).collect(Collectors.toList());
        return collect;
    }

    @Override
    public List<Map<String, Object>> getGwPermission(String gwType) {
        String username = SecurityUtils.getCurrentUsername();
        List<Application> application = applicationService.getByType(gwType);
        List<Long> collect = application.stream().filter(a -> a.getAppStatus().getValue().equals("ONLINE")).map(Application::getId).collect(Collectors.toList());
        List<AppPermission> appPermission = appPermissionService.getByApplicationIdList(collect);
        List<Map<String, Object>> appIds = new ArrayList<>();
        for (AppPermission permission : appPermission) {
            boolean b = iUserRuleService.validatePermission(permission.getCreateManager(), username);
            if (b) {
                List<Application> applications = application.stream().filter(a ->
                        a.getId().equals(permission.getApplicationId())
                ).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(applications)) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("label", applications.get(0).getName());
                    map.put("value", applications.get(0).getId());
                    map.put("sort", applications.get(0).getSort());
                    appIds.add(map);
                }
            }
        }
        appIds.sort(Comparator.comparing(o -> o.get("sort") != null ? o.get("sort").toString() : null, Comparator.nullsLast(String::compareTo)));
        return appIds;
    }

    @Override
    public List<Map<String, Object>> getGwClassify(String gwType) {
        List<AppSpace> appSpaceList = spaceService.queryList(new SpaceCriteria());
        List<Long> spaceId = appSpaceList.stream().map(AppSpace::getId).collect(Collectors.toList());
        List<Application> gwClassify = gwViewRepository.getGwClassify(spaceId, gwType);
        List<Long> collect = gwClassify.stream().filter(a -> a.getAppStatus().getValue().equals("ONLINE")).map(Application::getId).collect(Collectors.toList());

        List<AppPermission> appPermission = appPermissionService.getByApplicationIdList(collect);

        List<Map<String, Object>> list = new ArrayList<>();

        for (AppPermission permission : appPermission) {
            boolean b = iUserRuleService.validatePermission(permission.getCreateManager(), SecurityUtils.getCurrentUsername());
            if (b) {
                gwClassify.stream().filter(a ->
                        a.getId().equals(permission.getApplicationId()) && StringUtils.isNotEmpty(a.getProcDefKey())
                ).forEach(c -> {
                    ProcessDefinition pd = repositoryService.createProcessDefinitionQuery().processDefinitionKey(c.getProcDefKey()).latestVersion().singleResult();
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", c.getId());
                    map.put("name", c.getName());
                    map.put("key", pd.getKey());
                    list.add(map);
                });

            }
        }
        return list;
    }

    @Override
    public Object getGwUrgeSubject(String procInstId, String key) {
        WdSysOption firstByKey = wdSysOptionRepository.getFirstByKey(key);
        Assert.notNull(firstByKey, "短信模板没有配置!");
        String value = firstByKey.getValue();
        A07DocumentGwView byBpmInstanceId = gwViewRepository.findByBpmInstanceId(procInstId);
        Map map = new HashMap();
        Map m = (Map) deptService.queryCurrentUnit();
        String deptName = m.get("deptName").toString();
        map.put("username", deptName + "-" + userService.findByName(SecurityUtils.getCurrentUsername()).getNickName());
        map.put("bt", byBpmInstanceId.getBt());
        String content = SMSFormatUtil.processTemplate(value, map);
        return content;
    }

    @Override
    public Object getGwPrintAddress(String procInstId) {
        HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(procInstId).singleResult();
        String businessKey = processInstance.getBusinessKey();
        ApplicationDto byId = applicationService.getById(Long.valueOf(businessKey));
        if (Objects.isNull(byId.getPrintTemplateId())) {
            throw new BadRequestException("该应用未配置打印模板，请联系管理员！");
        }
        PrintTemplate template = printTemplateService.getById(byId.getPrintTemplateId());
        return template.getPrintUrl() + "&procInstId=" + procInstId;
    }

    @Override
    public String getUsernameByTaskId(String taskId) {
        HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(taskId).singleResult();
        return historicTaskInstance.getAssignee();
    }


    @Override
    public Map<String, Object> getAppIdAndFormData(String procInstId) {
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(procInstId).singleResult();
        QueryFormBO queryFormBO = new QueryFormBO();
        ApplicationDto byId = applicationService.getById(Long.valueOf(historicProcessInstance.getBusinessKey()));
        queryFormBO.setTemplateId(byId.getPcTemplateId());
        queryFormBO.setProcessInstanceId(procInstId);
        ResponseEntity responseEntity = null;
        try {
            responseEntity = formTemplateService.getFormData(queryFormBO);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Object result = responseEntity.getBody();
        if (Objects.isNull(result)) {
            return null;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("appId", historicProcessInstance.getBusinessKey());
        map.put("formData", result);
        return map;
    }

    @Override
    public Object count(Integer year, Integer gwType, List<Date> createTime) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT u.nick_name 单位, A.总量, A.办结, A.未办结, d.dept_sort,u.sort  FROM\n" +
                "\t(");
        sql.append("SELECT G.belong_to_dept, COUNT(1) AS 总量, SUM (CASE WHEN bpm_status IN ( 'TG', 'END' ) THEN 1 ELSE 0 END ) AS 办结, SUM (CASE WHEN bpm_status = 'INPROGRESS' THEN 1 ELSE 0 END ) AS 未办结 FROM gw_view G \n" +
                "\t\tJOIN act_hi_procinst p ON bpm_instance_id=p.proc_inst_id_\n" +
                "\t\tWHERE\n" +
                "\t\tgw_type IN ( ?1 )\n" +
                "\t\tAND bpm_process_key IN (SELECT bpm_process_key FROM sw_all_view UNION ALL SELECT bpm_process_key FROM fw_all_view)\n"
                );
        if (CollUtil.isNotEmpty(createTime)) {
            sql.append("\tAND G.create_time BETWEEN ?2 AND ?3\n");
        }
        sql.append("\tGROUP BY\n" +
                "\t\tG.belong_to_dept\n");
        sql.append("\t)\n" +
                "\tA RIGHT JOIN sys_user u ON A.belong_to_dept = u.username  \n" +
                "\tJOIN sys_dept d ON u.dept_id=d.dept_id  \n" +
                "\tJOIN wd_archive_info ON u.username=wd_archive_info.belong_to_dept\n" +
                "\tWHERE u.user_type=2 AND u.enabled=1\n");
        sql.append("ORDER BY\n" +
                "\td.dept_sort,u.sort;");
        Query nativeQuery = em.createNativeQuery(sql.toString());
        nativeQuery
                .setParameter(1, gwType == null ? Arrays.asList("0", "1") : gwType.toString());
        if (CollUtil.isNotEmpty(createTime)) {
            DateTime begin = DateUtil.beginOfDay(createTime.get(0));
            DateTime end = DateUtil.endOfDay(createTime.get(1));
            nativeQuery.setParameter(2, new Timestamp(begin.getTime()))
                    .setParameter(3, new Timestamp(end.getTime()));
        }
        List resultList = nativeQuery
                .unwrap(SQLQuery.class)
                .setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
                .getResultList();
        return resultList;
    }
}
