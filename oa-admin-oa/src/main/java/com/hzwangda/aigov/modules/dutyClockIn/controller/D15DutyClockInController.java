package com.hzwangda.aigov.modules.dutyClockIn.controller;

import com.hzwangda.aigov.modules.dutyClockIn.domain.dto.D15DutyClockInDto;
import com.hzwangda.aigov.modules.dutyClockIn.domain.entity.D15DutyClockIn;
import com.hzwangda.aigov.modules.dutyClockIn.domain.query.DutyClockInQueryCriteria;
import com.hzwangda.aigov.modules.dutyClockIn.service.D15DutyClockInService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @description: 值班打卡
 * @author: zzl
 * @date: 2025/4/1 15:18
 **/

@RestController
@RequestMapping("/d15DutyClockIn")
@Api(tags = "值班打卡")
public class D15DutyClockInController {

    @Resource
    private D15DutyClockInService dutyClockInService;

    @GetMapping(value = "/queryToDayIsClockIn")
    @ApiOperation("查询当天是否打卡")
    public ResponseEntity<Boolean> queryToDayIsClockIn() {
        return new ResponseEntity<>(dutyClockInService.queryToDayIsClockIn(), HttpStatus.OK);
    }

    @GetMapping(value = "/queryPageH5")
    @ApiOperation("移动端打卡记录列表查询")
    public ResponseEntity<Page<D15DutyClockIn>> queryPageH5(Pageable pageable) {
        return new ResponseEntity<>(dutyClockInService.queryPageH5(pageable), HttpStatus.OK);
    }

    @GetMapping(value = "/queryByToDay")
    @ApiOperation("查询当前用户今天的打卡数据")
    public ResponseEntity<D15DutyClockIn> queryByToDay() {
        return new ResponseEntity<>(dutyClockInService.queryByToDay(), HttpStatus.OK);
    }

    @GetMapping(value = "/queryById")
    @ApiOperation("根据id查询打卡数据")
    public ResponseEntity<D15DutyClockIn> queryById(@RequestParam("id") Long id) {
        return new ResponseEntity<>(dutyClockInService.queryById(id), HttpStatus.OK);
    }

    @ApiOperation("新增或更新打卡")
    @PostMapping(value = "/save")
    public ResponseEntity<D15DutyClockIn> save(@RequestBody D15DutyClockInDto dto) {
        return new ResponseEntity<>(dutyClockInService.save(dto), HttpStatus.OK);
    }

    @GetMapping(value = "/queryByIdentityPage")
    @ApiOperation("根据身份查询团队打卡记录")
    public ResponseEntity<Page<D15DutyClockIn>> queryByIdentityPage(DutyClockInQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(dutyClockInService.queryByIdentityPage(criteria, pageable), HttpStatus.OK);
    }

    @GetMapping(value = "/exportDutyClockIn")
    @ApiOperation("导出打卡数据")
    public ResponseEntity<Void> exportDutyClockIn(HttpServletResponse response, DutyClockInQueryCriteria criteria) throws IOException {
        dutyClockInService.exportDutyClockIn(response, criteria);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
