package com.hzwangda.aigov.modules.document.entity;

import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Where;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 动态信息-会签
 * <AUTHOR>
 * @date 2021/6/23 上午10:18
 */
@Data
@Entity
@Table(name = "a07_document_hq")
public class A07DocumentHq extends BaseBpmDomain implements Serializable {

    @ApiModelProperty(value = "文号")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "wh_id", referencedColumnName = "id")
    private ReferenceNumber gwwh;

    @Column(name = "hj")
    @ApiModelProperty(value = "缓急(普通,急)")
    private String hj;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @Column(name = "bt")
    @ApiModelProperty(value = "标题")
    private String bt;

    @Column(name = "lwdw")
    @ApiModelProperty(value = "来文单位")
    private String lwdw;

    @Column(name = "yj_qf", length = 2000)
    @ApiModelProperty(value = "签发-json格式")
    private String yjQf;

    @Column(name = "yj_hq", length = 2000)
    @ApiModelProperty(value = "会签-json格式")
    private String yjHq;

    @Column(name = "yj_fgzrsh", length = 2000)
    @ApiModelProperty(value = "分管主任审核意见-json格式")
    private String yjFgzrsh;

    @Column(name = "yj_msksh", length = 2000)
    @ApiModelProperty(value = "秘书科审核意见-json格式")
    private String yjMsksh;

    @Column(name = "yj_cssh", length = 2000)
    @ApiModelProperty(value = "处室审核意见-json格式")
    private String yjCssh;

    @Column(name = "yj_nb", length = 2000)
    @ApiModelProperty(value = "拟办意见-json格式")
    private String yjNb;

    @ApiModelProperty(value = "正文")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "zw", referencedColumnName = "id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private StorageBiz zw;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A07DocumentHq.fj'")
    private List<StorageBiz> fj;

    @ApiModelProperty(value = "主送单位")
    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "zsdw", referencedColumnName = "id")
    private SysDeptUserMain zsdw;

    @ApiModelProperty(value = "抄送单位")
    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "csdw", referencedColumnName = "id")
    private SysDeptUserMain csdw;

    @Column(name = "ngdw")
    @ApiModelProperty(value = "拟稿单位")
    private String ngdw;

    @Column(name = "bz", length = 2000)
    @ApiModelProperty(value = "备注")
    private String bz;

    public void setZw(StorageBiz zw) {
        if(Objects.nonNull(zw)) {
            this.zw = zw;
        }
        if(Objects.nonNull(this.zw) && StringUtils.isNotBlank(this.zw.getStorageId())) {
            if(this.id != null) {
                this.zw.setBizId(this.id.toString());
            }
        }else {
            this.zw = null;
        }
    }

    public void setFj(List<StorageBiz> fj) {
        if(fj != null) {
            if(this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }

    @Override
    @PrePersist
    @PreUpdate
    public void preCreateEntity() {
        super.preCreateEntity();
        if(!CollectionUtils.isEmpty(this.fj)) {
            for(StorageBiz storageBiz : this.fj) {
                if(Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                }
            }
        }
        if(Objects.nonNull(this.zw) && StringUtils.isNotBlank(this.zw.getStorageId())) {
            this.zw.setBizId(this.id.toString());
        }else {
            this.zw = null;
        }
    }
}
