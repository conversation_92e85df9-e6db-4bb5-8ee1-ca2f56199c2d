package com.hzwangda.aigov.modules.archive.entity;

import com.hzwangda.aigov.oa.dto.StorageBizDto;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FileArchiveDto extends BaseDomain implements Serializable {

    @ApiModelProperty(value = "流程实例id")
    private String procInstId;

    @ApiModelProperty(value = "流程定义Key")
    private String bpmProcessKey;

    @NotNull
    @ApiModelProperty(value = "年度")
    private String year;

    @ApiModelProperty(value = "档案状态(1已归档)")
    private String status;

    @ApiModelProperty(value = "保管期限")
    private String limitTime;

    @ApiModelProperty(value = "标题")
    private String subject;

    @ApiModelProperty(value = "全宗号")
    private String fondsNumber;

    @ApiModelProperty(value = "归档件号")
    private Integer no;

    @ApiModelProperty(value = "档号")
    private String fileNumber;

    @ApiModelProperty(value = "盒号")
    private String boxNo;

    @ApiModelProperty(value = "文件类别（收/发文）")
    private String docType;

    @ApiModelProperty(value = "文件编号")
    private String docNo;

    @ApiModelProperty(value = "电子文件号编号")
    private Integer erpNum;

    @ApiModelProperty(value = "电子文件号,业务编码_年份_5位流水号(累加)，如2_2021_00022.zip")
    private String erpFileId;

    @ApiModelProperty(value = "原件id")
    private Long docId;

    @Temporal(TemporalType.TIMESTAMP)
    @ApiModelProperty(value = "文件形成时间(发文取印发或分发日期；收文取收文登记日期)")
    private Date fileDate;

    @ApiModelProperty(value = "密级")
    private String secretLevel;

    @ApiModelProperty(value = "责任者")
    private String responsiblePerson;

    @ApiModelProperty(value = "载体数量")
    private Integer carrierNumber;

    @ApiModelProperty(value = "案卷号")
    private String archiveNo;

    @ApiModelProperty(value = "张页号")
    private String pageNo;

    @ApiModelProperty(value = "主题词")
    private String keywords;

    @ApiModelProperty(value = "附注")
    private String note;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "移交人")
    private String sender;

    @Temporal(TemporalType.TIMESTAMP)
    @ApiModelProperty(value = "移交时间")
    private Date sendTime;

    @ApiModelProperty(value = "文件页数")
    private String filePage;

    @ApiModelProperty(value = "附件")
    private List<StorageBizDto> fj;

    @ApiModelProperty(value = "kafka接收结果")
    private String resultJson;

    @ApiModelProperty(value = "是否移交")
    private String isHandOver;


    private String fwjg;
    private String qfr;
    private Date cwrq;
    private Date yfrq;
    private String yfjg;
    private String fbcc;

    @ApiModelProperty("申请事由")
    private String applicationReason;
}
