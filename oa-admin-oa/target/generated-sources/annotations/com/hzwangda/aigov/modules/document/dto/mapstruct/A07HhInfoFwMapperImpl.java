package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07HhInfoListFwDto;
import com.hzwangda.aigov.modules.document.entity.A07MeetingSea;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A07HhInfoFwMapperImpl implements A07HhInfoFwMapper {

    @Override
    public A07HhInfoListFwDto toDto(A07MeetingSea entity) {
        if ( entity == null ) {
            return null;
        }

        A07HhInfoListFwDto a07HhInfoListFwDto = new A07HhInfoListFwDto();

        a07HhInfoListFwDto.setCreateDate( entity.getCreateDate() );
        a07HhInfoListFwDto.setCreatorId( entity.getCreatorId() );
        a07HhInfoListFwDto.setId( entity.getId() );
        a07HhInfoListFwDto.setModifiedDate( entity.getModifiedDate() );
        a07HhInfoListFwDto.setSendStatus( entity.getSendStatus() );
        a07HhInfoListFwDto.setStartDate( entity.getStartDate() );
        a07HhInfoListFwDto.setStatus( entity.getStatus() );
        a07HhInfoListFwDto.setSubject( entity.getSubject() );
        a07HhInfoListFwDto.setUrgentLevel( entity.getUrgentLevel() );
        a07HhInfoListFwDto.setWfType( entity.getWfType() );

        return a07HhInfoListFwDto;
    }

    @Override
    public List<A07HhInfoListFwDto> toDto(List<A07MeetingSea> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A07HhInfoListFwDto> list = new ArrayList<A07HhInfoListFwDto>( entityList.size() );
        for ( A07MeetingSea a07MeetingSea : entityList ) {
            list.add( toDto( a07MeetingSea ) );
        }

        return list;
    }

    @Override
    public A07MeetingSea toEntity(A07HhInfoListFwDto dto) {
        if ( dto == null ) {
            return null;
        }

        A07MeetingSea a07MeetingSea = new A07MeetingSea();

        a07MeetingSea.setCreateDate( dto.getCreateDate() );
        a07MeetingSea.setCreatorId( dto.getCreatorId() );
        a07MeetingSea.setId( dto.getId() );
        a07MeetingSea.setModifiedDate( dto.getModifiedDate() );
        a07MeetingSea.setSendStatus( dto.getSendStatus() );
        a07MeetingSea.setStartDate( dto.getStartDate() );
        a07MeetingSea.setStatus( dto.getStatus() );
        a07MeetingSea.setSubject( dto.getSubject() );
        a07MeetingSea.setUrgentLevel( dto.getUrgentLevel() );
        a07MeetingSea.setWfType( dto.getWfType() );

        return a07MeetingSea;
    }

    @Override
    public List<A07MeetingSea> toEntity(List<A07HhInfoListFwDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A07MeetingSea> list = new ArrayList<A07MeetingSea>( dtoList.size() );
        for ( A07HhInfoListFwDto a07HhInfoListFwDto : dtoList ) {
            list.add( toEntity( a07HhInfoListFwDto ) );
        }

        return list;
    }
}
