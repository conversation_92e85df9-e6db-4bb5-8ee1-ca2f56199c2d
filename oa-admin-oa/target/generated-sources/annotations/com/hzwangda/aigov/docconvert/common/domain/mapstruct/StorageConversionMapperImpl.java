package com.hzwangda.aigov.docconvert.common.domain.mapstruct;

import com.hzwangda.aigov.docconvert.common.domain.dto.StorageConversionDto;
import com.hzwangda.aigov.docconvert.common.domain.mapstruct.util.StorageConversionUtil;
import com.wangda.oa.domain.LocalStorage;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class StorageConversionMapperImpl implements StorageConversionMapper {

    @Autowired
    private StorageConversionUtil storageConversionUtil;

    @Override
    public List<StorageConversionDto> toDto(List<LocalStorage> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<StorageConversionDto> list = new ArrayList<StorageConversionDto>( entityList.size() );
        for ( LocalStorage localStorage : entityList ) {
            list.add( toDto( localStorage ) );
        }

        return list;
    }

    @Override
    public LocalStorage toEntity(StorageConversionDto dto) {
        if ( dto == null ) {
            return null;
        }

        LocalStorage localStorage = new LocalStorage();

        localStorage.setId( dto.getId() );
        localStorage.setName( dto.getName() );
        localStorage.setPath( dto.getPath() );
        localStorage.setRealName( dto.getRealName() );
        if ( dto.getSize() != null ) {
            localStorage.setSize( Long.parseLong( dto.getSize() ) );
        }
        localStorage.setSuffix( dto.getSuffix() );
        localStorage.setType( dto.getType() );

        return localStorage;
    }

    @Override
    public List<LocalStorage> toEntity(List<StorageConversionDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<LocalStorage> list = new ArrayList<LocalStorage>( dtoList.size() );
        for ( StorageConversionDto storageConversionDto : dtoList ) {
            list.add( toEntity( storageConversionDto ) );
        }

        return list;
    }

    @Override
    public StorageConversionDto toDto(LocalStorage entity) {
        if ( entity == null ) {
            return null;
        }

        StorageConversionDto storageConversionDto = new StorageConversionDto();

        storageConversionDto.setConversionStorage( storageConversionUtil.findConvertedFile( entity.getId() ) );
        storageConversionDto.setStorageId( entity.getId() );
        storageConversionDto.setId( entity.getId() );
        storageConversionDto.setName( entity.getName() );
        storageConversionDto.setPath( entity.getPath() );
        storageConversionDto.setRealName( entity.getRealName() );
        if ( entity.getSize() != null ) {
            storageConversionDto.setSize( String.valueOf( entity.getSize() ) );
        }
        storageConversionDto.setSuffix( entity.getSuffix() );
        storageConversionDto.setType( entity.getType() );

        generateUrl( entity, storageConversionDto );

        return storageConversionDto;
    }
}
