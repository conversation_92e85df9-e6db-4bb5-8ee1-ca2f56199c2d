package com.hzwangda.aigov.modules.duty.domain.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class D15DutyLogCriteria {
    @ApiModelProperty(value = "开始值班日期")
    @Query(type = Query.Type.BETWEEN)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private List<Date> dutyTime;

    @ApiModelProperty(value = "模糊搜索")
    @Query(type = Query.Type.INNER_LIKE, blurry = "oldUsername,username")
    private String searchKeys;

    @ApiModelProperty(value = "是否同意")
    @Query
    private Integer type;
}
