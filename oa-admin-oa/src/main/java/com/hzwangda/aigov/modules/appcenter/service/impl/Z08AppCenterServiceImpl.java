package com.hzwangda.aigov.modules.appcenter.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.hzwangda.aigov.modules.appcenter.domain.criteria.Z08AppCenterCriteria;
import com.hzwangda.aigov.modules.appcenter.domain.entity.Z08AppCenter;
import com.hzwangda.aigov.modules.appcenter.repository.Z08AppCenterRepository;
import com.hzwangda.aigov.modules.appcenter.service.Z08AppCenterService;
import com.hzwangda.aigov.modules.document.constant.DocumentConstant;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwDto;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwQueryCriteria;
import com.hzwangda.aigov.modules.document.dto.AppAndSpaceDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwView;
import com.hzwangda.aigov.modules.document.repository.A07DocumentGwViewRepository;
import com.hzwangda.aigov.modules.enums.AppClassifyEnum;
import com.hzwangda.aigov.modules.favorite.domain.Favorite;
import com.hzwangda.aigov.modules.favorite.repository.FavoriteRepository;
import com.hzwangda.aigov.modules.workflow.enums.workflow.WorkflowType;
import com.wangda.oa.backlog.bo.BacklogBusinessSearchBO;
import com.wangda.oa.backlog.bo.BacklogListBO;
import com.wangda.oa.backlog.dto.BacklogListDto;
import com.wangda.oa.backlog.dto.BacklogListPageDto;
import com.wangda.oa.backlog.service.WdBacklogService;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.workflow.domain.application.AppType;
import com.wangda.oa.modules.workflow.repository.application.AppTypeRepository;
import com.wangda.oa.modules.workflow.service.IUserRuleService;
import com.wangda.oa.modules.workflow.service.application.AppTypeService;
import com.wangda.oa.modules.workflow.service.application.ApplicationService;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.xml.transform.Transformer;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.hzwangda.aigov.modules.document.constant.DocumentConstant.PROCESS_DEFINITION_XZFW_KEY_LIST;

/**
 * @author: zhangzhanlong
 * @date: 2022/11/4 15:09
 * @description:
 */
@Service
@RequiredArgsConstructor
public class Z08AppCenterServiceImpl implements Z08AppCenterService {
    private final Z08AppCenterRepository appCenterRepository;
    private final ApplicationService applicationService;
    private final IUserRuleService userRuleService;
    private final WdBacklogService wdBacklogService;
    private final A07DocumentGwViewRepository a07DocumentGwViewRepository;
    private final UserRepository userRepository;
    private final AppTypeRepository appTypeRepository;
    private final AppTypeService appTypeService;

    private final FavoriteRepository favoriteRepository;
    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Object getAppConterList(Z08AppCenterCriteria criteria) {
        List<Long> appIds;
        List<String> values = criteria.getValue();
        if (CollUtil.isEmpty(values)) {
            List<AppType> all = appTypeService.eliminateTypeList(Arrays.asList(AppClassifyEnum.GW_FW.getValue(), AppClassifyEnum.GW_SW.getValue()));
            List<String> collect = all.stream().map(AppType::getKey).collect(Collectors.toList());
            values = collect;
        }
        if (ObjectUtil.isNotEmpty(criteria.getType()) && criteria.getType().equals(1)) {
            List<Z08AppCenter> byUsername = appCenterRepository.findByUsername(SecurityUtils.getCurrentUsername());
            appIds = byUsername.stream().filter(center -> userRuleService.hasCreatePermission(center.getAppId(), SecurityUtils.getCurrentUsername())).map(Z08AppCenter::getAppId).collect(Collectors.toList());
            // 处理模糊类型
            Set<String> set = new HashSet<>();
            for (String key : values) {
                List<String> keys = appCenterRepository.getAppTypeByKeyLike("%" + key + "%");
                set.addAll(keys);
            }
            List<AppAndSpaceDto> appByTypeIn = appCenterRepository.getAppByTypeIn(new ArrayList<>(set));
            List<Long> collect = appByTypeIn.stream().filter(application -> userRuleService.hasCreatePermission(application.getApplicationId(), SecurityUtils.getCurrentUsername())).map(AppAndSpaceDto::getApplicationId).collect(Collectors.toList());
            appIds = (List<Long>) CollUtil.intersection(appIds, collect);
        } else {
            // 处理模糊类型
            Set<String> set = new HashSet<>();
            for (String key : values) {
                List<String> keys = appCenterRepository.getAppTypeByKeyLike("%" + key + "%");
                set.addAll(keys);
            }
            List<AppAndSpaceDto> appByTypeIn = appCenterRepository.getAppByTypeIn(new ArrayList<>(set));
            // 筛选符合本人权限的
            appIds = appByTypeIn.stream().filter(application -> userRuleService.hasCreatePermission(application.getApplicationId(), SecurityUtils.getCurrentUsername())).map(AppAndSpaceDto::getApplicationId).collect(Collectors.toList());
        }

        if (CollUtil.isEmpty(appIds)) {
            return new ArrayList<>();
        }
        // 总数据
        List<Map<String, Object>> list = new ArrayList<>();

        // 根据appid查询应用名称和空间名称
        List<AppAndSpaceDto> byAppIds;
        if (StringUtils.isBlank(criteria.getName())) {
            byAppIds = appCenterRepository.getAppByAppIds(appIds);
        } else {
            byAppIds = appCenterRepository.getAppByAppIdsAndName(appIds, "%" + criteria.getName() + "%");
        }
        for (AppAndSpaceDto app : byAppIds) {
            // 单个空间表格数据
            Map<String, Object> map = new HashMap<>();
            List<Map<String, Object>> spaceName = list.stream().filter(a -> a.get("spaceName").toString().equals(app.getSpaceName())).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(spaceName)) {
                continue;
            }
            map.put("spaceName", app.getSpaceName());
            List<Map<String, Object>> appList = new ArrayList<>();

            List<AppAndSpaceDto> appAndSpaceDto = byAppIds.stream().filter(a -> a.getSpaceName().equals(app.getSpaceName())).collect(Collectors.toList());
            for (AppAndSpaceDto andSpaceDto : appAndSpaceDto) {
                Map<String, Object> appMap = new HashMap<>();

                Z08AppCenter appCenter = appCenterRepository.getByUsernameAndAppId(SecurityUtils.getCurrentUsername(), andSpaceDto.getApplicationId());
                if (ObjectUtil.isNotEmpty(appCenter)) {
                    appMap.put("optional", true);
                } else {
                    appMap.put("optional", false);
                }
                appMap.put("appName", andSpaceDto.getAppName());
                appMap.put("appId", andSpaceDto.getApplicationId());
                appList.add(appMap);
            }

            map.put("appList", appList);
            list.add(map);
        }
        //应用空间按照应用数量排序
        list = list.stream().sorted((o1, o2) -> {
            ArrayList list1 = (ArrayList) o1.get("appList");
            ArrayList list2 = (ArrayList) o2.get("appList");
            return list2.size() - list1.size();
        }).collect(Collectors.toList());
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void saveAppConter(Long appId) {
        Z08AppCenter appCenter = appCenterRepository.findFirstByUsernameAndAppId(SecurityUtils.getCurrentUsername(), appId);
        if (Objects.isNull(appCenter)) {
            Z08AppCenter center = new Z08AppCenter();
            center.setUsername(SecurityUtils.getCurrentUsername());
            center.setAppId(appId);
            appCenterRepository.save(center);
            System.err.println();
        } else {
            appCenterRepository.delete(appCenter);
        }
    }

    @Override
    public Map<String, Object> getXzList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        //封装待办查询条件
        BacklogListBO backlogListBO = new BacklogListBO();
        backlogListBO.setUserId(SecurityUtils.getCurrentUsername());
        backlogListBO.setAppId("OA");
        if (StringUtils.isNotBlank(criteria.getProcessDefinitionKey())) {
            //实例定义不为空
            backlogListBO.setModuleCode(criteria.getProcessDefinitionKey());
        } else {
            //实例定义为空则查询公文管理对应定义
            backlogListBO.setModuleCodes(PROCESS_DEFINITION_XZFW_KEY_LIST);
        }
        List<BacklogBusinessSearchBO> businessSearchBOList = new ArrayList<>();
        if (StringUtils.isNotBlank(criteria.getBt())) {
            backlogListBO.setBt(criteria.getBt());
        }
        if (criteria.getTimeRange() != null && criteria.getTimeRange().size() > 1) {
            backlogListBO.setCreateStartTime(criteria.getTimeRange().get(0));
            backlogListBO.setCreateEndTime(criteria.getTimeRange().get(1));
        }
        backlogListBO.setBusinessSearchBOList(businessSearchBOList);
        backlogListBO.setStatus(criteria.getStatus());
        if (criteria.getStatus() == null) {
            //若不传默认查询所有，则为4
            backlogListBO.setStatus(4);
        }
        backlogListBO.setPage(pageable.getPageNumber());
        backlogListBO.setSize(pageable.getPageSize());

        //查询待办服务
        BacklogListPageDto backlogListPageDto = wdBacklogService.getBacklogList(backlogListBO);
        List<BacklogListDto> backlogListDtoList = backlogListPageDto.getContent();
        if (CollectionUtils.isEmpty(backlogListDtoList)) {
            return PageUtil.toPage(Page.empty());
        }

        //获取流程实例id
        Map<String, Date> bpmInstanceIdMap = new LinkedHashMap<>();
        for (BacklogListDto backlogListDto : backlogListDtoList) {
            bpmInstanceIdMap.put(backlogListDto.getBizId(), backlogListDto.getCreateDate());
        }
        criteria.setBpmInstanceId(Lists.newArrayList(bpmInstanceIdMap.keySet()));
        //查询业务视图表信息
        List<A07DocumentGwView> viewList = a07DocumentGwViewRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));

        String transactorNames = backlogListDtoList.stream().map(BacklogListDto::getCurrentHandler).collect(Collectors.joining(","));
        //用户名集合放入
        List<String> username = viewList.stream().map(A07DocumentGwView::getCjr).collect(Collectors.toList());
        //办理人放入用户名集合
        username.addAll(Arrays.asList(transactorNames.split(",")));

        //查询用户信息
        List<User> userList = userRepository.findByUsernameIn(username);
        Map<String, User> finalUserMap = userList.stream().collect(Collectors.toMap(User::getUsername, a -> a, (k1, k2) -> k1));

        List<A07DocumentGwDto> returnList = backlogListDtoList.stream().map(p -> {
            A07DocumentGwDto documentGwDto = new A07DocumentGwDto();
            for (A07DocumentGwView a07DocumentGwView : viewList) {
                if (a07DocumentGwView.getBpmInstanceId().equals(p.getBizId())) {
                    BeanUtils.copyProperties(a07DocumentGwView, documentGwDto);
                    if (StringUtils.isNotBlank(p.getCurrentHandler())) {
                        //当前办理人不为空根据username设置中文
                        String[] handlerArr = p.getCurrentHandler().split(",");
                        String handler = "";
                        for (int i = 0; i < handlerArr.length; i++) {
                            if (finalUserMap.get(handlerArr[i]) == null) {
                                continue;
                            }
                            handler += finalUserMap.get(handlerArr[i]).getNickName() + ",";
                        }
                        if (StringUtils.isNotBlank(handler)) {
                            handler = handler.substring(0, handler.length() - 1);
                        }
                        documentGwDto.setBlr(handler);
                    }
                    User cjrUser = finalUserMap.get(a07DocumentGwView.getCjr());
                    documentGwDto.setCjr(cjrUser == null ? null : cjrUser.getNickName());
                    WorkflowType workflowType = WorkflowType.getByValue(a07DocumentGwView.getModuleType());
                    documentGwDto.setModuleType(workflowType == null ? null : workflowType.getName());
                    documentGwDto.setBpmStatus(p.getHandleStatus());
                    documentGwDto.setUrgent(p.getUrgent());
                    if (Objects.nonNull(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()))) {
                        documentGwDto.setCreateTime(new Timestamp(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()).getTime()));
                    }
                    if (StringUtils.isBlank(documentGwDto.getBt())) {
                        documentGwDto.setBt(p.getTitle());
                    }
                    List<Favorite> favorites = favoriteRepository.findAllByProcInstanceIdAndTypeAndCreateBy(p.getBizId(), 0, SecurityUtils.getCurrentUsername());
                    if (favorites.size()>0) {
                        documentGwDto.setFavoriteId(favorites.get(0).getId());
                    }
                    return documentGwDto;
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, Object> returnMap = new LinkedHashMap<>(2);
        returnMap.put("content", returnList);
        returnMap.put("totalElements", backlogListPageDto.getTotalElements());
        return returnMap;
    }

    @Override
    public Page readList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {

        StringBuilder sql = new StringBuilder("SELECT bpm_task_user_read.bt, bpm_task_user_read.process_instance_id procInstId, ");
        sql.append(" (select nick_name from sys_user WHERE username=act_hi_varinst.text_) startedBy, ");
        sql.append(" act_hi_varinst.create_time_ createTime, ");
        sql.append(" (\n" +
                "\tSELECT\n" +
                "\t\tarray_to_string( ARRAY_AGG ( ( SELECT nick_name FROM sys_user WHERE username = assignee_ ) ), ',' ) \n" +
                "\tFROM\n" +
                "\t\tact_ru_task \n" +
                "\tWHERE\n" +
                "\t\tact_ru_task.proc_inst_id_ = bpm_task_user_read.process_instance_id \n" +
                "\t) curHandler,  ");
        sql.append(" favorite.id favoriteId ");
        sql.append(" FROM bpm_task_user_read ");
        sql.append(" LEFT JOIN act_hi_varinst ON bpm_task_user_read.process_instance_id = act_hi_varinst.proc_inst_id_ AND act_hi_varinst.name_ = 'startedBy'  ");
        sql.append(" LEFT JOIN favorite ON favorite.proc_instance_id=bpm_task_user_read.process_instance_id AND type=0 ");
        sql.append(" WHERE bpm_task_user_read.assignee = :assignee ");
        sql.append(" AND bpm_task_user_read.process_def_key in ('");
        sql.append(PROCESS_DEFINITION_XZFW_KEY_LIST.stream().collect(Collectors.joining("','")));
        sql.append("') ");
        String bt = criteria.getBt();
        if (StringUtils.isNotEmpty(bt)) {
            sql.append(" AND bt like :bt ");
        }
        Integer status = criteria.getStatus();
        if (status == 0) {
            sql.append(" AND last_read_time IS NULL ");
        } else {
            sql.append(" AND last_read_time IS NOT NULL ");
        }
        Query nativeQuery = entityManager.createNativeQuery(sql.toString());
        nativeQuery.setParameter("assignee", SecurityUtils.getCurrentUsername());
        if (StringUtils.isNotEmpty(bt)) {
            nativeQuery.setParameter("bt", "%" + bt + "%");
        }
        int count = nativeQuery.getResultList().size();
        if (pageable != null) { //分页
            nativeQuery.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());
            nativeQuery.setMaxResults(pageable.getPageSize());
        }
        List resultList = nativeQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).getResultList();
        resultList.stream().forEach(obj -> {
            Map map = (Map) obj;
            map.put("favoriteId", map.get("favoriteid"));
            map.remove("favoriteid");
        });
        return new PageImpl(resultList, pageable, count);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object readAll() {
        StringBuilder sql = new StringBuilder("UPDATE bpm_task_user_read SET ");
        sql.append(" first_read_time=?1, last_read_time=?2 ");
        sql.append(" WHERE assignee=?3 ");
        sql.append(" AND process_def_key in ('");
        sql.append(PROCESS_DEFINITION_XZFW_KEY_LIST.stream().collect(Collectors.joining("','")));
        sql.append("') ");
        sql.append(" AND last_read_time IS NULL ");
        Query nativeQuery = entityManager.createNativeQuery(sql.toString());
        Date date = new Date();
        nativeQuery.setParameter(1, date);
        nativeQuery.setParameter(2, date);
        nativeQuery.setParameter(3, SecurityUtils.getCurrentUsername());
        int i = nativeQuery.executeUpdate();
        return i;
    }
}
