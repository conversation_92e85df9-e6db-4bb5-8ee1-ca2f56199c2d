package com.hzwangda.aigov.modules.file.service;

import com.hzwangda.aigov.modules.file.entity.ReplaceFileLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface ReplaceFileService {
    Object replaceFile(Long id, MultipartFile file, HttpServletRequest request, HttpServletResponse response);

    Page<ReplaceFileLog> logs(Long id, String name, Pageable pageable);
}
