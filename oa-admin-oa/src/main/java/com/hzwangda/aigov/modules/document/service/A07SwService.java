package com.hzwangda.aigov.modules.document.service;

import com.hzwangda.aigov.modules.document.dto.*;
import com.hzwangda.aigov.modules.document.entity.A07DocumentSw;
import com.wangda.boot.platform.base.ResultJson;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface A07SwService {

    /**
     * 收文列表
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> getSwList(A07DocumentSwQueryCriteria criteria, Pageable pageable);

    /**
     * 领导批示办理单列表
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> getLdpsList(A07DocumentLdpsQueryCriteria criteria, Pageable pageable);

    /**
     * 签批办理单列表
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> getClqpList(A07DocumentClqpQueryCriteria criteria, Pageable pageable);

    /**
     * 信息公开申请列表
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> getXxgksqList(A07DocumentXxgksqQueryCriteria criteria, Pageable pageable);

    /**
     * 收文详情
     * @param id
     * @return
     */
    A07DocumentSwDto getSwInfo(Long id);

    /**
     * 领导批示办理单详情
     * @param id
     * @return
     */
    A07DocumentLdpsDto getLdpsInfo(Long id);

    /**
     * 签批办理单详情
     * @param id
     * @return
     */
    A07DocumentClqpDto getClqpInfo(Long id);

    /**
     * 信息公开申请详情
     * @param id
     * @return
     */
    A07DocumentXxgksqDto getXxgksqInfo(Long id);

    /**
     * 收文检索
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> getSwListRetrieve(A07DocumentGwRetrieveQueryCriteria criteria, Pageable pageable);

    /**
     * 删除收文
     * @param ids
     * @return
     */
    Boolean deleteSw(List<Long> ids);

    /**
     * 删除领导批示办理单
     * @param ids
     * @return
     */
    Boolean deleteLdps(List<Long> ids);

    /**
     * 删除签批办理
     * @param ids
     * @return
     */
    Boolean deleteClqps(List<Long> ids);

    /**
     * 删除信息公开申请
     * @param ids
     * @return
     */
    Boolean deleteXxgksq(List<Long> ids);

    /**
     * 根据代字获取最新的序号值
     * @param dz
     * @param nh
     * @return
     */
    Integer getXhByNh(String dz, String nh);

    /**
     * Description: 根据流程实例查询数据
     * @param processInstanceId
     * @return: com.hzwangda.aigov.modules.document.entity.A07DocumentSw
     * @Date: 2021/12/8 20:16
     * @Author: maogy
     * @throws:
     */
    A07DocumentSw getSwByProcessInstanceId(String processInstanceId);

    /**
     * @Description: 根据用户输入的来文单位匹配历史数据
     * @Author: lizh
     * @Date: 2021/12/17 16:17
     **/
    List<Object> getLwdwList(String lwdw);

    /**
     * @description 单位收文转内部收文
     * <AUTHOR>
     * @updateTime 2023/1/11 16:35
     * @return: com.wangda.boot.platform.base.ResultJson
     */
    ResultJson unitSwToInSw(String procInstId, Long appId);

    /**
     * @description 内部收文转内部发文
     * <AUTHOR>
     * @updateTime 2023/1/11 16:35
     */
    ResultJson inSwToInFw(String procInstId, Long appId);
}
