package com.hzwangda.aigov.modules.document.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface A07GwService {

    /**
     * @description 查询表单默认值
     * <AUTHOR>
     * @updateTime 2022/10/11 13:48
     * @return: java.lang.Object
     */

    Object getGwDefault();

    Object getDeptHeaderTemplateList(String procInstId);

    List<String> getDeptDzList(String dzType);


    List<Map<String, Object>> getGwPermission(String gwType);

    List<Map<String, Object>> getGwClassify(String gwType);

    /**
     * @param key:
     * @description 根据key查询短信模板
     * <AUTHOR>
     * @updateTime 2022/10/28 14:26
     * @return: java.lang.Object
     */

    Object getGwUrgeSubject(String procInstId, String key);

    /**
     * @param procInstId:
     * @description 根据流程实例id查询打印模板地址
     * <AUTHOR>
     * @updateTime 2022/11/29 15:51
     * @return: java.lang.Object
     */
    Object getGwPrintAddress(String procInstId);

    /**
     * @param taskId:
     * @description 根据任务id查询办理人
     * <AUTHOR>
     * @updateTime 2022/12/20 10:04
     * @return: java.lang.Object
     */

    String getUsernameByTaskId(String taskId);

    /**
     * @param procInstId:
     * @description 根据流程id查询appid和formData
     * <AUTHOR>
     * @updateTime 2022/12/20 10:04
     * @return: java.lang.Object
     */

    Map<String, Object> getAppIdAndFormData(String procInstId);

    Object count(Integer year, Integer gwType, List<Date> createTime);
}
