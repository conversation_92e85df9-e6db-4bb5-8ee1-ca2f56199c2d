package com.hzwangda.aigov.modules.collaboration.controller;

import com.hzwangda.aigov.modules.collaboration.domain.criteria.A10CollaborationInfoQueryCriteria;
import com.hzwangda.aigov.modules.collaboration.domain.criteria.SmsInfo;
import com.hzwangda.aigov.modules.collaboration.domain.dto.A10CollaborationInfo4ListDto;
import com.hzwangda.aigov.modules.collaboration.domain.dto.A10CollaborationInfo4ViewDto;
import com.hzwangda.aigov.modules.collaboration.domain.dto.A10CollaborationInfoDto;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationInfo;
import com.hzwangda.aigov.modules.collaboration.service.A10CollaborationInfoService;
import com.hzwangda.aigov.modules.zjedu.domain.dto.MyWork;
import com.hzwangda.aigov.modules.zjedu.service.MyWorkService;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.boot.response.ResponseInfo;
import com.wangda.oa.annotation.Log;
import com.wangda.oa.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @program: oa-mgt-server
 * @description: 工作协同对外接口
 * @author: liux
 * @create: 2021-08-12 18:47
 */
@Api(tags = "工作协同")
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/api/aigov/collaboration/info")
public class A10CollaborationInfoController {
    @Resource
    private A10CollaborationInfoService collaborationInfoService;
    @Resource
    private MyWorkService myWorkService;

    @Log("分页查询-所有待签收协同")
    @ApiOperation("分页查询-所有待签收协同")
    @GetMapping(value = "/unsignList")
    public ResponseEntity<Page<A10CollaborationInfo4ListDto>> unsignList(A10CollaborationInfoQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity(collaborationInfoService.queryUnsignList(criteria, pageable), HttpStatus.OK);
    }

    @Log("分页查询-所有已签收协同")
    @ApiOperation("分页查询-所有已签收协同")
    @GetMapping(value = "/signedList")
    public ResponseEntity<Page<A10CollaborationInfo4ListDto>> signedList(A10CollaborationInfoQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity(collaborationInfoService.querySignedList(criteria, pageable), HttpStatus.OK);
    }

    @Log("分页查询-所有工作协同")
    @ApiOperation("分页查询-所有工作协同")
    @GetMapping(value = "/allList")
    public ResponseEntity<Page<A10CollaborationInfo4ListDto>> allList(A10CollaborationInfoQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity(collaborationInfoService.queryAllList(criteria, pageable), HttpStatus.OK);
    }

    @GetMapping("/view")
    @Log("工作协同详情-查看")
    @ApiOperation("工作协同详情-查看")
    public ResponseEntity<A10CollaborationInfo4ViewDto> view(@RequestParam("id") Long id,@RequestParam("userType") Long userType) {
        return new ResponseEntity<>(collaborationInfoService.queryOneCollaborationByIdForView(id,userType), HttpStatus.OK);
    }

    @GetMapping("/editDetail")
    @Log("工作协同详情-编辑")
    @ApiOperation("工作协同详情-编辑")
    public ResponseEntity<A10CollaborationInfoDto> editDetail(Long id) {
        return new ResponseEntity<>(collaborationInfoService.queryOneCollaborationByIdForEdit(id), HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("工作协同-保存或发送")
    @ApiOperation("工作协同-保存或发送")
    public ResponseEntity<Long> save(@Validated @RequestBody A10CollaborationInfo entity) {
        return new ResponseEntity<>(collaborationInfoService.doSave(entity), HttpStatus.OK);
    }

    @PostMapping("/edit")
    @Log("工作协同-编辑")
    @ApiOperation("工作协同-编辑")
    public ResponseEntity<Long> edit(@Validated @RequestBody A10CollaborationInfo entity) {
        return new ResponseEntity<>(collaborationInfoService.doEdit(entity), HttpStatus.OK);
    }

    @PostMapping("/delete")
    @Log("工作协同-删除")
    @ApiOperation("工作协同-删除")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        collaborationInfoService.doDelete(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/readFirst")
    @Log("工作协同-首读")
    @ApiOperation("工作协同-首读")
    public ResponseEntity<Object> readFirst(Long id) {
        collaborationInfoService.doFirstRead(id);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    @PostMapping("/saveMyWork")
    @ApiOperation("领办-保存到我的工作")
    public ResponseEntity<Object> saveMyWork(@RequestParam Long id) {
        if (myWorkService.queryIsClaim(id.toString())) {
            return ResponseEntity.ok("该任务已认领");
        }
        MyWork myWork = new MyWork();
        myWork.setType("collaboration");
        myWork.setUsername(SecurityUtils.getCurrentUsername());
        myWork.setProcessInstanceId(id.toString());
        ResponseInfo responseInfo = myWorkService.createOrUpdate(myWork);
        String message = "领办成功";
        if (StringUtils.isNotBlank(responseInfo.getMessage())) {
            message = responseInfo.getMessage();
        }
        return ResponseEntity.ok(message);
    }

    @PostMapping("/cancelMyWork")
    @ApiOperation("取消工作-去掉我的工作")
    public ResponseEntity<Object> cancelMyWork(@RequestParam Long id) {
        boolean result = myWorkService.deleteByProcInstanceId(id.toString(), "collaboration");
        if (!result) {
            return ResponseEntity.ok("完成工作失败，请联系管理员");
        }
        return ResponseEntity.ok("完成工作成功");
    }

    @PostMapping("/sendBacklogFromUnsignedList")
    @ApiOperation("重新发送未办理的待办消息")
    public ResponseEntity<Object> sendBacklogFromUnsignedList() {
        collaborationInfoService.sendBacklogFromUnsignedList();
        return ResponseEntity.ok("已发送");
    }

    @ApiOperation("短信催收")
    @PostMapping("/smsUrge")
    public ResponseEntity<String> smsUrge(@RequestBody SmsInfo smsInfo) {
        return ResponseEntity.ok(collaborationInfoService.smsUrge(smsInfo));
    }

    @ApiOperation("获取短信催收内容")
    @GetMapping("/getSms")
    public ResponseEntity<Map> getSms(@RequestParam("id") Long id,@RequestParam("userType") Long userType) {
        return ResponseEntity.ok(collaborationInfoService.getSms(id,userType));
    }

    @Log("工作协同转收文")
    @ApiOperation("工作协同转收文")
    @PostMapping("/doTransferToDeptOA")
    public ResultJson doTransferToDeptOA(@RequestParam Long id) {
        return collaborationInfoService.doTransferToDeptOA(id);
    }
}
