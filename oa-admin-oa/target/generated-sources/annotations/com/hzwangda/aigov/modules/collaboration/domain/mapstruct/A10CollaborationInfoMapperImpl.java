package com.hzwangda.aigov.modules.collaboration.domain.mapstruct;

import com.hzwangda.aigov.modules.collaboration.domain.dto.A10CollaborationInfo4ListDto;
import com.hzwangda.aigov.modules.collaboration.domain.dto.A10CollaborationInfo4ViewDto;
import com.hzwangda.aigov.modules.collaboration.domain.dto.A10CollaborationInfoDto;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationAssignee;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationInfo;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto.SimpleUserDtoBuilder;
import com.wangda.oa.modules.system.service.mapstruct.UserMapperUtil;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A10CollaborationInfoMapperImpl implements A10CollaborationInfoMapper {

    @Autowired
    private UserMapperUtil userMapperUtil;
    @Autowired
    private StoragesMapperUtil storagesMapperUtil;

    @Override
    public A10CollaborationInfo4ViewDto toDto(A10CollaborationInfo entity) {
        if ( entity == null ) {
            return null;
        }

        A10CollaborationInfo4ViewDto a10CollaborationInfo4ViewDto = new A10CollaborationInfo4ViewDto();

        a10CollaborationInfo4ViewDto.setAssignees( a10CollaborationAssigneeListToSimpleUserDtoList( entity.getAssignees() ) );
        a10CollaborationInfo4ViewDto.setAttachments( storagesMapperUtil.listDto2Long( entity.getAttachments() ) );
        a10CollaborationInfo4ViewDto.setContent( entity.getContent() );
        a10CollaborationInfo4ViewDto.setCreateBy( entity.getCreateBy() );
        List<StorageBiz> list2 = entity.getFj();
        if ( list2 != null ) {
            a10CollaborationInfo4ViewDto.setFj( new ArrayList<StorageBiz>( list2 ) );
        }
        a10CollaborationInfo4ViewDto.setId( entity.getId() );
        a10CollaborationInfo4ViewDto.setLink( entity.getLink() );
        a10CollaborationInfo4ViewDto.setSendTime( entity.getSendTime() );
        a10CollaborationInfo4ViewDto.setStatus( entity.getStatus() );
        a10CollaborationInfo4ViewDto.setSubject( entity.getSubject() );

        return a10CollaborationInfo4ViewDto;
    }

    @Override
    public List<A10CollaborationInfo4ViewDto> toDto(List<A10CollaborationInfo> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A10CollaborationInfo4ViewDto> list = new ArrayList<A10CollaborationInfo4ViewDto>( entityList.size() );
        for ( A10CollaborationInfo a10CollaborationInfo : entityList ) {
            list.add( toDto( a10CollaborationInfo ) );
        }

        return list;
    }

    @Override
    public A10CollaborationInfo toEntity(A10CollaborationInfo4ViewDto dto) {
        if ( dto == null ) {
            return null;
        }

        A10CollaborationInfo a10CollaborationInfo = new A10CollaborationInfo();

        a10CollaborationInfo.setAssignees( simpleUserDtoListToA10CollaborationAssigneeList( dto.getAssignees() ) );
        a10CollaborationInfo.setAttachments( storagesMapperUtil.listLong2Dto( dto.getAttachments() ) );
        a10CollaborationInfo.setContent( dto.getContent() );
        a10CollaborationInfo.setCreateBy( dto.getCreateBy() );
        a10CollaborationInfo.setId( dto.getId() );
        a10CollaborationInfo.setLink( dto.getLink() );
        a10CollaborationInfo.setSendTime( dto.getSendTime() );
        a10CollaborationInfo.setStatus( dto.getStatus() );
        a10CollaborationInfo.setSubject( dto.getSubject() );
        List<StorageBiz> list2 = dto.getFj();
        if ( list2 != null ) {
            a10CollaborationInfo.setFj( new ArrayList<StorageBiz>( list2 ) );
        }

        return a10CollaborationInfo;
    }

    @Override
    public List<A10CollaborationInfo> toEntity(List<A10CollaborationInfo4ViewDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A10CollaborationInfo> list = new ArrayList<A10CollaborationInfo>( dtoList.size() );
        for ( A10CollaborationInfo4ViewDto a10CollaborationInfo4ViewDto : dtoList ) {
            list.add( toEntity( a10CollaborationInfo4ViewDto ) );
        }

        return list;
    }

    @Override
    public List<A10CollaborationInfo4ListDto> toList(List<A10CollaborationInfo> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A10CollaborationInfo4ListDto> list = new ArrayList<A10CollaborationInfo4ListDto>( entityList.size() );
        for ( A10CollaborationInfo a10CollaborationInfo : entityList ) {
            list.add( toListDto( a10CollaborationInfo ) );
        }

        return list;
    }

    @Override
    public A10CollaborationInfo4ListDto toListDto(A10CollaborationInfo entityList) {
        if ( entityList == null ) {
            return null;
        }

        A10CollaborationInfo4ListDto a10CollaborationInfo4ListDto = new A10CollaborationInfo4ListDto();

        a10CollaborationInfo4ListDto.setCreateBy( userMapperUtil.toUser( entityList.getCreateBy() ) );
        a10CollaborationInfo4ListDto.setCreateTime( entityList.getCreateTime() );
        a10CollaborationInfo4ListDto.setId( entityList.getId() );
        a10CollaborationInfo4ListDto.setSendTime( entityList.getSendTime() );
        a10CollaborationInfo4ListDto.setStatus( entityList.getStatus() );
        a10CollaborationInfo4ListDto.setSubject( entityList.getSubject() );
        a10CollaborationInfo4ListDto.setUrgent( entityList.getUrgent() );

        return a10CollaborationInfo4ListDto;
    }

    @Override
    public A10CollaborationInfoDto toInfoDto(A10CollaborationInfo entity) {
        if ( entity == null ) {
            return null;
        }

        A10CollaborationInfoDto a10CollaborationInfoDto = new A10CollaborationInfoDto();

        a10CollaborationInfoDto.setAssignees( a10CollaborationAssigneeListToSimpleUserDtoList( entity.getAssignees() ) );
        a10CollaborationInfoDto.setAttachments( storagesMapperUtil.listDto2Long( entity.getAttachments() ) );
        a10CollaborationInfoDto.setContent( entity.getContent() );
        a10CollaborationInfoDto.setId( entity.getId() );
        a10CollaborationInfoDto.setStatus( entity.getStatus() );
        a10CollaborationInfoDto.setSubject( entity.getSubject() );
        a10CollaborationInfoDto.setUrgent( entity.getUrgent() );

        return a10CollaborationInfoDto;
    }

    protected SimpleUserDto a10CollaborationAssigneeToSimpleUserDto(A10CollaborationAssignee a10CollaborationAssignee) {
        if ( a10CollaborationAssignee == null ) {
            return null;
        }

        SimpleUserDtoBuilder simpleUserDto = SimpleUserDto.builder();

        simpleUserDto.id( a10CollaborationAssignee.getId() );

        return simpleUserDto.build();
    }

    protected List<SimpleUserDto> a10CollaborationAssigneeListToSimpleUserDtoList(List<A10CollaborationAssignee> list) {
        if ( list == null ) {
            return null;
        }

        List<SimpleUserDto> list1 = new ArrayList<SimpleUserDto>( list.size() );
        for ( A10CollaborationAssignee a10CollaborationAssignee : list ) {
            list1.add( a10CollaborationAssigneeToSimpleUserDto( a10CollaborationAssignee ) );
        }

        return list1;
    }

    protected A10CollaborationAssignee simpleUserDtoToA10CollaborationAssignee(SimpleUserDto simpleUserDto) {
        if ( simpleUserDto == null ) {
            return null;
        }

        A10CollaborationAssignee a10CollaborationAssignee = new A10CollaborationAssignee();

        a10CollaborationAssignee.setId( simpleUserDto.getId() );

        return a10CollaborationAssignee;
    }

    protected List<A10CollaborationAssignee> simpleUserDtoListToA10CollaborationAssigneeList(List<SimpleUserDto> list) {
        if ( list == null ) {
            return null;
        }

        List<A10CollaborationAssignee> list1 = new ArrayList<A10CollaborationAssignee>( list.size() );
        for ( SimpleUserDto simpleUserDto : list ) {
            list1.add( simpleUserDtoToA10CollaborationAssignee( simpleUserDto ) );
        }

        return list1;
    }
}
