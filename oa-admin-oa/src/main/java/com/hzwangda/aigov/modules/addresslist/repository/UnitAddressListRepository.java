package com.hzwangda.aigov.modules.addresslist.repository;


import com.hzwangda.aigov.modules.addresslist.domain.entity.UnitAddressList;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitAddressListRepository extends JpaRepository<UnitAddressList, Long>, JpaSpecificationExecutor<UnitAddressList> {
    List<UnitAddressList> findByGroupId(Long id);
}
