/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.modules.document.repository;

import com.hzwangda.aigov.modules.document.entity.Z08DocumentType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-05-14
 **/
public interface Z08DocumentTypeRepository extends JpaRepository<Z08DocumentType, Long>, JpaSpecificationExecutor<Z08DocumentType> {

    List<Z08DocumentType> findByDzLike(String s);

    Z08DocumentType findByDzAndAndApplicationId(String s, Long appId);


    List<Z08DocumentType> findByApplicationId(Long appId);

    List<Z08DocumentType> findByApplicationIdAndDeptId(Long appId,boolean flag);


    List<Z08DocumentType> findByApplicationIdAndDeptId(Long appId,String deptId);

    List<Z08DocumentType> findByDeptIdIsNullOrDeptIdIn(List<String> deptNames);
}