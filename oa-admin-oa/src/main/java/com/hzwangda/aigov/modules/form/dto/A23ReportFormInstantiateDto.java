package com.hzwangda.aigov.modules.form.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.hzwangda.aigov.modules.form.domain.A23ReportForm;
import com.hzwangda.aigov.modules.form.domain.A23ReportFormInstantiate.ReportType;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import lombok.Data;

import java.util.Date;

@Data
public class A23ReportFormInstantiateDto {
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    private String title;
    private Date startTime;
    private Date endTime;
    private ReportType reportType;
    private String bizType;
    private SimpleUserDto creatorId;

    private A23ReportForm form;
    private Boolean enable;
    private String formJson;

}
