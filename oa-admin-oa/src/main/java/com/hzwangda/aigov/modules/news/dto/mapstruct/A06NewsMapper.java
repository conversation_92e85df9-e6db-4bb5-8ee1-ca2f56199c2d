/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.modules.news.dto.mapstruct;

import com.hzwangda.aigov.modules.news.dto.A06NewsDto;
import com.hzwangda.aigov.modules.news.entity.A06News;
import com.wangda.oa.base.BaseMapper;
import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import com.wangda.oa.utils.StringUtils;
import org.mapstruct.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-06-09
 **/
@Mapper(componentModel = "spring", uses = StorageMapperUtil.class, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface A06NewsMapper extends BaseMapper<A06NewsDto, A06News> {

    @AfterMapping
    default void splitTime(A06News entity, @MappingTarget A06NewsDto dto) {
        if (StringUtils.isNotEmpty(entity.getBpmStatus())) {
            ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(entity.getBpmStatus());
            dto.setBpmStatus(procStatusEnum == null ? null : procStatusEnum.getName());
        }
    }

    @Override
    @Mappings({
            @Mapping(target = "read", expression = "java(storageMapperUtil.toConvertToRead(entity.getId()))"),
            @Mapping(target = "guidePicture", expression = "java(storageMapperUtil.toConvertToGuide(entity.getGuideId()))"),
            @Mapping(target = "attachments", expression = "java(storageMapperUtil.toConvertToLocalStorageSimpleDto(entity.getAttachments()))")
    })
    A06NewsDto toDto(A06News entity);
}
