package com.hzwangda.aigov.modules.form.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "a23_report_form_instantiate")
@Data
public class A23ReportFormInstantiate extends BaseEntity {
    @Id
    @JSONField(serializeUsing = ToStringSerializer.class)

    private Long id;
    private String title;
    @ManyToOne
    @ApiModelProperty("表单模版")
    private A23ReportForm form;

    @ApiModelProperty("开始填报时间")
    private Date startTime;

    @ApiModelProperty("填报结束时间")
    private Date endTime;

    @ApiModelProperty("表单类型")
    @Enumerated(EnumType.STRING)
    private ReportType reportType;

    @ApiModelProperty("业务类型")
    private String bizType;
    @ApiModelProperty("表单是否可填报")
    private Boolean enable = true;
    @Lob
    private String formJson;


    @Column(
            name = "create_by",
            updatable = false
    )
    @ApiModelProperty(
            value = "创建人",
            hidden = true
    )
    private String createBy;
    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }

    public enum ReportType {
        single, // 允许填一次
        multiple  // 允许填写多次
    }
}
