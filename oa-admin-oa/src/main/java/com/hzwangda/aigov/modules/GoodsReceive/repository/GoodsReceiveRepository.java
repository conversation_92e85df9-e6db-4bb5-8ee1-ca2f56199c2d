package com.hzwangda.aigov.modules.GoodsReceive.repository;

import com.hzwangda.aigov.modules.GoodsReceive.domain.GoodsReceive;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface GoodsReceiveRepository extends JpaRepository<GoodsReceive,Long>, JpaSpecificationExecutor<GoodsReceive> {
    GoodsReceive findByBpmInstanceId(String processInstanceId);

    void deleteByBpmInstanceId(String instanceId);
}
