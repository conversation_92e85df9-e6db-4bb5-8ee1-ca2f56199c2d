package com.hzwangda.aigov.modules.duty.service;

import com.hzwangda.aigov.modules.duty.domain.dto.D15DutyRecordCriteria;
import com.hzwangda.aigov.modules.duty.domain.dto.TotalQueryCriteria;
import com.hzwangda.aigov.modules.duty.domain.entity.D15DutyRecord;
import com.hzwangda.aigov.modules.duty.domain.entity.D15DutyRecordFeedBack;
import com.hzwangda.aigov.modules.duty.domain.entity.D15DutyRecordList;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

public interface D15DutyRecordService {

    Page<D15DutyRecordList> queryList(D15DutyRecordCriteria d15DutyRecordCriteria, Pageable pageable);

    D15DutyRecord queryById(Long id, Boolean ck);

    Long save(D15DutyRecord d15DutyRecord);

    String batchDel(Long[] ids);

    String getMaxNumber(String type);

    Long read(Long id);

    String mark(Long[] ids);

    Map<String, Object> total(TotalQueryCriteria criteria) throws ParseException;

    List<D15DutyRecordFeedBack> findByNumber(String number);

    Long saveFeedback(D15DutyRecordFeedBack d15DutyRecordFeedBack);

    Boolean existsByNumber(String number);
}
