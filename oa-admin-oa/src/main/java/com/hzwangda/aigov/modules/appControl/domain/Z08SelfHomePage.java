package com.hzwangda.aigov.modules.appControl.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.hzwangda.aigov.modules.appControl.enums.SystemTypeEnum;
import com.wangda.boot.platform.base.BaseDomain;
import com.wangda.oa.modules.workflow.domain.application.Application;
import com.wangda.oa.modules.workflow.domain.common.WFStorageBiz;
import com.wangda.oa.modules.workflow.enums.application.AppStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.*;
import java.util.Objects;

/**
 * @author: cy
 * @date: 2022/8/24 10:17
 * @description: 首页配置
 */
@Data
@Entity
@ApiModel("首页配置")
@Table(name = "z08_self_home_page")
public class Z08SelfHomePage extends BaseDomain {

    @Column(name = "name", length = 128)
    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "可见范围")
    private String appliedRange;

    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "icon", referencedColumnName = "storage_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @ApiModelProperty(value = "应用图标")
    private WFStorageBiz icon;

    @Column(name = "sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @Column(name = "type")
    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "系统类别")
    @Enumerated(value = EnumType.STRING)
    private SystemTypeEnum systemType;

    @ApiModelProperty(value = "内部链接")
    private String internalLink;

    @ApiModelProperty(value = "外部链接")
    private String externalLink;

    @Column(name = "remark")
    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty(value = "状态")
    @Enumerated(value = EnumType.STRING)
    private AppStatusEnum appStatus;

    public void setIcon(WFStorageBiz icon) {
        if(Objects.nonNull(icon)) {
            this.icon = icon;
        }
        if(Objects.nonNull(this.icon) && StringUtils.isNotBlank(this.icon.getStorageId())) {
            if(this.id != null) {
                this.icon.setBizId(this.id.toString());
            }
        }else {
            this.icon = null;
        }
    }

    @Override
    @PrePersist
    public void preCreateEntity() {
        super.preCreateEntity();
        if(Objects.nonNull(this.icon) && StringUtils.isNotBlank(this.icon.getStorageId())) {
            if(this.id != null) {
                this.icon.setBizId(this.id.toString());
                this.icon.setBizType("application.icon");
            }
        }else {
            this.icon = null;
        }
    }

    public void copy(Application application) {
        BeanUtil.copyProperties(application, this, CopyOptions.create().setIgnoreProperties("version", "createDate", "modifiedDate", "createBy", "modifiedBy", "enabled").setIgnoreNullValue(false));
    }

}
