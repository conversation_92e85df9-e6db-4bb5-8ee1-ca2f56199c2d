package com.hzwangda.aigov.modules.document.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.document.entity.A07DocumentSw;
import com.hzwangda.aigov.modules.document.repository.A07DocumentSwRepository;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.wangda.oa.modules.workflow.dto.BacklogListDto;
import com.wangda.oa.modules.workflow.dto.MyWorkDto;
import com.wangda.oa.modules.workflow.enums.MyWorkStatusEnum;
import com.wangda.oa.modules.workflow.service.FlowFormHandle;
import com.wangda.oa.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/13
 * @description 收文表单处理
 */
@Component
@Slf4j
public class A07DocumentSwFormHandle implements FlowFormHandle {

    @Autowired
    private A07DocumentSwRepository a07DocumentSwRepository;

    @Autowired
    private HistoryService historyService;

    @Override
    public void handleFormForMyWork(BacklogListDto myWork, MyWorkDto myWorkDto) {

        myWorkDto.setId(myWork.getId());
        myWorkDto.setProcessInstanceId(myWork.getBizId());
        myWorkDto.setTitle(myWork.getTitle());
        myWorkDto.setType(myWork.getModuleName());
        myWorkDto.setCreateTime(myWork.getCreateDate());
        myWorkDto.setPcUrl(myWork.getPcUrl());
        myWorkDto.setLogo(myWork.getLogo());
        if (Objects.nonNull(myWork.getUrgent())) {
            // 0:非急件,1:急件
            if (myWork.getUrgent().equals(1)) {
                myWorkDto.setHj("急");
            } else {
                myWorkDto.setHj("普通");
            }
        }

        A07DocumentSw a07DocumentSw = a07DocumentSwRepository.findFirstByBpmInstanceId(myWork.getBizId());
        if (Objects.nonNull(a07DocumentSw)) {
            myWorkDto.setTitle(a07DocumentSw.getBt());
            myWorkDto.setInstruction(a07DocumentSw.getYjLdqp());
            myWorkDto.setHj(a07DocumentSw.getHj());

            List<HistoricTaskInstance> historicTaskInstanceList = historyService.createHistoricTaskInstanceQuery().processInstanceId(myWork.getBizId()).taskAssignee(SecurityUtils.getCurrentUsername()).orderByHistoricTaskInstanceStartTime().desc().list();
            if (!CollectionUtils.isEmpty(historicTaskInstanceList)) {
                HistoricTaskInstance historicTaskInstance = historicTaskInstanceList.get(0);
                myWorkDto.setCreateTime(historicTaskInstance.getCreateTime());
                if (Objects.isNull(historicTaskInstance.getEndTime())) {
                    myWorkDto.setStatus(MyWorkStatusEnum.BLZ);
                } else {
                    myWorkDto.setStatus(MyWorkStatusEnum.YBL);
                }
            }
        }

    }

    @Override
    public String handleSubjectRule(JSONObject formDataObj, String subjectRule) {
        return null;
    }

    @Override
    public Object handleFormRecord(String procInstanceId, String taskDefKey, JSONObject bpmFormData) {
        // 如果表单数据有值且不需要处理，直接返回
        if (Objects.nonNull(bpmFormData)) {
            return bpmFormData;
        }
        A07DocumentSw document = a07DocumentSwRepository.findFirstByBpmInstanceId(procInstanceId);
        if (Objects.isNull(document)) {
            document = new A07DocumentSw();
            //收文设置默认值
            Date date = new Date();
            document.setHj("普通");
            document.setSwrq(date);//默认收文日期为当天
            document.setSwbh(new ReferenceNumber());
            document.getSwbh().setNh(LocalDate.now().getYear());//当前年份
        }

        return JSONObject.toJSON(document);
    }

    @Override
    public void deleteFormRecord(String instanceId) {
        a07DocumentSwRepository.deleteByBpmInstanceId(instanceId);
    }
}
