package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.service.Z08AuthorityScopeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2022/10/26 17:56
 * @description:
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "权限范围")
@RequestMapping("/api/aigov/authority")
@CrossOrigin
public class Z08AuthorityScopeController {
    private final Z08AuthorityScopeService authorityService;

    @ApiOperation("获取权限范围标识")
    @GetMapping(value = "/getAuthorityScope")
    public ResponseEntity<String> getAuthorityScope(@RequestParam("type") String type, @RequestParam("id") String id) {
        return new ResponseEntity<>(authorityService.getAuthorityScope(type, id), HttpStatus.OK);
    }
}
