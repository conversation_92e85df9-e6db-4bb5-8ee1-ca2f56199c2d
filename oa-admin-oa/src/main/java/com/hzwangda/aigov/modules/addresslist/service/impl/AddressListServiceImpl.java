package com.hzwangda.aigov.modules.addresslist.service.impl;

import com.hzwangda.aigov.modules.addresslist.domain.dto.AddressListDto;
import com.hzwangda.aigov.modules.addresslist.service.AddressListService;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.extension.bo.CommonControlsBO;
import com.wangda.oa.modules.extension.bo.UserListBO;
import com.wangda.oa.modules.extension.domain.SysDeptUserPosition;
import com.wangda.oa.modules.extension.dto.UserDataDto;
import com.wangda.oa.modules.extension.dto.org.UserListDto;
import com.wangda.oa.modules.extension.enums.PositionEnum;
import com.wangda.oa.modules.extension.mapper.WdSysOptionCustomMapper;
import com.wangda.oa.modules.extension.mapper.WdSysOptionMapper;
import com.wangda.oa.modules.extension.repository.DeptUserPositionRepository;
import com.wangda.oa.modules.system.domain.Dept;
import com.wangda.oa.modules.system.repository.DeptRepository;
import com.wangda.oa.modules.system.repository.DeptRepositoryCustom;
import com.wangda.oa.modules.system.service.DeptService;
import com.wangda.oa.modules.system.utils.SystemConstant;
import com.wangda.oa.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @author: zhangzhanlong
 * @date: 2022/8/5 13:23
 * @description: 通讯录
 */
@Service
@RequiredArgsConstructor
public class AddressListServiceImpl implements AddressListService {

    private final DeptUserPositionRepository deptUserPositionRepository;

    private final DeptRepository deptRepository;
    
    private final DeptRepositoryCustom deptRepositoryCustom;

    private final WdSysOptionMapper wdSysOptionMapper;

    private final WdSysOptionCustomMapper wdSysOptionCustomMapper;

    private final DeptService deptService;

    @Override
    public ResultJson<Object> getOrgList(CommonControlsBO bo) {
        if (bo.getType() == SystemConstant.COMMON_CONTROLS_TYPE_OGR) {
            //查询所有正常组织
            List<Dept> deptList;
            Dept deptId = deptRepositoryCustom.findByDeptCode(SecurityUtils.getDeptCode());
            bo.setDeptIds(Arrays.asList(deptId.getId()));
            if (bo.getDeptIds() == null || bo.getDeptIds().size() < 1) {
                deptList = deptRepository.findByEnabled(true);
            } else {
                deptList = deptService.getListByDeptIdAndChildren(bo.getDeptIds());
            }
            //查询所有人员(写新方法为了不查询岗位、角色、部门浪费资源时间)
            List<UserDataDto> userInfoDtoList = wdSysOptionMapper.getUserListByEnabled();
            //查询所有负责人
            List<SysDeptUserPosition> positions = deptUserPositionRepository.findByTypeOrderBySortAsc(PositionEnum.HEAD.getValue());
            return ResultJson.generateResult(build(deptList, deptList.get(0).getPid(), userInfoDtoList, positions));
        }
        return ResultJson.generateResult();
    }

    @Override
    public ResultJson<Object> getOrgUserList(CommonControlsBO bo) {

        List<Dept>  deptList = deptService.getListByDeptIdAndChildren(bo.getDeptIds());
        List<Long> collect = deptList.stream().map(Dept::getId).collect(Collectors.toList());
        List<UserDataDto> userListByDeptId = wdSysOptionCustomMapper.getUserListByDeptIds(collect);
       List<AddressListDto> addressListDtos=new ArrayList<>();
        for (UserDataDto d: userListByDeptId) {
            AddressListDto listDto = new AddressListDto();
            listDto.setId(d.getUserId());
            listDto.setNickName(d.getNickName());
            listDto.setUserName(d.getUsername());
            listDto.setType(0);
            listDto.setSort(d.getSort());
            listDto.setPhone(d.getPhone());
            addressListDtos.add(listDto);
        }
        return ResultJson.generateResult(addressListDtos);
    }


    /**
     * 递归查子部门
     *
     * @param deptList
     * @param parentId
     * @param userInfoDtoList
     * @return
     */
    private List<AddressListDto> build(List<Dept> deptList, Long parentId, List<UserDataDto> userInfoDtoList, List<SysDeptUserPosition> positions) {
        List<AddressListDto> list = new ArrayList<>();
        for (Dept dept : deptList) {
            //传入的pid为空则查询pid为空符合,传入的pid不为空则需要pid不为空且相同符合
            Boolean flag = (parentId == null && dept.getPid() == null) ||
                    (parentId != null && dept.getPid() != null && dept.getPid().longValue() == parentId.longValue());
            if (flag) {
                AddressListDto orgListDto = new AddressListDto();
                orgListDto.setId(dept.getId());
                orgListDto.setDeptName(dept.getName());
                orgListDto.setNickName(dept.getName());
                orgListDto.setSort(dept.getDeptSort());
                orgListDto.setType(1);
                orgListDto.setExtId(dept.getId().toString());
                List<AddressListDto> child = orgListDto.getChildren();
                if (child != null && child.size() > 0) {
                    child.sort(Comparator.comparing(AddressListDto::getSort, Comparator.nullsLast(Integer::compareTo)));
                }
                List<AddressListDto> childList = build(deptList, dept.getId(), userInfoDtoList, positions);
                if (childList != null && childList.size() > 0) {
                    if (orgListDto.getChildren() == null) {
                        orgListDto.setChildren(new ArrayList<>());
                    }
                    //子部门加入children
                    orgListDto.getChildren().addAll(childList);
                }
                list.add(orgListDto);
            }
        }
        list.sort(Comparator.comparing(AddressListDto::getSort, Comparator.nullsLast(Integer::compareTo)));
        return list;
    }

    @Override
    public ResultJson<List<UserListDto>> getUserList(UserListBO bo) {
        Dept deptId = deptRepositoryCustom.findByDeptCode(SecurityUtils.getDeptCode());
        List<Dept> deptList = deptService.getListByDeptIdAndChildren(Arrays.asList(deptId.getId()));
        List<Long> collect = deptList.stream().map(Dept::getId).collect(Collectors.toList());
        List<UserDataDto> userListByDeptId = wdSysOptionCustomMapper.getUserListByDeptId(collect,bo.getName());
        List<AddressListDto> list=new ArrayList<>();
        for (UserDataDto d:userListByDeptId) {
            AddressListDto listDto = new AddressListDto();
            listDto.setId(d.getUserId());
            listDto.setNickName(d.getNickName());
            listDto.setUserName(d.getUsername());
            listDto.setType(0);
            listDto.setSort(d.getSort());
            listDto.setPhone(d.getPhone());
            list.add(listDto);
        }
        return ResultJson.generateResult(list);
    }

}
