package com.hzwangda.aigov.modules.appcenter.service;

import com.hzwangda.aigov.modules.appcenter.domain.criteria.Z08AppCenterCriteria;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwQueryCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Map;

public interface Z08AppCenterService {
    Object getAppConterList(Z08AppCenterCriteria criteria);

    void saveAppConter(Long appId);

    Map<String, Object> getXzList(A07DocumentGwQueryCriteria criteria, Pageable pageable);

    Page readList(A07DocumentGwQueryCriteria criteria, Pageable pageable);

    Object readAll();
}
