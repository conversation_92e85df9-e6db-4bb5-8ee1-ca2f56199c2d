package com.hzwangda.aigov.modules.document.service;

import com.wangda.boot.platform.base.ResultJson;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.servlet.http.HttpServletRequest;

public interface A07TzbLdpsService {

    ResultJson<Object> receive(String token, String dataJson, HttpServletRequest request);

    ResultJson<Object>  query(String token, String dataJson, HttpServletRequest request);
}
