package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.Z08DocumentSwTypeDto;
import com.hzwangda.aigov.modules.document.entity.Z08DocumentSwType;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class Z08DocumentSwTypeMapperImpl implements Z08DocumentSwTypeMapper {

    @Override
    public List<Z08DocumentSwTypeDto> toDto(List<Z08DocumentSwType> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<Z08DocumentSwTypeDto> list = new ArrayList<Z08DocumentSwTypeDto>( entityList.size() );
        for ( Z08DocumentSwType z08DocumentSwType : entityList ) {
            list.add( toDto( z08DocumentSwType ) );
        }

        return list;
    }

    @Override
    public Z08DocumentSwType toEntity(Z08DocumentSwTypeDto dto) {
        if ( dto == null ) {
            return null;
        }

        Z08DocumentSwType z08DocumentSwType = new Z08DocumentSwType();

        z08DocumentSwType.setBelongToDept( dto.getBelongToDept() );
        z08DocumentSwType.setBpmInstanceId( dto.getBpmInstanceId() );
        z08DocumentSwType.setBpmProcessKey( dto.getBpmProcessKey() );
        z08DocumentSwType.setBpmStatus( dto.getBpmStatus() );
        z08DocumentSwType.setBpmSubject( dto.getBpmSubject() );
        z08DocumentSwType.setCreateBy( dto.getCreateBy() );
        z08DocumentSwType.setCreateTime( dto.getCreateTime() );
        z08DocumentSwType.setId( dto.getId() );
        List<String> list = dto.getParticipateUser();
        if ( list != null ) {
            z08DocumentSwType.setParticipateUser( new ArrayList<String>( list ) );
        }
        z08DocumentSwType.setUpdateBy( dto.getUpdateBy() );
        z08DocumentSwType.setUpdateTime( dto.getUpdateTime() );
        z08DocumentSwType.setVersion( dto.getVersion() );
        z08DocumentSwType.setDeptId( dto.getDeptId() );
        z08DocumentSwType.setDeptName( dto.getDeptName() );
        z08DocumentSwType.setDz( dto.getDz() );
        z08DocumentSwType.setSort( dto.getSort() );

        return z08DocumentSwType;
    }

    @Override
    public List<Z08DocumentSwType> toEntity(List<Z08DocumentSwTypeDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Z08DocumentSwType> list = new ArrayList<Z08DocumentSwType>( dtoList.size() );
        for ( Z08DocumentSwTypeDto z08DocumentSwTypeDto : dtoList ) {
            list.add( toEntity( z08DocumentSwTypeDto ) );
        }

        return list;
    }

    @Override
    public Z08DocumentSwTypeDto toDto(Z08DocumentSwType entity) {
        if ( entity == null ) {
            return null;
        }

        Z08DocumentSwTypeDto z08DocumentSwTypeDto = new Z08DocumentSwTypeDto();

        z08DocumentSwTypeDto.setBelongToDept( entity.getBelongToDept() );
        z08DocumentSwTypeDto.setBpmInstanceId( entity.getBpmInstanceId() );
        z08DocumentSwTypeDto.setBpmProcessKey( entity.getBpmProcessKey() );
        z08DocumentSwTypeDto.setBpmStatus( entity.getBpmStatus() );
        z08DocumentSwTypeDto.setBpmSubject( entity.getBpmSubject() );
        z08DocumentSwTypeDto.setCreateBy( entity.getCreateBy() );
        z08DocumentSwTypeDto.setCreateTime( entity.getCreateTime() );
        z08DocumentSwTypeDto.setId( entity.getId() );
        List<String> list = entity.getParticipateUser();
        if ( list != null ) {
            z08DocumentSwTypeDto.setParticipateUser( new ArrayList<String>( list ) );
        }
        z08DocumentSwTypeDto.setUpdateBy( entity.getUpdateBy() );
        z08DocumentSwTypeDto.setUpdateTime( entity.getUpdateTime() );
        z08DocumentSwTypeDto.setVersion( entity.getVersion() );
        z08DocumentSwTypeDto.setDeptId( entity.getDeptId() );
        z08DocumentSwTypeDto.setDeptName( entity.getDeptName() );
        z08DocumentSwTypeDto.setDz( entity.getDz() );
        z08DocumentSwTypeDto.setSort( entity.getSort() );

        return z08DocumentSwTypeDto;
    }
}
