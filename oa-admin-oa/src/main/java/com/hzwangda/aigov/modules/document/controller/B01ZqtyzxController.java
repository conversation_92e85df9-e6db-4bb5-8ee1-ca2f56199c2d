package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.dto.B01ZqtyzxQueryCriteria;
import com.hzwangda.aigov.modules.document.service.B01ZqtyzxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "知情同意执行")
@RequestMapping("/api/zqtyzx")
@CrossOrigin
public class B01ZqtyzxController {

    private final B01ZqtyzxService zqtyzxService;
    @ApiOperation("全部知情同意执行-根据单位来查询")
    @GetMapping(value = "/getAllZqtyzxList")
    public ResponseEntity<Object> getAllFwList(B01ZqtyzxQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(zqtyzxService.getAllZqtyzxList(criteria, pageable), HttpStatus.OK);
    }
    @ApiOperation("查询病区")
    @GetMapping(value = "/queryAllBq")
    public ResponseEntity<Object> queryAllBq() {
        return new ResponseEntity<>(zqtyzxService.queryAllBq(), HttpStatus.OK);
    }

    @ApiOperation("查询科组")
    @GetMapping(value = "/queryAllKz")
    public ResponseEntity<Object> queryAllKz() {
        return new ResponseEntity<>(zqtyzxService.queryAllKz(), HttpStatus.OK);
    }

    @ApiOperation("查询负责人")
    @GetMapping(value = "/queryAllFzr")
    public ResponseEntity<Object> queryAllFzr() {
        return new ResponseEntity<>(zqtyzxService.queryAllFzr(), HttpStatus.OK);
    }
    /**
     * 导出
     */
    @ApiOperation("导出")
    @GetMapping(value="/exportZqtyzx")
    public void exportZqtyzx(HttpServletResponse response, B01ZqtyzxQueryCriteria queryCriteria) {
        zqtyzxService.exportZqtyzx(response,queryCriteria);
    }
}
