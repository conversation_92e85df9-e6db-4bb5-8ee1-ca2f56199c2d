package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07HhInfoListAllDto;
import com.hzwangda.aigov.modules.document.entity.A07MeetingSea;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A07HhInfoAllMapperImpl implements A07HhInfoAllMapper {

    @Override
    public A07HhInfoListAllDto toDto(A07MeetingSea entity) {
        if ( entity == null ) {
            return null;
        }

        A07HhInfoListAllDto a07HhInfoListAllDto = new A07HhInfoListAllDto();

        a07HhInfoListAllDto.setId( entity.getId() );
        a07HhInfoListAllDto.setIsPost( entity.getIsPost() );
        a07HhInfoListAllDto.setMainOrg2( entity.getMainOrg2() );
        a07HhInfoListAllDto.setSendStatus( entity.getSendStatus() );
        a07HhInfoListAllDto.setStartDate( entity.getStartDate() );
        a07HhInfoListAllDto.setStatus( entity.getStatus() );
        a07HhInfoListAllDto.setSubject( entity.getSubject() );
        a07HhInfoListAllDto.setUrgentLevel( entity.getUrgentLevel() );
        a07HhInfoListAllDto.setWfType( entity.getWfType() );

        return a07HhInfoListAllDto;
    }

    @Override
    public List<A07HhInfoListAllDto> toDto(List<A07MeetingSea> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A07HhInfoListAllDto> list = new ArrayList<A07HhInfoListAllDto>( entityList.size() );
        for ( A07MeetingSea a07MeetingSea : entityList ) {
            list.add( toDto( a07MeetingSea ) );
        }

        return list;
    }

    @Override
    public A07MeetingSea toEntity(A07HhInfoListAllDto dto) {
        if ( dto == null ) {
            return null;
        }

        A07MeetingSea a07MeetingSea = new A07MeetingSea();

        a07MeetingSea.setId( dto.getId() );
        a07MeetingSea.setIsPost( dto.getIsPost() );
        a07MeetingSea.setMainOrg2( dto.getMainOrg2() );
        a07MeetingSea.setSendStatus( dto.getSendStatus() );
        if ( dto.getStartDate() != null ) {
            a07MeetingSea.setStartDate( new Timestamp( dto.getStartDate().getTime() ) );
        }
        a07MeetingSea.setStatus( dto.getStatus() );
        a07MeetingSea.setSubject( dto.getSubject() );
        a07MeetingSea.setUrgentLevel( dto.getUrgentLevel() );
        a07MeetingSea.setWfType( dto.getWfType() );

        return a07MeetingSea;
    }

    @Override
    public List<A07MeetingSea> toEntity(List<A07HhInfoListAllDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A07MeetingSea> list = new ArrayList<A07MeetingSea>( dtoList.size() );
        for ( A07HhInfoListAllDto a07HhInfoListAllDto : dtoList ) {
            list.add( toEntity( a07HhInfoListAllDto ) );
        }

        return list;
    }
}
