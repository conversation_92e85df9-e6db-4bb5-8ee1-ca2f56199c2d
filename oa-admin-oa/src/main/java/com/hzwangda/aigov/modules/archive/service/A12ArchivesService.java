package com.hzwangda.aigov.modules.archive.service;


import com.hzwangda.aigov.modules.archive.dto.A12ArchivesAddListDto;
import com.hzwangda.aigov.modules.archive.dto.A12ArchivesCriteria;
import com.hzwangda.aigov.modules.archive.entity.A12Archives;
import org.springframework.data.domain.Pageable;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/9/7 下午4:36
 **/
public interface A12ArchivesService {

    /**
     * 新增或修改文件档案接口
     *
     * @param resources
     * @return
     */
    Boolean createOrUpdate(A12ArchivesAddListDto resources);

    /**
     * 根据目录id和存储id查找
     *
     * @param directoryId
     * @param storageId
     * @return
     */
    A12Archives findByDirectoryIdAndStorageId(Long directoryId, Long storageId);

    /**
     * 删除文件档案接口
     *
     * @param ids
     * @return
     */
    Boolean delete(Long[] ids);

    /**
     * 查询文件档案列表接口
     *
     * @param criteria
     * @param pageable
     * @return
     */
    Map query(A12ArchivesCriteria criteria, Pageable pageable);
}
