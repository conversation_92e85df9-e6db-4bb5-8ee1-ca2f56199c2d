package com.hzwangda.aigov.modules.GoodsReceive.controller;

import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.GoodsReceive.dto.GoodsCriteria;
import com.hzwangda.aigov.modules.GoodsReceive.dto.QueryCriteria;
import com.hzwangda.aigov.modules.GoodsReceive.service.GoodsReceiveService;
import com.hzwangda.aigov.modules.GoodsReceive.service.GoodsReturnService;
import com.hzwangda.aigov.modules.favorite.controller.FavoriteController;
import com.wangda.oa.annotation.rest.AnonymousGetMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;

@RestController
@RequestMapping("/GoodsReceive")
@Api(tags = "物品领用")
public class GoodsReceiveController {

    @Resource
    public GoodsReceiveService goodsReceiveService;
    @Resource
    public GoodsReturnService goodsReturnService;

    Logger logger = LoggerFactory.getLogger(GoodsReceiveController.class);

    @ApiOperation(value = "浙政钉组件物品领用归还列表")
    @AnonymousGetMapping("/zcgl/getList")
    public ResponseEntity queryReceiveAndReturnList(@RequestParam(required = false) String accountid,GoodsCriteria queryCriteria, Pageable pageable) {
        logger.info("浙政钉组件物品领用归还列表,当前访问人的accountid：{}",accountid);
        HashMap map=new HashMap();
        map.put("success",true);
        JSONObject content=new JSONObject();
        JSONObject cardData=new JSONObject();
        PageImpl receiveMap = (PageImpl)goodsReceiveService.goodsPageList(queryCriteria, pageable);
        PageImpl returnMap = (PageImpl)goodsReturnService.goodsReturnPageList(queryCriteria, pageable);
        cardData.put("attentionList", receiveMap.getContent());
        cardData.put("collectList",returnMap.getContent());
        cardData.put("moreUrl","https://ding.nanxun.gov.cn:806/newdd/#/goodsUse");
        content.put("cardData",cardData);
        map.put("content",content);
        return ResponseEntity.ok(map);
    }


    @ApiOperation(value = "列表")
    @GetMapping("/queryList")
    public ResponseEntity queryList(QueryCriteria queryCriteria, Pageable pageable) {
        return ResponseEntity.ok(goodsReceiveService.queryList(queryCriteria, pageable));
    }


    @ApiOperation(value = "物品领用列表")
    @GetMapping("/goodsPageList")
    public ResponseEntity goodsPageList(GoodsCriteria queryCriteria, Pageable pageable) {
        return ResponseEntity.ok(goodsReceiveService.goodsPageList(queryCriteria, pageable));
    }

    @ApiOperation(value = "查询统计使用列表")
    @GetMapping("/queryCount")
    public ResponseEntity queryCount(GoodsCriteria queryCriteria, Pageable pageable) {
        return ResponseEntity.ok(goodsReceiveService.queryCount(queryCriteria, pageable));
    }


    @ApiOperation(value = "单个物品领用详情")
    @GetMapping("/getByMcxh")
    public ResponseEntity getByMcxh(@RequestParam String mcxh) {
        return ResponseEntity.ok(goodsReceiveService.getByMcxh(mcxh));
    }

}
