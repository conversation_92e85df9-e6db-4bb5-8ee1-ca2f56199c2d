package com.hzwangda.aigov.modules.document.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlzUser;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * 收文单
 *
 * <AUTHOR>
 * @date 2021/6/21下午8:29
 */
@Data
public class A07DocumentGwlzDto extends BaseDomain {

    private Long id;

    @ApiModelProperty(value = "公文状态(草稿,正式)")
    private String gwzt;

    @ApiModelProperty(value = "公文种类(公文,会议)")
    private String gwzl;

    @ApiModelProperty(value = "是否公开(主动公开,依申请公开,不予公开)")
    private String sfgk;

    @ApiModelProperty(value = "文号")
    private ReferenceNumber gwwh;


    @ApiModelProperty(value = "缓急(特急,加急,平急)")
    private String hj;

    @ApiModelProperty(value = "签发人")
    private String yjQfr;

    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "正文")
    private StorageBizDto zw;

    @ApiModelProperty(value = "主送单位")
    private List<A07DocumentGwlzUser> zsdwDepts;

    @ApiModelProperty(value = "主送单位描述")
    private String zsdwms;

    @ApiModelProperty(value = "抄送单位")
    private List<A07DocumentGwlzUser> csdwDepts;

    @ApiModelProperty(value = "抄送单位描述")
    private String csdwms;

    @ApiModelProperty(value = "发文单位")
    private String fwdw;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "发文日期")
    private Timestamp fwrq;

    @ApiModelProperty(value = "附件")
    private List<StorageBizDto> fj;

    @ApiModelProperty(value = "数据采集")
    private List<StorageBizDto> sjcj;

    @ApiModelProperty(value = "附加说明")
    private String fjsm;

    @ApiModelProperty(value = "发文联系人")
    private String fwlxr;

    @ApiModelProperty(value = "联系方式")
    private String lxfs;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "操作日期")
    private Timestamp czrq;

    @ApiModelProperty(value = "短信通知")
    private String dxtz;

    @ApiModelProperty(value = "套红模版")
    private String thmb;

    @ApiModelProperty(value = "收文单位")
    private String swdw;

    @ApiModelProperty(value = "子部门OA添加用户")
    private String zbmtjyh;

    @ApiModelProperty(value = "是否来源自子部门OA")
    private String ly;

    @ApiModelProperty(value = "反馈单类别")
    private String fkdlb;

    @ApiModelProperty(value = "子部门OA添加用户真实名")
    private String oaname;

    @ApiModelProperty(value = "来源标识")
    private String lybs;

    @ApiModelProperty(value = "来源系统代码")
    private String lyxtdm;

    @ApiModelProperty(value = "完成签收(false:未完成,true:已完成)")
    private Boolean completeQs;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "签收时间")
    private Timestamp qsrq;

    @ApiModelProperty(value = "备注")
    private String bz;
}
