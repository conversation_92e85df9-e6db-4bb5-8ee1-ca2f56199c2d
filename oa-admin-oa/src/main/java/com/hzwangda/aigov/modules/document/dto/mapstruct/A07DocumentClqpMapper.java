/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07DocumentClqpDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentClqp;
import com.wangda.oa.base.BaseMapper;
import org.mapstruct.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-06-09
 **/
@Mapper(componentModel = "spring", uses = A07DocumentMapperUtil.class, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface A07DocumentClqpMapper extends BaseMapper<A07DocumentClqpDto, A07DocumentClqp> {

    @AfterMapping
    default void splitTime(A07DocumentClqp entity, @MappingTarget A07DocumentClqpDto dto) {
    }

    @Override
    @Mappings({
            @Mapping(target = "fj", expression = "java(a07DocumentMapperUtil.toFj(entity.getFj()))"),
    })
    A07DocumentClqpDto toDto(A07DocumentClqp entity);
}
