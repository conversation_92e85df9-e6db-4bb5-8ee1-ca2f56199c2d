package com.hzwangda.aigov.modules.document.entity;

import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 审批
 *
 * <AUTHOR>
 * @date 2021/7/15 上午11:18
 */
@Data
@Entity
@Table(name = "a07_document_xcjtdjwp_sp")
public class A07DocumentXcjtdjwpSp extends BaseBpmDomain implements Serializable {

    @ApiModelProperty(value = "申请部门")
    private String sqBm;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @ApiModelProperty(value = "购买名称及数量")
    private String nameAndNum;

    @ApiModelProperty(value = "申请理由")
    private String reason;

    @ApiModelProperty(value = "部门负责人意见")
    @Lob
    private String yjfzr;

    @ApiModelProperty(value = "综合部意见")
    @Lob
    private String yjzhb;

    @ApiModelProperty(value = "分管领导意见")
    @Lob
    private String yjfgld;

    @ApiModelProperty(value = "主要领导意见")
    @Lob
    private String yjLdqp;

    @PrePersist
    @PreUpdate
    public void preCreateEntity() {
        super.preCreateEntity();
    }
}
