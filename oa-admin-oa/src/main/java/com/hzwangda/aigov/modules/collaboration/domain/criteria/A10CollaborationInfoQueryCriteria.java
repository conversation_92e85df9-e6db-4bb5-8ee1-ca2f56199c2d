package com.hzwangda.aigov.modules.collaboration.domain.criteria;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @program: oa-mgt-server
 * @description: 工作协同查询
 * @author: liux
 * @create: 2021-08-12 15:15
 */
@Data
public class A10CollaborationInfoQueryCriteria {

    /**
     * 主题  模糊查询
     */
    @Query(type = Query.Type.INNER_LIKE)
    private String subject;

    /**
     * 状态
     */
    @Query(type = Query.Type.EQUAL)
    private String status;

    /**
     * 接收/发送时间
     */
    @Query(type = Query.Type.BETWEEN)

    private List<String> sendTime;

    /**
     * 是否我创建的
     */
    @Query(type = Query.Type.EQUAL)
    private String createBy;

    /**
     * 个人待办还是单位待办
     */
    @ApiModelProperty(value = "查询的用户类型 1.个人待办  2.单位待办")
//    @Query(type = Query.Type.EQUAL)
    private Long userType;
}
