package com.hzwangda.aigov.modules.collaboration.enums;

import javax.persistence.AttributeConverter;
import java.util.Objects;

public enum CollaborationAssigneeStatusEnum implements AttributeConverter<CollaborationAssigneeStatusEnum, String> {
    NEW("new", "未读"),
    UNSIGN("unsign", "待签收"),
    SIGNED("signed", "已签收");

    private String value;
    private String name;

    CollaborationAssigneeStatusEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static CollaborationAssigneeStatusEnum fromValue(String value) {
        for (CollaborationAssigneeStatusEnum object : CollaborationAssigneeStatusEnum.values()) {
            if (Objects.equals(value, object.getValue())) {
                return object;
            }
        }
        throw new IllegalArgumentException("No enum value " + value + " of " + CollaborationAssigneeStatusEnum.class.getCanonicalName());
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    @Override
    public String convertToDatabaseColumn(CollaborationAssigneeStatusEnum collaborationAssigneeStatusEnum) {
        return collaborationAssigneeStatusEnum.getValue();
    }

    @Override
    public CollaborationAssigneeStatusEnum convertToEntityAttribute(String s) {
        return CollaborationAssigneeStatusEnum.fromValue(s);
    }
}
