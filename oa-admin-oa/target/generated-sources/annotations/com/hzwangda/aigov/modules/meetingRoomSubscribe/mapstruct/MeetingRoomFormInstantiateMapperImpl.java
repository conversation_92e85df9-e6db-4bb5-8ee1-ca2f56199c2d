package com.hzwangda.aigov.modules.meetingRoomSubscribe.mapstruct;

import com.hzwangda.aigov.modules.meetingRoomSubscribe.domain.MeetingRoomFormInstantiate;
import com.hzwangda.aigov.modules.meetingRoomSubscribe.dto.MeetingRoomFormInstantiateDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class MeetingRoomFormInstantiateMapperImpl implements MeetingRoomFormInstantiateMapper {

    @Override
    public MeetingRoomFormInstantiateDto toDto(MeetingRoomFormInstantiate entity) {
        if ( entity == null ) {
            return null;
        }

        MeetingRoomFormInstantiateDto meetingRoomFormInstantiateDto = new MeetingRoomFormInstantiateDto();

        meetingRoomFormInstantiateDto.setBizType( entity.getBizType() );
        meetingRoomFormInstantiateDto.setEndTime( entity.getEndTime() );
        meetingRoomFormInstantiateDto.setId( entity.getId() );
        meetingRoomFormInstantiateDto.setStartTime( entity.getStartTime() );
        meetingRoomFormInstantiateDto.setTitle( entity.getTitle() );

        return meetingRoomFormInstantiateDto;
    }

    @Override
    public List<MeetingRoomFormInstantiateDto> toDto(List<MeetingRoomFormInstantiate> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MeetingRoomFormInstantiateDto> list = new ArrayList<MeetingRoomFormInstantiateDto>( entityList.size() );
        for ( MeetingRoomFormInstantiate meetingRoomFormInstantiate : entityList ) {
            list.add( toDto( meetingRoomFormInstantiate ) );
        }

        return list;
    }

    @Override
    public MeetingRoomFormInstantiate toEntity(MeetingRoomFormInstantiateDto dto) {
        if ( dto == null ) {
            return null;
        }

        MeetingRoomFormInstantiate meetingRoomFormInstantiate = new MeetingRoomFormInstantiate();

        meetingRoomFormInstantiate.setBizType( dto.getBizType() );
        meetingRoomFormInstantiate.setEndTime( dto.getEndTime() );
        meetingRoomFormInstantiate.setId( dto.getId() );
        meetingRoomFormInstantiate.setStartTime( dto.getStartTime() );
        meetingRoomFormInstantiate.setTitle( dto.getTitle() );

        return meetingRoomFormInstantiate;
    }

    @Override
    public List<MeetingRoomFormInstantiate> toEntity(List<MeetingRoomFormInstantiateDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MeetingRoomFormInstantiate> list = new ArrayList<MeetingRoomFormInstantiate>( dtoList.size() );
        for ( MeetingRoomFormInstantiateDto meetingRoomFormInstantiateDto : dtoList ) {
            list.add( toEntity( meetingRoomFormInstantiateDto ) );
        }

        return list;
    }
}
