package com.hzwangda.aigov.modules.archive.mapstruct;

import com.hzwangda.aigov.modules.archive.dto.A12DirectoryDto;
import com.hzwangda.aigov.modules.archive.entity.A12Directory;
import com.wangda.oa.base.BaseMapper;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @Date 2021/9/8
 **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface A12DirectoryMapper extends BaseMapper<A12DirectoryDto, A12Directory> {

    @AfterMapping
    default void splitTime(A12Directory entity, @MappingTarget A12DirectoryDto dto) {
    }
}
