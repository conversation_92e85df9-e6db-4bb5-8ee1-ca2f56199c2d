package com.hzwangda.aigov.modules.document.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel
@Data
public class Z08DocumentTypeQueryCriteria {

    @ApiModelProperty(value = "搜索内容")
    private String searchKeys;

    @Query(type = Query.Type.EQUAL)
    @ApiModelProperty(value = "部门id")
    private String deptId;


}
