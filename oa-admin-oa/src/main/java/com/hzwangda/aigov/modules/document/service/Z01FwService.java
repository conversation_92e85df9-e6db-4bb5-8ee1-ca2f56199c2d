package com.hzwangda.aigov.modules.document.service;

import com.hzwangda.aigov.modules.document.dto.A07DocumentGwQueryCriteria;
import org.springframework.data.domain.Pageable;

import java.util.Map;

public interface Z01FwService {

    /**
     * @param criteria:
     * @param pageable:
     * @description 发文分发列表
     * <AUTHOR>
     * @updateTime 2023/2/14 13:31
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     */

    Map<String, Object> getFwDistributeList(A07DocumentGwQueryCriteria criteria, Pageable pageable);


}
