package com.hzwangda.aigov.modules.archive.mapstruct;

import com.hzwangda.aigov.modules.archive.dto.A12DirectoryDto;
import com.hzwangda.aigov.modules.archive.entity.A12Directory;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A12DirectoryMapperImpl implements A12DirectoryMapper {

    @Override
    public A12DirectoryDto toDto(A12Directory entity) {
        if ( entity == null ) {
            return null;
        }

        A12DirectoryDto a12DirectoryDto = new A12DirectoryDto();

        a12DirectoryDto.setCreateBy( entity.getCreateBy() );
        a12DirectoryDto.setCreateTime( entity.getCreateTime() );
        a12DirectoryDto.setUpdateTime( entity.getUpdateTime() );
        a12DirectoryDto.setUpdatedBy( entity.getUpdatedBy() );
        a12DirectoryDto.setDeptId( entity.getDeptId() );
        a12DirectoryDto.setId( entity.getId() );
        a12DirectoryDto.setName( entity.getName() );
        a12DirectoryDto.setPid( entity.getPid() );
        a12DirectoryDto.setSort( entity.getSort() );

        splitTime( entity, a12DirectoryDto );

        return a12DirectoryDto;
    }

    @Override
    public List<A12DirectoryDto> toDto(List<A12Directory> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A12DirectoryDto> list = new ArrayList<A12DirectoryDto>( entityList.size() );
        for ( A12Directory a12Directory : entityList ) {
            list.add( toDto( a12Directory ) );
        }

        return list;
    }

    @Override
    public A12Directory toEntity(A12DirectoryDto dto) {
        if ( dto == null ) {
            return null;
        }

        A12Directory a12Directory = new A12Directory();

        a12Directory.setCreateBy( dto.getCreateBy() );
        a12Directory.setCreateTime( dto.getCreateTime() );
        a12Directory.setUpdateTime( dto.getUpdateTime() );
        a12Directory.setUpdatedBy( dto.getUpdatedBy() );
        a12Directory.setDeptId( dto.getDeptId() );
        a12Directory.setId( dto.getId() );
        a12Directory.setName( dto.getName() );
        a12Directory.setPid( dto.getPid() );
        a12Directory.setSort( dto.getSort() );

        return a12Directory;
    }

    @Override
    public List<A12Directory> toEntity(List<A12DirectoryDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A12Directory> list = new ArrayList<A12Directory>( dtoList.size() );
        for ( A12DirectoryDto a12DirectoryDto : dtoList ) {
            list.add( toEntity( a12DirectoryDto ) );
        }

        return list;
    }
}
