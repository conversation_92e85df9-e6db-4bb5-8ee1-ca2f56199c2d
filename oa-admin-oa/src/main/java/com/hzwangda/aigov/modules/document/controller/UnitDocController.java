package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.dto.UnitDocCriteria;
import com.hzwangda.aigov.modules.document.service.UnitDocService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: zhang<PERSON>hanlong
 * @date: 2022/12/20 14:56
 * @description: 单位转办单
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "单位转办单")
@RequestMapping("/api/aigov/untidoc")
public class UnitDocController {
    private final UnitDocService unitDocService;

    @ApiOperation("收文列表")
    @GetMapping(value = "/getUnitDocList")
    public ResponseEntity<Object> getUnitDocList(UnitDocCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(unitDocService.getUnitDocList(criteria, pageable), HttpStatus.OK);
    }


    @ApiOperation("转办单督办")
    @GetMapping("/transferDoc")
    public ResponseEntity<Page> transferDoc(UnitDocCriteria unitDocCriteria, Pageable pageable) {
        return ResponseEntity.ok(unitDocService.transferDoc(unitDocCriteria, pageable));
    }
}
