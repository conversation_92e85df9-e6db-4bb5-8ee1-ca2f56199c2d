package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07DocumentGwlzDto;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwlzListDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlz;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlzUser;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A07DocumentGwlzMapperImpl implements A07DocumentGwlzMapper {

    @Autowired
    private A07DocumentMapperUtil a07DocumentMapperUtil;

    @Override
    public List<A07DocumentGwlzDto> toDto(List<A07DocumentGwlz> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A07DocumentGwlzDto> list = new ArrayList<A07DocumentGwlzDto>( entityList.size() );
        for ( A07DocumentGwlz a07DocumentGwlz : entityList ) {
            list.add( toDto( a07DocumentGwlz ) );
        }

        return list;
    }

    @Override
    public A07DocumentGwlz toEntity(A07DocumentGwlzDto dto) {
        if ( dto == null ) {
            return null;
        }

        A07DocumentGwlz a07DocumentGwlz = new A07DocumentGwlz();

        a07DocumentGwlz.setCreateBy( dto.getCreateBy() );
        a07DocumentGwlz.setCreateDate( dto.getCreateDate() );
        a07DocumentGwlz.setCreatorId( dto.getCreatorId() );
        a07DocumentGwlz.setEnabled( dto.getEnabled() );
        a07DocumentGwlz.setId( dto.getId() );
        a07DocumentGwlz.setModifiedBy( dto.getModifiedBy() );
        a07DocumentGwlz.setModifiedDate( dto.getModifiedDate() );
        a07DocumentGwlz.setModifiedId( dto.getModifiedId() );
        a07DocumentGwlz.setVersion( dto.getVersion() );
        a07DocumentGwlz.setBt( dto.getBt() );
        a07DocumentGwlz.setBz( dto.getBz() );
        a07DocumentGwlz.setCsdwms( dto.getCsdwms() );
        a07DocumentGwlz.setCzrq( dto.getCzrq() );
        a07DocumentGwlz.setDxtz( dto.getDxtz() );
        a07DocumentGwlz.setFjsm( dto.getFjsm() );
        a07DocumentGwlz.setFkdlb( dto.getFkdlb() );
        a07DocumentGwlz.setFwdw( dto.getFwdw() );
        a07DocumentGwlz.setFwlxr( dto.getFwlxr() );
        a07DocumentGwlz.setFwrq( dto.getFwrq() );
        a07DocumentGwlz.setGwwh( dto.getGwwh() );
        a07DocumentGwlz.setGwzl( dto.getGwzl() );
        a07DocumentGwlz.setGwzt( dto.getGwzt() );
        a07DocumentGwlz.setHj( dto.getHj() );
        a07DocumentGwlz.setLxfs( dto.getLxfs() );
        a07DocumentGwlz.setLy( dto.getLy() );
        a07DocumentGwlz.setLybs( dto.getLybs() );
        a07DocumentGwlz.setLyxtdm( dto.getLyxtdm() );
        a07DocumentGwlz.setOaname( dto.getOaname() );
        a07DocumentGwlz.setSfgk( dto.getSfgk() );
        a07DocumentGwlz.setSwdw( dto.getSwdw() );
        a07DocumentGwlz.setThmb( dto.getThmb() );
        a07DocumentGwlz.setYjQfr( dto.getYjQfr() );
        a07DocumentGwlz.setZbmtjyh( dto.getZbmtjyh() );
        a07DocumentGwlz.setZsdwms( dto.getZsdwms() );
        a07DocumentGwlz.setZw( storageBizDtoToStorageBiz( dto.getZw() ) );
        List<A07DocumentGwlzUser> list = dto.getZsdwDepts();
        if ( list != null ) {
            a07DocumentGwlz.setZsdwDepts( new ArrayList<A07DocumentGwlzUser>( list ) );
        }
        List<A07DocumentGwlzUser> list1 = dto.getCsdwDepts();
        if ( list1 != null ) {
            a07DocumentGwlz.setCsdwDepts( new ArrayList<A07DocumentGwlzUser>( list1 ) );
        }
        a07DocumentGwlz.setFj( a07DocumentMapperUtil.toConvertToId( dto.getFj() ) );
        a07DocumentGwlz.setSjcj( a07DocumentMapperUtil.toConvertToId( dto.getSjcj() ) );

        return a07DocumentGwlz;
    }

    @Override
    public List<A07DocumentGwlz> toEntity(List<A07DocumentGwlzDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A07DocumentGwlz> list = new ArrayList<A07DocumentGwlz>( dtoList.size() );
        for ( A07DocumentGwlzDto a07DocumentGwlzDto : dtoList ) {
            list.add( toEntity( a07DocumentGwlzDto ) );
        }

        return list;
    }

    @Override
    public A07DocumentGwlzDto toDto(A07DocumentGwlz entity) {
        if ( entity == null ) {
            return null;
        }

        A07DocumentGwlzDto a07DocumentGwlzDto = new A07DocumentGwlzDto();

        a07DocumentGwlzDto.setCreateBy( entity.getCreateBy() );
        a07DocumentGwlzDto.setCreateDate( entity.getCreateDate() );
        a07DocumentGwlzDto.setCreatorId( entity.getCreatorId() );
        a07DocumentGwlzDto.setEnabled( entity.getEnabled() );
        a07DocumentGwlzDto.setModifiedBy( entity.getModifiedBy() );
        a07DocumentGwlzDto.setModifiedDate( entity.getModifiedDate() );
        a07DocumentGwlzDto.setModifiedId( entity.getModifiedId() );
        a07DocumentGwlzDto.setVersion( entity.getVersion() );
        a07DocumentGwlzDto.setBt( entity.getBt() );
        a07DocumentGwlzDto.setBz( entity.getBz() );
        List<A07DocumentGwlzUser> list = entity.getCsdwDepts();
        if ( list != null ) {
            a07DocumentGwlzDto.setCsdwDepts( new ArrayList<A07DocumentGwlzUser>( list ) );
        }
        a07DocumentGwlzDto.setCsdwms( entity.getCsdwms() );
        a07DocumentGwlzDto.setCzrq( entity.getCzrq() );
        a07DocumentGwlzDto.setDxtz( entity.getDxtz() );
        a07DocumentGwlzDto.setFjsm( entity.getFjsm() );
        a07DocumentGwlzDto.setFkdlb( entity.getFkdlb() );
        a07DocumentGwlzDto.setFwdw( entity.getFwdw() );
        a07DocumentGwlzDto.setFwlxr( entity.getFwlxr() );
        a07DocumentGwlzDto.setFwrq( entity.getFwrq() );
        a07DocumentGwlzDto.setGwwh( entity.getGwwh() );
        a07DocumentGwlzDto.setGwzl( entity.getGwzl() );
        a07DocumentGwlzDto.setGwzt( entity.getGwzt() );
        a07DocumentGwlzDto.setHj( entity.getHj() );
        a07DocumentGwlzDto.setId( entity.getId() );
        a07DocumentGwlzDto.setLxfs( entity.getLxfs() );
        a07DocumentGwlzDto.setLy( entity.getLy() );
        a07DocumentGwlzDto.setLybs( entity.getLybs() );
        a07DocumentGwlzDto.setLyxtdm( entity.getLyxtdm() );
        a07DocumentGwlzDto.setOaname( entity.getOaname() );
        a07DocumentGwlzDto.setSfgk( entity.getSfgk() );
        a07DocumentGwlzDto.setSwdw( entity.getSwdw() );
        a07DocumentGwlzDto.setThmb( entity.getThmb() );
        a07DocumentGwlzDto.setYjQfr( entity.getYjQfr() );
        a07DocumentGwlzDto.setZbmtjyh( entity.getZbmtjyh() );
        List<A07DocumentGwlzUser> list1 = entity.getZsdwDepts();
        if ( list1 != null ) {
            a07DocumentGwlzDto.setZsdwDepts( new ArrayList<A07DocumentGwlzUser>( list1 ) );
        }
        a07DocumentGwlzDto.setZsdwms( entity.getZsdwms() );

        a07DocumentGwlzDto.setFj( a07DocumentMapperUtil.toFj(entity.getFj()) );
        a07DocumentGwlzDto.setCompleteQs( a07DocumentMapperUtil.toComplete(entity.getId()) );
        a07DocumentGwlzDto.setSjcj( a07DocumentMapperUtil.toSjcj(entity.getSjcj()) );
        a07DocumentGwlzDto.setZw( a07DocumentMapperUtil.toZw(entity.getZw()) );

        splitTime( entity, a07DocumentGwlzDto );

        return a07DocumentGwlzDto;
    }

    @Override
    public A07DocumentGwlzListDto toListDto(A07DocumentGwlz entity) {
        if ( entity == null ) {
            return null;
        }

        A07DocumentGwlzListDto a07DocumentGwlzListDto = new A07DocumentGwlzListDto();

        a07DocumentGwlzListDto.setCreateBy( entity.getCreateBy() );
        a07DocumentGwlzListDto.setCreateDate( entity.getCreateDate() );
        a07DocumentGwlzListDto.setCreatorId( entity.getCreatorId() );
        a07DocumentGwlzListDto.setEnabled( entity.getEnabled() );
        a07DocumentGwlzListDto.setId( entity.getId() );
        a07DocumentGwlzListDto.setModifiedBy( entity.getModifiedBy() );
        a07DocumentGwlzListDto.setModifiedDate( entity.getModifiedDate() );
        a07DocumentGwlzListDto.setModifiedId( entity.getModifiedId() );
        a07DocumentGwlzListDto.setVersion( entity.getVersion() );
        a07DocumentGwlzListDto.setBt( entity.getBt() );
        a07DocumentGwlzListDto.setCsdwms( entity.getCsdwms() );
        a07DocumentGwlzListDto.setCzrq( entity.getCzrq() );
        a07DocumentGwlzListDto.setDxtz( entity.getDxtz() );
        a07DocumentGwlzListDto.setFjsm( entity.getFjsm() );
        a07DocumentGwlzListDto.setFkdlb( entity.getFkdlb() );
        a07DocumentGwlzListDto.setFwdw( entity.getFwdw() );
        a07DocumentGwlzListDto.setFwlxr( entity.getFwlxr() );
        a07DocumentGwlzListDto.setFwrq( entity.getFwrq() );
        a07DocumentGwlzListDto.setGwwh( entity.getGwwh() );
        a07DocumentGwlzListDto.setGwzl( entity.getGwzl() );
        a07DocumentGwlzListDto.setGwzt( entity.getGwzt() );
        a07DocumentGwlzListDto.setHj( entity.getHj() );
        a07DocumentGwlzListDto.setLxfs( entity.getLxfs() );
        a07DocumentGwlzListDto.setLy( entity.getLy() );
        a07DocumentGwlzListDto.setLybs( entity.getLybs() );
        a07DocumentGwlzListDto.setLyxtdm( entity.getLyxtdm() );
        a07DocumentGwlzListDto.setOaname( entity.getOaname() );
        a07DocumentGwlzListDto.setSfgk( entity.getSfgk() );
        a07DocumentGwlzListDto.setSwdw( entity.getSwdw() );
        a07DocumentGwlzListDto.setThmb( entity.getThmb() );
        a07DocumentGwlzListDto.setYjQfr( entity.getYjQfr() );
        a07DocumentGwlzListDto.setZbmtjyh( entity.getZbmtjyh() );
        a07DocumentGwlzListDto.setZsdwms( entity.getZsdwms() );

        a07DocumentGwlzListDto.setCompleteQs( a07DocumentMapperUtil.toComplete(entity.getId()) );

        return a07DocumentGwlzListDto;
    }

    protected StorageBiz storageBizDtoToStorageBiz(StorageBizDto storageBizDto) {
        if ( storageBizDto == null ) {
            return null;
        }

        StorageBiz storageBiz = new StorageBiz();

        storageBiz.setBizId( storageBizDto.getBizId() );
        storageBiz.setBizType( storageBizDto.getBizType() );
        storageBiz.setSorted( storageBizDto.getSorted() );

        return storageBiz;
    }
}
