package com.hzwangda.aigov.bpm.domain.entity;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Entity
@Getter
@Setter
@Table(name = "bpm_button")
@ApiModel(value = "流程操作按钮")
public class BpmButton extends BaseEntity {

    @Id
    @NotNull(groups = {Update.class})
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "操作标识")
    private String btnKey;

    @ApiModelProperty(value = "操作名称")
    private String btnName;

    @ApiModelProperty(value = "CSS样式")
    private String btnStyle;

    @ApiModelProperty(value = "排序")
    private Integer btnSort;

    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }

    public void copy(BpmButton source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }

}
