package com.hzwangda.aigov.modules.document.repository;

import com.hzwangda.aigov.modules.document.dto.AppAndPermissionDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentFwView;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface A07DocumentFwViewRepository extends JpaRepository<A07DocumentFwView, Long>, JpaSpecificationExecutor<A07DocumentFwView> {
    A07DocumentFwView findByBpmInstanceId(String instanceId);

    List<A07DocumentFwView> findByUpdateTimeGreaterThanEqual(Date lastModify);

    List<A07DocumentFwView> findByUpdateTimeBefore(Date lastModify);

    @Query("select new com.hzwangda.aigov.modules.document.dto.AppAndPermissionDto(ap.applicationId,ap.manager,ap.businessManager,ap.createManager,app.name) from AppPermission ap inner join Application app on ap.applicationId=app.id where app.type =:type")
    List<AppAndPermissionDto> getCreateByDz(String type);

    @Query("select distinct a.bpmProcessKey from A07DocumentFwView a")
    List<String> findDistinctBpmProcessKey();
}
