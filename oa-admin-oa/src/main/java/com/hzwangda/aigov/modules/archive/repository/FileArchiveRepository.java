package com.hzwangda.aigov.modules.archive.repository;


import com.hzwangda.aigov.modules.archive.entity.A07FileArchive;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface FileArchiveRepository extends JpaRepository<A07FileArchive, Long>, JpaSpecificationExecutor<A07FileArchive> {

    A07FileArchive findFirstByErpNumIsNotNullOrderByErpNumDesc();

    @Query("select max(no) from A07FileArchive where limitTime=?1 and year=?2")
    Integer findMaxNoByLimitTimeAndYear(String limitTime, String year);

    A07FileArchive findFirstByFileNumber(String fileNumber);

    A07FileArchive findByDocId(Long docId);

    void deleteAllByDocIdIn(Long[] ids);

    List<A07FileArchive> findAllByStatus(String status);
}