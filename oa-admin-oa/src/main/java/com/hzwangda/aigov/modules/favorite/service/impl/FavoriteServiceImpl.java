package com.hzwangda.aigov.modules.favorite.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.bpm.repository.A07DocumentGwlzRepository;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationAssignee;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationInfo;
import com.hzwangda.aigov.modules.collaboration.repository.A10CollaborationInfoRepository;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlz;
import com.hzwangda.aigov.modules.favorite.domain.Favorite;
import com.hzwangda.aigov.modules.favorite.repository.FavoriteRepository;
import com.hzwangda.aigov.modules.favorite.service.FavoriteService;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.extension.domain.SysUserPlatform;
import com.wangda.oa.modules.system.repository.SysUserPlatformRepositoryCustom;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.task.api.Task;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FavoriteServiceImpl implements FavoriteService {

    @Resource
    private FavoriteRepository favoriteRepository;
    @Resource
    private HistoryService historyService;
    @Resource
    private TaskService taskService;
    @Resource
    private UserService userService;
    @Resource
    private A07DocumentGwlzRepository a07DocumentGwlzRepository;
    @Resource
    private A10CollaborationInfoRepository a10CollaborationInfoRepository;
    @Resource
    private SysUserPlatformRepositoryCustom sysUserPlatformRepositoryCustom;


    @Override
    public Long save(Favorite favorite) {
        Integer count = favoriteRepository.countByProcInstanceIdAndCreateByAndType(favorite.getProcInstanceId(), SecurityUtils.getCurrentUsername(), favorite.getType());
        if (count > 0) {
            throw new BadRequestException("不可重复" + (favorite.getType() == 0 ? "关注" : "收藏"));
        }
        Favorite save = favoriteRepository.save(favorite);
        return save.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long del(Favorite favorite) {
        favoriteRepository.deleteByProcInstanceIdAndType(favorite.getProcInstanceId(), favorite.getType());
        return 200L;
    }

    @Override
    public List<Favorite> list(Integer type, String accountid) {

        String currentUserName="";
        if(StringUtils.isNotEmpty(accountid)){
            SysUserPlatform sysuer = sysUserPlatformRepositoryCustom.findFirstByPlatformUserId(accountid);
            if(Objects.isNull(sysuer)){
                log.error("查询关注收藏列表，"+accountid+"未查询到用户信息");
                return null;
            }
            currentUserName=sysuer.getUserName();
        }else{
            currentUserName=SecurityUtils.getCurrentUsername();
        }
        List<Favorite> list = favoriteRepository.findAllByTypeAndCreateByOrderByModifiedDateDesc(type, currentUserName);
        List<Favorite> result = new ArrayList<>();

        list.stream().forEach(favorite -> {
            String logo = favorite.getLogo();
            if (StringUtils.isNotEmpty(logo) && "gwjh".equals(logo)) {
                Optional<A07DocumentGwlz> optional = a07DocumentGwlzRepository.findById(Long.valueOf(favorite.getProcInstanceId()));
                if (optional.isPresent()) {
                    A07DocumentGwlz a07DocumentGwlz = optional.get();
                    Favorite f = favorite;
                    f.setTitle(a07DocumentGwlz.getBt());
                    //TODO 公文交换和自由协调的当前办理人是否需要展示？
                    f.setDate(favorite.getCreateDate());
                    f.setUrl("https://ding.nanxun.gov.cn:806/newdd/#/documentCirculation?id="+a07DocumentGwlz.getId());
                    result.add(f);
                }
            } else if (StringUtils.isNotEmpty(logo) && "gzxt".equals(logo)) {
                Optional<A10CollaborationInfo> optional = a10CollaborationInfoRepository.findById(Long.valueOf(favorite.getProcInstanceId()));
                if (optional.isPresent()) {
                    A10CollaborationInfo a10CollaborationInfo = optional.get();
                    Favorite f = favorite;
                    f.setTitle(a10CollaborationInfo.getSubject());
                    List<A10CollaborationAssignee> assignees = a10CollaborationInfo.getAssignees();
                    f.setHandle(StringUtils.join(assignees,","));
                    f.setDate(favorite.getCreateDate());
                    f.setUrl("https://ding.nanxun.gov.cn:806/newdd/#/checkSee?userType=1&id="+a10CollaborationInfo.getId());
                    result.add(f);
                }
            }
            else {
                HistoricProcessInstanceQuery historicProcessInstanceQuery = historyService.createHistoricProcessInstanceQuery();
                if (type == 0) {
                    historicProcessInstanceQuery.unfinished();
                }
                List<HistoricProcessInstance> instances = historicProcessInstanceQuery
                        .processInstanceId(favorite.getProcInstanceId())
                        // 返回流程变量
                        .includeProcessVariables()
                        .list();
                if (!instances.isEmpty()) {
                    Favorite f = favorite;
                    HistoricProcessInstance historicProcessInstance = instances.get(0);

                    // 流程名称
                    Map<String, Object> processVariables = historicProcessInstance.getProcessVariables();
                    f.setTitle(String.valueOf(processVariables.get("bpmFormTitle")));
                    // 当前办理人
                    List<Task> taskList = taskService.createTaskQuery().processInstanceId(favorite.getProcInstanceId()).list();
                    List<String> handlers = new ArrayList<>();
                    for (int i = 0; i < taskList.size(); i++) {
                        Task task = taskList.get(i);
                        String assignee = task.getAssignee();
                        if (StringUtils.isNotEmpty(assignee)) {
                            UserDto userDto = userService.findByName(assignee);
                            if (userDto != null) {
                                String nickName = userDto.getNickName();
                                handlers.add(nickName);
                            }
                        }
                    }
                    f.setHandlers(handlers);
                    f.setHandle(StringUtils.join(handlers, ","));
                    f.setDate(favorite.getCreateDate());
                    f.setUrl("https://ding.nanxun.gov.cn:806/newdd/#/bpmRead/browse?processInstanceId=" + favorite.getProcInstanceId());
                    result.add(f);
                }
            }
        });
        return result;
    }

    @Override
    public Page<Favorite> page(Integer type, String accountid, Pageable pageable) {
        List<Favorite> result = list(type, accountid);
        List<Favorite> collect = result.stream().skip(pageable.getOffset()).limit(pageable.getPageSize()).collect(Collectors.toList());
        return new PageImpl<>(collect, pageable, result.size());
    }

    @Override
    public JSONObject check(String procInstanceId) {
        List<Favorite> list = favoriteRepository.findAllByProcInstanceId(procInstanceId);
        JSONObject result = new JSONObject();
        result.put("concern", false);
        result.put("favorite", false);
        list.stream().forEach(favorite -> {
            result.put(favorite.getType() == 0 ? "concern" : "favorite", true);
        });
        return result;
    }
}
