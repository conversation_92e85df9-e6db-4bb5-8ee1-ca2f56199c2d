package com.hzwangda.aigov.modules.form.repository;

import com.hzwangda.aigov.modules.form.domain.A23ReportData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface A23ReportDataRepository extends JpaRepository<A23ReportData, Long>, JpaSpecificationExecutor<A23ReportData> {
    void deleteByInstantiateId(String id);

    @Query(value = "select d from A23ReportData d where d.instantiate.id =:instantiateId")
    List<A23ReportData> findByInstantiateId(Long instantiateId);

}
