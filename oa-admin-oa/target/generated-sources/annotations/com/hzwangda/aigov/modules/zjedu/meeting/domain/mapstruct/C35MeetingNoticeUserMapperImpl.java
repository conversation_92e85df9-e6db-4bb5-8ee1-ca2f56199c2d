package com.hzwangda.aigov.modules.zjedu.meeting.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35MeetingNoticeUserDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35MeetingNoticeUser;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class C35MeetingNoticeUserMapperImpl implements C35MeetingNoticeUserMapper {

    @Override
    public C35MeetingNoticeUserDto toDto(C35MeetingNoticeUser entity) {
        if ( entity == null ) {
            return null;
        }

        C35MeetingNoticeUserDto c35MeetingNoticeUserDto = new C35MeetingNoticeUserDto();

        c35MeetingNoticeUserDto.setCreateBy( entity.getCreateBy() );
        c35MeetingNoticeUserDto.setCreateTime( entity.getCreateTime() );
        c35MeetingNoticeUserDto.setUpdateTime( entity.getUpdateTime() );
        c35MeetingNoticeUserDto.setUpdatedBy( entity.getUpdatedBy() );
        c35MeetingNoticeUserDto.setMeetingNoticeId( entity.getMeetingNoticeId() );
        c35MeetingNoticeUserDto.setStatus( entity.getStatus() );
        c35MeetingNoticeUserDto.setUserName( entity.getUserName() );

        return c35MeetingNoticeUserDto;
    }

    @Override
    public List<C35MeetingNoticeUserDto> toDto(List<C35MeetingNoticeUser> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<C35MeetingNoticeUserDto> list = new ArrayList<C35MeetingNoticeUserDto>( entityList.size() );
        for ( C35MeetingNoticeUser c35MeetingNoticeUser : entityList ) {
            list.add( toDto( c35MeetingNoticeUser ) );
        }

        return list;
    }

    @Override
    public C35MeetingNoticeUser toEntity(C35MeetingNoticeUserDto dto) {
        if ( dto == null ) {
            return null;
        }

        C35MeetingNoticeUser c35MeetingNoticeUser = new C35MeetingNoticeUser();

        c35MeetingNoticeUser.setCreateBy( dto.getCreateBy() );
        c35MeetingNoticeUser.setCreateTime( dto.getCreateTime() );
        c35MeetingNoticeUser.setUpdateTime( dto.getUpdateTime() );
        c35MeetingNoticeUser.setUpdatedBy( dto.getUpdatedBy() );
        c35MeetingNoticeUser.setMeetingNoticeId( dto.getMeetingNoticeId() );
        c35MeetingNoticeUser.setStatus( dto.getStatus() );
        c35MeetingNoticeUser.setUserName( dto.getUserName() );

        return c35MeetingNoticeUser;
    }

    @Override
    public List<C35MeetingNoticeUser> toEntity(List<C35MeetingNoticeUserDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<C35MeetingNoticeUser> list = new ArrayList<C35MeetingNoticeUser>( dtoList.size() );
        for ( C35MeetingNoticeUserDto c35MeetingNoticeUserDto : dtoList ) {
            list.add( toEntity( c35MeetingNoticeUserDto ) );
        }

        return list;
    }
}
