package com.hzwangda.aigov.modules.addresslist.mapstruct;

import com.hzwangda.aigov.modules.addresslist.domain.dto.PersonAddressListGroupDto;
import com.hzwangda.aigov.modules.addresslist.domain.entity.PersonAddressListGroup;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class PersonAddressListGroupMapperImpl implements PersonAddressListGroupMapper {

    @Override
    public PersonAddressListGroupDto toDto(PersonAddressListGroup entity) {
        if ( entity == null ) {
            return null;
        }

        PersonAddressListGroupDto personAddressListGroupDto = new PersonAddressListGroupDto();

        personAddressListGroupDto.setCreateBy( entity.getCreateBy() );
        personAddressListGroupDto.setCreateTime( entity.getCreateTime() );
        personAddressListGroupDto.setUpdateTime( entity.getUpdateTime() );
        personAddressListGroupDto.setUpdatedBy( entity.getUpdatedBy() );
        personAddressListGroupDto.setGroupName( entity.getGroupName() );
        personAddressListGroupDto.setGroupUsername( entity.getGroupUsername() );
        personAddressListGroupDto.setId( entity.getId() );
        personAddressListGroupDto.setSort( entity.getSort() );

        return personAddressListGroupDto;
    }

    @Override
    public List<PersonAddressListGroupDto> toDto(List<PersonAddressListGroup> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<PersonAddressListGroupDto> list = new ArrayList<PersonAddressListGroupDto>( entityList.size() );
        for ( PersonAddressListGroup personAddressListGroup : entityList ) {
            list.add( toDto( personAddressListGroup ) );
        }

        return list;
    }

    @Override
    public PersonAddressListGroup toEntity(PersonAddressListGroupDto dto) {
        if ( dto == null ) {
            return null;
        }

        PersonAddressListGroup personAddressListGroup = new PersonAddressListGroup();

        personAddressListGroup.setCreateBy( dto.getCreateBy() );
        personAddressListGroup.setCreateTime( dto.getCreateTime() );
        personAddressListGroup.setUpdateTime( dto.getUpdateTime() );
        personAddressListGroup.setUpdatedBy( dto.getUpdatedBy() );
        personAddressListGroup.setGroupName( dto.getGroupName() );
        personAddressListGroup.setGroupUsername( dto.getGroupUsername() );
        personAddressListGroup.setId( dto.getId() );
        personAddressListGroup.setSort( dto.getSort() );

        return personAddressListGroup;
    }

    @Override
    public List<PersonAddressListGroup> toEntity(List<PersonAddressListGroupDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<PersonAddressListGroup> list = new ArrayList<PersonAddressListGroup>( dtoList.size() );
        for ( PersonAddressListGroupDto personAddressListGroupDto : dtoList ) {
            list.add( toEntity( personAddressListGroupDto ) );
        }

        return list;
    }
}
