package com.hzwangda.aigov.modules.document.enums;

import com.alibaba.fastjson.annotation.JSONType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/03/24
 * @description
 */
@Getter
@AllArgsConstructor
@ToString
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@JSONType(serializeEnumAsJavaBean = true)
public enum RoleIdentificationEnum {
    /**
     * 权限标识
     */
    AllFW("全部发文", "fw:allfw"),
    ALLSW("全部收文", "sw:allSw");

    private String name;
    private String value;


    public static RoleIdentificationEnum getByValue(String name) {
        for(RoleIdentificationEnum roleIdent : RoleIdentificationEnum.values()) {
            if(roleIdent.name.equals(name)) {
                return roleIdent;
            }
        }
        return null;
    }
}
