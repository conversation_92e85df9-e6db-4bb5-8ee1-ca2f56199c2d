package com.hzwangda.aigov.modules.document.service.impl;

import com.google.common.collect.Lists;
import com.hzwangda.aigov.modules.document.constant.DocumentConstant;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwDto;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwQueryCriteria;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwView;
import com.hzwangda.aigov.modules.document.repository.A07DocumentGwViewRepository;
import com.hzwangda.aigov.modules.document.service.Z01FwService;
import com.hzwangda.aigov.modules.workflow.enums.workflow.WorkflowType;
import com.wangda.oa.backlog.bo.BacklogBusinessSearchBO;
import com.wangda.oa.backlog.bo.BacklogListBO;
import com.wangda.oa.backlog.dto.BacklogListDto;
import com.wangda.oa.backlog.dto.BacklogListPageDto;
import com.wangda.oa.backlog.service.WdBacklogService;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class Z01FwServiceImpl implements Z01FwService {

    private final A07DocumentGwViewRepository a07DocumentGwViewRepository;

    private final UserRepository userRepository;

    private final WdBacklogService wdBacklogService;

    @Override
    public Map<String, Object> getFwDistributeList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        //封装待办查询条件
        BacklogListBO backlogListBO = new BacklogListBO();
        backlogListBO.setUserId(SecurityUtils.getCurrentUsername());
        backlogListBO.setAppId("OA");
        if(StringUtils.isNotBlank(criteria.getProcessDefinitionKey())) {
            //实例定义不为空
            backlogListBO.setModuleCode(criteria.getProcessDefinitionKey());
        }else {
            //实例定义为空则查询公文管理对应定义
            backlogListBO.setModuleCodes(DocumentConstant.PROCESS_DEFINITION_KEY_FW_LIST);
        }
        List<BacklogBusinessSearchBO> businessSearchBOList = new ArrayList<>();
        if(StringUtils.isNotEmpty(criteria.getYear())) {
            BacklogBusinessSearchBO businessSearchBO = new BacklogBusinessSearchBO();
            businessSearchBO.setType("contains");
            businessSearchBO.setKey("gwwh");
            businessSearchBO.setValue("\"nh\":\"" + criteria.getYear() + "\"");
            businessSearchBOList.add(businessSearchBO);
        }
        if(StringUtils.isNotEmpty(criteria.getDz())) {
            BacklogBusinessSearchBO businessSearchBO = new BacklogBusinessSearchBO();
            businessSearchBO.setType("contains");
            businessSearchBO.setKey("gwwh");
            businessSearchBO.setValue("\"dz\":\"" + criteria.getDz() + "\"");
            businessSearchBOList.add(businessSearchBO);
        }
        if(StringUtils.isNotEmpty(criteria.getGwxh())) {
            BacklogBusinessSearchBO businessSearchBO = new BacklogBusinessSearchBO();
            businessSearchBO.setType("contains");
            businessSearchBO.setKey("gwwh");
            businessSearchBO.setValue("\"xh\":\"" + criteria.getGwxh() + "\"");
            businessSearchBOList.add(businessSearchBO);
        }

        if(StringUtils.isNotBlank(criteria.getBt())) {
            backlogListBO.setBt(criteria.getBt());
        }
        if(criteria.getTimeRange() != null && criteria.getTimeRange().size() > 1) {
            backlogListBO.setCreateStartTime(criteria.getTimeRange().get(0));
            backlogListBO.setCreateEndTime(criteria.getTimeRange().get(1));
        }
        backlogListBO.setBusinessSearchBOList(businessSearchBOList);
        backlogListBO.setStatus(criteria.getStatus());
        if(criteria.getStatus() == null) {
            //若不传默认查询所有，则为4
            backlogListBO.setStatus(4);
        }
        backlogListBO.setPage(pageable.getPageNumber());
        backlogListBO.setSize(pageable.getPageSize());

        //查询待办服务
        BacklogListPageDto backlogListPageDto = wdBacklogService.getBacklogList(backlogListBO);
        List<BacklogListDto> backlogListDtoList = backlogListPageDto.getContent();
        if(CollectionUtils.isEmpty(backlogListDtoList)) {
            return PageUtil.toPage(Page.empty());
        }

        //获取流程实例id
        Map<String, Date> bpmInstanceIdMap = new LinkedHashMap<>();
        for(BacklogListDto backlogListDto : backlogListDtoList) {
            bpmInstanceIdMap.put(backlogListDto.getBizId(), backlogListDto.getCreateDate());
        }
        criteria.setBpmInstanceId(Lists.newArrayList(bpmInstanceIdMap.keySet()));
        //查询业务视图表信息
        List<A07DocumentGwView> viewList = a07DocumentGwViewRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));

        String transactorNames = backlogListDtoList.stream().map(BacklogListDto::getCurrentHandler).collect(Collectors.joining(","));
        //用户名集合放入
        List<String> username = viewList.stream().map(A07DocumentGwView::getCjr).collect(Collectors.toList());
        //办理人放入用户名集合
        username.addAll(Arrays.asList(transactorNames.split(",")));

        //查询用户信息
        List<User> userList = userRepository.findByUsernameIn(username);
        Map<String, User> finalUserMap = userList.stream().collect(Collectors.toMap(User::getUsername, a -> a, (k1, k2) -> k1));

        List<A07DocumentGwDto> returnList = backlogListDtoList.stream().map(p -> {
            A07DocumentGwDto documentGwDto = new A07DocumentGwDto();
            for(A07DocumentGwView a07DocumentGwView : viewList) {
                if(a07DocumentGwView.getBpmInstanceId().equals(p.getBizId())) {
                    BeanUtils.copyProperties(a07DocumentGwView, documentGwDto);
                    if(StringUtils.isNotBlank(p.getCurrentHandler())) {
                        //当前办理人不为空根据username设置中文
                        String[] handlerArr = p.getCurrentHandler().split(",");
                        String handler = "";
                        for(int i = 0; i < handlerArr.length; i++) {
                            if(finalUserMap.get(handlerArr[i]) == null) {
                                continue;
                            }
                            handler += finalUserMap.get(handlerArr[i]).getNickName() + ",";
                        }
                        if(StringUtils.isNotBlank(handler)) {
                            handler = handler.substring(0, handler.length() - 1);
                        }
                        documentGwDto.setBlr(handler);
                    }
                    User cjrUser = finalUserMap.get(a07DocumentGwView.getCjr());
                    documentGwDto.setCjr(cjrUser == null ? null : cjrUser.getNickName());
                    WorkflowType workflowType = WorkflowType.getByValue(a07DocumentGwView.getModuleType());
                    documentGwDto.setModuleType(workflowType == null ? null : workflowType.getName());
                    documentGwDto.setUrgent(p.getUrgent());
                    documentGwDto.setExpiredStatus(p.getExpiredStatus());
                    if(Objects.nonNull(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()))) {
                        documentGwDto.setCreateTime(new Timestamp(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()).getTime()));
                    }
                    if(StringUtils.isBlank(documentGwDto.getBt())) {
                        documentGwDto.setBt(p.getTitle());
                    }
                    // 流程状态
                    ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(a07DocumentGwView.getBpmStatus());
                    documentGwDto.setBpmStatus(procStatusEnum == null ? null : procStatusEnum.getName());
                    return documentGwDto;
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, Object> returnMap = new LinkedHashMap<>(2);
        returnMap.put("content", returnList);
        returnMap.put("totalElements", backlogListPageDto.getTotalElements());
        return returnMap;
    }
}
