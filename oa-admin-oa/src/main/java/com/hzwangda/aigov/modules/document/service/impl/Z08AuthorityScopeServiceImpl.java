package com.hzwangda.aigov.modules.document.service.impl;

import com.hzwangda.aigov.modules.document.enums.AuthoryScopeEnum;
import com.hzwangda.aigov.modules.document.service.Z08AuthorityScopeService;
import com.hzwangda.aigov.modules.meetingRoomSubscribe.domain.MeetingRoomFamily;
import com.hzwangda.aigov.modules.meetingRoomSubscribe.service.MeetingRoomFamilyService;
import com.wangda.oa.modules.workflow.domain.application.AppPermission;
import com.wangda.oa.modules.workflow.domain.space.AppSpace;
import com.wangda.oa.modules.workflow.service.application.AppPermissionService;
import com.wangda.oa.modules.workflow.service.space.SpaceService;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.springframework.stereotype.Service;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2022/10/26 9:55
 * @description:
 */
@Service
@RequiredArgsConstructor
public class Z08AuthorityScopeServiceImpl implements Z08AuthorityScopeService {
    private final HistoryService historyService;
    private final AppPermissionService permissionService;
    private final SpaceService spaceService;
    private final MeetingRoomFamilyService meetingRoomFamilyService;

    @Override
    public String getAuthorityScope(String type, String id) {
        if(AuthoryScopeEnum.GW.getValue().equals(type)) {
            HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(id).singleResult();
            AppPermission byApplicationId = permissionService.getByApplicationId(Long.valueOf(processInstance.getBusinessKey()));
            AppSpace byId = spaceService.getById(byApplicationId.getSpaceId());
            return byId.getAppliedRange();
        }else if(AuthoryScopeEnum.MEETING.getValue().equals(type)) {
            MeetingRoomFamily byId = meetingRoomFamilyService.getById(Long.valueOf(id));
            return byId.getDeptCode();
        }else {
            return null;
        }
    }
}
