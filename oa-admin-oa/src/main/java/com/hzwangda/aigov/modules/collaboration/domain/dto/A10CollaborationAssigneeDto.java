package com.hzwangda.aigov.modules.collaboration.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class A10CollaborationAssigneeDto {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "工作协同id")
    private Long infoId;

    @ApiModelProperty(value = "操作记录id")
    private Long recordId;

    @ApiModelProperty(value = "处理人username")
    private SimpleUserDto handleUser;

    @ApiModelProperty(value = "发送人username，冗余记录来自于谁")
    private SimpleUserDto fromUser;

    @ApiModelProperty(value = "状态代码[未读(new),已读/未签收(unsign),已签收(signed)]")
    private String status;

    @ApiModelProperty(value = "状态名称[未读(new),已读/未签收(unsign),已签收(signed)]")
    private String statusName;

    @ApiModelProperty(value = "首读时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date readingTime;

    @ApiModelProperty(value = "办结时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date finishedTime;

}
