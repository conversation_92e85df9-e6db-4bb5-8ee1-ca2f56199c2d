/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.modules.news.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Transient;
import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-06-04
 **/
@Data
public class A06NewsManagerQueryCriteria {
    @Query
    @ApiModelProperty(value = "类型(0:电子公告,1:今日择报,2:工作交流,3:图文报道)")
    private Integer infoType;

    @ApiModelProperty(value = "0:查阅,1:管理")
    private Integer type;

    @Transient
    @Query(type = Query.Type.IN)
    private List<String> bpmStatus;

    @Query(type = Query.Type.INNER_LIKE)
    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "公文类型key")
    private String processDefinitionKey;

    @ApiModelProperty(value = "签收状态(未签收0/已签收1/全部null)")
    private Integer receiveStatus;
}
