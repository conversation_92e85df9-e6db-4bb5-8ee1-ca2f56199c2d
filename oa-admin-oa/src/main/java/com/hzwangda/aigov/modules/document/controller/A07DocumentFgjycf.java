package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.dto.A07FgjycfQueryCriteria;
import com.hzwangda.aigov.modules.document.service.A07FgjycfspService;
import com.hzwangda.aigov.modules.zjedu.info.domain.criteria.D3InfoUseLogQueryCriteria;
import com.wangda.oa.annotation.Log;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "发改经信局夜餐费管理")
@RequestMapping("/api/aigov/fgjycf/")
@CrossOrigin
public class A07DocumentFgjycf {
    private final A07FgjycfspService a07FgjycfspService;
    /**
     * 局列表
     */
    @ApiOperation("夜餐费列表")
    @GetMapping(value = "/getList")
    public ResponseEntity<Object> getFwList(A07FgjycfQueryCriteria criteria) throws ParseException {
        return new ResponseEntity<>(a07FgjycfspService.getList(criteria), HttpStatus.OK);
    }

    /**
     * 中心列表
     */
    @ApiOperation("夜餐费列表")
    @GetMapping(value = "/getListzx")
    public ResponseEntity<Object> getFwListzx(A07FgjycfQueryCriteria criteria) throws ParseException {
        return new ResponseEntity<>(a07FgjycfspService.getListzx(criteria), HttpStatus.OK);
    }

    /**
     * 局导出
     */
    @Log("局导出")
    @ApiOperation("局导出")
    @GetMapping(value="/exportExcel")
    public void exportExcel(HttpServletResponse response, A07FgjycfQueryCriteria queryCriteria) throws IOException, ParseException {
        a07FgjycfspService.exportExcel(response,queryCriteria);
    }

    /**
     * 中心导出
     */
    @Log("中心导出")
    @ApiOperation("中心导出")
    @GetMapping(value="/exportExcelzx")
    public void exportExcelzx(HttpServletResponse response,A07FgjycfQueryCriteria queryCriteria) throws IOException, ParseException {
        a07FgjycfspService.exportExcelzx(response,queryCriteria);
    }
}
