package com.hzwangda.aigov.modules.addresslist.domain.entity;

import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2022/8/5 10:47
 * @description: 个人通讯录分组
 */
@Entity
@Data
@Table(name = "z08_person_address_list_group")
public class PersonAddressListGroup extends BaseEntity {
    @Id
    private Long id;

    @ApiModelProperty(value = "分类名称")
    @Column(name = "group_name")
    private String groupName;

    @ApiModelProperty(value = "使用人")
    @Column(name = "group_username")
    private String groupUsername;

    @ApiModelProperty(value = "排序")
    @Column(name = "sort")
    private Integer sort;


    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }
}
