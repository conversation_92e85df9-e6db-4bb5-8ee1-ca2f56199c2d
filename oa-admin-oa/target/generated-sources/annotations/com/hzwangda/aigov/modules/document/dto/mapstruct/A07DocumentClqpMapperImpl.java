package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07DocumentClqpDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentClqp;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A07DocumentClqpMapperImpl implements A07DocumentClqpMapper {

    @Autowired
    private A07DocumentMapperUtil a07DocumentMapperUtil;

    @Override
    public List<A07DocumentClqpDto> toDto(List<A07DocumentClqp> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A07DocumentClqpDto> list = new ArrayList<A07DocumentClqpDto>( entityList.size() );
        for ( A07DocumentClqp a07DocumentClqp : entityList ) {
            list.add( toDto( a07DocumentClqp ) );
        }

        return list;
    }

    @Override
    public A07DocumentClqp toEntity(A07DocumentClqpDto dto) {
        if ( dto == null ) {
            return null;
        }

        A07DocumentClqp a07DocumentClqp = new A07DocumentClqp();

        a07DocumentClqp.setBelongToDept( dto.getBelongToDept() );
        a07DocumentClqp.setBpmInstanceId( dto.getBpmInstanceId() );
        a07DocumentClqp.setBpmProcessKey( dto.getBpmProcessKey() );
        a07DocumentClqp.setBpmStatus( dto.getBpmStatus() );
        a07DocumentClqp.setBpmSubject( dto.getBpmSubject() );
        a07DocumentClqp.setCreateBy( dto.getCreateBy() );
        a07DocumentClqp.setCreateTime( dto.getCreateTime() );
        a07DocumentClqp.setId( dto.getId() );
        List<String> list = dto.getParticipateUser();
        if ( list != null ) {
            a07DocumentClqp.setParticipateUser( new ArrayList<String>( list ) );
        }
        a07DocumentClqp.setUpdateBy( dto.getUpdateBy() );
        a07DocumentClqp.setUpdateTime( dto.getUpdateTime() );
        a07DocumentClqp.setVersion( dto.getVersion() );
        a07DocumentClqp.setClmc( dto.getClmc() );
        a07DocumentClqp.setFj( a07DocumentMapperUtil.toConvertToId( dto.getFj() ) );
        a07DocumentClqp.setZw( dto.getZw() );

        return a07DocumentClqp;
    }

    @Override
    public List<A07DocumentClqp> toEntity(List<A07DocumentClqpDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A07DocumentClqp> list = new ArrayList<A07DocumentClqp>( dtoList.size() );
        for ( A07DocumentClqpDto a07DocumentClqpDto : dtoList ) {
            list.add( toEntity( a07DocumentClqpDto ) );
        }

        return list;
    }

    @Override
    public A07DocumentClqpDto toDto(A07DocumentClqp entity) {
        if ( entity == null ) {
            return null;
        }

        A07DocumentClqpDto a07DocumentClqpDto = new A07DocumentClqpDto();

        a07DocumentClqpDto.setBelongToDept( entity.getBelongToDept() );
        a07DocumentClqpDto.setBpmInstanceId( entity.getBpmInstanceId() );
        a07DocumentClqpDto.setBpmProcessKey( entity.getBpmProcessKey() );
        a07DocumentClqpDto.setBpmStatus( entity.getBpmStatus() );
        a07DocumentClqpDto.setBpmSubject( entity.getBpmSubject() );
        a07DocumentClqpDto.setCreateBy( entity.getCreateBy() );
        a07DocumentClqpDto.setCreateTime( entity.getCreateTime() );
        a07DocumentClqpDto.setId( entity.getId() );
        List<String> list = entity.getParticipateUser();
        if ( list != null ) {
            a07DocumentClqpDto.setParticipateUser( new ArrayList<String>( list ) );
        }
        a07DocumentClqpDto.setUpdateBy( entity.getUpdateBy() );
        a07DocumentClqpDto.setUpdateTime( entity.getUpdateTime() );
        a07DocumentClqpDto.setVersion( entity.getVersion() );
        a07DocumentClqpDto.setClmc( entity.getClmc() );
        a07DocumentClqpDto.setZw( entity.getZw() );

        a07DocumentClqpDto.setFj( a07DocumentMapperUtil.toFj(entity.getFj()) );

        splitTime( entity, a07DocumentClqpDto );

        return a07DocumentClqpDto;
    }
}
