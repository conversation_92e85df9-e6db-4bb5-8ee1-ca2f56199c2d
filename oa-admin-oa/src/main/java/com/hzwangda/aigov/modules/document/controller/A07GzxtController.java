package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.dto.A08GzxtDto;
import com.hzwangda.aigov.modules.document.dto.A08GzxtQueryCriteria;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGzxt;
import com.hzwangda.aigov.modules.document.service.A07GzxtService;
import com.hzwangda.aigov.modules.workflow.bo.GzxtUserListBO;
import com.hzwangda.aigov.modules.workflow.dto.MyConferenceListDto;
import com.wangda.oa.annotation.Log;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "工作协同管理")
@RequestMapping("/api/aigov/document/gzxt")
@CrossOrigin
public class A07GzxtController {

    private final A07GzxtService a07GzxtService;

    @ApiOperation("协同列表")
    @GetMapping(value = "/getXtList")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Object> getXtList(A08GzxtQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07GzxtService.getXtList(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("协同详情")
    @PostMapping(value = "/getXtInfo")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<A08GzxtDto> getXtInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07GzxtService.getXtInfo(id), HttpStatus.OK);
    }

    @Log("删除协同")
    @ApiOperation("删除协同")
    @PostMapping(value = "/deleteXt")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Boolean> deleteXt(@NotNull @RequestBody List<Long> ids) {
        return new ResponseEntity<>(a07GzxtService.deleteXt(ids), HttpStatus.OK);
    }

    @PostMapping("/saveGzxt")
    @Log("新增或修改gzxt")
    @ApiOperation("新增或修改gzxt")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Object> saveGzxt(@Validated @RequestBody A07DocumentGzxt resources) {
        return new ResponseEntity<>(a07GzxtService.createOrUpdateGzxt(resources), HttpStatus.CREATED);
    }

    @Log("用户工作协同列表")
    @ApiOperation("用户工作协同列表")
    @GetMapping(value = "/getGzxtUserList")
    @PreAuthorize("@el.check('a07:list')")
    public MyConferenceListDto getGzxtUserList(GzxtUserListBO gzxtUserListBO) {
        return a07GzxtService.getGzxtUserList(gzxtUserListBO);
    }

}
