package com.hzwangda.aigov.modules.archive.service;

import com.hzwangda.aigov.modules.archive.entity.A07FileArchive;
import com.hzwangda.aigov.modules.archive.entity.FileArchiveCriteria;
import com.hzwangda.aigov.modules.archive.entity.FileArchiveDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public interface FileArchiveService {
    Long save(A07FileArchive fileArchive) throws IOException;

    Page<A07FileArchive> queryList(FileArchiveCriteria criteria, Pageable pageable);

    FileArchiveDto queryOne(Long id);

    Object upload(String fileNumber) throws IOException;

    void download(String name, HttpServletRequest request, HttpServletResponse response) throws IOException;

    Object mark(Long[] ids, String status);

    Object cancelMark(Long[] ids);

    Object getAccessToken(String usercode);

    Object getNpDataByProcessInstanceId(String processInstanceId);

    Page getNpDatas(String fileStatus, String bt, String belongToDept, Pageable pageable);
}
