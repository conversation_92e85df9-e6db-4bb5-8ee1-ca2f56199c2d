package com.hzwangda.aigov.modules.document.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bstek.ureport.export.ExportConfigure;
import com.bstek.ureport.export.ExportManager;
import com.google.common.collect.Maps;
import com.hzwangda.aigov.modules.document.constant.DocumentConstant;
import com.hzwangda.aigov.modules.document.dto.*;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07DocumentClqpMapper;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07DocumentLdpsMapper;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07DocumentSwMapper;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07DocumentXxgksqMapper;
import com.hzwangda.aigov.modules.document.entity.*;
import com.hzwangda.aigov.modules.document.repository.*;
import com.hzwangda.aigov.modules.document.service.A07GwService;
import com.hzwangda.aigov.modules.document.service.A07SwService;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.modules.workflow.enums.workflow.WorkflowType;
import com.hzwangda.aigov.modules.workflow.repository.ReferenceNumberRepository;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.hzwangda.aigov.tables.domain.IdClass;
import com.hzwangda.aigov.tables.service.TablesService;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.config.FileProperties;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.domain.form.FormTemplate;
import com.wangda.oa.modules.workflow.domain.form.PrintTemplate;
import com.wangda.oa.modules.workflow.dto.application.ApplicationDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskDto;
import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import com.wangda.oa.modules.workflow.service.application.ApplicationService;
import com.wangda.oa.modules.workflow.service.form.FormTemplateService;
import com.wangda.oa.modules.workflow.service.form.PrintTemplateService;
import com.wangda.oa.modules.workflow.service.workflow.FlowTaskService;
import com.wangda.oa.repository.LocalStorageRepository;
import com.wangda.oa.service.IStorageService;
import com.wangda.oa.utils.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.formula.functions.T;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.hibernate.annotations.Where;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.persistence.criteria.*;
import java.io.*;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class A07SwServiceImpl implements A07SwService {

    private final A07DocumentSwRepository a07DocumentSwRepository;

    private final A07DocumentSwMapper a07DocumentSwMapper;

    private final TaskService taskService;

    private final FlowTaskService flowTaskService;

    private final A07DocumentLdpsRepository a07DocumentLdpsRepository;

    private final A07DocumentLdpsMapper a07DocumentLdpsMapper;

    private final A07DocumentClqpRepository a07DocumentClqpRepository;

    private final A07DocumentClqpMapper a07DocumentClqpMapper;

    private final A07DocumentXxgksqRepository a07DocumentXxgksqRepository;

    private final A07DocumentXxgksqMapper a07DocumentXxgksqMapper;

    private final A07DocumentSwViewRepository a07DocumentSwViewRepository;

    private final ReferenceNumberRepository referenceNumberRepository;

    private final LocalStorageRepository localStorageRepository;

    private final A07GwService gwService;
    private final ApplicationService applicationService;
    private final FormTemplateService formTemplateService;
    private final TablesService tablesService;

    private final FileProperties properties;
    private final ExportManager exportManager;
    private final IStorageService iStorageService;
    private final PrintTemplateService printTemplateService;

    @Override
    public Map<String, Object> getSwList(A07DocumentSwQueryCriteria criteria, Pageable pageable) {
        A07SearchDtoCriteria searchDtoCriteria = new A07SearchDtoCriteria();
        searchDtoCriteria.setBt(criteria.getBt());
        searchDtoCriteria.setType(criteria.getStatus());
        searchDtoCriteria.setSwlx(criteria.getSwlx());
        Map<String, FlowTaskInfoDto> map = this.getProcessInstanceIdMap(criteria.getStatus(), WorkflowType.DOCUMENT_ADDRESSEE.getValue(), pageable, searchDtoCriteria);
        if(CollectionUtils.isEmpty(map)) {
            return PageUtil.toPage(Page.empty());
        }
        criteria.setBpmInstanceId(map.keySet());
        Page<A07DocumentSw> page = a07DocumentSwRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Page<A07DocumentSwDto> pageDto = page.map(a07DocumentSwMapper::toDto);
        pageDto.getContent().forEach(a07DocumentSwDto -> {
            FlowTaskInfoDto flowTaskInfoDto = map.get(a07DocumentSwDto.getBpmInstanceId());
            a07DocumentSwDto.setFlowTaskInfoDto(flowTaskInfoDto);
        });
        return PageUtil.toPage(pageDto);
    }

    @Override
    public Map<String, Object> getLdpsList(A07DocumentLdpsQueryCriteria criteria, Pageable pageable) {
        A07SearchDtoCriteria searchDtoCriteria = new A07SearchDtoCriteria();
        searchDtoCriteria.setBt(criteria.getBt());
        searchDtoCriteria.setType(criteria.getStatus());
        Map<String, FlowTaskInfoDto> map = this.getProcessInstanceIdMap(criteria.getStatus(), WorkflowType.DOCUMENT_LDPS.getValue(), pageable, searchDtoCriteria);
        if(CollectionUtils.isEmpty(map)) {
            return PageUtil.toPage(Page.empty());
        }
        criteria.setBpmInstanceId(map.keySet());
        Page<A07DocumentLdps> page = a07DocumentLdpsRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Page<A07DocumentLdpsDto> pageDto = page.map(a07DocumentLdpsMapper::toDto);
        pageDto.getContent().forEach(a07DocumentLdpsDto -> {
            FlowTaskInfoDto flowTaskInfoDto = map.get(a07DocumentLdpsDto.getBpmInstanceId());
            a07DocumentLdpsDto.setFlowTaskInfoDto(flowTaskInfoDto);
        });
        return PageUtil.toPage(pageDto);
    }

    @Override
    public Map<String, Object> getClqpList(A07DocumentClqpQueryCriteria criteria, Pageable pageable) {
        A07SearchDtoCriteria searchDtoCriteria = new A07SearchDtoCriteria();
        searchDtoCriteria.setBt(criteria.getBt());
        searchDtoCriteria.setType(criteria.getStatus());
        Map<String, FlowTaskInfoDto> map = this.getProcessInstanceIdMap(criteria.getStatus(), WorkflowType.DOCUMENT_QPBL.getValue(), pageable, searchDtoCriteria);
        if(CollectionUtils.isEmpty(map)) {
            return PageUtil.toPage(Page.empty());
        }
        criteria.setBpmInstanceId(map.keySet());
        Page<A07DocumentClqp> page = a07DocumentClqpRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Page<A07DocumentClqpDto> pageDto = page.map(a07DocumentClqpMapper::toDto);
        pageDto.getContent().forEach(a07DocumentClqpDto -> {
            FlowTaskInfoDto flowTaskInfoDto = map.get(a07DocumentClqpDto.getBpmInstanceId());
            a07DocumentClqpDto.setFlowTaskInfoDto(flowTaskInfoDto);
        });
        return PageUtil.toPage(pageDto);
    }

    @Override
    public Map<String, Object> getXxgksqList(A07DocumentXxgksqQueryCriteria criteria, Pageable pageable) {
        A07SearchDtoCriteria searchDtoCriteria = new A07SearchDtoCriteria();
        searchDtoCriteria.setBt(criteria.getBt());
        searchDtoCriteria.setType(criteria.getStatus());
        Map<String, FlowTaskInfoDto> map = this.getProcessInstanceIdMap(criteria.getStatus(), WorkflowType.DOCUMENT_XXGKSQ.getValue(), pageable, searchDtoCriteria);
        if(CollectionUtils.isEmpty(map)) {
            return PageUtil.toPage(Page.empty());
        }
        criteria.setBpmInstanceId(map.keySet());
        Page<A07DocumentXxgksq> page = a07DocumentXxgksqRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Page<A07DocumentXxgksqDto> pageDto = page.map(a07DocumentXxgksqMapper::toDto);
        pageDto.getContent().forEach(a07DocumentXxgksqDto -> {
            FlowTaskInfoDto flowTaskInfoDto = map.get(a07DocumentXxgksqDto.getBpmInstanceId());
            a07DocumentXxgksqDto.setFlowTaskInfoDto(flowTaskInfoDto);
        });
        return PageUtil.toPage(pageDto);
    }

    @Override
    public A07DocumentSwDto getSwInfo(Long id) {
        A07DocumentSw sw = a07DocumentSwRepository.findById(id).orElseGet(A07DocumentSw::new);
        ValidationUtil.isNull(sw.getId(), "sw", "id", id);
        return a07DocumentSwMapper.toDto(sw);
    }

    @Override
    public A07DocumentLdpsDto getLdpsInfo(Long id) {
        A07DocumentLdps ldps = a07DocumentLdpsRepository.findById(id).orElseGet(A07DocumentLdps::new);
        ValidationUtil.isNull(ldps.getId(), "ldps", "id", id);
        return a07DocumentLdpsMapper.toDto(ldps);
    }

    @Override
    public A07DocumentClqpDto getClqpInfo(Long id) {
        A07DocumentClqp clqp = a07DocumentClqpRepository.findById(id).orElseGet(A07DocumentClqp::new);
        ValidationUtil.isNull(clqp.getId(), "clqp", "id", id);
        return a07DocumentClqpMapper.toDto(clqp);
    }

    @Override
    public A07DocumentXxgksqDto getXxgksqInfo(Long id) {
        A07DocumentXxgksq xxgksq = a07DocumentXxgksqRepository.findById(id).orElseGet(A07DocumentXxgksq::new);
        ValidationUtil.isNull(xxgksq.getId(), "xxgksq", "id", id);
        return a07DocumentXxgksqMapper.toDto(xxgksq);
    }

    @Override
    public Map<String, Object> getSwListRetrieve(A07DocumentGwRetrieveQueryCriteria criteria, Pageable pageable) {
        criteria.setGwglType(3);
        Page<A07DocumentSwView> page = a07DocumentSwViewRepository.findAll((Specification) retrieveFilter(criteria), pageable);
        page.getContent().stream().forEach(p -> {
            ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(p.getBpmStatus());
            p.setBpmStatus(procStatusEnum == null ? null : procStatusEnum.getName());
        });
        return PageUtil.toPage(page);
    }

    @Override
    public Boolean deleteSw(List<Long> ids) {
        for(Long id : ids) {
            a07DocumentSwRepository.deleteById(id);
        }
        return true;
    }

    @Override
    public Boolean deleteLdps(List<Long> ids) {
        for(Long id : ids) {
            a07DocumentLdpsRepository.deleteById(id);
        }
        return true;
    }

    @Override
    public Boolean deleteClqps(List<Long> ids) {
        for(Long id : ids) {
            a07DocumentClqpRepository.deleteById(id);
        }
        return true;
    }

    @Override
    public Boolean deleteXxgksq(List<Long> ids) {
        for(Long id : ids) {
            a07DocumentXxgksqRepository.deleteById(id);
        }
        return true;
    }

    @Override
    public Integer getXhByNh(String dz, String nh) {
        Integer xh = 0;

        xh = referenceNumberRepository.getMaxXhByTypeAndNh(nh);
        if(xh == null) {
            xh = 0;
        }
        return ++xh;
    }

    @Override
    public A07DocumentSw getSwByProcessInstanceId(String processInstanceId) {
        return a07DocumentSwRepository.findFirstByBpmInstanceId(processInstanceId);
    }

    /**
     * 查询我的不同状态的流程实例id
     * @param status
     * @param processDefinitionKey
     * @param pageable
     * @return
     */
    private Map<String, FlowTaskInfoDto> getProcessInstanceIdMap(Integer status, String processDefinitionKey, Pageable pageable, A07SearchDtoCriteria searchDto) {
        Map<String, List<FlowTaskInfoDto>> result;
        Map<String, FlowTaskInfoDto> returnResult = new HashMap<>();
        if(DocumentConstant.QUERY_STATUS_DB == status || DocumentConstant.QUERY_STATUS_DY == status) {
            //待办任务列表
            TaskQuery taskQuery = taskService.createTaskQuery()
                    .active()
                    .processDefinitionKey(processDefinitionKey)
                    .taskAssignee(SecurityUtils.getCurrentUsername()) // userName关联处理人
                    .includeProcessVariables()
                    .orderByTaskCreateTime().desc();
            if(StringUtils.isNotEmpty(searchDto.getBt())) {
                taskQuery.processVariableValueLikeIgnoreCase(ProcessConstants.BPM_FORM_TITLE, searchDto.getBt() + "%");
            }
            if(StringUtils.isNotEmpty(searchDto.getSwlx())) {
                if(DocumentConstant.QUERY_STATUS_DY == status) {
                    taskQuery.processVariableValueEquals(ProcessConstants.BPM_FORM_DOCUMENT_REVIEW, searchDto.getSwlx());
                }else {
                    taskQuery.processVariableValueNotEquals(ProcessConstants.BPM_FORM_DOCUMENT_REVIEW, searchDto.getSwlx());
                }
            }

            List<Task> taskList = taskQuery.listPage(pageable.getPageNumber() * pageable.getPageSize(), pageable.getPageSize());
            if(CollectionUtils.isEmpty(taskList)) {
                return returnResult;
            }
            result = taskList.stream().collect(Collectors.groupingBy(Task::getProcessInstanceId, Collectors.mapping(FlowTaskInfoDto::new, Collectors.toList())));
        }else {
            //根据查询条件改成已办未完结和已办已完结
            ResultJson resultJson = flowTaskService.finishedList(pageable.getPageNumber(), pageable.getPageSize(), processDefinitionKey, searchDto);
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<FlowTaskDto> page = (com.baomidou.mybatisplus.extension.plugins.pagination.Page<FlowTaskDto>) resultJson.getData();
            result = page.getRecords().stream().collect(Collectors.groupingBy(FlowTaskDto::getProcInsId, Collectors.mapping(FlowTaskInfoDto::new, Collectors.toList())));
        }
        for(Map.Entry<String, List<FlowTaskInfoDto>> m : result.entrySet()) {
            returnResult.put(m.getKey(), m.getValue() == null ? null : m.getValue().get(0));
        }
        return returnResult;
    }

    /**
     * 检索条件
     * @param params
     * @return
     */
    private Specification<T> retrieveFilter(A07DocumentGwRetrieveQueryCriteria params) {
        return (root, query, cb) -> {
            //封装and查询条件
            ArrayList<Predicate> andList = new ArrayList<>();
            if(StringUtils.isNotBlank(params.getBt())) {
                //标题不为空
                andList.add(cb.like(root.get("bt"), "%" + params.getBt() + "%"));
            }
            if(StringUtils.isNotBlank(params.getBpmStatus())) {
                //流程状态不为空
                andList.add(cb.equal(root.get("bpmStatus"), params.getBpmStatus()));
            }
            if(params.getMyParticipate() != null && params.getMyParticipate()) {
                //我参与的
                andList.add(cb.isMember(cb.literal(SecurityUtils.getCurrentUsername()), root.<Collection<String>>get("participateUser")));//存入条件集合里
            }
            if(StringUtils.isNotBlank(params.getStartTime()) && StringUtils.isNotBlank(params.getEndTime())) {
                //自定义时间
                andList.add(cb.between(root.get("createTime").as(Timestamp.class), cn.hutool.core.date.DateUtil.parse(params.getStartTime(), "yyyy-MM-dd"), DateUtil.parse(params.getEndTime(), "yyyy-MM-dd")));
            }
            if(StringUtils.isNotBlank(params.getDz()) || params.getNh() != null
                    || params.getXh() != null) {
                if(StringUtils.isNotBlank(params.getDz())) {
                    setWh(root, cb, params, andList, "dz");
                }
                if(params.getNh() != null) {
                    setWh(root, cb, params, andList, "nh");
                }
                if(params.getXh() != null) {
                    setWh(root, cb, params, andList, "xh");
                }
            }
            return cb.and(andList.toArray(new Predicate[andList.size()]));
        };
    }

    /**
     * @param root
     * @param cb
     * @param params
     * @param andList
     * @param name
     */
    private void setWh(Root<T> root, CriteriaBuilder cb, A07DocumentGwRetrieveQueryCriteria params, ArrayList<Predicate> andList, String name) {
        if(params.getGwglType() == null) {
            return;
        }
        ArrayList<Predicate> orList = new ArrayList<>();
        if(0 == params.getGwglType() || 1 == params.getGwglType() || 2 == params.getGwglType()) {
            //0:发文 1:会签 2:领导批示
            Join<T, ReferenceNumber> gwwh = root.join("gwwh", JoinType.LEFT);
            if("dz".equals(name)) {
                //文号-代字不为空
                orList.add(cb.equal(gwwh.get("dz"), params.getDz()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }else if("nh".equals(name)) {
                //文号-年号不为空
                orList.add(cb.equal(gwwh.get("nh"), params.getNh()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }else if("xh".equals(name)) {
                //文号-年号不为空
                orList.add(cb.equal(gwwh.get("xh"), params.getXh()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }
        }else if(3 == params.getGwglType()) {
            //3:收文
            Join<T, ReferenceNumber> swbh = root.join("swbh", JoinType.LEFT);
            Join<T, ReferenceNumber> lwbh = root.join("lwbh", JoinType.LEFT);
            if("dz".equals(name)) {
                //文号-代字不为空
                orList.add(cb.equal(swbh.get("dz"), params.getDz()));
                orList.add(cb.equal(lwbh.get("dz"), params.getDz()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }else if("nh".equals(name)) {
                //文号-年号不为空
                orList.add(cb.equal(swbh.get("nh"), params.getNh()));
                orList.add(cb.equal(lwbh.get("nh"), params.getNh()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }else if("xh".equals(name)) {
                //文号-年号不为空
                orList.add(cb.equal(swbh.get("xh"), params.getXh()));
                orList.add(cb.equal(lwbh.get("xh"), params.getXh()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }
        }
    }

    @Override
    public List<Object> getLwdwList(String lwdw) {
        List<A07DocumentSw> swList = a07DocumentSwRepository.findByLwdwContains(lwdw);
        List<Object> list = new ArrayList<>();
        if(swList.size() == 0) {
            return list;
        }
        Set<String> lwdwSet = new HashSet<>();
        swList.stream().forEach(p -> {
            lwdwSet.add(p.getLwdw());
        });

        lwdwSet.forEach(p -> {
            Map<String, String> map = new HashMap<>();
            map.put("value", p);
            list.add(map);
        });

        return list;
    }

    @Override
    public ResultJson unitSwToInSw(String procInstId, Long appId) {
        Map<String, Object> appIdAndFormData = gwService.getAppIdAndFormData(procInstId);
        if(CollUtil.isEmpty(appIdAndFormData)) {
            throw new BadRequestException("表单数据为空！");
        }
        IdClass idClass = new IdClass();
        ApplicationDto byId = applicationService.getById(appId);
        FormTemplate template = formTemplateService.getById(byId.getPcTemplateId());

        idClass.setTargetClassName(template.getClassName());
        idClass.setAppId(Long.valueOf(appIdAndFormData.get("appId").toString()));
        SwAllView formData = Convert.convert(SwAllView.class, appIdAndFormData.get("formData"));
        Object o = tablesService.mapToTargetClass(idClass, formData);

        // 生成单位oa处理单返回到表单
        try {
            Field field = o.getClass().getDeclaredField("fj");
            field.setAccessible(true);
            List<StorageBiz> storageBizs = (List<StorageBiz>) field.get(o);
            String fileName = System.currentTimeMillis() + "_领导批示处理单.pdf";
            genPdf("文档", procInstId, formData.getBpmProcessKey(), fileName);
            InputStream inputStream = iStorageService.getInputStream("文档" + File.separator + fileName);
            FileItem fileItem = new DiskFileItemFactory().createItem("file", MediaType.ALL_VALUE, true, "领导批示处理单.pdf");
            OutputStream outputStream = null;
            try {
                outputStream = fileItem.getOutputStream();
                IOUtils.copy(inputStream, outputStream);
            }catch(IOException e) {
                e.printStackTrace();
            }
            LocalStorage save = iStorageService.create("领导批示处理单.pdf", new CommonsMultipartFile(fileItem));
            StorageBiz biz = new StorageBiz();
            biz.setStorageId(save.getId().toString());
            Where where = field.getAnnotation(Where.class);
            String bizType = null;
            if(where != null) {
                String clause = where.clause();
                bizType = clause.substring(clause.indexOf("'") + 1, clause.length() - 1);
            }
            biz.setBizType(bizType);
            storageBizs.add(biz);
        }catch(Exception e) {
            e.printStackTrace();
        }
        return ResultJson.generateResult(o);
    }


    public void genPdf(String filePath, String procInstId, String procKey, String filename) {
        ExportConfigure exportConfigure = new ExportConfigure() {
            @SneakyThrows
            @Override
            public OutputStream getOutputStream() {
                File pdfFile = new File(properties.getPath().getPath() + filePath + File.separator + filename);
                return new FileOutputStream(pdfFile);
            }

            @Override
            public String getFile() {
                PrintTemplate printTemplate = printTemplateService.queryByTemplateKey(procKey);
                if(Objects.nonNull(printTemplate)) {
                    return printTemplate.getFileName();
                }
                return null;
            }

            @Override
            public Map<String, Object> getParameters() {
                Map<String, Object> params = Maps.newHashMap();
                params.put("procInstId", procInstId);
                return params;
            }
        };
        exportManager.exportPdf(exportConfigure);
    }


    @Override
    public ResultJson inSwToInFw(String procInstId, Long appId) {

        Map<String, Object> appIdAndFormData = gwService.getAppIdAndFormData(procInstId);
        if(CollUtil.isEmpty(appIdAndFormData)) {
            throw new BadRequestException("表单数据为空！");
        }
        IdClass idClass = new IdClass();
        ApplicationDto byId = applicationService.getById(appId);
        FormTemplate template = formTemplateService.getById(byId.getPcTemplateId());

        idClass.setTargetClassName(template.getClassName());
        idClass.setAppId(Long.valueOf(appIdAndFormData.get("appId").toString()));
        Object formData = appIdAndFormData.get("formData");
        Object data = tablesService.mapToTargetClass(idClass, formData);

        try {
            JSONObject jsonObject = JSON.parseObject(formData.toString());
            JSONArray jsonArray = new JSONArray();
            JSONArray yjLdqp = JSONArray.parseArray(jsonObject.get("yjLdqp").toString());
            if(Objects.nonNull(yjLdqp)) {
                forJsonArray(jsonArray, yjLdqp);
            }
            JSONArray yjNb = JSONArray.parseArray(jsonObject.get("yjNb").toString());
            if(Objects.nonNull(yjNb)) {
                forJsonArray(jsonArray, yjNb);
            }
            JSONArray yjBlqk = JSONArray.parseArray(jsonObject.get("yjBlqk").toString());
            if(Objects.nonNull(yjBlqk)) {
                forJsonArray(jsonArray, yjBlqk);
            }
            Class<?> aClass = data.getClass();
            Field field = aClass.getDeclaredField("yjSw");
            field.setAccessible(true);
            field.set(data, jsonArray.toJSONString());
        }catch(Exception e) {
            e.printStackTrace();
        }
        return ResultJson.generateResult(data);
    }

    /**
     * 处理拼接意见
     */
    private void forJsonArray(JSONArray jsonArray, JSONArray data) {
        for(int i = 0; i < data.size(); i++) {
            jsonArray.add(data.get(i));
        }
    }

}
