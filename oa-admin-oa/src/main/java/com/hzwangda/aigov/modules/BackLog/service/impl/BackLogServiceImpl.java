package com.hzwangda.aigov.modules.BackLog.service.impl;

import com.google.common.collect.Maps;
import com.hzwangda.aigov.modules.BackLog.service.BackLogService;
import com.wangda.oa.backlog.base.SecurityUtil;
import com.wangda.oa.backlog.mapper.WdBacklogMapper;
import com.wangda.oa.modules.workflow.repository.workflow.BpmTaskUserReadRepository;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class BackLogServiceImpl implements BackLogService {
    @Resource
    private WdBacklogMapper wdBacklogMapper;
    @Resource
    private BpmTaskUserReadRepository readRepository;

    @Override
    public Map<String, Object> getCountByWaitBacklog() {
        List<Map<String, Object>> list = wdBacklogMapper.getModuleCountByWaitBacklog(SecurityUtil.getUsername());
        Map map = Maps.newHashMap();
        //待办数量
        if(list != null && list.size() > 0) {
            int backLogAllSize = 0;
            int number;
            for(Iterator iterator = list.iterator(); iterator.hasNext(); backLogAllSize += number) {
                Map<String, Object> objectMap = (Map) iterator.next();
                number = 0;
                if(Objects.nonNull(objectMap.get("module_code"))) {
                    if(Objects.nonNull(objectMap.get("number"))) {
                        number = ((Long) objectMap.get("number")).intValue();
                    }
                    //map.put(objectMap.get("module_code"), number);
                }
            }
            map.put("backLogAllSize", backLogAllSize);
        }
        int unitBackLogAllSize = 0;
        String deptUserName = SecurityUtils.getBindDeptUserNameByNotify();
        if(StringUtils.isNotEmpty(deptUserName)) {
            List<Map<String, Object>> unitList = wdBacklogMapper.getModuleCountByWaitBacklog(deptUserName);
            Map unitMap = Maps.newHashMap();
            //单位待办数量
            if(unitList != null && unitList.size() > 0) {
                int number;
                for(Iterator iterator = unitList.iterator(); iterator.hasNext(); unitBackLogAllSize += number) {
                    Map<String, Object> objectMap = (Map) iterator.next();
                    number = 0;
                    if(Objects.nonNull(objectMap.get("module_code"))) {
                        if(Objects.nonNull(objectMap.get("number"))) {
                            number = ((Long) objectMap.get("number")).intValue();
                        }
                        //map.put(objectMap.get("module_code"), number);
                    }
                }
            }
        }
        // 待阅数量
        Integer toBeReadAllSize = readRepository.countByAssigneeAndLastReadTimeIsNull(SecurityUtils.getCurrentUsername());

        //单位待办数量
        map.put("unitAllSize", unitBackLogAllSize);
        //待阅数量
        map.put("toBeReadAllSize", toBeReadAllSize);
        //会议/通知数量
        map.put("meetingAllSize", 0);
        return map;
    }
}
