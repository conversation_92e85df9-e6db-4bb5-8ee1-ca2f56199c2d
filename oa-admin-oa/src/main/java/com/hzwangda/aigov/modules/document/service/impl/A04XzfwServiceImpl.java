package com.hzwangda.aigov.modules.document.service.impl;

import cn.hutool.core.date.DateUtil;
import com.hzwangda.aigov.modules.document.service.A04XzfwService;
import com.hzwangda.aigov.modules.form.utils.ExportUtil;
import com.hzwangda.aigov.oa.domain.A04JobEntry;
import com.hzwangda.aigov.oa.repository.A04JobEntryRepository;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;

@Service
public class A04XzfwServiceImpl implements A04XzfwService {
    @Autowired
    private A04JobEntryRepository a04JobEntryRepository;

    @Override
    public void exportAll(HttpServletRequest request, HttpServletResponse response) throws IOException {
        List<A04JobEntry> list = a04JobEntryRepository.findAllByJobEndTimeAfter(new Date());
        if (list.size() == 0) {
            return;
        }

        ServletOutputStream out = response.getOutputStream();
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet();
        XSSFRow titleRow = sheet.createRow(0);
        try {
            for (int i = 0; i < list.size(); i++) {
                XSSFCellStyle cellStyle = ExportUtil.getBasicCellStyle(workbook);
                titleRow.setRowStyle(cellStyle);
                // 处理表头
                if (i == 0) {
                    titleRow.createCell(0).setCellValue("姓名");
                    titleRow.createCell(1).setCellValue("性别");
                    titleRow.createCell(2).setCellValue("身份证号");
                    titleRow.createCell(3).setCellValue("联系电话");
                    titleRow.createCell(4).setCellValue("入职类型");
                    titleRow.createCell(5).setCellValue("原单位");
                    titleRow.createCell(6).setCellValue("职务职称");
                    titleRow.createCell(7).setCellValue("政治面貌");
                    titleRow.createCell(8).setCellValue("入职处室");
                    titleRow.createCell(9).setCellValue("开始时间");
                    titleRow.createCell(10).setCellValue("结束时间");
                    titleRow.createCell(11).setCellValue("办公室门牌号");
                    titleRow.createCell(12).setCellValue("办公室座机号");
                }

                // 处理用户数据
                XSSFRow row = sheet.createRow(i + 1);
                row.createCell(0).setCellValue(list.get(i).getName());
                row.createCell(1).setCellValue(list.get(i).getGender());
                row.createCell(2).setCellValue(list.get(i).getIdCard());
                row.createCell(3).setCellValue(list.get(i).getMobile());
                row.createCell(4).setCellValue(list.get(i).getEntryType());
                row.createCell(5).setCellValue(list.get(i).getFromOrganization());
                row.createCell(6).setCellValue(list.get(i).getJobTitle());
                row.createCell(7).setCellValue(list.get(i).getParty());
                row.createCell(8).setCellValue(list.get(i).getDept());
                row.createCell(9).setCellValue(DateUtil.format(list.get(i).getJobBeginTime(), "yyyy-MM-hh"));
                row.createCell(10).setCellValue(DateUtil.format(list.get(i).getJobEndTime(), "yyyy-MM-hh"));
                row.createCell(11).setCellValue(list.get(i).getOfficeRoomNumber());
                row.createCell(12).setCellValue(list.get(i).getOfficeTel());
            }

            response.setCharacterEncoding(request.getCharacterEncoding());
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("入职在职人员" +
                    "", request.getCharacterEncoding()));

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            workbook.write(out);
            out.flush();
            workbook.close();
            out.close();
        }

    }
}
