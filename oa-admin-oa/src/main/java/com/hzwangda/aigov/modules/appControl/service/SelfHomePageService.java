package com.hzwangda.aigov.modules.appControl.service;


import com.hzwangda.aigov.modules.appControl.domain.Z08SelfHomePage;
import com.hzwangda.aigov.modules.appControl.dto.SelfHomePageCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 应用基础设置服务
 * <AUTHOR>
 * @date 2022-08-03
 */
public interface SelfHomePageService {
    /**
     * 新增或修改应用基础设置
     * @param z08SelfHomePage 应用
     * @return Z08SelfHomePage
     */
    Z08SelfHomePage addOrUpdateBasics(Z08SelfHomePage z08SelfHomePage);

    /**
     * 获取应用基础设置
     * @param id 应用id
     * @return Z08SelfHomePage 应用
     */
    Z08SelfHomePage getById(Long id);

    /**
     * 查询应用列表分页
     * @param criteria 请求字段
     * @param pageable 分页
     * @return List
     */
    Page<Z08SelfHomePage> queryListPage(SelfHomePageCriteria criteria, Pageable pageable);

    /**
     * 删除应用
     * @param id 表应用id
     * @return boolean
     */
    Boolean delete(Long id);


    /**
     * 根据应用名称查询应用
     * @param z08SelfHomePageName 应用名称
     * @return Z08SelfHomePage 应用
     */
    List<Z08SelfHomePage> getByZ08SelfHomePageName(String z08SelfHomePageName);

    /**
     * 根据类别查询应用
     * @param type 应用名称
     * @return Z08SelfHomePage 应用
     */
    List<Z08SelfHomePage> getByType(String type);

    /**
     * 根据应用名称查询应用
     * @param applicationName 应用名称
     * @return Application 应用
     */
    List<Z08SelfHomePage> getByApplicationName(String applicationName);

    String getHomePageLink(String systemType);
}
