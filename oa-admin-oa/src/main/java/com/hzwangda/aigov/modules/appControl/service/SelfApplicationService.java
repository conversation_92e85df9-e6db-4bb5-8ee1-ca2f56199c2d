package com.hzwangda.aigov.modules.appControl.service;


import com.hzwangda.aigov.modules.appControl.domain.Z08SelfApplication;
import com.hzwangda.aigov.modules.appControl.dto.SelfApplicationCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 应用基础设置服务
 * <AUTHOR>
 * @date 2022-08-03
 */
public interface SelfApplicationService {
    /**
     * 新增或修改应用基础设置
     * @param Z08SelfApplication 应用
     * @return Z08SelfApplication
     */
    Z08SelfApplication addOrUpdateBasics(Z08SelfApplication Z08SelfApplication);

    /**
     * 获取应用基础设置
     * @param id 应用id
     * @return Z08SelfApplication 应用
     */
    Z08SelfApplication getById(Long id);

    /**
     * 查询应用列表分页
     * @param criteria 请求字段
     * @param pageable 分页
     * @return List
     */
    Page<Z08SelfApplication> queryListPage(SelfApplicationCriteria criteria, Pageable pageable);

    /**
     * 删除应用
     * @param id 表应用id
     * @return boolean
     */
    Boolean delete(Long id);


    /**
     * 根据应用名称查询应用
     * @param Z08SelfApplicationName 应用名称
     * @return Z08SelfApplication 应用
     */
    List<Z08SelfApplication> getByZ08SelfApplicationName(String Z08SelfApplicationName);

    /**
     * 根据类别查询应用
     * @param type 应用名称
     * @return Z08SelfApplication 应用
     */
    List<Z08SelfApplication> getByType(String type);

    /**
     * 根据应用名称查询应用
     * @param applicationName 应用名称
     * @return Application 应用
     */
    List<Z08SelfApplication> getByApplicationName(String applicationName);

}
