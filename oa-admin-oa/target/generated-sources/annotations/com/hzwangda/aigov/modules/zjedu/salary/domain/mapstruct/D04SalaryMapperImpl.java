package com.hzwangda.aigov.modules.zjedu.salary.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.salary.domain.dto.D04SalaryDto;
import com.hzwangda.aigov.modules.zjedu.salary.domain.entity.D04Salary;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class D04SalaryMapperImpl implements D04SalaryMapper {

    @Override
    public D04SalaryDto toDto(D04Salary entity) {
        if ( entity == null ) {
            return null;
        }

        D04SalaryDto d04SalaryDto = new D04SalaryDto();

        d04SalaryDto.setCreateBy( entity.getCreateBy() );
        d04SalaryDto.setCreateTime( entity.getCreateTime() );
        d04SalaryDto.setUpdateTime( entity.getUpdateTime() );
        d04SalaryDto.setUpdatedBy( entity.getUpdatedBy() );
        d04SalaryDto.setBizMsgId( entity.getBizMsgId() );
        d04SalaryDto.setDetail( entity.getDetail() );
        d04SalaryDto.setId( entity.getId() );
        d04SalaryDto.setPaySalary( entity.getPaySalary() );
        d04SalaryDto.setSeeStatus( entity.getSeeStatus() );
        d04SalaryDto.setSendSalary( entity.getSendSalary() );
        d04SalaryDto.setSendStatus( entity.getSendStatus() );
        d04SalaryDto.setStaffName( entity.getStaffName() );
        d04SalaryDto.setStaffNo( entity.getStaffNo() );

        return d04SalaryDto;
    }

    @Override
    public List<D04SalaryDto> toDto(List<D04Salary> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<D04SalaryDto> list = new ArrayList<D04SalaryDto>( entityList.size() );
        for ( D04Salary d04Salary : entityList ) {
            list.add( toDto( d04Salary ) );
        }

        return list;
    }

    @Override
    public D04Salary toEntity(D04SalaryDto dto) {
        if ( dto == null ) {
            return null;
        }

        D04Salary d04Salary = new D04Salary();

        d04Salary.setCreateBy( dto.getCreateBy() );
        d04Salary.setCreateTime( dto.getCreateTime() );
        d04Salary.setUpdateTime( dto.getUpdateTime() );
        d04Salary.setUpdatedBy( dto.getUpdatedBy() );
        d04Salary.setBizMsgId( dto.getBizMsgId() );
        d04Salary.setDetail( dto.getDetail() );
        d04Salary.setId( dto.getId() );
        d04Salary.setPaySalary( dto.getPaySalary() );
        d04Salary.setSeeStatus( dto.getSeeStatus() );
        d04Salary.setSendSalary( dto.getSendSalary() );
        d04Salary.setSendStatus( dto.getSendStatus() );
        d04Salary.setStaffName( dto.getStaffName() );
        d04Salary.setStaffNo( dto.getStaffNo() );

        return d04Salary;
    }

    @Override
    public List<D04Salary> toEntity(List<D04SalaryDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<D04Salary> list = new ArrayList<D04Salary>( dtoList.size() );
        for ( D04SalaryDto d04SalaryDto : dtoList ) {
            list.add( toEntity( d04SalaryDto ) );
        }

        return list;
    }
}
