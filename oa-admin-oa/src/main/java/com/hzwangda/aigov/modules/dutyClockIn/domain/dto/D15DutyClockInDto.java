package com.hzwangda.aigov.modules.dutyClockIn.domain.dto;

import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("值班打卡")
public class D15DutyClockInDto extends BaseDomain {

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "真实姓名")
    private String nickName;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "打卡时间")
    @Temporal(TemporalType.TIMESTAMP)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date clockInTime;

    @ApiModelProperty(value = "打卡地址")
    private String address;

    @ApiModelProperty(value = "打卡图片")
    private List<StorageBiz> fj;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "地图json")
    private String geolocationJSON;
}
