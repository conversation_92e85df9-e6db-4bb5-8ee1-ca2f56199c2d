package com.hzwangda.aigov.modules.document.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.document.dto.Z08DocumentSwTypeDto;
import com.hzwangda.aigov.modules.document.dto.Z08DocumentTypeQueryCriteria;
import com.hzwangda.aigov.modules.document.dto.mapstruct.Z08DocumentSwTypeMapper;
import com.hzwangda.aigov.modules.document.entity.Z08DocumentSwType;
import com.hzwangda.aigov.modules.document.repository.Z08DocumentSwTypeRepository;
import com.hzwangda.aigov.modules.document.service.Z08DocumentSwTypeService;
import com.hzwangda.aigov.modules.workflow.repository.ReferenceNumberRepository;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.system.service.DeptService;
import com.wangda.oa.modules.system.service.dto.RedisDto;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Tuple;
import javax.persistence.criteria.Predicate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: zhangzhanlong
 * @date: 2022/10/26 9:55
 * @description:
 */
@Service
@RequiredArgsConstructor
public class Z08DocumentSwTypeServiceImpl implements Z08DocumentSwTypeService {
    private final Z08DocumentSwTypeRepository z08DocumentSwTypeRepository;
    private final Z08DocumentSwTypeMapper documentTypeMapper;
    private final DeptService deptService;
    private final ReferenceNumberRepository referenceNumberRepository;

    @Override
    @Transactional
    public Object saveDocumentType(Z08DocumentSwType documentType) {
        Z08DocumentSwType save;
        Z08DocumentSwType byDzAndAppId = getByDzAndDeptId(documentType.getDz(), documentType.getDeptId());
        if (ObjectUtil.isNotEmpty(documentType.getId())) {
            if (Objects.nonNull(byDzAndAppId)) {
                if (!byDzAndAppId.getId().equals(documentType.getId())) {
                    throw new BadRequestException("该代字已存在！");
                }
            }
            Z08DocumentSwType documentByid = z08DocumentSwTypeRepository.findById(documentType.getId()).orElseGet(Z08DocumentSwType::new);
            documentByid.copy(documentType);
            save = z08DocumentSwTypeRepository.save(documentByid);
        } else {
            if (Objects.nonNull(byDzAndAppId)) {
                throw new BadRequestException("该代字已存在！");
            }
            save = z08DocumentSwTypeRepository.save(documentType);
        }
        return save;
    }

    @Override
    public Object getDocumentTypePage(Z08DocumentTypeQueryCriteria criteria, Pageable pageable) {
        Specification<Z08DocumentSwType> sp = (root, cq, cb) -> {
            Predicate pe;
            if (StringUtils.isBlank(criteria.getDeptId())) {
                pe = cb.isNull(root.get("deptId"));
            } else {
                pe = cb.or(
                        cb.isNull(root.get("deptId")),
                        cb.equal(root.get("deptId"), criteria.getDeptId())
                );
            }
            if (StringUtils.isNotEmpty(criteria.getSearchKeys())) {
                Predicate dz = cb.or(cb.like(root.get("dz"), "%" + criteria.getSearchKeys() + "%"));
                Predicate deptName = cb.or(cb.like(root.get("deptName"), "%" + criteria.getSearchKeys() + "%"));
                pe = cb.and(pe, cb.and(dz, deptName));
            }

            cq.orderBy(
                    cb.asc(root.get("sort"))
            );
            return pe;
        };
        Page<Z08DocumentSwType> all = z08DocumentSwTypeRepository.findAll(sp, pageable);
        all.stream().forEach(z08DocumentType -> {
            String deptId = z08DocumentType.getDeptId();
            if (StringUtils.isEmpty(deptId)) {
                deptId = criteria.getDeptId();
            }
            if (StringUtils.isNotEmpty(deptId)) {
                List<Tuple> maxXh = referenceNumberRepository.findMaxXhByDzAndBelongToDeptGroupByNh(z08DocumentType.getDz(), deptId);
                if (maxXh != null && maxXh.size() > 0) {
                    Map map = new HashMap();
                    maxXh.stream().forEach(m -> {
                        if (m.get("nh") != null && m.get("max") != null) {
                            map.put(m.get("nh").toString(), m.get("max"));
                        }
                    });
                    z08DocumentType.setMaxXh(JSONObject.toJSONString(map));
                }
            }
        });
        return all;
    }

    @Override
    public Object getDocumentTypeList(Z08DocumentTypeQueryCriteria criteria) {
        Specification<Z08DocumentSwType> sp = (root, cq, cb) -> {
            Predicate pe = QueryHelp.getPredicate(root, criteria, cb);
            if (StringUtils.isNotEmpty(criteria.getSearchKeys())) {
                Predicate dz = cb.or(cb.like(root.get("dz"), "%" + criteria.getSearchKeys() + "%"));
                Predicate deptName = cb.or(cb.like(root.get("deptName"), "%" + criteria.getSearchKeys() + "%"));
                Predicate applicationName = cb.or(cb.like(root.get("applicationName"), "%" + criteria.getSearchKeys() + "%"));
                pe = cb.and(pe, cb.and(dz, deptName, applicationName));
            }
            if (Objects.isNull(criteria.getDeptId())) {
                pe = cb.and(pe, cb.isNull(root.get("deptId")));
            }
            cq.orderBy(
                    cb.asc(root.get("sort"))
            );
            return pe;
        };
        List<Z08DocumentSwType> all = z08DocumentSwTypeRepository.findAll(sp);
        List<Z08DocumentSwTypeDto> collect = all.stream().map(documentTypeMapper::toDto).collect(Collectors.toList());
        return collect;
    }

    @Override
    public Object getOneById(Long id) {
        Z08DocumentSwType Z08DocumentSwType = z08DocumentSwTypeRepository.findById(id).orElseGet(Z08DocumentSwType::new);
        return Z08DocumentSwType;
    }

    @Override
    public void deleteById(Long id) {
        z08DocumentSwTypeRepository.deleteById(id);
    }

    @Override
    public Object getDzList(String dz) {
        List<Z08DocumentSwType> byDzLike = z08DocumentSwTypeRepository.findByDzLike("%" + dz + "%");
        return byDzLike;
    }

    @Override
    public Z08DocumentSwType getByDzAndDeptId(String dz, String deptId) {
        return z08DocumentSwTypeRepository.findByDzAndDeptId(dz, deptId);
    }

    @Override
    public List<Z08DocumentSwType> getByDeptId(String deptId) {
        if (StringUtils.isBlank(deptId)) {
            RedisDto convert = Convert.convert(RedisDto.class, deptService.queryCurrentUnit());
            deptId = convert.getBelongToDept();
        }
        List<Z08DocumentSwType> swTypeList = z08DocumentSwTypeRepository.findByDeptId(deptId);
        if (CollUtil.isEmpty(swTypeList)) {
            swTypeList = z08DocumentSwTypeRepository.findByDeptIdIsNull();
        }
        return swTypeList;
    }
}
