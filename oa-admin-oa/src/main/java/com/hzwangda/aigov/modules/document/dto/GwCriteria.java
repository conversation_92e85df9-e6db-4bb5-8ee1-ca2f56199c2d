package com.hzwangda.aigov.modules.document.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class GwCriteria {
    @NotNull
    Integer year;
    Integer gwType;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    List<Date> createTime;
}
