package com.hzwangda.aigov.modules.addresslist.service;

import com.hzwangda.aigov.modules.addresslist.domain.criteria.PersonAddressListQueryCriteria;
import com.hzwangda.aigov.modules.addresslist.domain.dto.PersonAddressListDto;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.extension.bo.UserListBO;
import com.wangda.oa.modules.extension.dto.org.UserListDto;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface PersonAddressListService {
    Object queryList(PersonAddressListQueryCriteria criteria, Pageable pageable);

    Object queryOne(Long id);

    Object create(PersonAddressListDto addressListDto);

    Object update(PersonAddressListDto addressListDto);

    Object del(Long id);

    Object queryModule(PersonAddressListQueryCriteria criteria);

    ResultJson<List<UserListDto>> getUserList(UserListBO bo);
}
