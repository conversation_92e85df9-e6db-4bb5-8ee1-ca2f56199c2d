/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.modules.document.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-06-25
 **/
@Data
public class A07AllSwQueryCriteria {

    @Query(joinName = "gwwh", propName = "nh")
    @ApiModelProperty(value = "年份")
    private String year;

    @Query(joinName = "gwwh")
    @ApiModelProperty(value = "代字")
    private String dz;

    @ApiModelProperty(value = "标题")
    private String bt;

    @Query(type = Query.Type.INNER_LIKE)
    @ApiModelProperty(value = "来文单位")
    private String lwdw;

    @ApiModelProperty(value = "收文编号")
    private String swbm;

    @Query(type = Query.Type.INNER_LIKE, propName = "lwwh")
    @ApiModelProperty(value = "来文文号序号")
    private String lwxh;

    @Query(joinName = "gwwh", propName = "xh")
    @ApiModelProperty(value = "收文文号序号")
    private String swxh;

    @ApiModelProperty(value = "待办创建开始结束时间")
    private List<String> timeRange;

    @Query(propName = "bpmProcessKey")
    @ApiModelProperty(value = "公文类型key")
    private String processDefinitionKey;

    @Query(propName = "bpmStatus")
    @ApiModelProperty(value = "流程状态")
    private String procStatus;

    @ApiModelProperty(value = "收文类型")
    private String swlx;

    @Query
    @ApiModelProperty(value = "所属单位")
    private String belongToDept;

    @ApiModelProperty(value = "拟稿日期")
    private String ngrqEnd;

    @ApiModelProperty(value = "用户名")
    private String username;


    @ApiModelProperty(value = "当前办理人")
    private String currentAssignee;
}
