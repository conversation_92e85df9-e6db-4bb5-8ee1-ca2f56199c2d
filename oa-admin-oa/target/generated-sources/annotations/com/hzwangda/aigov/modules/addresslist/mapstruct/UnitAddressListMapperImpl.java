package com.hzwangda.aigov.modules.addresslist.mapstruct;

import com.hzwangda.aigov.modules.addresslist.domain.dto.UnitAddressListDto;
import com.hzwangda.aigov.modules.addresslist.domain.entity.UnitAddressList;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class UnitAddressListMapperImpl implements UnitAddressListMapper {

    @Override
    public UnitAddressListDto toDto(UnitAddressList entity) {
        if ( entity == null ) {
            return null;
        }

        UnitAddressListDto unitAddressListDto = new UnitAddressListDto();

        unitAddressListDto.setCreateBy( entity.getCreateBy() );
        unitAddressListDto.setCreateTime( entity.getCreateTime() );
        unitAddressListDto.setUpdateTime( entity.getUpdateTime() );
        unitAddressListDto.setUpdatedBy( entity.getUpdatedBy() );
        unitAddressListDto.setGroupId( entity.getGroupId() );
        unitAddressListDto.setGroupName( entity.getGroupName() );
        unitAddressListDto.setId( entity.getId() );
        unitAddressListDto.setJobName( entity.getJobName() );
        unitAddressListDto.setNickName( entity.getNickName() );
        unitAddressListDto.setOfficeTel( entity.getOfficeTel() );
        unitAddressListDto.setPhone( entity.getPhone() );
        unitAddressListDto.setRemark( entity.getRemark() );
        unitAddressListDto.setUnitId( entity.getUnitId() );
        unitAddressListDto.setUnitName( entity.getUnitName() );
        unitAddressListDto.setUsername( entity.getUsername() );

        return unitAddressListDto;
    }

    @Override
    public List<UnitAddressListDto> toDto(List<UnitAddressList> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<UnitAddressListDto> list = new ArrayList<UnitAddressListDto>( entityList.size() );
        for ( UnitAddressList unitAddressList : entityList ) {
            list.add( toDto( unitAddressList ) );
        }

        return list;
    }

    @Override
    public UnitAddressList toEntity(UnitAddressListDto dto) {
        if ( dto == null ) {
            return null;
        }

        UnitAddressList unitAddressList = new UnitAddressList();

        unitAddressList.setCreateBy( dto.getCreateBy() );
        unitAddressList.setCreateTime( dto.getCreateTime() );
        unitAddressList.setUpdateTime( dto.getUpdateTime() );
        unitAddressList.setUpdatedBy( dto.getUpdatedBy() );
        unitAddressList.setGroupId( dto.getGroupId() );
        unitAddressList.setGroupName( dto.getGroupName() );
        unitAddressList.setId( dto.getId() );
        unitAddressList.setJobName( dto.getJobName() );
        unitAddressList.setNickName( dto.getNickName() );
        unitAddressList.setOfficeTel( dto.getOfficeTel() );
        unitAddressList.setPhone( dto.getPhone() );
        unitAddressList.setRemark( dto.getRemark() );
        unitAddressList.setUnitId( dto.getUnitId() );
        unitAddressList.setUnitName( dto.getUnitName() );
        unitAddressList.setUsername( dto.getUsername() );

        return unitAddressList;
    }

    @Override
    public List<UnitAddressList> toEntity(List<UnitAddressListDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<UnitAddressList> list = new ArrayList<UnitAddressList>( dtoList.size() );
        for ( UnitAddressListDto unitAddressListDto : dtoList ) {
            list.add( toEntity( unitAddressListDto ) );
        }

        return list;
    }
}
