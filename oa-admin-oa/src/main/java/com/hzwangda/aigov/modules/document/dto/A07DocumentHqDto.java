package com.hzwangda.aigov.modules.document.dto;

import com.hzwangda.aigov.modules.document.entity.SysDeptUserMain;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 动态信息-会签
 *
 * <AUTHOR>
 * @date 2021/6/25 下午4:18
 */
@Data
public class A07DocumentHqDto extends BaseBpmDomain {

    @ApiModelProperty(value = "文号")
    private ReferenceNumber gwwh;

    @ApiModelProperty(value = "缓急(普通,急)")
    private String hj;

    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "签发-json格式")
    private String yjQf;

    @ApiModelProperty(value = "会签-json格式")
    private String yjHq;

    @ApiModelProperty(value = "分管主任审核意见-json格式")
    private String yjFgzrsh;

    @ApiModelProperty(value = "秘书科审核意见-json格式")
    private String yjMsksh;

    @ApiModelProperty(value = "处室审核意见-json格式")
    private String yjCssh;

    @ApiModelProperty(value = "拟办意见-json格式")
    private String yjNb;

    @ApiModelProperty(value = "正文")
    private StorageBizDto zw;

    @ApiModelProperty(value = "附件")
    private List<StorageBizDto> fj;

    @ApiModelProperty(value = "主送单位")
    private SysDeptUserMain zsdw;

    @ApiModelProperty(value = "抄送单位")
    private SysDeptUserMain csdw;

    @ApiModelProperty(value = "拟稿单位")
    private String ngdw;

    @ApiModelProperty(value = "备注")
    private String bz;

    @ApiModelProperty(value = "流程任务相关信息")
    private FlowTaskInfoDto flowTaskInfoDto;
}
