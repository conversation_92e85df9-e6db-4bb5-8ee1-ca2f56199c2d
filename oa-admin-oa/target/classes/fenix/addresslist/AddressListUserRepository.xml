<?xml version="1.0" encoding="UTF-8"?>
<fenixs namespace="com.hzwangda.aigov.modules.addresslist.repository.AddressListUserRepository">
    <fenix id="getUserListByDeptId">
        SELECT us.id as userId,
        us.dept.id as deptId,
        us.username as username,
        us.nickName as nickName,
        us.email as email,
        us.phone as phone
        FROM User us
        WHERE us.enabled = 1
        <andLike field="us.nickName" name="name" value="name" match="name != empty" />
        <andIn field="dept.id" name="collect" value="collect" match="collect != empty" />
        ORDER BY dept.deptSort asc , us.sort asc
    </fenix>
</fenixs>
