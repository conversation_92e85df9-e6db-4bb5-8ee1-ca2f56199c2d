<?xml version="1.0" encoding="UTF-8"?>
<ureport>
    <cell expand="None" name="A1" row="1" col="1" col-span="6">
        <cell-style font-size="24" forecolor="255,0,0" bold="true" align="center" valign="middle"
                    line-height="1"></cell-style>
        <simple-value><![CDATA[会议详情单]]></simple-value>
    </cell>
    <cell expand="None" name="A2" row="2" col="1">
        <cell-style font-size="12" forecolor="255,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <simple-value><![CDATA[标题]]></simple-value>
    </cell>
    <cell expand="Down" name="B2" row="2" col="2" col-span="5">
        <cell-style font-size="11" forecolor="43,34,34" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <dataset-value dataset-name="loadMeetingSeaData" aggregate="group" property="subject" order="none"
                       mapping-type="simple"></dataset-value>
    </cell>
    <cell expand="None" name="A3" row="3" col="1">
        <cell-style font-size="12" forecolor="255,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <simple-value><![CDATA[会议时间]]></simple-value>
    </cell>
    <cell expand="Down" name="B3" row="3" col="2" col-span="2">
        <cell-style font-size="11" forecolor="0,0,0" font-family="宋体" format="yyyy年MM月dd日 HH:mm:ss" align="center"
                    valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <dataset-value dataset-name="loadMeetingSeaData" aggregate="group" property="startDate" order="none"
                       mapping-type="simple"></dataset-value>
    </cell>
    <cell expand="None" name="D3" row="3" col="4">
        <cell-style font-size="12" forecolor="255,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <simple-value><![CDATA[会议经费]]></simple-value>
    </cell>
    <cell expand="Down" name="E3" row="3" col="5" col-span="2">
        <cell-style font-size="12" forecolor="0,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <dataset-value dataset-name="loadMeetingSeaData" aggregate="group" property="meetingMoney" order="none"
                       mapping-type="simple"></dataset-value>
    </cell>
    <cell expand="None" name="A4" row="4" col="1">
        <cell-style font-size="12" forecolor="255,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <simple-value><![CDATA[会议类型]]></simple-value>
    </cell>
    <cell expand="Down" name="B4" row="4" col="2" col-span="2">
        <cell-style font-size="11" forecolor="0,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <dataset-value dataset-name="loadMeetingSeaData" aggregate="group" property="wfType" order="none"
                       mapping-type="simple"></dataset-value>
    </cell>
    <cell expand="None" name="D4" row="4" col="4">
        <cell-style font-size="12" forecolor="255,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <simple-value><![CDATA[会议归属]]></simple-value>
    </cell>
    <cell expand="Down" name="E4" row="4" col="5" col-span="2">
        <cell-style font-size="12" forecolor="0,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <dataset-value dataset-name="loadMeetingSeaData" aggregate="group" property="ascription" order="none"
                       mapping-type="simple"></dataset-value>
    </cell>
    <cell expand="None" name="A5" row="5" col="1">
        <cell-style font-size="12" forecolor="255,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <simple-value><![CDATA[参会人员]]></simple-value>
    </cell>
    <cell expand="Down" name="B5" row="5" col="2" col-span="5">
        <cell-style font-size="11" forecolor="0,0,0" font-family="宋体" align="left" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <dataset-value dataset-name="loadMeetingSeaData" aggregate="group" property="attendancePeople" order="none"
                       mapping-type="simple"></dataset-value>
    </cell>
    <cell expand="None" name="A6" row="6" col="1">
        <cell-style font-size="12" forecolor="255,0,0" font-family="宋体" align="center" valign="middle" line-height="5">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <simple-value><![CDATA[主送单位]]></simple-value>
    </cell>
    <cell expand="Down" name="B6" row="6" col="2" col-span="5">
        <cell-style font-size="11" forecolor="0,0,0" font-family="宋体" align="left" valign="middle" line-height="5">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <dataset-value dataset-name="loadMeetingSeaData" aggregate="group" property="mainToms" order="none"
                       mapping-type="simple"></dataset-value>
    </cell>
    <cell expand="None" name="A7" row="7" col="1">
        <cell-style font-size="12" forecolor="255,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <simple-value><![CDATA[会议申报单位]]></simple-value>
    </cell>
    <cell expand="Down" name="B7" row="7" col="2" col-span="2">
        <cell-style font-size="11" forecolor="46,37,37" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <dataset-value dataset-name="loadMeetingSeaData" aggregate="group" property="mainOrg2" order="none"
                       mapping-type="simple"></dataset-value>
    </cell>
    <cell expand="None" name="D7" row="7" col="4">
        <cell-style font-size="12" forecolor="255,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <simple-value><![CDATA[紧急程度]]></simple-value>
    </cell>
    <cell expand="Down" name="E7" row="7" col="5" col-span="2">
        <cell-style font-size="11" forecolor="46,37,37" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <dataset-value dataset-name="loadMeetingSeaData" aggregate="group" property="urgentLevel" order="none"
                       mapping-type="simple"></dataset-value>
    </cell>
    <cell expand="None" name="A8" row="8" col="1">
        <cell-style font-size="12" forecolor="255,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <simple-value><![CDATA[会议时长]]></simple-value>
    </cell>
    <cell expand="Down" name="B8" row="8" col="2" col-span="2">
        <cell-style font-size="11" forecolor="46,36,36" font-family="宋体" wrap-compute="false" align="center"
                    valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <dataset-value dataset-name="loadMeetingSeaData" aggregate="group" property="meetingPeriod" order="none"
                       mapping-type="simple"></dataset-value>
    </cell>
    <cell expand="None" name="D8" row="8" col="4">
        <cell-style font-size="12" forecolor="255,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <simple-value><![CDATA[是否视频会议]]></simple-value>
    </cell>
    <cell expand="Down" name="E8" row="8" col="5" col-span="2">
        <cell-style font-size="11" forecolor="46,36,36" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <dataset-value dataset-name="loadMeetingSeaData" aggregate="group" property="isNetMeeting" order="none"
                       mapping-type="simple"></dataset-value>
    </cell>
    <cell expand="None" name="A9" row="9" col="1">
        <cell-style font-size="12" forecolor="255,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <simple-value><![CDATA[联系人]]></simple-value>
    </cell>
    <cell expand="Down" name="B9" row="9" col="2" col-span="2">
        <cell-style font-size="11" forecolor="44,34,34" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <dataset-value dataset-name="loadMeetingSeaData" aggregate="group" property="fromPerson" order="none"
                       mapping-type="simple"></dataset-value>
    </cell>
    <cell expand="None" name="D9" row="9" col="4">
        <cell-style font-size="12" forecolor="255,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <simple-value><![CDATA[联系方式]]></simple-value>
    </cell>
    <cell expand="Down" name="E9" row="9" col="5" col-span="2">
        <cell-style font-size="11" forecolor="0,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <dataset-value dataset-name="loadMeetingSeaData" aggregate="group" property="fromPersonMobile" order="none"
                       mapping-type="simple"></dataset-value>
    </cell>
    <cell expand="None" name="A10" row="10" col="1">
        <cell-style font-size="12" forecolor="255,0,0" font-family="宋体" align="center" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <simple-value><![CDATA[附件管理]]></simple-value>
    </cell>
    <cell expand="Down" name="B10" row="10" col="2" col-span="5">
        <cell-style font-size="11" forecolor="41,32,32" font-family="宋体" align="left" valign="middle">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <dataset-value dataset-name="loadMeetingSeaData" aggregate="group" property="fileName" order="none"
                       mapping-type="simple"></dataset-value>
    </cell>
    <cell expand="None" name="A11" row="11" col="1">
        <cell-style font-size="12" forecolor="255,0,0" font-family="宋体" align="center" valign="middle" line-height="5">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <simple-value><![CDATA[备注]]></simple-value>
    </cell>
    <cell expand="Down" name="B11" row="11" col="2" col-span="5">
        <cell-style font-size="11" forecolor="0,0,0" font-family="宋体" align="left" valign="middle" line-height="5">
            <left-border width="1" style="solid" color="255,0,0"/>
            <right-border width="1" style="solid" color="255,0,0"/>
            <top-border width="1" style="solid" color="255,0,0"/>
            <bottom-border width="1" style="solid" color="255,0,0"/>
        </cell-style>
        <dataset-value dataset-name="loadMeetingSeaData" aggregate="group" property="remark" order="none"
                       mapping-type="simple"></dataset-value>
    </cell>
    <row row-number="1" height="47"/>
    <row row-number="2" height="33"/>
    <row row-number="3" height="33"/>
    <row row-number="4" height="33"/>
    <row row-number="5" height="33"/>
    <row row-number="6" height="101"/>
    <row row-number="7" height="53"/>
    <row row-number="8" height="53"/>
    <row row-number="9" height="53"/>
    <row row-number="10" height="36"/>
    <row row-number="11" height="92"/>
    <column col-number="1" width="75"/>
    <column col-number="2" width="75"/>
    <column col-number="3" width="75"/>
    <column col-number="4" width="75"/>
    <column col-number="5" width="75"/>
    <column col-number="6" width="75"/>
    <datasource name="MeetingSeaBean" type="spring" bean="meetingSeaBean">
        <dataset name="loadMeetingSeaData" type="bean" method="loadMeetingSeaData"
                 clazz="com.hzwangda.aigov.modules.ureport.ureportDto.A07MeetingSeaUreportDto">
            <field name="addtime"/>
            <field name="adduser"/>
            <field name="ascription"/>
            <field name="attach"/>
            <field name="attendancePeople"/>
            <field name="copyTo"/>
            <field name="copyToJson"/>
            <field name="createBy"/>
            <field name="createDate"/>
            <field name="creatorId"/>
            <field name="enabled"/>
            <field name="fileName"/>
            <field name="fj"/>
            <field name="fromPerson"/>
            <field name="fromPersonMobile"/>
            <field name="id"/>
            <field name="isNetMeeting"/>
            <field name="isPost"/>
            <field name="mainOrg2"/>
            <field name="mainTo"/>
            <field name="mainToJson"/>
            <field name="mainToms"/>
            <field name="meetingMoney"/>
            <field name="meetingPeriod"/>
            <field name="modifiedBy"/>
            <field name="modifiedDate"/>
            <field name="modifiedId"/>
            <field name="modifytime"/>
            <field name="modifyuser"/>
            <field name="prop1"/>
            <field name="prop2"/>
            <field name="prop3"/>
            <field name="prop4"/>
            <field name="prop5"/>
            <field name="remark"/>
            <field name="sendStatus"/>
            <field name="startDate"/>
            <field name="status"/>
            <field name="subject"/>
            <field name="urgentLevel"/>
            <field name="version"/>
            <field name="wfType"/>
        </dataset>
    </datasource>
    <paper type="A4" left-margin="71" right-margin="71"
           top-margin="71" bottom-margin="71" paging-mode="fitpage" fixrows="0"
           width="595" height="842" orientation="portrait" html-report-align="center" bg-image=""
           html-interval-refresh-value="0" column-enabled="false"></paper>
    <search-form form-position="up"/>
</ureport>