package com.hzwangda.aigov.modules.matter.service.mapstruct;

import com.hzwangda.aigov.modules.matter.domain.MatterEvaluate;
import com.hzwangda.aigov.modules.matter.service.dto.MatterEvaluateDto;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class MatterEvaluateMapperImpl implements MatterEvaluateMapper {

    @Override
    public MatterEvaluateDto toDto(MatterEvaluate entity) {
        if ( entity == null ) {
            return null;
        }

        MatterEvaluateDto matterEvaluateDto = new MatterEvaluateDto();

        matterEvaluateDto.setCreateBy( entity.getCreateBy() );
        matterEvaluateDto.setCommentContent( entity.getCommentContent() );
        if ( entity.getCreateDate() != null ) {
            matterEvaluateDto.setCreateDate( new Timestamp( entity.getCreateDate().getTime() ) );
        }
        matterEvaluateDto.setCreatorId( entity.getCreatorId() );
        matterEvaluateDto.setEnabled( entity.getEnabled() );
        matterEvaluateDto.setEvaluatorCode( entity.getEvaluatorCode() );
        matterEvaluateDto.setEvaluatorName( entity.getEvaluatorName() );
        matterEvaluateDto.setId( entity.getId() );
        matterEvaluateDto.setMatterId( entity.getMatterId() );
        if ( entity.getModifiedDate() != null ) {
            matterEvaluateDto.setModifiedDate( new Timestamp( entity.getModifiedDate().getTime() ) );
        }
        matterEvaluateDto.setModifiedId( entity.getModifiedId() );
        matterEvaluateDto.setProId( entity.getProId() );
        matterEvaluateDto.setProKey( entity.getProKey() );
        matterEvaluateDto.setRealRunTimes( entity.getRealRunTimes() );
        matterEvaluateDto.setStarNum( entity.getStarNum() );
        matterEvaluateDto.setSuggestions( entity.getSuggestions() );
        matterEvaluateDto.setVersion( entity.getVersion() );

        return matterEvaluateDto;
    }

    @Override
    public List<MatterEvaluateDto> toDto(List<MatterEvaluate> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MatterEvaluateDto> list = new ArrayList<MatterEvaluateDto>( entityList.size() );
        for ( MatterEvaluate matterEvaluate : entityList ) {
            list.add( toDto( matterEvaluate ) );
        }

        return list;
    }

    @Override
    public MatterEvaluate toEntity(MatterEvaluateDto dto) {
        if ( dto == null ) {
            return null;
        }

        MatterEvaluate matterEvaluate = new MatterEvaluate();

        matterEvaluate.setCreateBy( dto.getCreateBy() );
        matterEvaluate.setCreateDate( dto.getCreateDate() );
        matterEvaluate.setCreatorId( dto.getCreatorId() );
        matterEvaluate.setEnabled( dto.getEnabled() );
        matterEvaluate.setId( dto.getId() );
        matterEvaluate.setModifiedDate( dto.getModifiedDate() );
        matterEvaluate.setModifiedId( dto.getModifiedId() );
        matterEvaluate.setVersion( dto.getVersion() );
        matterEvaluate.setCommentContent( dto.getCommentContent() );
        matterEvaluate.setEvaluatorCode( dto.getEvaluatorCode() );
        matterEvaluate.setEvaluatorName( dto.getEvaluatorName() );
        matterEvaluate.setMatterId( dto.getMatterId() );
        matterEvaluate.setProId( dto.getProId() );
        matterEvaluate.setProKey( dto.getProKey() );
        matterEvaluate.setRealRunTimes( dto.getRealRunTimes() );
        matterEvaluate.setStarNum( dto.getStarNum() );
        matterEvaluate.setSuggestions( dto.getSuggestions() );

        return matterEvaluate;
    }

    @Override
    public List<MatterEvaluate> toEntity(List<MatterEvaluateDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MatterEvaluate> list = new ArrayList<MatterEvaluate>( dtoList.size() );
        for ( MatterEvaluateDto matterEvaluateDto : dtoList ) {
            list.add( toEntity( matterEvaluateDto ) );
        }

        return list;
    }
}
