package com.hzwangda.aigov.modules.appcenter.domain.entity;

import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @author: zhang<PERSON><PERSON><PERSON>
 * @date: 2022/11/4 15:01
 * @description:
 */
@Data
@Entity
@Table(name = "z08_app_center")
public class Z08AppCenter extends BaseBpmDomain implements Serializable {

    @Column(name = "username")
    @ApiModelProperty(value = "用户名")
    private String username;

    @Column(name = "app_id")
    @ApiModelProperty(value = "应用id")
    private Long appId;
}
