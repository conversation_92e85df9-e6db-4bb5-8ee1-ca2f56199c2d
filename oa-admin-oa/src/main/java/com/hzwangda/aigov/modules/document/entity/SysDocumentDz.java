package com.hzwangda.aigov.modules.document.entity;

import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 通用-发文
 * <AUTHOR>
 * @date 2021/6/15 上午10:18
 */
@Data
@Entity
@Table(name = "sys_document_dz")
public class SysDocumentDz extends BaseEntity implements Serializable {

    @Id
    private Long id;

    @Column(name = "dz")
    @ApiModelProperty(value = "代字")
    private String dz;

    @Column(name = "dz_type")
    @ApiModelProperty(value = "代字类型")
    private String dzType;

    @Column(name = "belong_to_dept")
    @ApiModelProperty(value = "所属单位")
    private String belongToDept;

}
