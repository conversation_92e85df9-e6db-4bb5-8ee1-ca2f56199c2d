package com.hzwangda.aigov.modules.document.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@Data
public class A07DocumentGwViewDto {
    private Long id;
    @ApiModelProperty(value = "标题")
    private String bt;
    private String bpmStatus;
    @ApiModelProperty(value = "创建人")
    private String cjr;
    @ApiModelProperty(value = "类型")
    private String moduleType;
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;
    @Column(name = "bpm_instance_id")
    private String bpmInstanceId;
}
