package com.hzwangda.aigov.modules.appcenter.controller;

import com.hzwangda.aigov.modules.appcenter.domain.criteria.Z08AppCenterCriteria;
import com.hzwangda.aigov.modules.appcenter.service.Z08AppCenterService;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwQueryCriteria;
import com.wangda.oa.annotation.AnonymousAccess;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * @author: zhangzhanlong
 * @date: 2022/11/4 14:59
 * @description:
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "应用中心")
@RequestMapping("/api/aigov/appcenter")
@CrossOrigin
public class Z08AppCenterController {
    private final Z08AppCenterService appCenterService;

    @GetMapping("/getAppConterList")
    @ApiOperation("查询应用中心列表")
    public ResponseEntity<Object> getAppConterList(Z08AppCenterCriteria criteria) {
        return new ResponseEntity<>(appCenterService.getAppConterList(criteria), HttpStatus.OK);
    }

    @PostMapping("/saveAppConter")
    @ApiOperation("加入自选或取消自选")
    public ResponseEntity<Object> saveAppConter(@RequestParam("appId") Long appId) {
        appCenterService.saveAppConter(appId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("行政管理列表")
    @GetMapping(value = "/getXzList")
    public ResponseEntity<Object> getXzList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(appCenterService.getXzList(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("待阅已阅列表")
    @GetMapping("/readList")
    public ResponseEntity<Page> readList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        return ResponseEntity.ok(appCenterService.readList(criteria, pageable));
    }


    @ApiOperation("全部已阅")
    @PostMapping("/readAll")
    public ResponseEntity readAll() {
        return ResponseEntity.ok(appCenterService.readAll());
    }
}
