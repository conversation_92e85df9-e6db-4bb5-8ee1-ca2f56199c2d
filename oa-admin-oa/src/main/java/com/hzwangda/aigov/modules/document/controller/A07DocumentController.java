package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.dto.*;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlz;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlzUser;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGzxt;
import com.hzwangda.aigov.modules.document.entity.A07DocumentInfoWwfb;
import com.hzwangda.aigov.modules.document.service.A07DocumentService;
import com.hzwangda.aigov.modules.workflow.bo.GwlzUserListBO;
import com.hzwangda.aigov.modules.workflow.bo.GzxtUserListBO;
import com.hzwangda.aigov.modules.workflow.dto.MyConferenceListDto;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.annotation.Log;
import com.wangda.oa.annotation.rest.AnonymousPostMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "公文管理")
@RequestMapping("/api/aigov/a07")
@CrossOrigin
public class A07DocumentController {

    private final A07DocumentService a07DocumentService;

    /**
     * 行政审批管理列表
     */
    @ApiOperation("行政审批管理列表")
    @GetMapping(value = "/getXzList")
    public ResponseEntity<Object> getXzList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.getXzList(criteria, pageable), HttpStatus.OK);
    }

    /**
     * 公文管理列表
     * @param criteria
     * @param pageable
     * @return
     */
    @ApiOperation("公文管理列表")
    @GetMapping(value = "/getGwList")
    public ResponseEntity<Object> getGwList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.getGwList(criteria, pageable), HttpStatus.OK);
    }

    /**
     * 发文管理列表
     * @param criteria
     * @param pageable
     * @return
     */
    @ApiOperation("发文管理列表")
    @GetMapping(value = "/getFwList")
    public ResponseEntity<Object> getFwList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.getFwList(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("全部发文-根据单位来查询")
    @GetMapping(value = "/getAllFwList")
    public ResponseEntity<Object> getAllFwList(A07AllFwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.getAllFwList(criteria, pageable), HttpStatus.OK);
    }

    /**
     * 收文管理列表
     * @param criteria
     * @param pageable
     * @return
     */
    @ApiOperation("收文管理列表")
    @GetMapping(value = "/getSwList")
    public ResponseEntity<Object> getSwList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.getSwList(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("全部收文-根据单位来查询")
    @GetMapping(value = "/getAllSwList")
    public ResponseEntity<Object> getAllSwList(A07AllSwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.getAllSwList(criteria, pageable), HttpStatus.OK);
    }

    @Log("政策文件列表")
    @ApiOperation("政策文件列表")
    @GetMapping(value = "/getPolicyDocumentList")
    public ResponseEntity<Object> getPolicyDocumentList(A07DocumentZcwjQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.getPolicyDocumentList(criteria, pageable), HttpStatus.OK);
    }

    @Log("政策文件列表移动端")
    @ApiOperation("政策文件列表移动端")
    @GetMapping(value = "/getPolicyDocumentListForAPP")
    public ResponseEntity<Object> getPolicyDocumentListForAPP(A07DocumentZcwjQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.getPolicyDocumentListForAPP(criteria, pageable), HttpStatus.OK);
    }

    @Log("政策文件详情(发文详情)")
    @ApiOperation("政策文件详情(发文详情)")
    @PostMapping(value = "/findByDocumentInfo")
    public ResponseEntity<A07DocumentFwDto> findByDocumentInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07DocumentService.findByDocumentInfo(id), HttpStatus.OK);
    }

    @Log("删除政策文件")
    @ApiOperation("删除政策文件")
    @PostMapping(value = "/deleteDocumentInfo")
    public ResponseEntity<Boolean> deleteDocumentInfo(@NotNull @RequestBody List<Long> ids) {
        return new ResponseEntity<>(a07DocumentService.deleteDocumentInfo(ids), HttpStatus.OK);
    }


    @ApiOperation("会签列表")
    @GetMapping(value = "/getHqList")
    public ResponseEntity<Object> getHqList(A07DocumentHqQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.getHqList(criteria, pageable), HttpStatus.OK);
    }


    @ApiOperation("领导批示办理单列表")
    @GetMapping(value = "/getLdpsList")
    public ResponseEntity<Object> getLdpsList(A07DocumentLdpsQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.getLdpsList(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("签批办理单列表")
    @GetMapping(value = "/getClqpList")
    public ResponseEntity<Object> getClqpList(A07DocumentClqpQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.getClqpList(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("协同列表")
    @GetMapping(value = "/getXtList")
    public ResponseEntity<Object> getXtList(A08GzxtQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.getXtList(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("信息公开申请列表")
    @GetMapping(value = "/getXxgksqList")
    public ResponseEntity<Object> getXxgksqList(A07DocumentXxgksqQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.getXxgksqList(criteria, pageable), HttpStatus.OK);
    }


    @ApiOperation("会签详情")
    @PostMapping(value = "/getHqInfo")
    public ResponseEntity<A07DocumentHqDto> getHqInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07DocumentService.getHqInfo(id), HttpStatus.OK);
    }

    @ApiOperation("领导批示办理单详情")
    @PostMapping(value = "/getLdpsInfo")
    public ResponseEntity<A07DocumentLdpsDto> getLdpsInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07DocumentService.getLdpsInfo(id), HttpStatus.OK);
    }

    @ApiOperation("签批办理单详情")
    @PostMapping(value = "/getClqpInfo")
    public ResponseEntity<A07DocumentClqpDto> getClqpInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07DocumentService.getClqpInfo(id), HttpStatus.OK);
    }

    @ApiOperation("协同详情")
    @PostMapping(value = "/getXtInfo")
    public ResponseEntity<A08GzxtDto> getXtInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07DocumentService.getXtInfo(id), HttpStatus.OK);
    }

    @ApiOperation("信息公开申请详情")
    @PostMapping(value = "/getXxgksqInfo")
    public ResponseEntity<A07DocumentXxgksqDto> getXxgksqInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07DocumentService.getXxgksqInfo(id), HttpStatus.OK);
    }


    @Log("删除会签")
    @ApiOperation("删除会签")
    @PostMapping(value = "/deleteHq")
    public ResponseEntity<Boolean> deleteHq(@NotNull @RequestBody List<Long> ids) {
        return new ResponseEntity<>(a07DocumentService.deleteHq(ids), HttpStatus.OK);
    }

    @Log("删除领导批示办理单")
    @ApiOperation("删除领导批示办理单")
    @PostMapping(value = "/deleteLdps")
    public ResponseEntity<Boolean> deleteLdps(@NotNull @RequestBody List<Long> ids) {
        return new ResponseEntity<>(a07DocumentService.deleteLdps(ids), HttpStatus.OK);
    }

    @Log("删除签批办理")
    @ApiOperation("删除签批办理")
    @PostMapping(value = "/deleteClqp")
    public ResponseEntity<Boolean> deleteClqps(@NotNull @RequestBody List<Long> ids) {
        return new ResponseEntity<>(a07DocumentService.deleteClqp(ids), HttpStatus.OK);
    }

    @Log("删除协同")
    @ApiOperation("删除协同")
    @PostMapping(value = "/deleteXt")
    public ResponseEntity<Boolean> deleteXt(@NotNull @RequestBody List<Long> ids) {
        return new ResponseEntity<>(a07DocumentService.deleteXt(ids), HttpStatus.OK);
    }

    @Log("删除信息公开申请")
    @ApiOperation("删除信息公开申请")
    @PostMapping(value = "/deleteXxgksq")
    public ResponseEntity<Boolean> deleteXxgksq(@NotNull @RequestBody List<Long> ids) {
        return new ResponseEntity<>(a07DocumentService.deleteXxgksq(ids), HttpStatus.OK);
    }

    @Log("公文流转列表")
    @ApiOperation("公文流转列表")
    @GetMapping(value = "/getGwlzList")
    public ResponseEntity<Object> getGwlzList(A07DocumentGwlzQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.gwlzList(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping("/saveGwlz")
    @Log("新增或修改公文流转")
    @ApiOperation("新增或修改gwlz")
    public ResponseEntity<Object> saveGwlz(@Validated @RequestBody A07DocumentGwlz resources) {
        return new ResponseEntity<>(a07DocumentService.createOrUpdate(resources), HttpStatus.OK);
    }

    @Log("用户公文流转列表")
    @ApiOperation("用户公文流转列表")
    @GetMapping(value = "/getGwlzUserList")
    public ResponseEntity<Object> getGwlzUserList(GwlzUserListBO gwlzUserListBO) {
        return new ResponseEntity<>(a07DocumentService.getGwlzUserList(gwlzUserListBO), HttpStatus.CREATED);
    }

    @Log("公文流转详情")
    @ApiOperation("公文流转详情")
    @PostMapping(value = "/getGwlzInfo")
    public ResponseEntity<A07DocumentGwlz> getGwlzInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07DocumentService.getGwlzInfo(id), HttpStatus.OK);
    }

    @Log("公文流转用户详情")
    @ApiOperation("公文流转用户详情")
    @PostMapping(value = "/getGwlzUserInfo")
    public ResponseEntity<A07DocumentGwlzUserDto> getGwlzUserInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07DocumentService.getGwlzUserInfo(id), HttpStatus.OK);
    }

    @PostMapping("/saveGwlzUser")
    @Log("新增或修改公文流转User")
    @ApiOperation("新增或修改gwlzUser")
    public ResponseEntity<Object> saveGwlzUser(@Validated @RequestBody A07DocumentGwlzUser resources) {
        return new ResponseEntity<>(a07DocumentService.createOrUpdateUser(resources), HttpStatus.CREATED);
    }

    @PostMapping("/gwlzQs")
    @Log("公文流转签收")
    @ApiOperation("公文流转签收")
    public ResponseEntity<Object> gwlzQs(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07DocumentService.gwlzQs(id), HttpStatus.OK);
    }

    @PostMapping("/gwlzLwSc")
    @Log("公文流转来文删除")
    @ApiOperation("公文流转来文删除")
    public ResponseEntity<Object> gwlzLwSc(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07DocumentService.gwlzLwSc(id), HttpStatus.OK);
    }

    @PostMapping("/gwlzSc")
    @Log("公文流转删除")
    @ApiOperation("公文流转删除")
    public ResponseEntity<Object> gwlzSc(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07DocumentService.gwlzSc(id), HttpStatus.OK);
    }

    @PostMapping("/gwlzCh")
    @Log("公文流转撤回")
    @ApiOperation("公文流转撤回")
    public ResponseEntity<Object> gwlzCh(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07DocumentService.gwlzCh(id), HttpStatus.OK);
    }

    @PostMapping("/saveGzxt")
    @Log("新增或修改gzxt")
    @ApiOperation("新增或修改gzxt")
    public ResponseEntity<Object> saveGzxt(@Validated @RequestBody A07DocumentGzxt resources) {
        return new ResponseEntity<>(a07DocumentService.createOrUpdateGzxt(resources), HttpStatus.CREATED);
    }

    @Log("外网发布数据集合")
    @ApiOperation("外网发布数据集合")
    @AnonymousPostMapping(value = "/getWwfbList")
    public ResponseEntity<List<A07DocumentInfoWwfb>> getWwfbList() {
        return new ResponseEntity<>(a07DocumentService.getWwfbList(), HttpStatus.OK);
    }

    @Log("用户工作协同列表")
    @ApiOperation("用户工作协同列表")
    @GetMapping(value = "/getGzxtUserList")
    public MyConferenceListDto getGzxtUserList(GzxtUserListBO gzxtUserListBO) {
        return a07DocumentService.getGzxtUserList(gzxtUserListBO);
    }

    @ApiOperation("收文检索")
    @GetMapping(value = "/getSwListRetrieve")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Object> getSwListRetrieve(A07DocumentRetrieveQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.getSwListRetrieve(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("发文检索")
    @GetMapping(value = "/getFwListRetrieve")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Object> getFwListRetrieve(A07DocumentRetrieveQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.getFwListRetrieve(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("服务事项检索")
    @GetMapping(value = "/getFwsxListRetrieve")
    @PreAuthorize("@el.check('a07:list')")
    public ResultJson getFwsxListRetrieve(A07DocumentRetrieveQueryCriteria criteria, Pageable pageable) {
        return ResultJson.generateResult(a07DocumentService.getFwsxListRetrieve(criteria, pageable));
    }


    @Log("分页查询所有收文")
    @ApiOperation("分页查询所有收文")
    @GetMapping(value = "/queryPageSw")
    public ResponseEntity<Object> queryPageSw(A07DocumentSwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity(a07DocumentService.queryPageSw(criteria, pageable), HttpStatus.OK);
    }

    @Log("分页查询所有发文")
    @ApiOperation("分页查询所有发文")
    @GetMapping(value = "/queryPageFw")
    public ResponseEntity<Object> queryPageFw(A07DocumentFwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity(a07DocumentService.queryPageFw(criteria, pageable), HttpStatus.OK);
    }

    @Log("分页查询所有领导批示")
    @ApiOperation("分页查询所有领导批示")
    @GetMapping(value = "/queryPageLdps")
    public ResponseEntity<Object> queryPageLdps(A07DocumentLdpsQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity(a07DocumentService.queryPageLdps(criteria, pageable), HttpStatus.OK);
    }


    @Log("分页查询所有领厅内呈阅")
    @ApiOperation("分页查询所有领厅内呈阅")
    @GetMapping(value = "/queryPageTncy")
    public ResponseEntity<Object> queryPageTncy(A07DocumentClqpQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity(a07DocumentService.queryPageTncy(criteria, pageable), HttpStatus.OK);
    }


    @Log("分页查询所有会签")
    @ApiOperation("分页查询所有会签")
    @GetMapping(value = "/queryPageHq")
    public ResponseEntity<Object> queryPageHq(A07DocumentHqQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity(a07DocumentService.queryPageHq(criteria, pageable), HttpStatus.OK);
    }

}
