package com.hzwangda.aigov.modules.zjedu.goodsmanagement.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.goodsmanagement.domain.dto.D06GoodsCategoryDto;
import com.hzwangda.aigov.modules.zjedu.goodsmanagement.domain.entity.D06Goods;
import com.hzwangda.aigov.modules.zjedu.goodsmanagement.domain.entity.D06GoodsCategory;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class D06GoodsCategoryMapperImpl implements D06GoodsCategoryMapper {

    @Override
    public D06GoodsCategoryDto toDto(D06GoodsCategory entity) {
        if ( entity == null ) {
            return null;
        }

        D06GoodsCategoryDto d06GoodsCategoryDto = new D06GoodsCategoryDto();

        d06GoodsCategoryDto.setCreateBy( entity.getCreateBy() );
        d06GoodsCategoryDto.setCreateTime( entity.getCreateTime() );
        d06GoodsCategoryDto.setUpdateTime( entity.getUpdateTime() );
        d06GoodsCategoryDto.setUpdatedBy( entity.getUpdatedBy() );
        d06GoodsCategoryDto.setCategoryCode( entity.getCategoryCode() );
        d06GoodsCategoryDto.setCategoryName( entity.getCategoryName() );
        d06GoodsCategoryDto.setDescription( entity.getDescription() );
        List<D06Goods> list = entity.getGoodsList();
        if ( list != null ) {
            d06GoodsCategoryDto.setGoodsList( new ArrayList<D06Goods>( list ) );
        }
        d06GoodsCategoryDto.setId( entity.getId() );
        d06GoodsCategoryDto.setParentId( entity.getParentId() );

        return d06GoodsCategoryDto;
    }

    @Override
    public List<D06GoodsCategoryDto> toDto(List<D06GoodsCategory> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<D06GoodsCategoryDto> list = new ArrayList<D06GoodsCategoryDto>( entityList.size() );
        for ( D06GoodsCategory d06GoodsCategory : entityList ) {
            list.add( toDto( d06GoodsCategory ) );
        }

        return list;
    }

    @Override
    public D06GoodsCategory toEntity(D06GoodsCategoryDto dto) {
        if ( dto == null ) {
            return null;
        }

        D06GoodsCategory d06GoodsCategory = new D06GoodsCategory();

        d06GoodsCategory.setCreateBy( dto.getCreateBy() );
        d06GoodsCategory.setCreateTime( dto.getCreateTime() );
        d06GoodsCategory.setUpdateTime( dto.getUpdateTime() );
        d06GoodsCategory.setUpdatedBy( dto.getUpdatedBy() );
        d06GoodsCategory.setCategoryCode( dto.getCategoryCode() );
        d06GoodsCategory.setCategoryName( dto.getCategoryName() );
        d06GoodsCategory.setDescription( dto.getDescription() );
        List<D06Goods> list = dto.getGoodsList();
        if ( list != null ) {
            d06GoodsCategory.setGoodsList( new ArrayList<D06Goods>( list ) );
        }
        d06GoodsCategory.setId( dto.getId() );
        d06GoodsCategory.setParentId( dto.getParentId() );

        return d06GoodsCategory;
    }

    @Override
    public List<D06GoodsCategory> toEntity(List<D06GoodsCategoryDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<D06GoodsCategory> list = new ArrayList<D06GoodsCategory>( dtoList.size() );
        for ( D06GoodsCategoryDto d06GoodsCategoryDto : dtoList ) {
            list.add( toEntity( d06GoodsCategoryDto ) );
        }

        return list;
    }
}
