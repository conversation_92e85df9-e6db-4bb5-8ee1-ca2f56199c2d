/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07DocumentGwViewDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwView;
import com.wangda.oa.base.BaseMapper;
import com.wangda.oa.modules.system.service.mapstruct.UserMapperUtil;
import org.mapstruct.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-07-28
 **/
@Mapper(componentModel = "spring", uses = UserMapperUtil.class, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface A07DocumentGwViewMapper extends BaseMapper<A07DocumentGwViewDto, A07DocumentGwView> {

    @AfterMapping
    default void splitTime(A07DocumentGwView entity, @MappingTarget A07DocumentGwViewDto dto) {
    }

    @Override
    @Mappings({
            //@Mapping(target = "cjr", expression = "java(userMapperUtil.toUser(entity.getCjr()))"),
    })
    A07DocumentGwViewDto toDto(A07DocumentGwView entity);
}
