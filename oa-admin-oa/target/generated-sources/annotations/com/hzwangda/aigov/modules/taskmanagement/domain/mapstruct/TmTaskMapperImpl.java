package com.hzwangda.aigov.modules.taskmanagement.domain.mapstruct;

import com.hzwangda.aigov.modules.taskmanagement.domain.dto.TmTaskDto;
import com.hzwangda.aigov.modules.taskmanagement.domain.entity.TmClass;
import com.hzwangda.aigov.modules.taskmanagement.domain.entity.TmTask;
import com.wangda.oa.modules.system.domain.Dept;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class TmTaskMapperImpl implements TmTaskMapper {

    @Override
    public TmTaskDto toDto(TmTask entity) {
        if ( entity == null ) {
            return null;
        }

        TmTaskDto tmTaskDto = new TmTaskDto();

        List<Dept> list = entity.getAssistDepts();
        if ( list != null ) {
            tmTaskDto.setAssistDepts( new ArrayList<Dept>( list ) );
        }
        List<TmTask> list1 = entity.getAssociatedTask();
        if ( list1 != null ) {
            tmTaskDto.setAssociatedTask( new ArrayList<TmTask>( list1 ) );
        }
        Set<TmClass> set = entity.getClasses();
        if ( set != null ) {
            tmTaskDto.setClasses( new HashSet<TmClass>( set ) );
        }
        tmTaskDto.setContacts( entity.getContacts() );
        tmTaskDto.setCreateBy( entity.getCreateBy() );
        tmTaskDto.setCreateTime( entity.getCreateTime() );
        List<Dept> list2 = entity.getDepts();
        if ( list2 != null ) {
            tmTaskDto.setDepts( new ArrayList<Dept>( list2 ) );
        }
        tmTaskDto.setFinished( entity.getFinished() );
        if ( entity.getIsDel() != null ) {
            tmTaskDto.setIsDel( entity.getIsDel().longValue() );
        }
        tmTaskDto.setName( entity.getName() );
        if ( entity.getProcess() != null ) {
            tmTaskDto.setProcess( String.valueOf( entity.getProcess() ) );
        }
        tmTaskDto.setQtDept( entity.getQtDept() );
        tmTaskDto.setTaskId( entity.getTaskId() );
        tmTaskDto.setTaskNo( entity.getTaskNo() );
        tmTaskDto.setTaskSource( entity.getTaskSource() );
        tmTaskDto.setType( entity.getType() );
        tmTaskDto.setUpdateTime( entity.getUpdateTime() );

        return tmTaskDto;
    }

    @Override
    public List<TmTaskDto> toDto(List<TmTask> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmTaskDto> list = new ArrayList<TmTaskDto>( entityList.size() );
        for ( TmTask tmTask : entityList ) {
            list.add( toDto( tmTask ) );
        }

        return list;
    }

    @Override
    public TmTask toEntity(TmTaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        TmTask tmTask = new TmTask();

        tmTask.setCreateBy( dto.getCreateBy() );
        tmTask.setCreateTime( dto.getCreateTime() );
        tmTask.setUpdateTime( dto.getUpdateTime() );
        List<Dept> list = dto.getAssistDepts();
        if ( list != null ) {
            tmTask.setAssistDepts( new ArrayList<Dept>( list ) );
        }
        List<TmTask> list1 = dto.getAssociatedTask();
        if ( list1 != null ) {
            tmTask.setAssociatedTask( new ArrayList<TmTask>( list1 ) );
        }
        Set<TmClass> set = dto.getClasses();
        if ( set != null ) {
            tmTask.setClasses( new HashSet<TmClass>( set ) );
        }
        tmTask.setContacts( dto.getContacts() );
        List<Dept> list2 = dto.getDepts();
        if ( list2 != null ) {
            tmTask.setDepts( new ArrayList<Dept>( list2 ) );
        }
        tmTask.setFinished( dto.getFinished() );
        if ( dto.getIsDel() != null ) {
            tmTask.setIsDel( dto.getIsDel().intValue() );
        }
        tmTask.setName( dto.getName() );
        if ( dto.getProcess() != null ) {
            tmTask.setProcess( Integer.parseInt( dto.getProcess() ) );
        }
        tmTask.setQtDept( dto.getQtDept() );
        tmTask.setTaskId( dto.getTaskId() );
        tmTask.setTaskNo( dto.getTaskNo() );
        tmTask.setTaskSource( dto.getTaskSource() );
        tmTask.setType( dto.getType() );

        return tmTask;
    }

    @Override
    public List<TmTask> toEntity(List<TmTaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TmTask> list = new ArrayList<TmTask>( dtoList.size() );
        for ( TmTaskDto tmTaskDto : dtoList ) {
            list.add( toEntity( tmTaskDto ) );
        }

        return list;
    }
}
