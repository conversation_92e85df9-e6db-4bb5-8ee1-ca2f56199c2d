package com.hzwangda.aigov.modules.document.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hzwangda.aigov.modules.form.domain.A23ReportFormInstantiate;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Where;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 公文流转
 * <AUTHOR>
 * @date 2021/7/15 上午11:18
 */
@Data
@Entity
@Table(name = "a07_document_gwlz")
public class A07DocumentGwlz extends BaseDomain implements Serializable {

    @Column(name = "gwzt")
    @ApiModelProperty(value = "公文状态(0:草稿,1:正式,2:撤回,3:退回)")
    private String gwzt;

    @ApiModelProperty(value = "文件类型(规范类文件,非规范类文件,其他)")
    private String fileType;

    @Column(name = "gwzl")
    @ApiModelProperty(value = "公文种类(公文,会议)")
    private String gwzl;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @Column(name = "sfgk")
    @ApiModelProperty(value = "是否公开(主动公开,依申请公开,不予公开)")
    private String sfgk;

    @ApiModelProperty(value = "文号")
    @OneToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinColumn(name = "wh_id", referencedColumnName = "id")
    private ReferenceNumber gwwh;

    @Column(name = "hj")
    @ApiModelProperty(value = "缓急(特急,加急,平件)")
    private String hj;

    @Column(name = "yj_qfr", length = 2000)
    @ApiModelProperty(value = "签发人")
    private String yjQfr;

    @Column(name = "bt")
    @ApiModelProperty(value = "标题")
    private String bt;

    @Transient
    @ApiModelProperty(value = "正文")
//    @OneToOne(cascade = CascadeType.ALL,fetch = FetchType.EAGER)
//    @JoinColumn(name="zw", referencedColumnName = "storage_id")
    private StorageBiz zw;

    @Column(name = "zw")
    private String zwId;

    @ApiModelProperty(value = "原流程实例id")
    @Column(name = "doc_proc_inst_id")
    private String docPronInstId;

    @ApiModelProperty(value = "主送单位")
    @OneToMany(targetEntity = A07DocumentGwlzUser.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "gwid", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "qslx='zs'")
    private List<A07DocumentGwlzUser> zsdwDepts;

    @ApiModelProperty(value = "主送单位描述")
    @Column(length = 5000)
    private String zsdwms;

    @ApiModelProperty(value = "抄送单位")
    @OneToMany(targetEntity = A07DocumentGwlzUser.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "gwid", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "qslx='cs'")
    private List<A07DocumentGwlzUser> csdwDepts;

    @ApiModelProperty(value = "抄送单位描述")
    @Column(length = 5000)
    private String csdwms;

    @Column(name = "fwdw")
    @ApiModelProperty(value = "发文单位")
    private String fwdw;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "发文日期")
    private Timestamp fwrq;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A07DocumentGwlz.fj'")
    private List<StorageBiz> fj;

    @ApiModelProperty(value = "数据采集")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A07DocumentGwlz.sjcj'")
    private List<StorageBiz> sjcj;

    @Column(name = "fjsm")
    @ApiModelProperty(value = "附加说明")
    private String fjsm;

    @Column(name = "fwlxr")
    @ApiModelProperty(value = "发文联系人")
    private String fwlxr;

    @Column(name = "lxfs")
    @ApiModelProperty(value = "联系方式")
    private String lxfs;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "操作日期")
    private Timestamp czrq;

    @Column(name = "dxtz")
    @ApiModelProperty(value = "短信通知")
    private String dxtz;

    @Column(name = "thmb")
    @ApiModelProperty(value = "套红模版")
    private String thmb;

    @Column(name = "swdw")
    @ApiModelProperty(value = "收文单位")
    private String swdw;

    @Column(name = "zbmtjyh")
    @ApiModelProperty(value = "子部门OA添加用户")
    private String zbmtjyh;

    @Column(name = "ly")
    @ApiModelProperty(value = "是否来源自子部门OA")
    private String ly;

    @Column(name = "fkdlb")
    @ApiModelProperty(value = "反馈单类别")
    private String fkdlb;

    @Column(name = "oaname")
    @ApiModelProperty(value = "子部门OA添加用户真实名")
    private String oaname;

    @Column(name = "lybs")
    @ApiModelProperty(value = "来源标识")
    private String lybs;

    @Column(name = "lyxtdm")
    @ApiModelProperty(value = "来源系统代码")
    private String lyxtdm;

    @Column(name = "old_doc_id")
    @ApiModelProperty(value = "老oa公文id")
    private String oldDocID;
    @ApiModelProperty(value = "创建人name")
    private String addUser;
    @ApiModelProperty(value = "数据填报id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long reportFormInstantiateId;
    @Transient
    @ApiModelProperty(value = "数据填报模板实例")
    private A23ReportFormInstantiate sccj;

    @Lob
    @ApiModelProperty(value = "备注")
    private String bz;

    public void copy(A07DocumentGwlz source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }

    public void setBizId(Long bizId) {
        if(!CollectionUtils.isEmpty(this.fj)) {
            for(StorageBiz storageBiz : this.fj) {
                storageBiz.setBizId(bizId.toString());
            }
        }
        if(!CollectionUtils.isEmpty(this.sjcj)) {
            for(StorageBiz storageBiz : this.sjcj) {
                storageBiz.setBizId(bizId.toString());
            }
        }
        if(Objects.nonNull(this.zw) && StringUtils.isNotBlank(this.zw.getStorageId())) {
            this.zw.setBizId(bizId.toString());
        }
    }

    public void setZsdwDepts(List<A07DocumentGwlzUser> zsdwDepts) {
        if(zsdwDepts != null) {
            if(this.zsdwDepts == null) {
                this.zsdwDepts = new ArrayList<>();
            }
            this.zsdwDepts.clear();
            this.zsdwDepts.addAll(zsdwDepts);
        }
    }

    public void setCsdwDepts(List<A07DocumentGwlzUser> csdwDepts) {
        if(csdwDepts != null) {
            if(this.csdwDepts == null) {
                this.csdwDepts = new ArrayList<>();
            }
            this.csdwDepts.clear();
            this.csdwDepts.addAll(csdwDepts);
        }
    }

    public void setFj(List<StorageBiz> fj) {
        if(fj != null) {
            if(this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }

    public void setSjcj(List<StorageBiz> sjcj) {
        if(sjcj != null) {
            if(this.sjcj == null) {
                this.sjcj = new ArrayList<>();
            }
            this.sjcj.clear();
            this.sjcj.addAll(sjcj);
        }
    }

    public StorageBiz getZw() {
        if(StringUtils.isNotEmpty(zwId)) {
            zw = new StorageBiz();
            zw.setBizType("A07DocumentGwlz.zw");
            zw.setStorageId(zwId);
        }
        return zw;
    }
}
