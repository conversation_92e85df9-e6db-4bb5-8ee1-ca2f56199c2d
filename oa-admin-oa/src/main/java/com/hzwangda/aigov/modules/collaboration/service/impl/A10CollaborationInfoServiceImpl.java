package com.hzwangda.aigov.modules.collaboration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.Assert;
import cn.hutool.http.HtmlUtil;
import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.collaboration.domain.criteria.A10CollaborationInfoQueryCriteria;
import com.hzwangda.aigov.modules.collaboration.domain.criteria.SmsInfo;
import com.hzwangda.aigov.modules.collaboration.domain.dto.A10CollaborationInfo4ListDto;
import com.hzwangda.aigov.modules.collaboration.domain.dto.A10CollaborationInfo4ViewDto;
import com.hzwangda.aigov.modules.collaboration.domain.dto.A10CollaborationInfoDto;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationAssignee;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationHandleLog;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationInfo;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationInfoCopy;
import com.hzwangda.aigov.modules.collaboration.domain.mapstruct.A10CollaborationInfoMapper;
import com.hzwangda.aigov.modules.collaboration.repository.A10CollaborationAssigneeRepository;
import com.hzwangda.aigov.modules.collaboration.repository.A10CollaborationHandleLogRepository;
import com.hzwangda.aigov.modules.collaboration.repository.A10CollaborationInfoCopyRepository;
import com.hzwangda.aigov.modules.collaboration.repository.A10CollaborationInfoRepository;
import com.hzwangda.aigov.modules.collaboration.service.A10CollaborationInfoService;
import com.hzwangda.aigov.modules.task.base.TaskConstants;
import com.hzwangda.aigov.oa.bo.MasSendContentBO;
import com.hzwangda.aigov.oa.bo.MasUserBO;
import com.hzwangda.aigov.oa.constant.AuthorityConstant;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.hzwangda.aigov.oa.repository.StorageBizRepository;
import com.hzwangda.aigov.oa.repository.WdSysOptionRepository;
import com.hzwangda.aigov.oa.service.MasBusinessService;
import com.hzwangda.aigov.oa.util.OaUtil;
import com.hzwangda.aigov.oa.util.SMSFormatUtil;
import com.hzwangda.aigov.zwdd.service.ZwddService;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.config.ElPermissionConfig;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationBO;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationLinkBO;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.extension.domain.SysUserPlatform;
import com.wangda.oa.modules.extension.domain.WdSysOption;
import com.wangda.oa.modules.extension.repository.SysUserPlatformRepository;
import com.wangda.oa.modules.security.service.dto.JwtUserDto;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.system.service.mapstruct.UserMapperUtil;
import com.wangda.oa.modules.workflow.dto.BacklogDto;
import com.wangda.oa.modules.workflow.dto.BacklogUserDto;
import com.wangda.oa.modules.workflow.dto.DeleteByBizIdBo;
import com.wangda.oa.repository.LocalStorageRepository;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.*;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class A10CollaborationInfoServiceImpl implements A10CollaborationInfoService {

    private final A10CollaborationInfoRepository collaborationInfoRepository;
    private final A10CollaborationAssigneeRepository collaborationAssigneeRepository;
    private final A10CollaborationHandleLogRepository collaborationHandleLogRepository;
    private final StorageBizRepository storageBizRepository;
    private final A10CollaborationInfoMapper collaborationInfoMapper;
    private final UserMapperUtil userMapperUtil;
    private final UserRepository userRepository;
    private final MasBusinessService masBusinessService;
    private final WdSysOptionRepository wdSysOptionRepository;
    private final ZwddProperties zwddProperties;
    private final ZwddService zwddService;
    private final SysUserPlatformRepository sysUserPlatformRepository;
    private final ElPermissionConfig elPermissionConfig;
    @PersistenceContext
    private EntityManager entityManager;

    private final LocalStorageRepository localStorageRepository;
    private final A10CollaborationInfoCopyRepository a10CollaborationInfoCopyRepository;

    @Override
    public Page<A10CollaborationInfo4ListDto> queryUnsignList(A10CollaborationInfoQueryCriteria resources, Pageable pageable) {
        Specification<A10CollaborationInfo> sp = (root, query, cb) -> {
            List<Predicate> andPredicateList = new ArrayList<>();


            Join<A10CollaborationInfo, A10CollaborationAssignee> join = root.join("assignees", JoinType.LEFT);
            if (Objects.isNull(resources.getUserType()) || resources.getUserType() == TaskConstants.NUMBER_ONE) {
                andPredicateList.add(cb.equal(join.get("handleUser"), SecurityUtils.getCurrentUsername()));
            } else if (resources.getUserType() == TaskConstants.NUMBER_TWO) {
                andPredicateList.add(cb.equal(join.get("handleUser"), SecurityUtils.getBindDeptUserName()));
            }
            andPredicateList.add(join.get("status").in("new", "signed"));
            Predicate defaultPredicate = cb.and(andPredicateList.toArray(new Predicate[andPredicateList.size()]));

            query.distinct(true);
            resources.setUserType(null);
            Predicate queryPredicate = QueryHelp.getPredicate(root, resources, cb);
            return cb.and(queryPredicate, defaultPredicate);
        };

        Page<A10CollaborationInfo> page = collaborationInfoRepository.findAll(sp, pageable);
        Page<A10CollaborationInfo4ListDto> dtoPage = page.map(collaborationInfoMapper::toListDto);
        dtoPage.map(item -> {
            A10CollaborationAssignee assignee = collaborationAssigneeRepository.findFirstByInfoIdAndStatusInOrderByCreateTimeDesc(item.getId(), new String[]{"new", "signed"});
            Date arriveTime = assignee.getCreateTime();
            item.setArriveTime(arriveTime);
            return item;
        });

        return dtoPage;
    }

    @Override
    public Page<A10CollaborationInfo4ListDto> querySignedList(A10CollaborationInfoQueryCriteria resources, Pageable pageable) {
        Specification<A10CollaborationInfo> sp = (root, query, cb) -> {
            List<Predicate> andPredicateList = new ArrayList<>();
            Predicate queryPredicate = QueryHelp.getPredicate(root, resources, cb);
            String assigneesUserName = SecurityUtils.getCurrentUsername();
            if (resources.getUserType() == TaskConstants.NUMBER_TWO) {
                assigneesUserName = SecurityUtils.getBindDeptUserName();
            }

            Join<A10CollaborationInfo, A10CollaborationAssignee> join = root.join("assignees", JoinType.LEFT);
            andPredicateList.add(cb.and(cb.or(
                            cb.and(
                                    cb.equal(join.get("handleUser"), assigneesUserName),
                                    cb.not(join.get("status").in(new Object[]{"draft", "new", "signed"}))
                            ),
                            cb.and(
                                    cb.equal(root.get("createBy"), assigneesUserName),
                                    cb.equal(root.get("status"), "sent")
                            )
                    ))
            );
            Predicate defaultPredicate = cb.and(andPredicateList.toArray(new Predicate[andPredicateList.size()]));

            query.distinct(true);
            return cb.and(queryPredicate, defaultPredicate);
        };

        Page<A10CollaborationInfo> page = collaborationInfoRepository.findAll(sp, pageable);
        Page<A10CollaborationInfo4ListDto> dtoPage = page.map(collaborationInfoMapper::toListDto);

        return dtoPage;

    }

    @Override
    public Page<A10CollaborationInfo4ListDto> queryAllList(A10CollaborationInfoQueryCriteria resources, Pageable pageable) {

        Specification<A10CollaborationInfo> sp = (root, query, cb) -> {
            String assigneesUserName = SecurityUtils.getCurrentUsername();
            if (resources.getUserType() == TaskConstants.NUMBER_TWO) {
                assigneesUserName = SecurityUtils.getBindDeptUserName();
            }

            Predicate queryPredicate = QueryHelp.getPredicate(root, resources, cb);
            Join<A10CollaborationInfo, A10CollaborationAssignee> join = root.join("assignees", JoinType.LEFT);

            Predicate p1 = cb.equal(root.get("createBy"), assigneesUserName);
            Predicate p2 = cb.and(cb.equal(join.get("handleUser"), assigneesUserName));
            Predicate p3 = cb.and(cb.notEqual(join.get("status"), "draft"));
            query.distinct(true);
            if (!elPermissionConfig.check(AuthorityConstant.AUTHORITY_COLLABORATION_ADMIN)) {
                Predicate defaultPredicate = cb.or(p1, cb.and(p2, p3));
                return cb.and(queryPredicate, defaultPredicate);
            }

            // 找到所有单位账号
            Subquery<String> subquery = query.subquery(String.class);
            Root<User> from = subquery.from(User.class);
            Subquery<String> where = subquery.select(from.get("username"))
                    .where(cb.and(
                            cb.equal(from.get("userType"), 2),
                            cb.isTrue(from.get("enabled"))
                    ));
            Predicate createBy;
            if (resources.getUserType() == TaskConstants.NUMBER_ONE) {
                createBy = cb.not(root.get("createBy").in(where));
            } else {
                createBy = root.get("createBy").in(where);
            }
            Predicate or = cb.or(p1, cb.and(
                            createBy,
                            cb.notEqual(root.get("status"), "draft")
                    )
            );

            return cb.and(queryPredicate, or);
        };

        Page<A10CollaborationInfo> page = collaborationInfoRepository.findAll(sp, pageable);
        Page<A10CollaborationInfo4ListDto> dtoPage = page.map(collaborationInfoMapper::toListDto);

        return dtoPage;
    }

    @Override
    public A10CollaborationInfo4ViewDto queryOneCollaborationByIdForView(Long id, Long userType) {
        A10CollaborationInfo a10CollaborationInfo = collaborationInfoRepository.findById(id).orElseGet(A10CollaborationInfo::new);
        A10CollaborationInfo4ViewDto viewDto = collaborationInfoMapper.toDto(a10CollaborationInfo);
        List<SimpleUserDto> simpleUserDtoList = userMapperUtil.toConvertToSimpleUserDto(collaborationAssigneeRepository.findDistinctHandleUserByInfoId(id));

        String[] userNameIn = null;
        if (userType == 1) {
            userNameIn = new String[]{SecurityUtils.getCurrentUsername()};
        } else if (userType == 2) {
            userNameIn = new String[]{SecurityUtils.getBindDeptUserName()};
        }
        //前端是否显示催收按钮
        if (collaborationAssigneeRepository.countByInfoIdAndFromUserIn(id, userNameIn) > 0) {
            viewDto.setHasCollection(true);
        } else {
            viewDto.setHasCollection(false);
        }
        viewDto.setAssignees(simpleUserDtoList);
        List<A10CollaborationHandleLog> remarks = collaborationHandleLogRepository.findByInfoIdAndRemarkIsNotNullOrderByHandleTimeDesc(id);
        remarks.forEach(a10CollaborationHandleLog -> {
            User byUsername = userRepository.findByUsername(a10CollaborationHandleLog.getHandleUser());
            if (byUsername != null) {
                a10CollaborationHandleLog.setHandleUserName(byUsername.getNickName());
            }
        });
        viewDto.setRemarks(remarks);

        return viewDto;
    }

    @Override
    public A10CollaborationInfoDto queryOneCollaborationByIdForEdit(Long id) {
        A10CollaborationInfo a10CollaborationInfo = collaborationInfoRepository.findById(id).orElseGet(A10CollaborationInfo::new);
        A10CollaborationInfoDto infoDto = collaborationInfoMapper.toInfoDto(a10CollaborationInfo);
        List<SimpleUserDto> simpleUserDtoList = userMapperUtil.toConvertToSimpleUserDto(collaborationAssigneeRepository.findDistinctHandleUserByInfoId(id));
        infoDto.setAssignees(simpleUserDtoList);
        return infoDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long doSave(A10CollaborationInfo collaborationInfo) {
        final String currentUsername = collaborationInfo.getUserType() == TaskConstants.NUMBER_TWO ? SecurityUtils.getBindDeptUserName() : SecurityUtils.getCurrentUsername();

        if (collaborationInfo.getId() != null) {
            // 删除成员表
            List<A10CollaborationAssignee> oldAssigneeList = collaborationAssigneeRepository.findByInfoId(collaborationInfo.getId());
            collaborationAssigneeRepository.deleteAll(oldAssigneeList);
        }

        List<A10CollaborationHandleLog> handleLogsList = collaborationInfo.getHandleLogs();
        List<A10CollaborationAssignee> AssigneeList = collaborationInfo.getAssignees();

        if ("sent".equals(collaborationInfo.getStatus())) {
            A10CollaborationHandleLog handleLog = handleLogsList.get(0);
//            handleLog.setId(IdWorker.generateId());
            if (collaborationInfo.getUserType() == TaskConstants.NUMBER_TWO) {
                handleLog.setPrefixDeptName(SecurityUtils.getBindDeptName());
            } else {
                UserDto user = ((JwtUserDto) SecurityUtils.getCurrentUser()).getUser();
                handleLog.setPrefixDeptName(SecurityUtils.getUnitAbbrName(Long.valueOf(SecurityUtils.getRedisCurrentDeptCode().get("deptId") + "")) + "-" + user.getNickName());
            }
            handleLog.setHandleUser(currentUsername);
            handleLog.setHandleTime(new Date());
            handleLog.setHandleType("create");
            handleLogsList.add(handleLog);

            collaborationInfo.setSendTime(new Date());
            if ("sent".equals(collaborationInfo.getStatus())) {
                for (int i = 0; i < AssigneeList.size(); i++) {
                    A10CollaborationAssignee assignee = AssigneeList.get(i);

                    assignee.setStatus("new");
                    assignee.setFromUser(currentUsername);
//                    assignee.setLogId(handleLog.getId());
                }
                handleLog.setAssignees(AssigneeList);
            }


            // 短信通知接收人
            Boolean sms = collaborationInfo.getSms();
            if (sms != null && sms) {
                MasSendContentBO bo = new MasSendContentBO();
                WdSysOption firstByKey = wdSysOptionRepository.getFirstByKey("A10Collaboration.SMS");
                Assert.notNull(firstByKey, "短信模板没有配置!");
                String value = firstByKey.getValue();
                Map map = new HashMap();
                String sendName = "";
                if (collaborationInfo.getUserType() == TaskConstants.NUMBER_ONE) {
                    sendName = ((JwtUserDto) SecurityUtils.getCurrentUser()).getUser().getNickName();
                } else if (collaborationInfo.getUserType() == TaskConstants.NUMBER_TWO) {
                    sendName = SecurityUtils.getBindDeptName();
                }
                map.put("username", sendName);
                map.put("bt", collaborationInfo.getSubject());
                String content = SMSFormatUtil.processTemplate(value, map);
                bo.setContent(content);
                List<MasUserBO> userBOList = AssigneeList.stream().map(assignee -> {
                    MasUserBO masUserBO = new MasUserBO();
                    User handleUser = userRepository.findByUsername(assignee.getHandleUser());
                    masUserBO.setId(String.valueOf(handleUser.getId()));
                    masUserBO.setNickName(handleUser.getNickName());
                    masUserBO.setPhone(handleUser.getPhone());

                    return masUserBO;
                }).collect(Collectors.toList());
                bo.setUserBOList(userBOList);
                bo.setSendDate(new Date());
                bo.setTiming(0);
                masBusinessService.sendSMContent(bo, 0, 0);
            }
        } else {
            for (int i = 0; i < AssigneeList.size(); i++) {
                A10CollaborationAssignee assignee = AssigneeList.get(i);
                assignee.setFromUser(currentUsername);
                assignee.setStatus("draft");
            }
        }

        collaborationInfo.setCreateBy(currentUsername);
        collaborationInfoRepository.save(collaborationInfo);


        if ("sent".equals(collaborationInfo.getStatus())) {

            WdSysOption firstByKey = wdSysOptionRepository.getFirstByKey("A10Collaboration.SMS");
            Assert.notNull(firstByKey, "请联系管理员在配置项管理中添加【A10Collaboration.SMS】的内容!");
            String value = firstByKey.getValue();
            Map map = new HashMap();
            String sendName = "";
            if (collaborationInfo.getUserType() == TaskConstants.NUMBER_ONE) {
                sendName = ((JwtUserDto) SecurityUtils.getCurrentUser()).getUser().getNickName();
            } else if (collaborationInfo.getUserType() == TaskConstants.NUMBER_TWO) {
                sendName = SecurityUtils.getBindDeptName();
            }
            map.put("username", sendName);
            map.put("bt", collaborationInfo.getSubject());
            String content = SMSFormatUtil.processTemplate(value, map);
            // 是否浙政钉通知
            Boolean ding = collaborationInfo.getDing();
            if (ding != null && ding) {
                // 浙政钉消息通知
                AssigneeList.stream().forEach(assignee -> {
                    List<SysUserPlatform> sysUserPlatforms = sysUserPlatformRepository.findByUserNameAndType(assignee.getHandleUser(), zwddProperties.getType());
                    String receiverIds = sysUserPlatforms.stream().map(SysUserPlatform::getPlatformUserId).collect(Collectors.joining(","));
                    //  String serviceUrl = zwddProperties.getAppUrl() + "checkSee?id=" + collaborationInfo.getId();
                    String serviceUrl = zwddProperties.getAppUrl() + "workbench";

                    // 待办改为工作通知
                    WorkNotificationBO workNotificationBO = new WorkNotificationBO();
                    workNotificationBO.setReceiverIds(receiverIds);
                    workNotificationBO.setType(1);
                    WorkNotificationLinkBO workNotificationLinkBO = new WorkNotificationLinkBO();
                    workNotificationLinkBO.setText(content);
                    workNotificationLinkBO.setTitle("工作协同");
                    workNotificationLinkBO.setMessageUrl(serviceUrl);
                    workNotificationBO.setLinkBO(workNotificationLinkBO);
                    zwddService.workNotification(workNotificationBO);
                });
            }
            //推送待办
            List<BacklogUserDto> backlogUserList = AssigneeList.stream().map(assignee -> {
                BacklogUserDto backlogUser = new BacklogUserDto();
                backlogUser.setCc(0);
                backlogUser.setUserId(assignee.getHandleUser());
                backlogUser.setCreateDate(System.currentTimeMillis() / 1000);
                return backlogUser;
            }).collect(Collectors.toList());
            BacklogDto backlogDto = new BacklogDto();
            backlogDto.setAppId("OA");
            backlogDto.setBizId(collaborationInfo.getId().toString());
            backlogDto.setModuleCode("collaborationInfo");
            backlogDto.setModuleName("工作协同");
            backlogDto.setTitle(collaborationInfo.getSubject());
            backlogDto.setExtensionJson("{}");
            backlogDto.setPcUrl(zwddProperties.getWebUrl() + "/#/document/collaboration/info?sidebarHide=false&navbar=false&id=" + collaborationInfo.getId());
            //backlogDto.setPcUrl(zwddProperties.getWebUrl()+"/#/meetingMgt/meetingNote/feedBackIndex");
            //backlogDto.setUrl(zwddProperties.getAppUrl() + "/#/checkSee?id=" + collaborationInfo.getId());
            backlogDto.setUrl(zwddProperties.getAppUrl() + "workbench" +
                    "" +
                    "");
            backlogDto.setUrgent(0);
            backlogDto.setUserId(collaborationInfo.getCreateBy());
            backlogDto.setLogo("gzxt");
            backlogDto.setBacklogUserList(backlogUserList);
            Boolean bool = OaUtil.pushToBacklog(backlogDto);
        }
        return collaborationInfo.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long doEdit(A10CollaborationInfo collaborationInfo) {
        final String currentUsername = collaborationInfo.getUserType() == TaskConstants.NUMBER_TWO ? SecurityUtils.getBindDeptUserName() : SecurityUtils.getCurrentUsername();
        A10CollaborationInfo infoNew = collaborationInfoRepository.findById(collaborationInfo.getId()).orElseGet(A10CollaborationInfo::new);
        BeanUtil.copyProperties(collaborationInfo,infoNew, CopyOptions.create().setIgnoreNullValue(true));
    /*    infoNew.setContent(collaborationInfo.getContent());
        infoNew.setFj(collaborationInfo.getFj());
        infoNew.setSubject(collaborationInfo.getSubject());*/
        collaborationInfoRepository.save(infoNew);


     
        return collaborationInfo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doDelete(Long[] ids) {
        if (ids.length > 0) {
            List<Long> list = Arrays.stream(ids).collect(Collectors.toList());
            List<A10CollaborationInfoCopy> infoList = list.stream().map(p -> {
                return a10CollaborationInfoCopyRepository.findById(p).orElseGet(A10CollaborationInfoCopy::new);
            }).collect(Collectors.toList());
            a10CollaborationInfoCopyRepository.deleteAll(infoList);

            //删除待办
            for (int i = 0; i < ids.length; i++) {
                String bizId = ids[i].toString();
                DeleteByBizIdBo deleteByBizIdBo = DeleteByBizIdBo.builder().bizId(bizId).build();
                OaUtil.deleteBackLog(deleteByBizIdBo);
            }

        }
    }
    

    @Override
    public void doFirstRead(Long id) {
        List<A10CollaborationAssignee> assigneeList = collaborationAssigneeRepository.findByInfoIdAndStatus(id, "new");
        for (int i = 0; i < assigneeList.size(); i++) {
            A10CollaborationAssignee assignee = assigneeList.get(i);
            assignee.setStatus("signed");
            assignee.setReadingTime(new Date());
            assignee.setSignedTime(new Date());
        }

        collaborationAssigneeRepository.saveAll(assigneeList);

    }


    /**
     * 重新发送未办理的待办消息
     */
    @Override
    public void sendBacklogFromUnsignedList() {
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT a.*,wd_backlog.id,wd_backlog.title,wd_backlog_transactor.transactor_id \n");
        sql.append(" FROM\n");
        sql.append(" (\n");
        sql.append(" select a10_collaboration_assignee.info_id,a10_collaboration_assignee.handle_user,a10_collaboration_assignee.from_user,a10_collaboration_assignee.create_time \n");
        sql.append(" from a10_collaboration_assignee \n");
        sql.append(" LEFT JOIN a10_collaboration_handle_log \n");
        sql.append(" ON a10_collaboration_assignee.info_id = a10_collaboration_handle_log.info_id AND \n" +
                "a10_collaboration_assignee.handle_user = a10_collaboration_handle_log.handle_user \n");
        sql.append(" WHERE a10_collaboration_handle_log.id is null \n");
        sql.append(" ) a \n");
        sql.append(" LEFT JOIN wd_backlog on a.info_id=wd_backlog.biz_id \n");
        sql.append(" LEFT JOIN wd_backlog_transactor on wd_backlog.id=wd_backlog_transactor.backlog_id AND a.handle_user=wd_backlog_transactor.transactor_id\n");
        sql.append(" WHERE wd_backlog.id is not null AND wd_backlog_transactor.id is null");
        List resultList = entityManager.createNativeQuery(sql.toString()).getResultList();

        if (resultList != null && resultList.size() > 0) {
            resultList.stream().forEach(o -> {
                Object[] objects = (Object[]) o;
                String id = objects[0].toString();
                String username = objects[1].toString();
                String fromUser = objects[2].toString();
                String title = objects[5].toString();
                Date date = (Date) objects[3];

                BacklogDto backlogDto = new BacklogDto();
                backlogDto.setAppId("OA");
                backlogDto.setBizId(id);
                backlogDto.setModuleCode("collaborationInfo");
                backlogDto.setModuleName("工作协同");
                backlogDto.setTitle(title);
                backlogDto.setExtensionJson("{}");
                backlogDto.setPcUrl(zwddProperties.getWebUrl() + "/#/document/collaboration/info?sidebarHide=false&navbar=false&id=" + id);
                //backlogDto.setPcUrl(zwddProperties.getWebUrl()+"/#/meetingMgt/meetingNote/feedBackIndex");
                backlogDto.setUrl(zwddProperties.getAppUrl() + "workbench");
                // backlogDto.setUrl(zwddProperties.getAppUrl() + "/#/document/collaboration/info?sidebarHide=false&navbar=false&id=" + id);
                backlogDto.setUrgent(0);
                backlogDto.setUserId(fromUser);
                backlogDto.setLogo("gzxt");
                List<BacklogUserDto> backlogUserList = new ArrayList<>();
                BacklogUserDto backlogUser = new BacklogUserDto();
                backlogUser.setCc(0);
                backlogUser.setUserId(username);
                backlogUser.setCreateDate(date.getTime() / 1000);
                backlogUserList.add(backlogUser);
                backlogDto.setBacklogUserList(backlogUserList);
                Boolean bool = OaUtil.pushToBacklog(backlogDto);
            });
        }
    }

    @Override
    public String smsUrge(SmsInfo smsInfo) {

        // "状态[草稿(draft)，未读(new),已读/未签收(unsign),已签收(signed)]"
        //默认通知未签收
        String[] status = {"new", "unsign"};
        if (smsInfo.getType() == TaskConstants.NUMBER_ONE) {
            status = new String[]{"signed"};
        } else if (smsInfo.getType() == TaskConstants.NUMBER_TWO) {
            status = new String[]{"new", "unsign", "signed"};
        }
        String[] fromNameIn = null;
        String sendName = "";
        if (smsInfo.getUserType() == 1) {
            fromNameIn = new String[]{SecurityUtils.getCurrentUsername()};
            sendName = ((JwtUserDto) SecurityUtils.getCurrentUser()).getUser().getNickName();
        } else if (smsInfo.getUserType() == 2) {
            fromNameIn = new String[]{SecurityUtils.getBindDeptUserName()};
            sendName = SecurityUtils.getBindDeptName();
        }
        A10CollaborationInfo collaborationInfo = collaborationInfoRepository.findById(smsInfo.getId()).get();
        List<A10CollaborationAssignee> collaborationAssigneeList = collaborationAssigneeRepository.findByInfoIdAndFromUserInAndStatusIn(
                smsInfo.getId(),
                fromNameIn,
                status);
        String content = "";
        if (collaborationAssigneeList.size() > 0) {
            Set<String> userList = collaborationAssigneeList.stream().map(assignee -> assignee.getHandleUser()).collect(Collectors.toSet());
            // 短信通知接收人
            Boolean sms = smsInfo.getSms();
            if (sms != null && sms) {
                MasSendContentBO bo = new MasSendContentBO();
                WdSysOption firstByKey = wdSysOptionRepository.getFirstByKey("A10Collaboration.SMS");
                Assert.notNull(firstByKey, "短信模板没有配置!");
                String value = firstByKey.getValue();
                Map map = new HashMap();
                map.put("username", sendName);
                map.put("bt", collaborationInfo.getSubject());
                content = SMSFormatUtil.processTemplate(value, map);
                bo.setContent(content);
                List<MasUserBO> userBOList = userList.stream().map(assignee -> {
                    MasUserBO masUserBO = new MasUserBO();
                    User handleUser = userRepository.findByUsername(assignee);
                    masUserBO.setId(String.valueOf(handleUser.getId()));
                    masUserBO.setNickName(handleUser.getNickName());
                    masUserBO.setPhone(handleUser.getPhone());
                    return masUserBO;
                }).collect(Collectors.toList());
                bo.setUserBOList(userBOList);
                bo.setSendDate(new Date());
                bo.setTiming(0);
                masBusinessService.sendSMContent(bo, 0, 0);
            }
            // 是否浙政钉通知
            Boolean ding = smsInfo.getDing();
            if (ding != null && ding) {
                final String dingContent = content;
                // 浙政钉消息通知
                userList.stream().forEach(assignee -> {
                    List<SysUserPlatform> sysUserPlatforms = sysUserPlatformRepository.findByUserNameAndType(assignee, zwddProperties.getType());
                    String receiverIds = sysUserPlatforms.stream().map(SysUserPlatform::getPlatformUserId).collect(Collectors.joining(","));
                    WorkNotificationBO workNotificationBO = new WorkNotificationBO();
                    workNotificationBO.setReceiverIds(receiverIds);
                    workNotificationBO.setType(1);
                    WorkNotificationLinkBO workNotificationLinkBO = new WorkNotificationLinkBO();
                    workNotificationLinkBO.setText(dingContent.toString());
                    workNotificationLinkBO.setTitle("工作协同");
                    String serviceUrl = zwddProperties.getAppUrl() + "workbench";
                    // String serviceUrl = zwddProperties.getAppUrl() + "/#/checkSee?id=" + collaborationInfo.getId();
                    workNotificationLinkBO.setMessageUrl(serviceUrl);
                    workNotificationBO.setLinkBO(workNotificationLinkBO);
                    zwddService.workNotification(workNotificationBO);

          /*              String[] split = receiverIds.split(",");
                        for (String id : split) {
                            TaskCreateBO taskBO = TaskCreateBO.builder()
                                    .subject(collaborationInfo.getSubject())
                                    .creatorId(SecurityUtils.getCurrentUsername())
                                    .bizTaskId(collaborationInfo.getId().toString())
                                    .url(serviceUrl)
                                    .mobileUrl(serviceUrl)
                                    .assigneeId(id)
                                    .build();
                            zwddService.createTask(taskBO);*/

                });
            }
        }
        return "催收成功";
    }

    @Override
    public Map getSms(Long id, Long userType) {
        WdSysOption firstByKey = wdSysOptionRepository.getFirstByKey("A10Collaboration.SMS");
        Assert.notNull(firstByKey, "短信模板没有配置!");
        String value = firstByKey.getValue();
        A10CollaborationInfo info = collaborationInfoRepository.findById(id).get();

        Map map = new HashMap();
        String sendName = "";
        if (userType == TaskConstants.NUMBER_ONE) {
            sendName = ((JwtUserDto) SecurityUtils.getCurrentUser()).getUser().getNickName();
        } else if (userType == TaskConstants.NUMBER_TWO) {
            sendName = SecurityUtils.getBindDeptName();
        }
        map.put("username", sendName);
        map.put("bt", info.getSubject());
        String content = SMSFormatUtil.processTemplate(value, map);

        List<Map<String, Object>> users = new ArrayList<>();
        Map<String, Object> unsign = new HashMap<>(3);
        Map<String, Object> signed = new HashMap<>(3);
        users.add(unsign);
        users.add(signed);
        List<A10CollaborationAssignee> assignees = info.getAssignees();
        if (assignees != null) {
            Set<Map> collect = assignees.stream().filter(assignee -> "new".equals(assignee.getStatus()))
                    .map(assignee -> {
                        Map node = new HashMap(2);
                        node.put("id", assignee.getHandleUser());
                        User user = userRepository.findByUsername(assignee.getHandleUser());
                        if (user != null) {
                            assignee.setHandleUserName(user.getNickName());
                        }
                        node.put("label", assignee.getHandleUserName());
                        return node;
                    }).collect(Collectors.toSet());
            unsign.put("id", -1);
            unsign.put("label", "未签收");
            unsign.put("children", collect);

            Set<Map> collect1 = assignees.stream().filter(assignee -> "signed".equals(assignee.getStatus()))
                    .map(assignee -> {
                        Map node = new HashMap(2);
                        node.put("id", assignee.getHandleUser());
                        User user = userRepository.findByUsername(assignee.getHandleUser());
                        if (user != null) {
                            assignee.setHandleUserName(user.getNickName());
                        }
                        node.put("label", assignee.getHandleUserName());
                        return node;
                    }).collect(Collectors.toSet());
            signed.put("id", -2);
            signed.put("label", "已签收");
            signed.put("children", collect1);


        }
        Map result = new HashMap(2);
        result.put("content", content);
        result.put("users", users);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultJson<JSONObject> doTransferToDeptOA(Long id) {
        A10CollaborationInfo collaborationInfo = collaborationInfoRepository.findById(id).orElseThrow(() -> new BadRequestException("数据不存在"));
        List<Long> fjIds = collaborationInfo.getAttachments();
        List<String> fjIds2Str = fjIds.stream().map(aLong -> {
            return String.valueOf(aLong);
        }).collect(Collectors.toList());
        List<StorageBiz> fjList = storageBizRepository.findAllByStorageIdIn(fjIds2Str);
        String bt = collaborationInfo.getSubject();
        String conten = HtmlUtil.cleanHtmlTag(collaborationInfo.getContent()).replaceAll("&nbsp;", "");
        JSONObject result = new JSONObject();
        //标题
        result.put("bt", bt);
        //备注
        result.put("bz", conten);
        String createBy = collaborationInfo.getCreateBy();
        User user = userRepository.findByUsername(createBy);
        //来文单位
        if (user.getUserType() == TaskConstants.NUMBER_ONE)
            result.put("lwdw", SecurityUtils.getUnitName(user.getDept().getId()));
        else
            result.put("lwdw", user.getNickName());
        //附件
        List<JSONObject> storageIds = new ArrayList<>();
        fjList.stream().forEach(storageBiz -> {
            createStorage(storageIds, storageBiz);
        });
        result.put("fj", storageIds);

        return ResultJson.generateResult(result);
    }

    private void createStorage(List<JSONObject> storageIds, StorageBiz storageBiz) {
        LocalStorage localStorage = localStorageRepository.findById(new Long(storageBiz.getStorageId())).get();
        if (localStorage != null) {
            LocalStorage storage = new LocalStorage();
            storage.copy(localStorage);
            storage.setId(null);
            JSONObject s = new JSONObject();
            s.put("storageId", localStorageRepository.save(storage).getId());
            storageIds.add(s);
        }
    }
}
