package com.hzwangda.aigov.modules.collaboration.domain.dto;

import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import com.wangda.oa.service.dto.LocalStorageDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @program: oa-mgt-server
 * @description:
 * @author: liux
 * @create: 2021-08-12 14:57
 */
@Data
@ApiModel(value = "返回工作协同-草稿状态编辑")
public class A10CollaborationInfoDto {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "主题")
    private String subject;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "紧急程度")
    private String urgent;

    @ApiModelProperty(value = "状态[草稿(draft),已发送(sent)]")
    private String status;

    @ApiModelProperty(value = "相关附件Id")
    private List<Long> fileId;

    @ApiModelProperty(value = "相关附件")
    private List<LocalStorageDto> attachments;

    @ApiModelProperty(value = "接收用户")
    private List<SimpleUserDto> assignees;
    //需处理SimpleUserDto类型
}
