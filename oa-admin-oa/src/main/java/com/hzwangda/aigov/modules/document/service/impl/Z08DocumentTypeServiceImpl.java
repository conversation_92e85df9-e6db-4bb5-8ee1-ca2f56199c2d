package com.hzwangda.aigov.modules.document.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.docconvert.common.domain.entity.SysStorageConversion;
import com.hzwangda.aigov.docconvert.common.repository.SysStorageConversionRepository;
import com.hzwangda.aigov.docconvert.common.service.FormatConvertService;
import com.hzwangda.aigov.modules.document.dto.Z08DocumentTypeDto;
import com.hzwangda.aigov.modules.document.dto.Z08DocumentTypeQueryCriteria;
import com.hzwangda.aigov.modules.document.dto.mapstruct.Z08DocumentTypeMapper;
import com.hzwangda.aigov.modules.document.entity.Z08DocumentType;
import com.hzwangda.aigov.modules.document.repository.Z08DocumentTypeRepository;
import com.hzwangda.aigov.modules.document.service.Z08DocumentTypeService;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.modules.workflow.repository.ReferenceNumberRepository;
import com.hzwangda.aigov.oa.util.PdfUtil;
import com.wangda.oa.config.FileProperties;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.workflow.domain.common.WFStorageBiz;
import com.wangda.oa.service.IStorageService;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.service.dto.LocalStorageDto;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Tuple;
import javax.persistence.criteria.Predicate;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: zhangzhanlong
 * @date: 2022/10/26 9:55
 * @description:
 */
@Service
@RequiredArgsConstructor
public class Z08DocumentTypeServiceImpl implements Z08DocumentTypeService {
    private final Z08DocumentTypeRepository documentTypeRepository;
    private final Z08DocumentTypeMapper documentTypeMapper;
    private final IStorageService storageService;
    private final StorageManageService storageManageService;
    private final SysStorageConversionRepository sysStorageConversionRepository;
    private final FileProperties properties;
    private final FormatConvertService formatService;
    private final HistoryService historyService;
    private final ReferenceNumberRepository referenceNumberRepository;

    @Override
    @Transactional
    public Object saveDocumentType(Z08DocumentType documentType) {
        Z08DocumentType save;
        Z08DocumentType byDzAndAppId = getByDzAndAppId(documentType.getDz(), documentType.getApplicationId());
        if(ObjectUtil.isNotEmpty(documentType.getId())) {
            if(Objects.nonNull(byDzAndAppId)) {
                if(!byDzAndAppId.getId().equals(documentType.getId())) {
                    throw new BadRequestException("该应用代字已存在！");
                }
            }
            if(ObjectUtil.isNotEmpty(documentType.getFj())) {
                WFStorageBiz biz = generateThumbnails(documentType.getFj().getStorageId());
                documentType.setThumbnail(biz);
            }
            Z08DocumentType documentByid = documentTypeRepository.findById(documentType.getId()).orElseGet(Z08DocumentType::new);
            documentByid.copy(documentType);

            save = documentTypeRepository.save(documentByid);
        }else {
            if(Objects.nonNull(byDzAndAppId)) {
                throw new BadRequestException("该应用代字已存在！");
            }
            if(ObjectUtil.isNotEmpty(documentType.getFj())) {
                WFStorageBiz biz = generateThumbnails(documentType.getFj().getStorageId());
                documentType.setThumbnail(biz);
            }
            save = documentTypeRepository.save(documentType);
        }
        return save;
    }

    public WFStorageBiz generateThumbnails(String storageId) {
        SysStorageConversion sysStorageConversion = sysStorageConversionRepository.findByOriginalStorageIdAndConversionType(Long.valueOf(storageId), "pdf");
        if(Objects.isNull(sysStorageConversion)){
            formatService.officeClean(Long.valueOf(storageId));
            sysStorageConversion = sysStorageConversionRepository.findByOriginalStorageIdAndConversionType(Long.valueOf(storageId), "pdf");
        }
        LocalStorageDto localStorageDto = storageManageService.findById(sysStorageConversion.getConversionStorageId());
        String basePath = properties.getPath().getPath();
        String relativePath = localStorageDto.getPath();
        if(!relativePath.startsWith(basePath)) {
            relativePath = basePath + localStorageDto.getPath();
        }
        String outPath = properties.getPath().getPath() + "图片" + File.separator;
        List<String> strings = PdfUtil.pdf2Image(relativePath, outPath, 150);
        if(CollUtil.isNotEmpty(strings)) {
            String s = strings.get(0);
            File file = new File(s);

            LocalStorage storage = storageService.create("", file);
            WFStorageBiz biz = new WFStorageBiz();
            biz.setStorageId(storage.getId().toString());
            return biz;
        }
        return null;
    }

    @Override
    public Object getDocumentTypePage(Z08DocumentTypeQueryCriteria criteria, Pageable pageable) {
        Specification<Z08DocumentType> sp = (root, cq, cb) -> {
            Predicate pe;
            if (StringUtils.isBlank(criteria.getDeptId())) {
                pe = cb.isNull(root.get("deptId"));
            } else {
                pe = cb.or(
                        cb.isNull(root.get("deptId")),
                        cb.equal(root.get("deptId"), criteria.getDeptId())
                );
            }
            if(StringUtils.isNotEmpty(criteria.getSearchKeys())) {
                Predicate dz = cb.or(cb.like(root.get("dz"), "%" + criteria.getSearchKeys() + "%"));
                Predicate deptName = cb.or(cb.like(root.get("deptName"), "%" + criteria.getSearchKeys() + "%"));
                Predicate applicationName = cb.or(cb.like(root.get("applicationName"), "%" + criteria.getSearchKeys() + "%"));
                pe = cb.and(pe, cb.or(dz, deptName, applicationName));
            }

            cq.orderBy(
                    cb.asc(root.get("sort"))
            );
            return pe;
        };
        Page<Z08DocumentType> all = documentTypeRepository.findAll(sp, pageable);
        all.stream().forEach(z08DocumentType -> {
            String deptId = z08DocumentType.getDeptId();
            if (StringUtils.isEmpty(deptId)) {
                deptId = criteria.getDeptId();
            }
            if (StringUtils.isNotEmpty(deptId)) {
                List<Tuple> maxXh = referenceNumberRepository.findMaxXhByDzAndBelongToDeptGroupByNh(z08DocumentType.getDz(), deptId);
                if (maxXh != null && maxXh.size() > 0) {
                    Map map = new HashMap();
                    maxXh.stream().forEach(m -> {
                        if (m.get("nh") != null && m.get("max") != null) {
                            map.put(m.get("nh").toString(), m.get("max"));
                        }
                    });
                    z08DocumentType.setMaxXh(JSONObject.toJSONString(map));
                }
            }
        });
        Page<Z08DocumentTypeDto> map = all.map(documentTypeMapper::toDto);
        return map;
    }

    @Override
    public Object getDocumentTypeList(Z08DocumentTypeQueryCriteria criteria) {
        Specification<Z08DocumentType> sp = (root, cq, cb) -> {
            Predicate pe = QueryHelp.getPredicate(root, criteria, cb);
            if(StringUtils.isNotEmpty(criteria.getSearchKeys())) {
                Predicate dz = cb.or(cb.like(root.get("dz"), "%" + criteria.getSearchKeys() + "%"));
                Predicate deptName = cb.or(cb.like(root.get("deptName"), "%" + criteria.getSearchKeys() + "%"));
                Predicate applicationName = cb.or(cb.like(root.get("applicationName"), "%" + criteria.getSearchKeys() + "%"));
                pe = cb.and(pe, cb.and(dz, deptName, applicationName));
            }
            if(Objects.isNull(criteria.getDeptId())) {
                pe = cb.and(pe, cb.isNull(root.get("deptId")));
            }
            cq.orderBy(
                    cb.asc(root.get("sort"))
            );
            return pe;
        };
        List<Z08DocumentType> all = documentTypeRepository.findAll(sp);
        List<Z08DocumentTypeDto> collect = all.stream().map(documentTypeMapper::toDto).collect(Collectors.toList());
        return collect;
    }

    @Override
    public Object getOneById(Long id) {
        Z08DocumentType z08DocumentType = documentTypeRepository.findById(id).orElseGet(Z08DocumentType::new);
        return z08DocumentType;
    }

    @Override
    public void deleteById(Long id) {
        documentTypeRepository.deleteById(id);
    }

    @Override
    public Object getDzList(String dz) {
        List<Z08DocumentType> byDzLike = documentTypeRepository.findByDzLike("%" + dz + "%");
        return byDzLike;
    }

    @Override
    public Z08DocumentType getByDzAndAppId(String dz, Long appId) {
        return documentTypeRepository.findByDzAndAndApplicationId(dz, appId);
    }


    @Override
    public List<Z08DocumentType> getByAppId(Long appId) {
        List<Z08DocumentType> byApplicationId = documentTypeRepository.findByApplicationId(appId);
        return byApplicationId;
    }

    @Override
    public List<Z08DocumentType> getByAppIdWithoutDeptId(Long appId) {
        List<Z08DocumentType> byApplicationId = documentTypeRepository.findByApplicationIdAndDeptId(appId,null);
        return byApplicationId;
    }


    @Override
    public List<Z08DocumentType> getByAppId(Long appId,String deptId) {
        List<Z08DocumentType> byApplicationId = documentTypeRepository.findByApplicationIdAndDeptId(appId,deptId);
        return byApplicationId;
    }

    @Override
    public List<String> getByProcInstIdAndDept(String procInstId) {
        String belongToDept = SecurityUtils.getCurrentBelongToDept();
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(procInstId).singleResult();
        String businessKey = historicProcessInstance.getBusinessKey();
        Specification<Z08DocumentType> sp = (root, cq, cb) -> {

            Predicate and1 = cb.and(cb.equal(root.get("applicationId"), businessKey), cb.equal(root.get("deptId"), belongToDept));
            Predicate and2 = cb.and(cb.equal(root.get("applicationId"), businessKey), cb.isNull(root.get("deptId")));
            Predicate pe = cb.or(and1, and2);
            return pe;
        };
        List<Z08DocumentType> all = documentTypeRepository.findAll(sp);
        List<String> collect = all.stream().map(Z08DocumentType::getDz).collect(Collectors.toList());
        return collect;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateMaxWh(String dz, Integer nh, Integer wh, String belongToDept) {
        Integer maxXhByDzAndNh = referenceNumberRepository.getMaxXhByDzAndNh(dz, String.valueOf(nh), belongToDept);
        List<ReferenceNumber> all = referenceNumberRepository.findAll((root, query, criteriaBuilder) -> {
            return criteriaBuilder.and(
                    criteriaBuilder.equal(root.get("dz"), dz),
                    criteriaBuilder.equal(root.get("nh"), nh),
                    criteriaBuilder.equal(root.get("xh"), maxXhByDzAndNh),
                    criteriaBuilder.equal(root.get("belongToDept"), belongToDept)
            );
        });
        all.stream().forEach(referenceNumber -> {
            referenceNumber.setXh(wh);
        });
        referenceNumberRepository.saveAll(all);
        return "更新成功";
    }
}
