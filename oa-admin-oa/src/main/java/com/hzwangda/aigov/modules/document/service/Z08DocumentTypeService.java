package com.hzwangda.aigov.modules.document.service;

import com.hzwangda.aigov.modules.document.dto.Z08DocumentTypeQueryCriteria;
import com.hzwangda.aigov.modules.document.entity.Z08DocumentType;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface Z08DocumentTypeService {
    Object saveDocumentType(Z08DocumentType documentType);

    Object getDocumentTypePage(Z08DocumentTypeQueryCriteria criteria, Pageable pageable);

    Object getDocumentTypeList(Z08DocumentTypeQueryCriteria criteria);

    Object getOneById(Long id);

    void deleteById(Long id);

    Object getDzList(String dz);

    Z08DocumentType getByDzAndAppId(String dz, Long appId);

    List<Z08DocumentType> getByAppId(Long appId);
    List<Z08DocumentType> getByAppId(Long appId,String deptId);
    List<Z08DocumentType> getByAppIdWithoutDeptId(Long appId);

    List<String> getByProcInstIdAndDept(String procInstId);

    String updateMaxWh(String dz, Integer nh, Integer wh, String belongToDept);
}
