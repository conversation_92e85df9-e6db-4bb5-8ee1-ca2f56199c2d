package com.hzwangda.aigov.modules.document.dto;

import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlzUser;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * 收文单
 *
 * <AUTHOR>
 * @date 2021/7/17下午4:29
 */
@Data
public class A07DocumentGwlzUserDto extends BaseDomain {

    @ApiModelProperty(value = "待签收用户")
    private Set<A07DocumentGwlzUser> waitList;

    @ApiModelProperty(value = "待签收用户")
    private Set<A07DocumentGwlzUser> completeList;

}
