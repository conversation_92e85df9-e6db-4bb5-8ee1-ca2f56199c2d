package com.hzwangda.aigov.modules.archive.controller;

import com.hzwangda.aigov.modules.archive.service.FileArchiveService;
import com.wangda.oa.annotation.rest.AnonymousGetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping("/api/archive")
public class ArchiveDownloadController {
    @Resource
    private FileArchiveService fileArchiveService;


    @AnonymousGetMapping("/download")
    public void download(@RequestParam String name, HttpServletRequest request, HttpServletResponse response) throws IOException {
        fileArchiveService.download(name, request, response);
    }
}
