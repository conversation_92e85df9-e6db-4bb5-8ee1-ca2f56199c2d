package com.hzwangda.aigov.modules.appControl.enums;

import com.alibaba.fastjson.annotation.JSONType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * @author: cy
 * @create: 2022-11-11 09:55
 **/

@AllArgsConstructor
@ToString
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@JSONType(serializeEnumAsJavaBean = true)
public enum SystemTypeEnum {

    PC_TYPE("PC_TYPE", "PC端"),
    MOBILE_TYPE("MOBILE_TYPE", "移动端");


    private String value;
    private String name;

    public static SystemTypeEnum getByValue(String value) {
        for(SystemTypeEnum systemTypeEnum : SystemTypeEnum.values()) {
            if(systemTypeEnum.value.equals(value)) {
                return systemTypeEnum;
            }
        }
        return null;
    }
}
