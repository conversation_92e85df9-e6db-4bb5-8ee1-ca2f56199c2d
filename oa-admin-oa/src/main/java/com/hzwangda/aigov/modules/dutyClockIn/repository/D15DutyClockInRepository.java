package com.hzwangda.aigov.modules.dutyClockIn.repository;

import cn.hutool.core.date.DateTime;
import com.hzwangda.aigov.modules.dutyClockIn.domain.entity.D15DutyClockIn;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Date;

public interface D15DutyClockInRepository extends JpaRepository<D15DutyClockIn, Long>, JpaSpecificationExecutor<D15DutyClockIn> {

    boolean existsByUsernameAndClockInTimeAfter(String currentUsername, Date today);

    D15DutyClockIn findFirstByUsernameAndClockInTimeAfter(String currentUsername, DateTime today);

}
