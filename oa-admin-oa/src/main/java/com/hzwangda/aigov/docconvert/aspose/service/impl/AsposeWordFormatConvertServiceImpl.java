package com.hzwangda.aigov.docconvert.aspose.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import com.aspose.cells.PdfSaveOptions;
import com.aspose.cells.Workbook;
import com.aspose.words.Document;
import com.aspose.words.FontSettings;
import com.aspose.words.License;
import com.aspose.words.SaveFormat;
import com.hzwangda.aigov.docconvert.aspose.config.AsposeProperties;
import com.hzwangda.aigov.docconvert.common.domain.entity.SysStorageConversion;
import com.hzwangda.aigov.docconvert.common.enums.FileConversionEnum;
import com.hzwangda.aigov.docconvert.common.service.FormatConvertService;
import com.hzwangda.aigov.docconvert.common.service.SysStorageConversionService;
import com.hzwangda.aigov.oa.bo.MasSendContentBO;
import com.hzwangda.aigov.oa.bo.MasUserBO;
import com.hzwangda.aigov.oa.repository.WdSysOptionRepository;
import com.hzwangda.aigov.oa.service.MasBusinessService;
import com.hzwangda.aigov.officeonline.wps.util.WpsFileUtil;
import com.wangda.oa.config.FileProperties;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.modules.extension.domain.WdSysOption;
import com.wangda.oa.modules.system.exception.CustomException;
import com.wangda.oa.service.IStorageService;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.service.dto.LocalStorageDto;
import com.wangda.oa.utils.FileUtil;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service("formatConvertServiceImpl")
@ConditionalOnProperty(value = "docconvert.version", havingValue = "aspose-word", matchIfMissing = true)
@RequiredArgsConstructor
@Slf4j
public class AsposeWordFormatConvertServiceImpl implements FormatConvertService {

    private final AsposeProperties asposeProperties;
    private final FileProperties fileProperties;
    private final StorageManageService storageManageService;
    private final IStorageService storageService;
    private final SysStorageConversionService storageConversionService;
    private final  WdSysOptionRepository wdSysOptionRepository;
    private final MasBusinessService masBusinessService;

    @PostConstruct
    private void loadLicense() throws Exception {
        InputStream is = null;

        String fontsDirPath = asposeProperties.getFontsDirPath();
        if(Objects.nonNull(fontsDirPath) && fontsDirPath.length() > 0) {
            FontSettings.getDefaultInstance().setFontsFolder(fontsDirPath, true);
        }


        String licenseFilePath = asposeProperties.getLicenseFilePath();
        if(StringUtils.isBlank(licenseFilePath)) {
            return;
        }else if(licenseFilePath.startsWith("classpath:")) {
            is = this.getClass().getResourceAsStream(licenseFilePath.substring("classpath:".length()));
        }else if(licenseFilePath.startsWith("file:")) {
            is = new FileInputStream(licenseFilePath.substring("file:".length()));
        }else {
            is = new FileInputStream(licenseFilePath);
        }

        License aposeLic = new License();
        aposeLic.setLicense(is);

        // 上面设置license后流关闭了，需要重新获
        if(licenseFilePath.startsWith("classpath:")) {
            is = this.getClass().getResourceAsStream(licenseFilePath.substring("classpath:".length()));
        }else if(licenseFilePath.startsWith("file:")) {
            is = new FileInputStream(licenseFilePath.substring("file:".length()));
        }else {
            is = new FileInputStream(licenseFilePath);
        }
        License asposeCellsLic = new License();
        asposeCellsLic.setLicense(is);

    }

    @Override
    public boolean convertToFormats(Long storageId, FileConversionEnum[] formats) {
        if (ArrayUtils.isEmpty(formats)) {
            // 无需格式转化，视为成功
            return true;
        }
        LocalStorageDto storageDto = storageManageService.findById(storageId);
        if (Objects.isNull(storageDto)) {
            log.error("转化出错，未查找到对应文件，文件id：{}，当前操作人：{}",storageId,SecurityUtils.getCurrentUsername());
            return false;
        }
        log.info("文件《{}》进行转化，操作人：{}",storageDto.getName(),SecurityUtils.getCurrentUsername());

        InputStream is = storageService.getInputStream(storageDto.getPath());
        Document orgiDoc = null;
        try {
            if(!ArrayUtils.contains(WpsFileUtil.etExts, storageDto.getSuffix().toLowerCase())) {
                orgiDoc = new Document(is);
                orgiDoc.acceptAllRevisions(); // 去除批注
            }

            // 清稿
            if (ArrayUtils.contains(formats, FileConversionEnum.DOC)) {
                saveAs(orgiDoc, storageDto, conversionMap.get("docx"));
            }

            // PDF格式
            if (ArrayUtils.contains(formats, FileConversionEnum.PDF)) {
                saveAs(orgiDoc, storageDto, conversionMap.get("pdf"));
            }

            // TXT格式
            if (ArrayUtils.contains(formats, FileConversionEnum.TXT)) {
                saveAs(orgiDoc, storageDto, conversionMap.get("txt"));
            }
            log.info("文件《{}》转化成功，操作人：{}",storageDto.getName(),SecurityUtils.getCurrentUsername());
            return true;
        } catch (Exception e) {
            //去除其他非正常格式转化影响
            if(ArrayUtils.contains(new String[]{"doc","docx","wps"}, storageDto.getSuffix().toLowerCase())){
                log.error("文件《{}》转化出错，异常原因：{}",storageDto.getName(),e.toString());
                e.printStackTrace();
                MasSendContentBO bo = new MasSendContentBO();
                WdSysOption firstByKey = wdSysOptionRepository.getFirstByKey("A07Doc.DocConvertAdmin");
                Assert.notNull(firstByKey, "doc转化失败通知用户没有配置!");
                String userStr = firstByKey.getValue();
                List<MasUserBO> userBOList = Arrays.stream(userStr.split(",")).map(user -> {
                    MasUserBO masUserBO = new MasUserBO();
                    masUserBO.setId("superAdmin");
                    masUserBO.setNickName("转化服务管理员");
                    masUserBO.setPhone(user);
                    return masUserBO;
                }).collect(Collectors.toList());
                String content ="南浔OA系统文件《"+storageDto.getName()+"》转化出错，请及时处理";
                bo.setContent(content);
                bo.setUserBOList(userBOList);
                bo.setSendDate(new Date());
                bo.setTiming(0);
                masBusinessService.sendSMContent(bo, 0, 0);
            }
        } finally {
            IoUtil.close(is);
        }

        return false;
    }

    @Override
    public boolean officeClean(Long storageId) {
        return convertToFormats(storageId, new FileConversionEnum[]{
                FileConversionEnum.DOC, FileConversionEnum.PDF, FileConversionEnum.TXT});
    }

    @Override
    public boolean convertToPdf(Long storageId, String username) {
        LocalStorageDto storageDto = storageManageService.findById(storageId);
        if(Objects.isNull(storageDto)) {
            return false;
        }

        InputStream is = storageService.getInputStream(storageDto.getPath());
        try {
            Document orgiDoc = new Document(is);
            orgiDoc.acceptAllRevisions(); // 去除批注

            // PDF格式
            saveAs(orgiDoc, storageDto, conversionMap.get("pdf"));
        }catch(Exception e) {
            e.printStackTrace();
        }finally {
            IoUtil.close(is);
        }
        return true;
    }

    @Override
    public String wrapHeader(Long docStorageId, Long headerTemplateStorageId) {
        return null;
    }

    @Override
    public String mergeDoc(Long firstDocStorageId, Long secondDocStorageId) {
        return null;
    }

    @Override
    public String renderBookmarks(Long docStorageId, Map<String, String> bookmarkData) {
        return null;
    }

    @Override
    public boolean checkConvertTask(String taskId) {
        return false;
    }

    private void saveAs(Document orgiDoc, LocalStorageDto storageDto, ConversionType conversionType) throws Exception {
        String docBasePath = storageService.isLocalStorageMode() ? fileProperties.getPath().getPath() : FileUtil.getTmpDir().getPath();
        String docRelativeFilePath = this.newRelativeFilePath(storageDto.getName(), conversionType.getSuffix());
        String docAbsoluteFilePath = docBasePath + docRelativeFilePath;
        String newFileName = storageDto.getName().split("\\.")[0] + "." + conversionType.getSuffix();

        // 判断是否excel
        if(ArrayUtils.contains(WpsFileUtil.etExts, storageDto.getSuffix().toLowerCase()) && conversionType.getConversionType().equals(FileConversionEnum.PDF.getName())){
            Workbook workbook = new Workbook(docBasePath + storageDto.getPath());
            PdfSaveOptions options = new PdfSaveOptions();
            options.setOnePagePerSheet(true);
            workbook.save(docAbsoluteFilePath, options);
        }else if(Objects.nonNull(orgiDoc)) {
            // 转pdf不支持宏
            if(orgiDoc.hasMacros()) {
                orgiDoc.removeMacros();
            }
            orgiDoc.save(docAbsoluteFilePath, conversionType.getSaveFormat());
        }else {
            throw new CustomException("文档类型不支持转换");
        }

        LocalStorage docStorage = storageService.create(newFileName, new File(docAbsoluteFilePath));

        // 若是非本地存储的（如OSS），删除临时文件。
        if(!storageService.isLocalStorageMode()) {
            FileUtil.del(docAbsoluteFilePath);
        }

        SysStorageConversion docConv = new SysStorageConversion();
        docConv.setOriginalStorageId(storageDto.getId());
        docConv.setConversionStorageId(docStorage.getId());
        docConv.setConversionType(conversionType.getConversionType());
        storageConversionService.save(docConv);
    }

    private static Map<String, ConversionType> conversionMap = new HashMap<String, ConversionType>() {
        {
            put("docx", new ConversionType("docx", FileConversionEnum.DOC.getName(), SaveFormat.DOCX));
            put("pdf", new ConversionType("pdf", FileConversionEnum.PDF.getName(), SaveFormat.PDF));
            put("txt", new ConversionType("txt", FileConversionEnum.TXT.getName(), SaveFormat.TEXT));
        }
    };

    @Data
    @AllArgsConstructor
    private static class ConversionType {
        private String suffix;
        private String conversionType;
        private int saveFormat;
    }

    private String newRelativeFilePath(String name, String suffix) {
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddhhmmssS");
        String fileName = name + "-" + format.format(date) + "." + suffix;
        //处理文件长度过长导致转换失败问题，截取文件名
        int fileNameLength = fileName.getBytes(StandardCharsets.UTF_8).length;
        if(fileNameLength > 255) {
            fileName = IdUtil.fastSimpleUUID() + "." + suffix;
        }

        String type = getFileType(suffix);
        String filePath = type + File.separator + fileName;
        return filePath;
    }
    private  String getFileType(String type) {
        String documents = "txt doc pdf ppt pps xlsx xls docx";
        String music = "mp3 wav wma mpa ram ra aac aif m4a";
        String video = "avi mpg mpe mpeg asf wmv mov qt rm mp4 flv m4v webm ogv ogg";
        String image = "bmp dib pcp dif wmf gif jpg tif eps psd cdr iff tga pcd mpt png jpeg";
        if (image.contains(type)) {
            return "图片";
        } else if (documents.contains(type)) {
            return "文档";
        } else if (music.contains(type)) {
            return "音乐";
        } else {
            return video.contains(type) ? "视频" : "其他";
        }
    }

}
