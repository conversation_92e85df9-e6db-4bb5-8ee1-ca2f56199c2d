package com.hzwangda.aigov.modules.document.dto;

import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.hzwangda.openserch.annotation.WdSearchId;
import com.hzwangda.openserch.annotation.WdSearchMetaData;
import com.hzwangda.openserch.annotation.field.*;
import com.hzwangda.openserch.emnus.DataType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@WdSearchMetaData(indexName = "fw")
public class FwSearchDto {

    @WdSearchId
    private String id;

    @ESMapping(keyword = false, datatype = DataType.keyword_type)
    private Integer wh;

    @SearchField
    private String wz;

    @SearchField
    private Integer nf;


    @ApiModelProperty(value = "标题")
    @TitleField
    private String bt;

    @ApiModelProperty(value = "正文")
    @FileField
    private StorageBiz zw;
    @CreateTimeField
    private Date createTime;

    @PermissionField
    private List<String> permission;

}
