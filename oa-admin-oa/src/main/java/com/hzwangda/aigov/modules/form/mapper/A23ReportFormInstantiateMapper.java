package com.hzwangda.aigov.modules.form.mapper;

import com.hzwangda.aigov.modules.form.convert.IdToUserConvert;
import com.hzwangda.aigov.modules.form.domain.A23ReportFormInstantiate;
import com.hzwangda.aigov.modules.form.dto.A23ReportFormInstantiateDto;
import com.wangda.oa.base.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", uses = IdToUserConvert.class, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface A23ReportFormInstantiateMapper extends BaseMapper<A23ReportFormInstantiateDto, A23ReportFormInstantiate> {

    @Override
    @Mappings({@Mapping(target = "creatorId", source = "createBy")})
    A23ReportFormInstantiateDto toDto(A23ReportFormInstantiate entity);

    @Mappings({@Mapping(target = "createBy", source = "creatorId")})
    @Override
    A23ReportFormInstantiate toEntity(A23ReportFormInstantiateDto dto);
}
