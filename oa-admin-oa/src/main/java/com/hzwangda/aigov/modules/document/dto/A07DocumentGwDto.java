package com.hzwangda.aigov.modules.document.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 收文单
 * <AUTHOR>
 * @date 2021/7/28下午7:29
 */
@Data
public class A07DocumentGwDto extends BaseDomain {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "标题")
    private String bt;
    @ApiModelProperty(value = "流程实例id")
    private String bpmInstanceId;
    @ApiModelProperty(name = "状态")
    private String bpmStatus;
    @ApiModelProperty(name = "创建人")
    private String cjr;
    @ApiModelProperty(value = "类型")
    private String moduleType;
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;
    @ApiModelProperty(value = "办理人")
    private String blr;
    @ApiModelProperty(value = "taskid")
    private String taskId;

    @ApiModelProperty(value = "文号")
    private ReferenceNumber gwwh;

    @ApiModelProperty(value = "来文文号")
    private String lwwh;

    @ApiModelProperty(value = "急件(0:非急件,1:急件)")
    private Integer urgent;

    @ApiModelProperty(value = "临期逾期状态(1、2)")
    private Integer expiredStatus;

    @ApiModelProperty(value = "初次阅读时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstReadTime;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最后已阅时间")
    private Date lastReadTime;

    @ApiModelProperty(value = "最后阅读时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endReadTime;

    @ApiModelProperty("关注标识")
    private Long favoriteId;

    @ApiModelProperty(value = "本人最后办理时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transactionTime;

}
