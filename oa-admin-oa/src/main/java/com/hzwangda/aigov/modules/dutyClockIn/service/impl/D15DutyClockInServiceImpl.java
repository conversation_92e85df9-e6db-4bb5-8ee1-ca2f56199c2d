package com.hzwangda.aigov.modules.dutyClockIn.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.hzwangda.aigov.modules.dutyClockIn.domain.dto.D15DutyClockInDto;
import com.hzwangda.aigov.modules.dutyClockIn.domain.entity.D15DutyClockIn;
import com.hzwangda.aigov.modules.dutyClockIn.domain.query.DutyClockInQueryCriteria;
import com.hzwangda.aigov.modules.dutyClockIn.repository.D15DutyClockInRepository;
import com.hzwangda.aigov.modules.dutyClockIn.service.D15DutyClockInService;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.wangda.oa.modules.extension.domain.SysDeptUserPosition;
import com.wangda.oa.modules.extension.repository.DeptUserPositionCustomRepository;
import com.wangda.oa.modules.system.domain.UserDepts;
import com.wangda.oa.modules.system.repository.UserDeptsRepository;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zzl
 * @date: 2025/4/1 15:31
 **/
@Service
@AllArgsConstructor
public class D15DutyClockInServiceImpl implements D15DutyClockInService {

    private final D15DutyClockInRepository d15DutyClockInRepository;
    private final UserService userService;
    private final DeptUserPositionCustomRepository deptUserPositionRepository;
    private final UserDeptsRepository userDeptsRepository;

    @Override
    public Boolean queryToDayIsClockIn() {
        return d15DutyClockInRepository.existsByUsernameAndClockInTimeAfter(SecurityUtils.getCurrentUsername(), DateUtil.beginOfDay(new Date()));
    }

    @Override
    public Page<D15DutyClockIn> queryPageH5(Pageable pageable) {
        Specification<D15DutyClockIn> specification = (root, query, cb) -> {
            Predicate predicate = QueryHelp.getPredicate(root, query, cb);
            predicate = cb.and(predicate, cb.equal(root.get("username"), SecurityUtils.getCurrentUsername()));
            query.orderBy(cb.desc(root.get("clockInTime")));
            return predicate;
        };
        Page<D15DutyClockIn> all = d15DutyClockInRepository.findAll(specification, pageable);
        return all;
    }

    @Override
    public D15DutyClockIn queryByToDay() {
        return d15DutyClockInRepository.findFirstByUsernameAndClockInTimeAfter(SecurityUtils.getCurrentUsername(), DateUtil.beginOfDay(new Date()));
    }

    @Override
    public D15DutyClockIn save(D15DutyClockInDto dto) {
        D15DutyClockIn d15DutyClockIn;
        if(dto.getId() != null) {
            d15DutyClockIn = d15DutyClockInRepository.findById(dto.getId()).orElse(new D15DutyClockIn());
        }else {
            // 预防前端未传ID但已经存在当天数据
            D15DutyClockIn clockIn = d15DutyClockInRepository.findFirstByUsernameAndClockInTimeAfter(SecurityUtils.getCurrentUsername(), DateUtil.beginOfDay(new Date()));
            d15DutyClockIn = new D15DutyClockIn();
            if(clockIn != null) {
                d15DutyClockIn = clockIn;
            }
        }
        d15DutyClockIn.copy(dto);
        // 每次都更新成最新的时间
        d15DutyClockIn.setClockInTime(new Date());
        UserDto userDto = userService.findByName(SecurityUtils.getCurrentUsername());
        d15DutyClockIn.setUsername(userDto.getUsername());
        d15DutyClockIn.setNickName(userDto.getNickName());
        return d15DutyClockInRepository.save(d15DutyClockIn);
    }

    @Override
    public D15DutyClockIn queryById(Long id) {
        return d15DutyClockInRepository.findById(id).orElse(null);
    }

    @Override
    public Page<D15DutyClockIn> queryByIdentityPage(DutyClockInQueryCriteria criteria, Pageable pageable) {
        Specification<D15DutyClockIn> specification = (root, query, cb) -> {
            Predicate predicate = QueryHelp.getPredicate(root, criteria, cb);
            // 查询当前登录人有什么身份
            List<SysDeptUserPosition> sysDeptUserPosition = deptUserPositionRepository.findByName(SecurityUtils.getCurrentUsername());
            // 按权限类型分组（避免后续重复遍历）
            Map<String, List<Long>> positionMap = sysDeptUserPosition.stream()
                    .collect(Collectors.groupingBy(
                            SysDeptUserPosition::getType,
                            Collectors.mapping(SysDeptUserPosition::getDeptId, Collectors.toList())
                    ));
            boolean isAdmin = !positionMap.getOrDefault("dutyClockIn:admin", Collections.emptyList()).isEmpty();
            boolean isAreaManager =!positionMap.getOrDefault("dutyClockIn:areaManager", Collections.emptyList()).isEmpty();
            boolean isTeamLeader=!positionMap.getOrDefault("dutyClockIn:teamLeader", Collections.emptyList()).isEmpty();
            // 权限优先级：admin > areaManager > teamLeader
            if(isAdmin) {
                // 管理员查看所有
                if(criteria.getDeptId() != null) {
                    List<UserDepts> byUserId = userDeptsRepository.findByDeptIdIn(Collections.singletonList(criteria.getDeptId()));
                    List<Long> userIds = byUserId.stream().map(UserDepts::getUserId).filter(Objects::nonNull).collect(Collectors.toList());
                    List<String> usernames = userService.findUsernameByIds(userIds);
                    predicate = cb.and(predicate, cb.in(root.get("username")).value(usernames));
                }
            }else if(isAreaManager) {
                // 查询我所管理的片区的所有人员-片区长查询所管片区的
                if(criteria.getDeptId() != null) {
                    if(positionMap.get("dutyClockIn:areaManager").contains(criteria.getDeptId())) {
                        List<UserDepts> byUserId = userDeptsRepository.findByDeptIdIn(Collections.singletonList(criteria.getDeptId()));
                        List<Long> userIds = byUserId.stream().map(UserDepts::getUserId).filter(Objects::nonNull).collect(Collectors.toList());
                        List<String> usernames = userService.findUsernameByIds(userIds);
                        predicate = cb.and(predicate, cb.in(root.get("username")).value(usernames));
                    }else {
                        // 如果点击不是自己的分管部门，就查询一个永远都不存在的条件
                        predicate = cb.and(predicate, cb.equal(cb.literal(1), cb.literal(0)));
                    }
                }else {
                    List<UserDepts> byUserId = userDeptsRepository.findByDeptIdIn(positionMap.get("dutyClockIn:areaManager"));
                    List<Long> userIds = byUserId.stream().map(UserDepts::getUserId).filter(Objects::nonNull).collect(Collectors.toList());
                    List<String> usernames = userService.findUsernameByIds(userIds);
                    predicate = cb.and(predicate, cb.in(root.get("username")).value(usernames));
                }
            }else if(isTeamLeader) {
                // 查询我所在部门的所有人员-组长查询本小组的
                List<UserDepts> byUserId = userDeptsRepository.findByDeptIdIn(positionMap.get("dutyClockIn:teamLeader"));
                List<Long> userIds = byUserId.stream().map(UserDepts::getUserId).filter(Objects::nonNull).collect(Collectors.toList());
                List<String> usernames = userService.findUsernameByIds(userIds);
                predicate = cb.and(predicate, cb.in(root.get("username")).value(usernames));
            }else {
                // 其他人只能查询自己的
                predicate = cb.and(predicate, cb.equal(root.get("username"), SecurityUtils.getCurrentUsername()));
            }
            return predicate;
        };
        return d15DutyClockInRepository.findAll(specification, pageable);
    }

    @Override
    public void exportDutyClockIn(HttpServletResponse response,DutyClockInQueryCriteria criteria) throws IOException {
        Page<D15DutyClockIn> d15DutyClockIns = queryByIdentityPage(criteria, Pageable.unpaged());
        List<Map<String, Object>> list = new ArrayList<>();

        for(D15DutyClockIn d15DutyClockIn : d15DutyClockIns.getContent()) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("打卡人", d15DutyClockIn.getNickName());
            map.put("打卡时间", d15DutyClockIn.getClockInTime());
            map.put("打卡地点", d15DutyClockIn.getAddress());
            map.put("工作日志", d15DutyClockIn.getContent());
            String baseUrl = "https://ding.nanxun.gov.cn:806/oa-service/api/localStorage/previewFile/";
            StringBuilder fileUrlBuilder = new StringBuilder();

            for(StorageBiz storageBiz : d15DutyClockIn.getFj()) {
                if (StringUtils.isNotBlank(fileUrlBuilder)) {
                    fileUrlBuilder.append(";\r\n");
                }
                fileUrlBuilder.append(baseUrl).append(storageBiz.getStorageId());
            }
            map.put("相关照片", fileUrlBuilder);
            list.add(map);
        }
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.renameSheet("考勤记录");
        writer.setColumnWidth(0, 17);
        writer.setColumnWidth(1, 15);
        writer.setColumnWidth(2, 30);
        writer.setColumnWidth(3, 100);
        writer.setColumnWidth(4, 255);
        writer.write(list, true);
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=Template.xlsx");
        ServletOutputStream os = response.getOutputStream();
        writer.flush(os, true);
        // 关闭writer，释放内存
        writer.close();
        //此处记得关闭输出Servlet流
        IoUtil.close(os);
        //FileUtil.downloadExcel(list, response);
    }
}
