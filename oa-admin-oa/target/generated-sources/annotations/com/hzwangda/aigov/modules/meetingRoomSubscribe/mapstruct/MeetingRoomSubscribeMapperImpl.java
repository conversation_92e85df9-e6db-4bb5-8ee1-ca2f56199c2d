package com.hzwangda.aigov.modules.meetingRoomSubscribe.mapstruct;

import com.hzwangda.aigov.modules.meetingRoomSubscribe.domain.MeetingRoomSubscribe;
import com.hzwangda.aigov.modules.meetingRoomSubscribe.dto.MeetingRoomSubscribeDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class MeetingRoomSubscribeMapperImpl implements MeetingRoomSubscribeMapper {

    @Override
    public List<MeetingRoomSubscribeDto> toDto(List<MeetingRoomSubscribe> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MeetingRoomSubscribeDto> list = new ArrayList<MeetingRoomSubscribeDto>( entityList.size() );
        for ( MeetingRoomSubscribe meetingRoomSubscribe : entityList ) {
            list.add( toDto( meetingRoomSubscribe ) );
        }

        return list;
    }

    @Override
    public MeetingRoomSubscribe toEntity(MeetingRoomSubscribeDto dto) {
        if ( dto == null ) {
            return null;
        }

        MeetingRoomSubscribe meetingRoomSubscribe = new MeetingRoomSubscribe();

        meetingRoomSubscribe.setCreateBy( dto.getCreateBy() );
        meetingRoomSubscribe.setApplicantName( dto.getApplicantName() );
        meetingRoomSubscribe.setApplicantPhpne( dto.getApplicantPhpne() );
        meetingRoomSubscribe.setAttendance( dto.getAttendance() );
        meetingRoomSubscribe.setBanner( dto.getBanner() );
        meetingRoomSubscribe.setCreateByName( dto.getCreateByName() );
        meetingRoomSubscribe.setEndTime( dto.getEndTime() );
        meetingRoomSubscribe.setHostUnit( dto.getHostUnit() );
        meetingRoomSubscribe.setId( dto.getId() );
        if ( dto.getIsMeStatus() != null ) {
            meetingRoomSubscribe.setIsMeStatus( Integer.parseInt( dto.getIsMeStatus() ) );
        }
        meetingRoomSubscribe.setIsSecrecyPath( dto.getIsSecrecyPath() );
        meetingRoomSubscribe.setIsVideoMeeting( dto.getIsVideoMeeting() );
        meetingRoomSubscribe.setMeetingAddress( dto.getMeetingAddress() );
        meetingRoomSubscribe.setMeetingName( dto.getMeetingName() );
        meetingRoomSubscribe.setMeetingPlace( dto.getMeetingPlace() );
        meetingRoomSubscribe.setMeetingRoom( dto.getMeetingRoom() );
        meetingRoomSubscribe.setParticipantsLead( dto.getParticipantsLead() );
        meetingRoomSubscribe.setRemark( dto.getRemark() );
        meetingRoomSubscribe.setRoomName( dto.getRoomName() );
        meetingRoomSubscribe.setRostrumNum( dto.getRostrumNum() );
        meetingRoomSubscribe.setStartTime( dto.getStartTime() );
        meetingRoomSubscribe.setStatus( dto.getStatus() );
        meetingRoomSubscribe.setUseDay( dto.getUseDay() );
        meetingRoomSubscribe.setVideoMeetingScope( dto.getVideoMeetingScope() );

        return meetingRoomSubscribe;
    }

    @Override
    public List<MeetingRoomSubscribe> toEntity(List<MeetingRoomSubscribeDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MeetingRoomSubscribe> list = new ArrayList<MeetingRoomSubscribe>( dtoList.size() );
        for ( MeetingRoomSubscribeDto meetingRoomSubscribeDto : dtoList ) {
            list.add( toEntity( meetingRoomSubscribeDto ) );
        }

        return list;
    }

    @Override
    public MeetingRoomSubscribeDto toDto(MeetingRoomSubscribe entity) {
        if ( entity == null ) {
            return null;
        }

        MeetingRoomSubscribeDto meetingRoomSubscribeDto = new MeetingRoomSubscribeDto();

        meetingRoomSubscribeDto.setCreateBy( entity.getCreateBy() );
        meetingRoomSubscribeDto.setId( entity.getId() );
        meetingRoomSubscribeDto.setApplicantName( entity.getApplicantName() );
        meetingRoomSubscribeDto.setApplicantPhpne( entity.getApplicantPhpne() );
        meetingRoomSubscribeDto.setAttendance( entity.getAttendance() );
        meetingRoomSubscribeDto.setBanner( entity.getBanner() );
        meetingRoomSubscribeDto.setCreateByName( entity.getCreateByName() );
        meetingRoomSubscribeDto.setEndTime( entity.getEndTime() );
        meetingRoomSubscribeDto.setHostUnit( entity.getHostUnit() );
        if ( entity.getIsMeStatus() != null ) {
            meetingRoomSubscribeDto.setIsMeStatus( String.valueOf( entity.getIsMeStatus() ) );
        }
        meetingRoomSubscribeDto.setIsSecrecyPath( entity.getIsSecrecyPath() );
        meetingRoomSubscribeDto.setIsVideoMeeting( entity.getIsVideoMeeting() );
        meetingRoomSubscribeDto.setMeetingAddress( entity.getMeetingAddress() );
        meetingRoomSubscribeDto.setMeetingName( entity.getMeetingName() );
        meetingRoomSubscribeDto.setMeetingPlace( entity.getMeetingPlace() );
        meetingRoomSubscribeDto.setMeetingRoom( entity.getMeetingRoom() );
        meetingRoomSubscribeDto.setParticipantsLead( entity.getParticipantsLead() );
        meetingRoomSubscribeDto.setRemark( entity.getRemark() );
        meetingRoomSubscribeDto.setRoomName( entity.getRoomName() );
        meetingRoomSubscribeDto.setRostrumNum( entity.getRostrumNum() );
        meetingRoomSubscribeDto.setStartTime( entity.getStartTime() );
        meetingRoomSubscribeDto.setStatus( entity.getStatus() );
        meetingRoomSubscribeDto.setUseDay( entity.getUseDay() );
        meetingRoomSubscribeDto.setVideoMeetingScope( entity.getVideoMeetingScope() );

        return meetingRoomSubscribeDto;
    }
}
