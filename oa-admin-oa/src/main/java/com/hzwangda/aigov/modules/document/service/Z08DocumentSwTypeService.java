package com.hzwangda.aigov.modules.document.service;

import com.hzwangda.aigov.modules.document.dto.Z08DocumentTypeQueryCriteria;
import com.hzwangda.aigov.modules.document.entity.Z08DocumentSwType;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface Z08DocumentSwTypeService {
    Object saveDocumentType(Z08DocumentSwType documentType);

    Object getDocumentTypePage(Z08DocumentTypeQueryCriteria criteria, Pageable pageable);

    Object getDocumentTypeList(Z08DocumentTypeQueryCriteria criteria);

    Object getOneById(Long id);

    void deleteById(Long id);

    Object getDzList(String dz);

    Z08DocumentSwType getByDzAndDeptId(String dz, String deptId);

    List<Z08DocumentSwType> getByDeptId(String deptId);
}
