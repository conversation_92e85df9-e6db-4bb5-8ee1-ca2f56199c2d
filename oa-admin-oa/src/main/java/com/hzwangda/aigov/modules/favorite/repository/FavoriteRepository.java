package com.hzwangda.aigov.modules.favorite.repository;

import com.hzwangda.aigov.modules.favorite.domain.Favorite;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface FavoriteRepository extends JpaRepository<Favorite, Long>, JpaSpecificationExecutor<Favorite> {

    List<Favorite> findAllByTypeAndCreateByOrderByModifiedDateDesc(Integer type, String createBy);

    List<Favorite> findAllByProcInstanceId(String procInstanceId);

    Integer countByProcInstanceIdAndCreateByAndType(String procInstanceId, String createBy, Integer type);

    void deleteByProcInstanceIdAndType(String procInstanceId, Integer type);

    List<Favorite> findAllByProcInstanceIdAndTypeAndCreateBy(String procInstanceId, Integer type, String createBy);
}
