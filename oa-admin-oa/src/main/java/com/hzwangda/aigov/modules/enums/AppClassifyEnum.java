package com.hzwangda.aigov.modules.enums;

import com.alibaba.fastjson.annotation.JSONType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/2
 * @description 应用类型
 */
@AllArgsConstructor
@ToString
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@JSONType(serializeEnumAsJavaBean = true)
public enum AppClassifyEnum {

    GW_SW("gw_sw", "收文"),
    GW_FW("gw_fw", "发文"),
    GW_NP("gw_np", "内跑"),
    GW_XZ("gw_xz", "行政");

    private String value;
    private String name;

    public static AppClassifyEnum getByValue(String value) {
        for(AppClassifyEnum appClassifyEnum : AppClassifyEnum.values()) {
            if(appClassifyEnum.value.equals(value)) {
                return appClassifyEnum;
            }
        }
        return null;
    }

    public static List<String> getByValueList() {
        List<String> values = new ArrayList<>();
        for(AppClassifyEnum appClassifyEnum : AppClassifyEnum.values()) {
            if(!appClassifyEnum.value.equals(AppClassifyEnum.GW_FW.getValue()) && !appClassifyEnum.value.equals(AppClassifyEnum.GW_SW.getValue())) {
                values.add(appClassifyEnum.getValue());
            }
        }
        return values;
    }
}
