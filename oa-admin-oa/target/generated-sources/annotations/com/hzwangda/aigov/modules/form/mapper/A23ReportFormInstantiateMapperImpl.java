package com.hzwangda.aigov.modules.form.mapper;

import com.hzwangda.aigov.modules.form.convert.IdToUserConvert;
import com.hzwangda.aigov.modules.form.domain.A23ReportFormInstantiate;
import com.hzwangda.aigov.modules.form.dto.A23ReportFormInstantiateDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A23ReportFormInstantiateMapperImpl implements A23ReportFormInstantiateMapper {

    @Autowired
    private IdToUserConvert idToUserConvert;

    @Override
    public List<A23ReportFormInstantiateDto> toDto(List<A23ReportFormInstantiate> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A23ReportFormInstantiateDto> list = new ArrayList<A23ReportFormInstantiateDto>( entityList.size() );
        for ( A23ReportFormInstantiate a23ReportFormInstantiate : entityList ) {
            list.add( toDto( a23ReportFormInstantiate ) );
        }

        return list;
    }

    @Override
    public List<A23ReportFormInstantiate> toEntity(List<A23ReportFormInstantiateDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A23ReportFormInstantiate> list = new ArrayList<A23ReportFormInstantiate>( dtoList.size() );
        for ( A23ReportFormInstantiateDto a23ReportFormInstantiateDto : dtoList ) {
            list.add( toEntity( a23ReportFormInstantiateDto ) );
        }

        return list;
    }

    @Override
    public A23ReportFormInstantiateDto toDto(A23ReportFormInstantiate entity) {
        if ( entity == null ) {
            return null;
        }

        A23ReportFormInstantiateDto a23ReportFormInstantiateDto = new A23ReportFormInstantiateDto();

        a23ReportFormInstantiateDto.setCreatorId( idToUserConvert.convert( entity.getCreateBy() ) );
        a23ReportFormInstantiateDto.setBizType( entity.getBizType() );
        a23ReportFormInstantiateDto.setEnable( entity.getEnable() );
        a23ReportFormInstantiateDto.setEndTime( entity.getEndTime() );
        a23ReportFormInstantiateDto.setForm( entity.getForm() );
        a23ReportFormInstantiateDto.setFormJson( entity.getFormJson() );
        a23ReportFormInstantiateDto.setId( entity.getId() );
        a23ReportFormInstantiateDto.setReportType( entity.getReportType() );
        a23ReportFormInstantiateDto.setStartTime( entity.getStartTime() );
        a23ReportFormInstantiateDto.setTitle( entity.getTitle() );

        return a23ReportFormInstantiateDto;
    }

    @Override
    public A23ReportFormInstantiate toEntity(A23ReportFormInstantiateDto dto) {
        if ( dto == null ) {
            return null;
        }

        A23ReportFormInstantiate a23ReportFormInstantiate = new A23ReportFormInstantiate();

        a23ReportFormInstantiate.setCreateBy( idToUserConvert.convert( dto.getCreatorId() ) );
        a23ReportFormInstantiate.setBizType( dto.getBizType() );
        a23ReportFormInstantiate.setEnable( dto.getEnable() );
        a23ReportFormInstantiate.setEndTime( dto.getEndTime() );
        a23ReportFormInstantiate.setForm( dto.getForm() );
        a23ReportFormInstantiate.setFormJson( dto.getFormJson() );
        a23ReportFormInstantiate.setId( dto.getId() );
        a23ReportFormInstantiate.setReportType( dto.getReportType() );
        a23ReportFormInstantiate.setStartTime( dto.getStartTime() );
        a23ReportFormInstantiate.setTitle( dto.getTitle() );

        return a23ReportFormInstantiate;
    }
}
