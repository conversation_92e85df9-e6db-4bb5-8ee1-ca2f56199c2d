package com.hzwangda.aigov.modules.archive.controller;

import com.hzwangda.aigov.modules.archive.entity.A07FileArchive;
import com.hzwangda.aigov.modules.archive.entity.FileArchiveCriteria;
import com.hzwangda.aigov.modules.archive.entity.FileArchiveDto;
import com.hzwangda.aigov.modules.archive.entity.MarkParams;
import com.hzwangda.aigov.modules.archive.service.FileArchiveService;
import com.wangda.oa.annotation.AnonymousAccess;
import com.wangda.oa.annotation.Log;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

@RestController
@RequestMapping("/FileArchive")
public class FileArchiveController {

    @Resource
    private FileArchiveService fileArchiveService;

    @PostMapping("/save")
    @ApiOperation("归档整理")
    @Log("归档整理")
    public ResponseEntity save(@RequestBody A07FileArchive fileArchive) throws IOException {
        Long id = fileArchiveService.save(fileArchive);
        return new ResponseEntity(id, HttpStatus.OK);
    }

    @GetMapping("/queryList")
    @ApiOperation("列表查询")
    @Log("列表查询")
    public ResponseEntity queryList(FileArchiveCriteria criteria, Pageable pageable) {
        Page<A07FileArchive> fileArchives = fileArchiveService.queryList(criteria, pageable);
        return new ResponseEntity(fileArchives, HttpStatus.OK);
    }

    @GetMapping("/queryOne")
    @ApiOperation("编辑查询")
    @Log("编辑查询")
    public ResponseEntity<FileArchiveDto> queryOne(@RequestParam("id") Long id) {
        FileArchiveDto fileArchives = fileArchiveService.queryOne(id);
        return new ResponseEntity(fileArchives, HttpStatus.OK);
    }

    @PostMapping("/previewFile")
    @ApiOperation("预览归档文件")
    public void previewFile(@RequestParam Long id) {

    }

    @PostMapping("/upload")
    @ApiOperation("移交档案系统")
    public ResponseEntity upload(@RequestParam String fileNumber) throws IOException {
        return ResponseEntity.ok(fileArchiveService.upload(fileNumber));
    }

    @PostMapping("/mark")
    @ApiOperation("标记")
    public ResponseEntity mark(@RequestBody MarkParams markParams) {
        return ResponseEntity.ok(fileArchiveService.mark(markParams.getIds(), markParams.getStatus()));
    }

    @PostMapping("/cancelMark")
    @ApiOperation("取消标记")
    public ResponseEntity cancelMark(@RequestBody MarkParams markParams) {
        return ResponseEntity.ok(fileArchiveService.cancelMark(markParams.getIds()));
    }

    @GetMapping("/getAccessToken")
    @ApiOperation("获取token")
    @AnonymousAccess
    public ResponseEntity getAccessToken(@RequestParam String usercode) {
        return ResponseEntity.ok(fileArchiveService.getAccessToken(usercode));
    }

    @GetMapping("/getNpDataByProcessInstanceId")
    @ApiOperation("内跑归档数据")
    public ResponseEntity getNpDataByProcessInstanceId(@RequestParam String processInstanceId) {
        return ResponseEntity.ok(fileArchiveService.getNpDataByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/getNpDatas")
    @ApiOperation("内跑待归档列表")
    public ResponseEntity getNpDatas(String fileStatus, String bt, String belongToDept, Pageable pageable) {
        return ResponseEntity.ok(fileArchiveService.getNpDatas(fileStatus, bt, belongToDept, pageable));
    }
}
