package com.hzwangda.aigov.modules.GoodsReceive.controller;

import com.hzwangda.aigov.modules.GoodsReceive.domain.GoodsInventory;
import com.hzwangda.aigov.modules.GoodsReceive.dto.GoodsInventoryCriteria;
import com.hzwangda.aigov.modules.GoodsReceive.service.GoodsInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/goodsInventory")
@Api(tags = "物品库存")
public class GoodsInventoryController {
    @Resource
    private GoodsInventoryService goodsInventoryService;

    @ApiOperation("查询一级分类---物品分类")
    @GetMapping("/findAllByPidIsNull")
    public ResponseEntity findAllByPidIsNull(GoodsInventoryCriteria goodsInventoryCriteria){
        return ResponseEntity.ok(goodsInventoryService.findAllByPidIsNull(goodsInventoryCriteria));
    }

    @ApiOperation("根据id查询物品的子类")
    @GetMapping("/findChildrenById")
    public ResponseEntity findChildrenById(GoodsInventoryCriteria goodsInventoryCriteria){
        return ResponseEntity.ok(goodsInventoryService.findAllByPid(goodsInventoryCriteria));
    }

    @ApiOperation("根据id查询物品的子类")
    @GetMapping("/findChildrenByName")
    public ResponseEntity findChildrenByName(String name, Boolean leaf, GoodsInventoryCriteria goodsInventoryCriteria){
        return ResponseEntity.ok(goodsInventoryService.findChildrenByName(name, leaf, goodsInventoryCriteria));
    }

    @ApiOperation("保存")
    @PostMapping("/save")
    public ResponseEntity save(@RequestBody GoodsInventory goodsInventory){
        return ResponseEntity.ok(goodsInventoryService.save(goodsInventory));
    }

    @ApiOperation("列表")
    @GetMapping("/findAll")
    public ResponseEntity findAll(GoodsInventoryCriteria goodsInventoryCriteria){
        return ResponseEntity.ok(goodsInventoryService.findAll(goodsInventoryCriteria));
    }

    @ApiOperation("列表")
    @PostMapping("/delByIds")
    public ResponseEntity delByIds(@RequestBody Long[] ids){
        return ResponseEntity.ok(goodsInventoryService.delByIds(ids));
    }
}
