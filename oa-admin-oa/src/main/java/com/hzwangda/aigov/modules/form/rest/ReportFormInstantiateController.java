package com.hzwangda.aigov.modules.form.rest;

import com.hzwangda.aigov.modules.form.domain.A23ReportFormInstantiate;
import com.hzwangda.aigov.modules.form.dto.A23ReportFormInstantiateDto;
import com.hzwangda.aigov.modules.form.dto.ReportFormInstQueryCriteria;
import com.hzwangda.aigov.modules.form.service.ReportFormInstantiateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@Api(tags = "数据填报表单实例管理")
@RestController
@RequestMapping("/api/report/form/inst")
@RequiredArgsConstructor
public class ReportFormInstantiateController {

    private final ReportFormInstantiateService service;

    @ApiOperation(value = "查询实例", notes = "查询实例io")
    @GetMapping
    public Page<A23ReportFormInstantiateDto> query(ReportFormInstQueryCriteria criteria, Pageable pageable) {
        return service.query(criteria, pageable);
    }

    @ApiOperation(value = "删除实例", notes = "删除实例")

    @PostMapping("delete")
    public void delete(@RequestBody Set<Long> ids) {
        service.delete(ids);
    }

    @ApiOperation(value = "修改实例", notes = "修改实例")
    @PostMapping("update")
    public A23ReportFormInstantiateDto update(@RequestBody A23ReportFormInstantiate resources) {
        return service.update(resources);
    }

    @ApiOperation(value = "创建实例", notes = "创建实例")
    @PostMapping
    public A23ReportFormInstantiateDto create(@RequestBody A23ReportFormInstantiate resources) {
        return service.create(resources);
    }

    @GetMapping("reset")
    @ApiOperation(value = "关闭或开启表单填报", notes = "关闭或开启表单填报")
    public void reset(Long id) {
        service.reset(id);
    }

    @PostMapping("batchSave")
    @ApiOperation(value = "根据表单ID批量创建实例", notes = "根据表单ID批量创建实例")
    public List<A23ReportFormInstantiateDto> batchSave(@RequestBody Set<Long> ids) {
        return service.batchSave(ids);
    }

    @GetMapping("queryById")
    @ApiOperation(value = "查询表单实例信息", notes = "查询表单实例信息")
    public A23ReportFormInstantiateDto queryById(Long id) {
        return service.queryByIdToDto(id);
    }

}
