package com.hzwangda.aigov.modules.archive.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.annotation.JSONField;
import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/9/7 下午3:54
 **/
@Entity
@Table(name = "a12_archives")
@Data
@ApiModel(value = "文件档案表")
public class A12Archives extends BaseEntity {
    @Id
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "目录id")
    private Long directoryId;

    @ApiModelProperty(value = "文件id")
    private Long localStorageId;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "生效日期")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date reportDate;

    @ApiModelProperty(value = "序号")
    private Integer sort;

    @ApiModelProperty(value = "标签")
    @Column(length = 64)
    private String tag;

    public void copy(A12Archives a12Archives) {
        BeanUtil.copyProperties(a12Archives, this, CopyOptions.create().setIgnoreNullValue(true));
    }

    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }
}
