package com.hzwangda.aigov.modules.document.dto;

import com.hzwangda.aigov.modules.document.entity.SysDeptUserMain;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 动态信息-发文
 *
 * <AUTHOR>
 * @date 2021/6/15 上午10:18
 */
@Data
public class A07DocumentFwDto extends BaseBpmDomain implements Serializable {

    @ApiModelProperty(value = "文号")
    private ReferenceNumber gwwh;

    @ApiModelProperty(value = "缓急(普通,急)")
    private String hj;

    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "签发-json格式")
    private String yjQf;

    @ApiModelProperty(value = "会签-json格式")
    private String yjHq;

    @ApiModelProperty(value = "分管主任审核意见-json格式")
    private String yjFgzrsh;

    @ApiModelProperty(value = "秘书科审核意见-json格式")
    private String yjMsksh;

    @ApiModelProperty(value = "处室审核意见-json格式")
    private String yjCssh;

    @ApiModelProperty(value = "正文")
    private StorageBizDto zw;

    @ApiModelProperty(value = "附件")
    private List<StorageBizDto> fj;

    @ApiModelProperty(value = "主送单位")
    private SysDeptUserMain zsdw;

    @ApiModelProperty(value = "抄送单位")
    private SysDeptUserMain csdw;

    @ApiModelProperty(value = "主办部门")
    private String zbbm;

    @ApiModelProperty(value = "拟稿人")
    private String ngr;

    @ApiModelProperty(value = "拟稿单位")
    private String ngdw;

    @ApiModelProperty(value = "印发份数")
    private String yffs;

    @ApiModelProperty(value = "印发日期")
    private Date yfrq;

    @ApiModelProperty(value = "内网发布(是,否)")
    private String nwfb;

    @ApiModelProperty(value = "校对人")
    private String jdr;

    @ApiModelProperty(value = "外网发布栏目code")
    private String wwfblmCode;

    @ApiModelProperty(value = "公开类型(主动公开,依申请公开,不予公开)")
    private String gklx;

    @ApiModelProperty(value = "理由")
    private String ly;

    @ApiModelProperty(value = "备注")
    private String bz;

    @ApiModelProperty(value = "会签单位")
    private String hqdw;

    @ApiModelProperty(value = "流程任务相关信息")
    private FlowTaskInfoDto flowTaskInfoDto;

}
