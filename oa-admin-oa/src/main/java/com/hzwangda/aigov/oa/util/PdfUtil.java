package com.hzwangda.aigov.oa.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.itextpdf.text.pdf.PdfReader;
import lombok.extern.log4j.Log4j2;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: zhangzhanlong
 * @date: 2022/11/12 15:23
 * @description:
 */
@Log4j2
public class PdfUtil {
    public static final int DEFAULT_DPI = 150;

    /***
     * PDF文件转PNG图片，全部页数
     *
     * @param pdfFilePath pdf完整路径
     * @param dpi dpi越大转换后越清晰，相对转换速度越慢
     */
    public static List<String> pdf2Image(String pdfFilePath, String outPath, int dpi) {
        if(ObjectUtil.isEmpty(dpi)) {
            // 如果没有设置DPI，默认设置为150
            dpi = DEFAULT_DPI;
        }
        List<String> filePath = new ArrayList<>();

        File file = new File(pdfFilePath);
        PDDocument pdDocument;
        try {
            int dot = file.getName().lastIndexOf('-');
            // 获取图片文件名
            String imagePdfName = file.getName().substring(0, dot);

            pdDocument = Loader.loadPDF(file);
            PDFRenderer renderer = new PDFRenderer(pdDocument);
            /* dpi越大转换后越清晰，相对转换速度越慢 */
            PdfReader reader = new PdfReader(pdfFilePath);
            int pages = reader.getNumberOfPages();
            StringBuffer imgFilePath;
            for(int i = 0; i < pages; i++) {
                String imgFilePathPrefix = outPath + imagePdfName;
                imgFilePath = new StringBuffer();
                imgFilePath.append(imgFilePathPrefix);
                imgFilePath.append("-");
                imgFilePath.append(DateUtil.format(new Date(), "yyyyMMddhhmmssS"));
                imgFilePath.append(".png");
                File dstFile = new File(imgFilePath.toString());
                BufferedImage image = renderer.renderImageWithDPI(i, dpi);
                ImageIO.write(image, "png", dstFile);
                filePath.add(imgFilePath.toString());
            }
            log.info("PDF文档转PNG图片成功！");
        }catch(IOException e) {
            e.printStackTrace();
        }
        return filePath;
    }

    /**
     * pdf转图片
     * 多页PDF会每页转换为一张图片，下面会有多页组合成一页的方法
     * @param pdfFile pdf文件路径
     * @param outPath 图片输出路径
     * @param dpi     相当于图片的分辨率，值越大越清晰，但是转换时间变长
     */
    public static void pdf2multiImage(String pdfFile, String outPath, int dpi) {
        if(ObjectUtil.isEmpty(dpi)) {
            // 如果没有设置DPI，默认设置为150
            dpi = DEFAULT_DPI;
        }
        try(PDDocument pdf = Loader.loadPDF(new File(pdfFile))) {
            int actSize = pdf.getNumberOfPages();
            List<BufferedImage> picList = Lists.newArrayList();
            for(int i = 0; i < actSize; i++) {
                BufferedImage image = new PDFRenderer(pdf).renderImageWithDPI(i, dpi, ImageType.RGB);
                picList.add(image);
            }
            // 组合图片
            ImgUtil.splicePic(picList, outPath);
        }catch(IOException e) {
            e.printStackTrace();
        }
    }

}
