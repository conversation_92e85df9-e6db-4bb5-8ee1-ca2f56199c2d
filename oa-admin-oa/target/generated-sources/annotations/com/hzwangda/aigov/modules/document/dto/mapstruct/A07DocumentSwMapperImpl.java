package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07DocumentSwDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentSw;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A07DocumentSwMapperImpl implements A07DocumentSwMapper {

    @Autowired
    private A07DocumentMapperUtil a07DocumentMapperUtil;

    @Override
    public List<A07DocumentSwDto> toDto(List<A07DocumentSw> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A07DocumentSwDto> list = new ArrayList<A07DocumentSwDto>( entityList.size() );
        for ( A07DocumentSw a07DocumentSw : entityList ) {
            list.add( toDto( a07DocumentSw ) );
        }

        return list;
    }

    @Override
    public A07DocumentSw toEntity(A07DocumentSwDto dto) {
        if ( dto == null ) {
            return null;
        }

        A07DocumentSw a07DocumentSw = new A07DocumentSw();

        a07DocumentSw.setBelongToDept( dto.getBelongToDept() );
        a07DocumentSw.setBpmInstanceId( dto.getBpmInstanceId() );
        a07DocumentSw.setBpmProcessKey( dto.getBpmProcessKey() );
        a07DocumentSw.setBpmStatus( dto.getBpmStatus() );
        a07DocumentSw.setBpmSubject( dto.getBpmSubject() );
        a07DocumentSw.setCreateBy( dto.getCreateBy() );
        a07DocumentSw.setCreateTime( dto.getCreateTime() );
        a07DocumentSw.setId( dto.getId() );
        List<String> list = dto.getParticipateUser();
        if ( list != null ) {
            a07DocumentSw.setParticipateUser( new ArrayList<String>( list ) );
        }
        a07DocumentSw.setUpdateBy( dto.getUpdateBy() );
        a07DocumentSw.setUpdateTime( dto.getUpdateTime() );
        a07DocumentSw.setVersion( dto.getVersion() );
        a07DocumentSw.setBt( dto.getBt() );
        a07DocumentSw.setHj( dto.getHj() );
        a07DocumentSw.setLwdw( dto.getLwdw() );
        a07DocumentSw.setLxrdh( dto.getLxrdh() );
        a07DocumentSw.setSwbh( dto.getSwbh() );
        a07DocumentSw.setSwlx( dto.getSwlx() );
        a07DocumentSw.setSwrq( dto.getSwrq() );
        a07DocumentSw.setYjBljg( dto.getYjBljg() );
        a07DocumentSw.setYjLdqp( dto.getYjLdqp() );
        a07DocumentSw.setYjNb( dto.getYjNb() );
        a07DocumentSw.setFj( a07DocumentMapperUtil.toConvertToId( dto.getFj() ) );

        return a07DocumentSw;
    }

    @Override
    public List<A07DocumentSw> toEntity(List<A07DocumentSwDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A07DocumentSw> list = new ArrayList<A07DocumentSw>( dtoList.size() );
        for ( A07DocumentSwDto a07DocumentSwDto : dtoList ) {
            list.add( toEntity( a07DocumentSwDto ) );
        }

        return list;
    }

    @Override
    public A07DocumentSwDto toDto(A07DocumentSw entity) {
        if ( entity == null ) {
            return null;
        }

        A07DocumentSwDto a07DocumentSwDto = new A07DocumentSwDto();

        a07DocumentSwDto.setBelongToDept( entity.getBelongToDept() );
        a07DocumentSwDto.setBpmInstanceId( entity.getBpmInstanceId() );
        a07DocumentSwDto.setBpmProcessKey( entity.getBpmProcessKey() );
        a07DocumentSwDto.setBpmStatus( entity.getBpmStatus() );
        a07DocumentSwDto.setBpmSubject( entity.getBpmSubject() );
        a07DocumentSwDto.setCreateBy( entity.getCreateBy() );
        a07DocumentSwDto.setCreateTime( entity.getCreateTime() );
        a07DocumentSwDto.setId( entity.getId() );
        List<String> list = entity.getParticipateUser();
        if ( list != null ) {
            a07DocumentSwDto.setParticipateUser( new ArrayList<String>( list ) );
        }
        a07DocumentSwDto.setUpdateBy( entity.getUpdateBy() );
        a07DocumentSwDto.setUpdateTime( entity.getUpdateTime() );
        a07DocumentSwDto.setVersion( entity.getVersion() );
        a07DocumentSwDto.setBt( entity.getBt() );
        a07DocumentSwDto.setHj( entity.getHj() );
        a07DocumentSwDto.setLwdw( entity.getLwdw() );
        a07DocumentSwDto.setLxrdh( entity.getLxrdh() );
        a07DocumentSwDto.setSwbh( entity.getSwbh() );
        a07DocumentSwDto.setSwlx( entity.getSwlx() );
        a07DocumentSwDto.setSwrq( entity.getSwrq() );
        a07DocumentSwDto.setYjBljg( entity.getYjBljg() );
        a07DocumentSwDto.setYjLdqp( entity.getYjLdqp() );
        a07DocumentSwDto.setYjNb( entity.getYjNb() );

        a07DocumentSwDto.setFj( a07DocumentMapperUtil.toFj(entity.getFj()) );

        splitTime( entity, a07DocumentSwDto );

        return a07DocumentSwDto;
    }
}
