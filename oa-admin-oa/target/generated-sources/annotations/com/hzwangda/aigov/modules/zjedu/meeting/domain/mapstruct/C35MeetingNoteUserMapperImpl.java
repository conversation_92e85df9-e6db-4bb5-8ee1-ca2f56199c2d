package com.hzwangda.aigov.modules.zjedu.meeting.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35MeetingNoteUserDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35MeetingNoteUser;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class C35MeetingNoteUserMapperImpl implements C35MeetingNoteUserMapper {

    @Override
    public C35MeetingNoteUserDto toDto(C35MeetingNoteUser entity) {
        if ( entity == null ) {
            return null;
        }

        C35MeetingNoteUserDto c35MeetingNoteUserDto = new C35MeetingNoteUserDto();

        c35MeetingNoteUserDto.setCreateBy( entity.getCreateBy() );
        c35MeetingNoteUserDto.setCreateTime( entity.getCreateTime() );
        c35MeetingNoteUserDto.setUpdateTime( entity.getUpdateTime() );
        c35MeetingNoteUserDto.setUpdatedBy( entity.getUpdatedBy() );
        c35MeetingNoteUserDto.setMeetingNoteId( entity.getMeetingNoteId() );
        c35MeetingNoteUserDto.setNoteUserId( entity.getNoteUserId() );
        c35MeetingNoteUserDto.setStatus( entity.getStatus() );
        c35MeetingNoteUserDto.setUserName( entity.getUserName() );

        return c35MeetingNoteUserDto;
    }

    @Override
    public List<C35MeetingNoteUserDto> toDto(List<C35MeetingNoteUser> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<C35MeetingNoteUserDto> list = new ArrayList<C35MeetingNoteUserDto>( entityList.size() );
        for ( C35MeetingNoteUser c35MeetingNoteUser : entityList ) {
            list.add( toDto( c35MeetingNoteUser ) );
        }

        return list;
    }

    @Override
    public C35MeetingNoteUser toEntity(C35MeetingNoteUserDto dto) {
        if ( dto == null ) {
            return null;
        }

        C35MeetingNoteUser c35MeetingNoteUser = new C35MeetingNoteUser();

        c35MeetingNoteUser.setCreateBy( dto.getCreateBy() );
        c35MeetingNoteUser.setCreateTime( dto.getCreateTime() );
        c35MeetingNoteUser.setUpdateTime( dto.getUpdateTime() );
        c35MeetingNoteUser.setUpdatedBy( dto.getUpdatedBy() );
        c35MeetingNoteUser.setMeetingNoteId( dto.getMeetingNoteId() );
        c35MeetingNoteUser.setNoteUserId( dto.getNoteUserId() );
        c35MeetingNoteUser.setStatus( dto.getStatus() );
        c35MeetingNoteUser.setUserName( dto.getUserName() );

        return c35MeetingNoteUser;
    }

    @Override
    public List<C35MeetingNoteUser> toEntity(List<C35MeetingNoteUserDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<C35MeetingNoteUser> list = new ArrayList<C35MeetingNoteUser>( dtoList.size() );
        for ( C35MeetingNoteUserDto c35MeetingNoteUserDto : dtoList ) {
            list.add( toEntity( c35MeetingNoteUserDto ) );
        }

        return list;
    }
}
