package com.hzwangda.aigov.modules.document.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.persistence.Column;

@Data
@AllArgsConstructor
@ApiModel(value = "应用和权限返回表")
public class AppAndPermissionDto {
    @Column(name = "application_id")
    @ApiModelProperty(value = "应用id")
    private Long applicationId;

    @Column(name = "manager")
    @ApiModelProperty(value = "管理员")
    private String manager;

    @Column(name = "business_manager")
    @ApiModelProperty(value = "业务管理员")
    private String businessManager;

    @Column(name = "create_manager")
    @ApiModelProperty(value = "新建人员")
    private String createManager;

    @ApiModelProperty(value = "名称")
    private String name;

}
