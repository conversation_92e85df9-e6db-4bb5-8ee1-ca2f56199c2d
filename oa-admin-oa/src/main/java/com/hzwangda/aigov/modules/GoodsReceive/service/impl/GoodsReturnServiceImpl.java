package com.hzwangda.aigov.modules.GoodsReceive.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.hzwangda.aigov.modules.GoodsReceive.domain.GoodsCategory;
import com.hzwangda.aigov.modules.GoodsReceive.domain.GoodsCategoryDto;
import com.hzwangda.aigov.modules.GoodsReceive.domain.GoodsInventory;
import com.hzwangda.aigov.modules.GoodsReceive.domain.GoodsReceive;
import com.hzwangda.aigov.modules.GoodsReceive.dto.GoodsCriteria;
import com.hzwangda.aigov.modules.GoodsReceive.dto.GoodsReceiveVo;
import com.hzwangda.aigov.modules.GoodsReceive.dto.QueryCriteria;
import com.hzwangda.aigov.modules.GoodsReceive.repository.GoodsCategoryDtoRepostory;
import com.hzwangda.aigov.modules.GoodsReceive.repository.GoodsInventoryRepository;
import com.hzwangda.aigov.modules.GoodsReceive.repository.GoodsReceiveRepository;
import com.hzwangda.aigov.modules.GoodsReceive.service.GoodsReturnService;
import com.hzwangda.aigov.modules.document.constant.DocumentConstant;
import com.hzwangda.aigov.modules.workflow.dto.ListVo;
import com.wangda.oa.backlog.bo.BacklogBusinessSearchBO;
import com.wangda.oa.backlog.bo.BacklogListBO;
import com.wangda.oa.backlog.dto.BacklogListDto;
import com.wangda.oa.backlog.dto.BacklogListPageDto;
import com.wangda.oa.backlog.service.WdBacklogService;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskAssigneeDto;
import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import com.wangda.oa.modules.workflow.factory.RestUrlComponent;
import com.wangda.oa.modules.workflow.service.workflow.FlowInstanceService;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class GoodsReturnServiceImpl implements GoodsReturnService {

    @Resource
    protected TaskService taskService;
    @PersistenceContext
    private EntityManager entityManager;
    @Resource
    private FlowInstanceService flowInstanceService;


    


    @Override
    public Object goodsReturnPageList (GoodsCriteria queryCriteria, Pageable pageable)  {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> cq = cb.createTupleQuery();
        Root<GoodsCategoryDto> root = cq.from(GoodsCategoryDto.class);
        cq.multiselect(
                root.get("id").alias("id"),
                root.get("mcxh").alias("mcxh"),
                root.get("ytfl").alias("ytfl"),
                root.get("lysl").alias("lysl"),
                root.get("goodsReceive").get("bpmInstanceId").alias("bpmInstanceId"),
                root.get("goodsReceive").get("bpmStatus").alias("bpmStatus"),
                root.get("goodsReceive").get("updateTime").alias("updateTime"),
                root.get("createTime").alias("createTime"),
                root.get("returnStatus").alias("returnStatus"),
                root.get("borrowDay").alias("borrowDay")
                );
     /*   cq.multiselect(
                cb.sum(root.get("lysl")).alias("count"),
                cb.max(root.get("wpfl")).alias("wpfl"),
                cb.max(root.get("ytfl")).alias("ytfl"),
                cb.max(root.get("borrowDay")).alias("borrowDay"),
                root.get("mcxh").alias("mcxh"),
                cb.max(root.get("createTime")).alias("createTime")ß
        );*/
        Predicate p = QueryHelp.getPredicate(root,queryCriteria,cb);
        Predicate p1 = cb.equal(root.get("goodsReceive").get("bpmStatus"), ProcStatusEnum.TG.getValue());
        Predicate p2 = cb.isNotNull(root.get("borrowDay"));
        p = cb.and(p,p1,p2);
        cq.where(
                p
        );//.groupBy(root.get("mcxh"));
        int total = entityManager.createQuery(cq).getResultList().size();
        TypedQuery<Tuple> tupleTypedQuery = entityManager.createQuery(cq).setFirstResult(pageable.getPageNumber() * pageable.getPageSize()).setMaxResults(pageable.getPageSize());
        List<Tuple> resultList = tupleTypedQuery.getResultList();
        List<Map> collect = resultList.stream().map(item -> {
            Map map = new HashMap<>();
            map.put("id", item.get("id"));
            map.put("bpmInstanceId", item.get("bpmInstanceId"));
            map.put("goodsNum", (int)item.get("lysl"));
           // map.put("wpfl", item.get("wpfl"));
            map.put("goodType", item.get("ytfl"));
            map.put("goodName", item.get("mcxh"));
            map.put("borrowTime", item.get("borrowDay"));
            map.put("createTime", item.get("createTime"));
            map.put("updateTime", item.get("updateTime"));
            map.put("useType", Objects.isNull(item.get("borrowDay"))?"申领":"借用");
            if(!"TG".equals(String.valueOf(item.get("bpmStatus")))) {
                FlowTaskAssigneeDto taskDto=null;
                List<FlowTaskAssigneeDto> taskList = flowInstanceService.getTaskInfoByProcInsId(String.valueOf(item.get("bpmInstanceId")));
                if(taskList.size()>0){
                    taskDto=taskList.get(0);
                    if(!"task_5".equals(taskDto.getTaskDefKey())){
                        map.put("status", "审核中");
                    }else{
                        map.put("status", "已通过");
                    }
                    map.put("getStatus", "待领取");
                }
                map.put("assigneeName", taskDto.getAssigneeName());
            }else  if("TG".equals(String.valueOf(item.get("bpmStatus")))) {
                map.put("status", "已通过");
                map.put("getStatus", "已领取");
            }
            //TODO 物品未设置管理员
            map.put("goodAdmin","管理员");
            if(Objects.isNull(item.get("returnStatus"))||"待归还".equals(String.valueOf(item.get("returnStatus")))){
                map.put("returnStatus","待归还");
            }

            DateTime createDate = DateUtil.parse(String.valueOf(item.get("updateTime")));
            DateTime returnDate = DateUtil.offsetDay(createDate, (int) item.get("borrowDay"));

           map.put("overdue",DateUtil.between(returnDate,new Date(), DateUnit.DAY,false));
            map.put("url","https://ding.nanxun.gov.cn:806/newdd/#/bpmRead/browse?processInstanceId="+item.get("bpmInstanceId"));
            return map;
        }).collect(Collectors.toList());

        return  new PageImpl<>(collect,pageable,total);

    }


}
