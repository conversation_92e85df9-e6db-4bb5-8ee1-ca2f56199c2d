package com.hzwangda.aigov.modules.zjedu.goodsmanagement.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.goodsmanagement.domain.dto.D06GoodsDto;
import com.hzwangda.aigov.modules.zjedu.goodsmanagement.domain.entity.D06Goods;
import com.hzwangda.aigov.modules.zjedu.goodsmanagement.domain.entity.D06GoodsStore;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class D06GoodsMapperImpl implements D06GoodsMapper {

    @Override
    public D06GoodsDto toDto(D06Goods entity) {
        if ( entity == null ) {
            return null;
        }

        D06GoodsDto d06GoodsDto = new D06GoodsDto();

        d06GoodsDto.setCreateBy( entity.getCreateBy() );
        d06GoodsDto.setCreateTime( entity.getCreateTime() );
        d06GoodsDto.setUpdateTime( entity.getUpdateTime() );
        d06GoodsDto.setUpdatedBy( entity.getUpdatedBy() );
        d06GoodsDto.setCategory( entity.getCategory() );
        d06GoodsDto.setGoodsCode( entity.getGoodsCode() );
        d06GoodsDto.setGoodsName( entity.getGoodsName() );
        List<D06GoodsStore> list = entity.getGoodsStoreList();
        if ( list != null ) {
            d06GoodsDto.setGoodsStoreList( new ArrayList<D06GoodsStore>( list ) );
        }
        d06GoodsDto.setId( entity.getId() );
        d06GoodsDto.setIsBack( entity.getIsBack() );
        d06GoodsDto.setIsHC( entity.getIsHC() );
        d06GoodsDto.setStatus( entity.getStatus() );
        d06GoodsDto.setStock( entity.getStock() );
        d06GoodsDto.setTotal( entity.getTotal() );
        d06GoodsDto.setUnit( entity.getUnit() );
        d06GoodsDto.setUseNum( entity.getUseNum() );

        return d06GoodsDto;
    }

    @Override
    public List<D06GoodsDto> toDto(List<D06Goods> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<D06GoodsDto> list = new ArrayList<D06GoodsDto>( entityList.size() );
        for ( D06Goods d06Goods : entityList ) {
            list.add( toDto( d06Goods ) );
        }

        return list;
    }

    @Override
    public D06Goods toEntity(D06GoodsDto dto) {
        if ( dto == null ) {
            return null;
        }

        D06Goods d06Goods = new D06Goods();

        d06Goods.setCreateBy( dto.getCreateBy() );
        d06Goods.setCreateTime( dto.getCreateTime() );
        d06Goods.setUpdateTime( dto.getUpdateTime() );
        d06Goods.setUpdatedBy( dto.getUpdatedBy() );
        d06Goods.setCategory( dto.getCategory() );
        d06Goods.setGoodsCode( dto.getGoodsCode() );
        d06Goods.setGoodsName( dto.getGoodsName() );
        List<D06GoodsStore> list = dto.getGoodsStoreList();
        if ( list != null ) {
            d06Goods.setGoodsStoreList( new ArrayList<D06GoodsStore>( list ) );
        }
        d06Goods.setId( dto.getId() );
        d06Goods.setIsBack( dto.getIsBack() );
        d06Goods.setIsHC( dto.getIsHC() );
        d06Goods.setStatus( dto.getStatus() );
        d06Goods.setStock( dto.getStock() );
        d06Goods.setTotal( dto.getTotal() );
        d06Goods.setUnit( dto.getUnit() );
        d06Goods.setUseNum( dto.getUseNum() );

        return d06Goods;
    }

    @Override
    public List<D06Goods> toEntity(List<D06GoodsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<D06Goods> list = new ArrayList<D06Goods>( dtoList.size() );
        for ( D06GoodsDto d06GoodsDto : dtoList ) {
            list.add( toEntity( d06GoodsDto ) );
        }

        return list;
    }
}
