package com.hzwangda.aigov.modules.form.service;

import com.hzwangda.aigov.modules.form.domain.A23ReportForm;
import com.hzwangda.aigov.modules.form.dto.A23ReportFormDto;
import com.hzwangda.aigov.modules.form.dto.ReportFormQueryCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Set;

public interface ReportFromService {
    A23ReportForm save(A23ReportForm resources);

    void delete(Set<Long> ids);

    A23ReportForm update(A23ReportForm form);

    Page<A23ReportFormDto> query(ReportFormQueryCriteria criteria, Pageable pageable, boolean isAdmin);

    A23ReportForm queryById(Long id);
}
