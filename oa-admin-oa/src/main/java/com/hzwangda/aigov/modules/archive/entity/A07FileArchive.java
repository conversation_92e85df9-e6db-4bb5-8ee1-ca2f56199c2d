package com.hzwangda.aigov.modules.archive.entity;

import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "a07_file_archive")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "归档")
public class A07FileArchive extends BaseDomain implements Serializable {

    @Column(name = "bpm_process_key")
    @ApiModelProperty(value = "流程定义Key")
    private String bpmProcessKey;

    @ApiModelProperty(value = "流程实例id")
    private String procInstId;

    @NotNull
    @ApiModelProperty(value = "年度")
    private String year;

    @ApiModelProperty(value = "归档状态 0待归档，1预归档,2暂不归档，3不归档,4已归档，5归档中，-1移交失败")
    private String status;

    @ApiModelProperty(value = "保管期限")
    private String limitTime;

    @ApiModelProperty(value = "标题")
    private String subject;

    @ApiModelProperty(value = "全宗号")
    private String fondsNumber;

    @ApiModelProperty(value = "归档件号")
    private Integer no;

    @ApiModelProperty(value = "档号")
    private String fileNumber;

    @ApiModelProperty(value = "盒号")
    private String boxNo;

    @ApiModelProperty(value = "文件类别（收/发文）")
    private String docType;

    @ApiModelProperty(value = "文件编号")
    private String docNo;

    @ApiModelProperty(value = "电子文件号编号")
    private Integer erpNum;

    @ApiModelProperty(value = "电子文件号,业务编码_年份_5位流水号(累加)，如2_2021_00022.zip")
    private String erpFileId;

    @ApiModelProperty(value = "原件id")
    @Column(name = "doc_id")
    private Long docId;

    @Temporal(TemporalType.TIMESTAMP)
    @ApiModelProperty(value = "文件形成时间(发文取印发或分发日期；收文取收文登记日期)")
    private Date fileDate;

    @ApiModelProperty(value = "密级")
    private String secretLevel;

    @ApiModelProperty(value = "责任者")
    private String responsiblePerson;

    @ApiModelProperty(value = "载体数量")
    private Integer carrierNumber;

    @ApiModelProperty(value = "案卷号")
    private String archiveNo;

    @ApiModelProperty(value = "张页号")
    private String pageNo;

    @ApiModelProperty(value = "主题词")
    private String keywords;

    @ApiModelProperty(value = "附注")
    private String note;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "移交人")
    private String sender;

    @Temporal(TemporalType.TIMESTAMP)
    @ApiModelProperty(value = "移交时间")
    private Date sendTime;

    @ApiModelProperty(value = "文件页数")
    private String filePage;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A07FileArchive.fj'")
    private List<StorageBiz> fj;

    @ApiModelProperty(value = "接收结果")
    @Column(length = 2000)
    private String resultJson;

    public void setFj(List<StorageBiz> fj) {
        if (fj != null) {
            if (this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }

    @ApiModelProperty("签发人")
    private String qfr;

    @ApiModelProperty("发文机关")
    private String fwjg;

    @ApiModelProperty("成文日期")
    private Date cwrq;

    @ApiModelProperty("文种")
    private String wz;

    @ApiModelProperty("印发机关")
    private String yfjg;

    @ApiModelProperty("印发日期")
    private Date yfrq;

    @ApiModelProperty("发布层次")
    private String fbcc;

    @ApiModelProperty("压缩包下载路径")
    private String zipPath;

    @ApiModelProperty("申请事由")
    private String applicationReason;
}
