package com.hzwangda.aigov.config;

import com.bstek.ureport.console.UReportServlet;
import com.bstek.ureport.definition.datasource.BuildinDatasource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

@ImportResource("${ureport.resource-location}")
@Configuration
@Slf4j
public class UreportConfig {

    @Bean
    public BuildinDatasource ureportDataSource() {
        return new BuildinDatasource() {
            @Resource
            private DataSource dataSource;

            @Override
            public String name() {
                return "默认数据源";
            }

            @Override
            public Connection getConnection() {
                try {
                    return dataSource.getConnection();
                } catch (SQLException e) {
                    log.error("Ureport 数据源 获取连接失败！");
                    e.printStackTrace();
                }
                return null;
            }
        };
    }

    /**
     * 定义ureport的启动servlet
     */
    @Bean
    public ServletRegistrationBean ureportServlet() {
        ServletRegistrationBean servletRegistrationBean = new ServletRegistrationBean(new UReportServlet());
        servletRegistrationBean.addUrlMappings("/ureport/*");
        return servletRegistrationBean;
    }

}
