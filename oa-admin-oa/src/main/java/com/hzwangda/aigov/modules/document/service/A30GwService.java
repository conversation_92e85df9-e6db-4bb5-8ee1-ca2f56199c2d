package com.hzwangda.aigov.modules.document.service;


import com.hzwangda.aigov.modules.document.dto.A07DocumentGwQueryCriteria;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface A30GwService {

    /**
     * 待归档列表
     *
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> getGwWaitArchiveList(A07DocumentGwQueryCriteria criteria, Pageable pageable);


    /**
     * @param id:
     * @description 待归档整理
     * <AUTHOR>
     * @updateTime 2021/11/4 19:34
     * @return: java.lang.Object
     */

    Object getGwPutInOrder(Long id, String type);


    /**
     * @description 传阅意见上表单
     * <AUTHOR>
     * @updateTime 2022/3/24 11:20
     * @param remark :
     * @param users
     * @return: java.lang.Object
     */

    void remarkToFormField(String remark,String bpmProcInstId,String users);


    /**
     * @description 查询该流程的转阅操作记录
     * <AUTHOR>
     * @updateTime 2022/5/7 10:15
     * @param bpmProcInstId:
     * @return: java.lang.Object
     */

    List<Map<String, String>> getFlowDocReadOperateTable(String bpmProcInstId);

    /**
     * @description 查询该流程的转阅记录
     * <AUTHOR>
     * @updateTime 2022/4/2 9:51
     * @param bpmProcInstId:
     * @return: java.lang.Object
     */

    List<Map<String, String>> getFlowDocReadTable(String bpmProcInstId);

    /**
     * @description 查询转阅人是否重复
     * <AUTHOR>
     * @updateTime 2022/4/18 11:53
     * @param bpmProcInstId:
     * @param users:
     * @return: java.lang.Object
     */

    String getRepetitionReadUsers(String bpmProcInstId, String users);
}
