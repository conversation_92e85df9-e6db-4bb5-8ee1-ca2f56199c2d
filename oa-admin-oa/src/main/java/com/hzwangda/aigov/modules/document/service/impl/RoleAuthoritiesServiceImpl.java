package com.hzwangda.aigov.modules.document.service.impl;

import com.hzwangda.aigov.modules.document.enums.RoleIdentificationEnum;
import com.hzwangda.aigov.modules.document.service.RoleAuthoritiesService;
import com.wangda.oa.modules.workflow.constant.BpmAuthorityConstant;
import com.wangda.oa.utils.SecurityUtils;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: zhangzhanlong
 * @date: 2022/12/13 10:14
 * @description:
 */
@Service
public class RoleAuthoritiesServiceImpl implements RoleAuthoritiesService {
    @Override
    public Boolean isBpmAdmin() {
        List<String> elPermissions = SecurityUtils.getCurrentUser().getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList());
        if(Arrays.stream(new String[]{BpmAuthorityConstant.AUTHORITY_BPM_ADMIN, BpmAuthorityConstant.AUTHORITY_ADMIN}).anyMatch(elPermissions::contains)) {
            return true;
        }
        return false;
    }

    @Override
    public Boolean isAllFwAdmin() {
        List<String> elPermissions = SecurityUtils.getCurrentUser().getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList());
        if(Arrays.stream(new String[]{RoleIdentificationEnum.AllFW.getValue()}).anyMatch(elPermissions::contains)) {
            return true;
        }
        return false;
    }
}
