package com.hzwangda.aigov.modules.archive.controller;

import com.hzwangda.aigov.modules.archive.dto.A12DirectoryDto;
import com.hzwangda.aigov.modules.archive.entity.A12Directory;
import com.hzwangda.aigov.modules.archive.service.A12DirectoryService;
import com.wangda.boot.platform.enums.ResultCodeEnum;
import com.wangda.boot.response.ResponseInfo;
import com.wangda.oa.annotation.Log;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/7 下午7:35
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "文件档案目录")
@RequestMapping("/api/directory")
@CrossOrigin
public class A12DirectoryController {

    private final A12DirectoryService a12DirectoryService;

    @GetMapping
    @ApiOperation("查询文件档案目录树列表")
    public ResponseInfo getMyDirectory() {
        ResponseInfo responseInfo = new ResponseInfo();
        try {
            List<A12DirectoryDto> list = a12DirectoryService.getMyDirectory();
            responseInfo.setData(list);
            responseInfo.setCode(ResultCodeEnum.SUCCESS.getCode());
        } catch (Exception e) {
            e.printStackTrace();
            responseInfo.setCode(HttpStatus.BAD_REQUEST.value());
            responseInfo.setMessage(e.getMessage());
        }
        return responseInfo;
    }

    @PostMapping("/createOrUpdate")
    @Log("新增或修改文件档案目录")
    @ApiOperation("新增或修改文件档案目录")
    public ResponseEntity<Object> createOrUpdate(@Validated @RequestBody A12Directory resources) {
        return new ResponseEntity<>(a12DirectoryService.createOrUpdate(resources), HttpStatus.CREATED);
    }

    @Log("删除文件档案")
    @ApiOperation("删除文件档案")
    @PostMapping("/delete")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        a12DirectoryService.delete(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
