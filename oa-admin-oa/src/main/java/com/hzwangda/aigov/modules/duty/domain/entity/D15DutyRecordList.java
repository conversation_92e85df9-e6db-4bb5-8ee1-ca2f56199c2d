package com.hzwangda.aigov.modules.duty.domain.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Data
@Table(name = "d15_duty_record")
@ApiModel("值班记录")
@DynamicInsert
public class D15DutyRecordList extends BaseDomain {

    @ApiModelProperty(value = "编号")
    private String number;

    @ApiModelProperty(value = "值班日期")
    @Temporal(TemporalType.DATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dutyTime;

    @ApiModelProperty(value = "内容")
    @Column(length = 2000)
    private String content;

    @ApiModelProperty(value = "咨询举报投诉1、重大突发情况2")
    private String type;

    @ApiModelProperty(value = "值班人员")
    private String username;

    @ApiModelProperty(value = "值班人员姓名")
    private String nickName;

    @ApiModelProperty(value = "签转")
    private String transferTo;

    @ApiModelProperty(value = "抄送")
    private String copyTo;


    @ApiModelProperty(value = "是否处理完成 0进行中 1已完成 2代签转 ")
    @Column(nullable = false, columnDefinition = "int default 0")
    private Integer finished;

    @ApiModelProperty(value = "删除标志")
    @Column(nullable = false, columnDefinition = "int default 0")
    private Integer delFlag;

    @OneToMany
    @JoinColumn(name = "recordId")
    @JSONField(serialize = false)
    private List<D15DutyRecordUser> recordUsers;

    @ApiModelProperty(value = "处理情况")
    @OneToMany(targetEntity = D15DutyRecordDeal.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "recordId", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private List<D15DutyRecordDeal> d15DutyRecordDeals;

    @Transient
    @ApiModelProperty(value = "是否已读")
    private Integer readFlag;

    private String area;
}
