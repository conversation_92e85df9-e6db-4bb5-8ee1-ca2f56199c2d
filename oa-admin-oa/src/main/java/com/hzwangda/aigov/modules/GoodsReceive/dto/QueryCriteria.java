package com.hzwangda.aigov.modules.GoodsReceive.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel
@Data
public class QueryCriteria {

    @ApiModelProperty(value = "待办0/申请2/已办1/全部")
    private Integer status;

    @ApiModelProperty(value = "流程定义key")
    private String processDefinitionKey;

    @ApiModelProperty(value = "标题")
    private String subject;

    @Query(type = Query.Type.IN)
    @ApiModelProperty(value = "流程实例id")
    private List<String> bpmInstanceId;

    @ApiModelProperty(value = "待办创建开始结束时间")
    private List<String> timeRange;

    @ApiModelProperty(value = "月份")
    private String month;

    @Query(type = Query.Type.BETWEEN)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private List<Date> dutyDate;

}
