package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07DocExchangeListFwDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlz;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A07DocExchangeFwMapperImpl implements A07DocExchangeFwMapper {

    @Override
    public A07DocExchangeListFwDto toDto(A07DocumentGwlz entity) {
        if ( entity == null ) {
            return null;
        }

        A07DocExchangeListFwDto a07DocExchangeListFwDto = new A07DocExchangeListFwDto();

        a07DocExchangeListFwDto.setBt( entity.getBt() );
        a07DocExchangeListFwDto.setCreateDate( entity.getCreateDate() );
        a07DocExchangeListFwDto.setCreatorId( entity.getCreatorId() );
        a07DocExchangeListFwDto.setFwrq( entity.getFwrq() );
        a07DocExchangeListFwDto.setGwzl( entity.getGwzl() );
        a07DocExchangeListFwDto.setGwzt( entity.getGwzt() );
        a07DocExchangeListFwDto.setHj( entity.getHj() );
        a07DocExchangeListFwDto.setId( entity.getId() );
        a07DocExchangeListFwDto.setModifiedDate( entity.getModifiedDate() );

        return a07DocExchangeListFwDto;
    }

    @Override
    public List<A07DocExchangeListFwDto> toDto(List<A07DocumentGwlz> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A07DocExchangeListFwDto> list = new ArrayList<A07DocExchangeListFwDto>( entityList.size() );
        for ( A07DocumentGwlz a07DocumentGwlz : entityList ) {
            list.add( toDto( a07DocumentGwlz ) );
        }

        return list;
    }

    @Override
    public A07DocumentGwlz toEntity(A07DocExchangeListFwDto dto) {
        if ( dto == null ) {
            return null;
        }

        A07DocumentGwlz a07DocumentGwlz = new A07DocumentGwlz();

        a07DocumentGwlz.setCreateDate( dto.getCreateDate() );
        a07DocumentGwlz.setCreatorId( dto.getCreatorId() );
        a07DocumentGwlz.setId( dto.getId() );
        a07DocumentGwlz.setModifiedDate( dto.getModifiedDate() );
        a07DocumentGwlz.setBt( dto.getBt() );
        a07DocumentGwlz.setFwrq( dto.getFwrq() );
        a07DocumentGwlz.setGwzl( dto.getGwzl() );
        a07DocumentGwlz.setGwzt( dto.getGwzt() );
        a07DocumentGwlz.setHj( dto.getHj() );

        return a07DocumentGwlz;
    }

    @Override
    public List<A07DocumentGwlz> toEntity(List<A07DocExchangeListFwDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A07DocumentGwlz> list = new ArrayList<A07DocumentGwlz>( dtoList.size() );
        for ( A07DocExchangeListFwDto a07DocExchangeListFwDto : dtoList ) {
            list.add( toEntity( a07DocExchangeListFwDto ) );
        }

        return list;
    }

    @Override
    public List<A07DocExchangeListFwDto> toListDto(List<A07DocumentGwlz> list) {
        if ( list == null ) {
            return null;
        }

        List<A07DocExchangeListFwDto> list1 = new ArrayList<A07DocExchangeListFwDto>( list.size() );
        for ( A07DocumentGwlz a07DocumentGwlz : list ) {
            list1.add( toDto( a07DocumentGwlz ) );
        }

        return list1;
    }
}
