package com.hzwangda.aigov.modules.document.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * 信息公开申请
 *
 * <AUTHOR>
 * @date 2021/6/15 上午10:18
 */
@Data
@Entity
@Table(name = "a07_document_xxgksq")
public class A07DocumentXxgksq extends BaseBpmDomain implements Serializable {

    @Column(name = "old_id")
    @ApiModelProperty(value = "老数据id(匹配是否存在)")
    private Long oldId;

    @Column(name = "sqrsf")
    @ApiModelProperty(value = "申请人身份")
    private String sqrsf;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @ApiModelProperty(value = "姓名")
    private String gmXm;

    @ApiModelProperty(value = "工作单位")
    private String gmGzdw;

    @ApiModelProperty(value = "证件名称")
    private String gmZjmc;

    @ApiModelProperty(value = "证件号码")
    private String gmZjhm;

    @ApiModelProperty(value = "通讯地址")
    private String gmTxdz;

    @ApiModelProperty(value = "邮政编码")
    private String gmYzbm;

    @ApiModelProperty(value = "联系电话")
    private String gmLxdh;

    @ApiModelProperty(value = "电子邮箱")
    private String gmDzyx;

    @ApiModelProperty(value = "名称")
    private String frMc;

    @ApiModelProperty(value = "组织机构代码")
    private String frZzjgdm;

    @ApiModelProperty(value = "营业执照信息")
    private String frYyzzxx;

    @ApiModelProperty(value = "法定代表人或负责人")
    private String frFddbrhfzr;

    @ApiModelProperty(value = "联系人姓名")
    private String frLxrxm;

    @ApiModelProperty(value = "联系电话")
    private String frLxdh;

    @ApiModelProperty(value = "电子邮箱")
    private String frDzyx;

    @Column(name = "slsj")
    @ApiModelProperty(value = "受理时间")
    @JSONField(format = "yyyy-MM-dd")
    private Date slsj;

    @Column(name = "fkrq")
    @ApiModelProperty(value = "反馈日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date fkrq;

    @Column(name = "sxxxnrms", length = 2000)
    @ApiModelProperty(value = "所需信息内容描述")
    private String sxxxnrms;

    @Column(name = "sxxxytms", length = 2000)
    @ApiModelProperty(value = "所需信息用途描述")
    private String sxxxytms;

    @Column(name = "sfjmfy")
    @ApiModelProperty(value = "是否减免费用")
    private String sfjmfy;

    @ElementCollection
    @CollectionTable(name = "a07_xxgkcl_tgfs", joinColumns = @JoinColumn(name = "xxgkcl_id"))
    @Column(name = "sxxxzdtgfs")
    private Set<String> sxxxzdtgfs;

    @Column(name = "hqxxfs")
    @ApiModelProperty(value = "获取信息方式")
    private String hqxxfs;

    @Column(name = "jsqtfs")
    @ApiModelProperty(value = "接受其他方式")
    private String jsqtfs;

    @Column(name = "gklx")
    @ApiModelProperty(value = "公开类型")
    private String gklx;

    @Column(name = "yj_zrnb", length = 2000)
    @ApiModelProperty(value = "主任拟办意见-json格式")
    private String yjZrnb;

    @Column(name = "yj_fgc", length = 2000)
    @ApiModelProperty(value = "法规处意见-json格式")
    private String yjFgc;

    @Column(name = "yj_csbl", length = 2000)
    @ApiModelProperty(value = "处室办理意见-json格式")
    private String yjCsbl;

    @Column(name = "yj_zrfh", length = 2000)
    @ApiModelProperty(value = "主任复核意见-json格式")
    private String yjZrfh;
}
