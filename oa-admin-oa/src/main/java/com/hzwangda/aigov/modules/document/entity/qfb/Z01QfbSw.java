package com.hzwangda.aigov.modules.document.entity.qfb;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Where;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 南浔区府办-收文
 * @updateTime 2022/12/19 10:53
 */
@Data
@Entity
@Table(name = "z01_qfb_sw")
public class Z01QfbSw extends BaseBpmDomain implements Serializable {

    @Column(name = "old_id")
    @ApiModelProperty(value = "老数据id(匹配是否存在)")
    private Long oldId;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @ApiModelProperty(value = "收文编号")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "swbh_id", referencedColumnName = "id")
    private ReferenceNumber swbh;

    @ApiModelProperty(value = "转办单号")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "zbdh_id", referencedColumnName = "id")
    private ReferenceNumber zbdh;

    @Column(name = "zbdrq")
    @ApiModelProperty(value = "收文日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date zbdrq;

    @Column(name = "fksjbz")
    @ApiModelProperty(value = "转办单反馈时间")
    private String fksjbz;

    @Column(name = "sfzb")
    @ApiModelProperty(value = "是否转办")
    private String sfzb;

    @Column(name = "lwwh")
    @ApiModelProperty(value = "来文文号")
    private String lwwh;

    @Column(name = "hj")
    @ApiModelProperty(value = "缓急")
    private String hj;

    @Column(name = "swrq")
    @ApiModelProperty(value = "收文日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date swrq;

    @Column(name = "jdr")
    @ApiModelProperty(value = "校对人")
    private String jdr;

    @Column(name = "lwdw")
    @ApiModelProperty(value = "来文单位")
    private String lwdw;

    @Column(name = "lwfs")
    @ApiModelProperty(value = "来文方式")
    private String lwfs;

    @Column(name = "lwqfr")
    @ApiModelProperty(value = "来文签发人")
    private String lwqfr;

    @Column(name = "swlx")
    @ApiModelProperty(value = "收文类型")
    private String swlx;

    @Column(name = "bt")
    @ApiModelProperty(value = "标题")
    private String bt;

    @Column(name = "qsr")
    @ApiModelProperty(value = "签收人")
    private String qsr;

    @Column(name = "lxrdh")
    @ApiModelProperty(value = "联系人电话")
    private String lxrdh;

    @Column(name = "lwrq")
    @ApiModelProperty(value = "来文日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date lwrq;

    @ApiModelProperty(value = "正文附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='Z01QfbSw.zwfj'")
    private List<StorageBiz> zwfj;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='Z01QfbSw.fj'")
    private List<StorageBiz> fj;

    @ApiModelProperty(value = "拟办意见")
    @Column(columnDefinition = "text")
    private String yjNb;

    @ApiModelProperty(value = "领导签批")
    @Column(columnDefinition = "text")
    private String yjLdqp;

    @ApiModelProperty(value = "办理情况")
    @Column(columnDefinition = "text")
    private String yjBlqk;

    @ApiModelProperty(value = "办理结果")
    @Column(columnDefinition = "text")
    private String yjBljg;

    @Column(name = "bz", length = 2000)
    @ApiModelProperty(value = "备注")
    private String bz;


    public void setFj(List<StorageBiz> fj) {
        if(fj != null) {
            if(this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }

    public void setZwfj(List<StorageBiz> zwfj) {
        if(zwfj != null) {
            if(this.zwfj == null) {
                this.zwfj = new ArrayList<>();
            }
            this.zwfj.clear();
            this.zwfj.addAll(zwfj);
        }
    }

    @Override
    @PrePersist
    @PreUpdate
    public void preCreateEntity() {
        super.preCreateEntity();
        if(!CollectionUtils.isEmpty(this.fj)) {
            for(StorageBiz storageBiz : this.fj) {
                if(Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                }
            }
        }
        if(!CollectionUtils.isEmpty(this.zwfj)) {
            for(StorageBiz storageBiz : this.zwfj) {
                if(Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                }
            }
        }
    }


}
