package com.hzwangda.aigov.modules.document.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Where;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 领导批示办理单
 * <AUTHOR>
 * @date 2021/3/19上午11:29
 */
@Data
@Entity
@Table(name = "a07_document_ldps")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class A07DocumentLdps extends BaseBpmDomain {


    @Column(name = "old_id")
    @ApiModelProperty(value = "老数据id(匹配是否存在)")
    private Long oldId;

    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @ApiModelProperty(value = "收文编号")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "swbh_id", referencedColumnName = "id")
    private ReferenceNumber swbh;

    @Column(name = "lwwh")
    @ApiModelProperty(value = "来文文号")
    private String lwwh;

    @Column(name = "hj")
    @ApiModelProperty(value = "缓急")
    private String hj;

    @Column(name = "psxh")
    @ApiModelProperty(value = "批示序号")
    private String psxh;

    @Column(name = "swrq")
    @ApiModelProperty(value = "收文日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date swqr;

    @Column(name = "yjDwjnr", length = 4000)
    @ApiModelProperty(value = "原件单位及内容")
    private String yjDwjnr;

    @Column(name = "xyfk")
    @ApiModelProperty(value = "需要反馈")
    private String xyfk;

    @Column(name = "fkrq")
    @ApiModelProperty(value = "反馈日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date fkrq;

    @Column(name = "ldpsnr", length = 4000)
    @ApiModelProperty(value = "领导批示内容")
    private String ldpsnr;

    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A07DocumentLdps.zwfj'")
    @ApiModelProperty(value = "正文附件")
    private List<StorageBiz> zwfj;

    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A07DocumentLdps.psj'")
    @ApiModelProperty(value = "批示件")
    private List<StorageBiz> psj;

    @Column(name = "yj_bgsnb")
    @Lob
    @ApiModelProperty(value = "办公室拟办意见")
    private String yjBgsnb;

    @Column(name = "yj_tldps")
    @Lob
    @ApiModelProperty(value = "厅领导批示意见")
    private String yjTldps;

    @Column(name = "yj_blqk")
    @Lob
    @ApiModelProperty(value = "办理情况")
    private String yjBlqk;

    @Column(name = "bljg")
    @Lob
    @ApiModelProperty(value = "办理结果")
    private String bljg;

    @Column(name = "bz", length = 4000)
    @ApiModelProperty(value = "备注")
    private String bz;

    @Column(name = "lxr")
    @ApiModelProperty(value = "联系人")
    private String lxr;

    @Column(name = "lxrdh")
    @ApiModelProperty(value = "联系人电话")
    private String lxrdh;


    @ApiModelProperty("来文单位")
    private String lwdw;

    @ApiModelProperty("来文日期")
    private Date lwrq;

    @Override
    @PrePersist
    public void preCreateEntity() {
        super.preCreateEntity();
        if(!CollectionUtils.isEmpty(this.psj)) {
            for(StorageBiz storageBiz : this.psj) {
                storageBiz.setBizId(this.id.toString());
            }
        }
        if(!CollectionUtils.isEmpty(this.zwfj)) {
            for(StorageBiz storageBiz : this.zwfj) {
                storageBiz.setBizId(this.id.toString());
            }
        }
    }

    public void setPsj(List<StorageBiz> psj) {
        if(psj != null) {
            if(this.psj == null) {
                this.psj = new ArrayList<>();
            }
            this.psj.clear();
            this.psj.addAll(psj);
        }
    }

    public void setZwfj(List<StorageBiz> zwfj) {
        if(zwfj != null) {
            if(this.zwfj == null) {
                this.zwfj = new ArrayList<>();
            }
            this.zwfj.clear();
            this.zwfj.addAll(zwfj);
        }
    }

}
