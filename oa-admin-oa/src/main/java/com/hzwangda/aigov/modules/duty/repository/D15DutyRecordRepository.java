package com.hzwangda.aigov.modules.duty.repository;

import com.hzwangda.aigov.modules.duty.domain.entity.D15DutyRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface D15DutyRecordRepository extends JpaRepository<D15DutyRecord, Long>, JpaSpecificationExecutor<D15DutyRecord> {
    void deleteAllByIdIn(Long[] ids);

    @Modifying
    @Query("update D15DutyRecord set delFlag=1 where id in ?1")
    void updateDelFlagByIdIn(Long[] ids);

    List<D15DutyRecord> findByTypeOrderByCreateDateDesc(String type);

    @Modifying
    @Query("update D15DutyRecord set finished=1 where id in ?1")
    void updateFinishedByIdIn(Long[] ids);

    List<D15DutyRecord> findAllByIdInAndFinished(List<Long> ids, Integer finished);

    Boolean existsD15DutyRecordByNumber(String number);

    D15DutyRecord findByNumber(String number);
}
