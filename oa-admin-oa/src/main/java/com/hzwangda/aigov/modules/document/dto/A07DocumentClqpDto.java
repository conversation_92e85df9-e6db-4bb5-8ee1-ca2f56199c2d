package com.hzwangda.aigov.modules.document.dto;

import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 动态信息-签批办理单
 *
 * <AUTHOR>
 * @date 2021/6/15 上午10:18
 */
@Data
public class A07DocumentClqpDto extends BaseBpmDomain implements Serializable {

    @ApiModelProperty(value = "材料名称")
    private String clmc;

    @ApiModelProperty(value = "文件标题")
    private String wjbt;

    @ApiModelProperty(value = "签发-json格式")
    private String yjQf;

    @ApiModelProperty(value = "分管主任审核意见-json格式")
    private String yjFgzrsh;

    @ApiModelProperty(value = "秘书科审核意见-json格式")
    private String yjMsksh;

    @ApiModelProperty(value = "正文")
    private StorageBiz zw;

    @ApiModelProperty(value = "附件")
    private List<StorageBizDto> fj;

    @ApiModelProperty(value = "主办部门")
    private String zbbm;

    @ApiModelProperty(value = "拟稿人")
    private String ngr;

    @ApiModelProperty(value = "印发份数")
    private Integer yffs;

    @ApiModelProperty(value = "印发日期")
    private Date yfrq;

    @ApiModelProperty(value = "内网发布(是,否)")
    private String nwfb;

    @ApiModelProperty(value = "校对人")
    private String jdr;

    @ApiModelProperty(value = "外网发布栏目code")
    private String wwfblmCode;

    @ApiModelProperty(value = "公开类型(主动公开,依申请公开,不予公开)")
    private String gklx;

    @ApiModelProperty(value = "理由")
    private String ly;

    @ApiModelProperty(value = "备注")
    private String bz;

    @ApiModelProperty(value = "流程任务相关信息")
    private FlowTaskInfoDto flowTaskInfoDto;
}
