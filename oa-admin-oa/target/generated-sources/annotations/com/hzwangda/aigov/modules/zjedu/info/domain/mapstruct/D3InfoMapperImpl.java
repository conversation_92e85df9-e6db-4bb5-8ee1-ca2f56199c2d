package com.hzwangda.aigov.modules.zjedu.info.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.info.domain.dto.D3InfoDto;
import com.hzwangda.aigov.modules.zjedu.info.domain.entity.D3Info;
import com.hzwangda.aigov.modules.zjedu.info.domain.entity.D3InfoReceive;
import com.hzwangda.aigov.modules.zjedu.info.domain.entity.D3InfoUseLog;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class D3InfoMapperImpl implements D3InfoMapper {

    @Override
    public D3InfoDto toDto(D3Info entity) {
        if ( entity == null ) {
            return null;
        }

        D3InfoDto d3InfoDto = new D3InfoDto();

        d3InfoDto.setCreateBy( entity.getCreateBy() );
        d3InfoDto.setCreateTime( entity.getCreateTime() );
        d3InfoDto.setUpdateTime( entity.getUpdateTime() );
        d3InfoDto.setUpdatedBy( entity.getUpdatedBy() );
        d3InfoDto.setAttach( entity.getAttach() );
        d3InfoDto.setContactPhone( entity.getContactPhone() );
        d3InfoDto.setContacter( entity.getContacter() );
        d3InfoDto.setContent( entity.getContent() );
        d3InfoDto.setContributeDept( entity.getContributeDept() );
        d3InfoDto.setContributeMemo( entity.getContributeMemo() );
        d3InfoDto.setContributeUser( entity.getContributeUser() );
        d3InfoDto.setContributeUserDeptId( entity.getContributeUserDeptId() );
        d3InfoDto.setContributeUsername( entity.getContributeUsername() );
        d3InfoDto.setDeptType( entity.getDeptType() );
        d3InfoDto.setId( entity.getId() );
        d3InfoDto.setInfoGroupInnerCode( entity.getInfoGroupInnerCode() );
        List<D3InfoUseLog> list = entity.getInfoUseList();
        if ( list != null ) {
            d3InfoDto.setInfoUseList( new ArrayList<D3InfoUseLog>( list ) );
        }
        d3InfoDto.setInfomd5( entity.getInfomd5() );
        List<D3InfoReceive> list1 = entity.getReceives();
        if ( list1 != null ) {
            d3InfoDto.setReceives( new ArrayList<D3InfoReceive>( list1 ) );
        }
        d3InfoDto.setStatus( entity.getStatus() );
        d3InfoDto.setTitle( entity.getTitle() );
        d3InfoDto.setUrgentLevel( entity.getUrgentLevel() );

        return d3InfoDto;
    }

    @Override
    public List<D3InfoDto> toDto(List<D3Info> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<D3InfoDto> list = new ArrayList<D3InfoDto>( entityList.size() );
        for ( D3Info d3Info : entityList ) {
            list.add( toDto( d3Info ) );
        }

        return list;
    }

    @Override
    public D3Info toEntity(D3InfoDto dto) {
        if ( dto == null ) {
            return null;
        }

        D3Info d3Info = new D3Info();

        d3Info.setCreateBy( dto.getCreateBy() );
        d3Info.setCreateTime( dto.getCreateTime() );
        d3Info.setUpdateTime( dto.getUpdateTime() );
        d3Info.setUpdatedBy( dto.getUpdatedBy() );
        d3Info.setAttach( dto.getAttach() );
        d3Info.setContactPhone( dto.getContactPhone() );
        d3Info.setContacter( dto.getContacter() );
        d3Info.setContent( dto.getContent() );
        d3Info.setContributeDept( dto.getContributeDept() );
        d3Info.setContributeMemo( dto.getContributeMemo() );
        d3Info.setContributeUser( dto.getContributeUser() );
        d3Info.setContributeUserDeptId( dto.getContributeUserDeptId() );
        d3Info.setContributeUsername( dto.getContributeUsername() );
        d3Info.setDeptType( dto.getDeptType() );
        d3Info.setId( dto.getId() );
        d3Info.setInfoGroupInnerCode( dto.getInfoGroupInnerCode() );
        List<D3InfoUseLog> list = dto.getInfoUseList();
        if ( list != null ) {
            d3Info.setInfoUseList( new ArrayList<D3InfoUseLog>( list ) );
        }
        d3Info.setInfomd5( dto.getInfomd5() );
        List<D3InfoReceive> list1 = dto.getReceives();
        if ( list1 != null ) {
            d3Info.setReceives( new ArrayList<D3InfoReceive>( list1 ) );
        }
        d3Info.setStatus( dto.getStatus() );
        d3Info.setTitle( dto.getTitle() );
        d3Info.setUrgentLevel( dto.getUrgentLevel() );

        return d3Info;
    }

    @Override
    public List<D3Info> toEntity(List<D3InfoDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<D3Info> list = new ArrayList<D3Info>( dtoList.size() );
        for ( D3InfoDto d3InfoDto : dtoList ) {
            list.add( toEntity( d3InfoDto ) );
        }

        return list;
    }
}
