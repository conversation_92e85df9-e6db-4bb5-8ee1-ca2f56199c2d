package com.hzwangda.aigov.modules.GoodsReceive.repository;

import com.hzwangda.aigov.modules.GoodsReceive.domain.GoodsInventory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;


public interface GoodsInventoryRepository extends JpaRepository<GoodsInventory, Long>, JpaSpecificationExecutor<GoodsInventory> {

    List<GoodsInventory> findAllByPidIsNull();

    List<GoodsInventory> findAllByPidIsNullAndEnabled(Boolean enabled);

    List<GoodsInventory> findAllByPid(Long pid);

    List<GoodsInventory> findAllByPidAndEnabled(Long pid, Boolean enabled);

    Integer deleteAllByIdIn(Long[] ids);
}
