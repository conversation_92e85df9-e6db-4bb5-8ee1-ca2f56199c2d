package com.hzwangda.aigov.modules.zjedu.info.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.info.domain.dto.D3InfoReceiveDto;
import com.hzwangda.aigov.modules.zjedu.info.domain.entity.D3InfoReceive;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class D3InfoReceiveMapperImpl implements D3InfoReceiveMapper {

    @Override
    public D3InfoReceiveDto toDto(D3InfoReceive entity) {
        if ( entity == null ) {
            return null;
        }

        D3InfoReceiveDto d3InfoReceiveDto = new D3InfoReceiveDto();

        d3InfoReceiveDto.setBackMsg( entity.getBackMsg() );
        d3InfoReceiveDto.setBackTime( entity.getBackTime() );
        d3InfoReceiveDto.setContent( entity.getContent() );
        d3InfoReceiveDto.setDeptName( entity.getDeptName() );
        d3InfoReceiveDto.setEditAdvice( entity.getEditAdvice() );
        d3InfoReceiveDto.setId( entity.getId() );
        d3InfoReceiveDto.setInfoGroupInnerCode( entity.getInfoGroupInnerCode() );
        d3InfoReceiveDto.setInfoId( entity.getInfoId() );
        d3InfoReceiveDto.setInfoMD5( entity.getInfoMD5() );
        d3InfoReceiveDto.setIsShare( entity.getIsShare() );
        d3InfoReceiveDto.setReceiveStatus( entity.getReceiveStatus() );
        d3InfoReceiveDto.setReceiveTime( entity.getReceiveTime() );
        d3InfoReceiveDto.setSendTime( entity.getSendTime() );
        d3InfoReceiveDto.setTitle( entity.getTitle() );
        d3InfoReceiveDto.setUserName( entity.getUserName() );

        return d3InfoReceiveDto;
    }

    @Override
    public List<D3InfoReceiveDto> toDto(List<D3InfoReceive> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<D3InfoReceiveDto> list = new ArrayList<D3InfoReceiveDto>( entityList.size() );
        for ( D3InfoReceive d3InfoReceive : entityList ) {
            list.add( toDto( d3InfoReceive ) );
        }

        return list;
    }

    @Override
    public D3InfoReceive toEntity(D3InfoReceiveDto dto) {
        if ( dto == null ) {
            return null;
        }

        D3InfoReceive d3InfoReceive = new D3InfoReceive();

        d3InfoReceive.setBackMsg( dto.getBackMsg() );
        d3InfoReceive.setBackTime( dto.getBackTime() );
        d3InfoReceive.setContent( dto.getContent() );
        d3InfoReceive.setDeptName( dto.getDeptName() );
        d3InfoReceive.setEditAdvice( dto.getEditAdvice() );
        d3InfoReceive.setId( dto.getId() );
        d3InfoReceive.setInfoGroupInnerCode( dto.getInfoGroupInnerCode() );
        d3InfoReceive.setInfoId( dto.getInfoId() );
        d3InfoReceive.setInfoMD5( dto.getInfoMD5() );
        d3InfoReceive.setIsShare( dto.getIsShare() );
        d3InfoReceive.setReceiveStatus( dto.getReceiveStatus() );
        d3InfoReceive.setReceiveTime( dto.getReceiveTime() );
        d3InfoReceive.setSendTime( dto.getSendTime() );
        d3InfoReceive.setTitle( dto.getTitle() );
        d3InfoReceive.setUserName( dto.getUserName() );

        return d3InfoReceive;
    }

    @Override
    public List<D3InfoReceive> toEntity(List<D3InfoReceiveDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<D3InfoReceive> list = new ArrayList<D3InfoReceive>( dtoList.size() );
        for ( D3InfoReceiveDto d3InfoReceiveDto : dtoList ) {
            list.add( toEntity( d3InfoReceiveDto ) );
        }

        return list;
    }
}
