package com.hzwangda.aigov.modules.taskmanagement.domain.mapstruct;

import com.hzwangda.aigov.modules.taskmanagement.domain.dto.TmCatalogDto;
import com.hzwangda.aigov.modules.taskmanagement.domain.entity.TmCatalog;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class TmCatalogMapperImpl implements TmCatalogMapper {

    @Override
    public TmCatalogDto toDto(TmCatalog entity) {
        if ( entity == null ) {
            return null;
        }

        TmCatalogDto tmCatalogDto = new TmCatalogDto();

        tmCatalogDto.setCreateBy( entity.getCreateBy() );
        tmCatalogDto.setCreateTime( entity.getCreateTime() );
        tmCatalogDto.setUpdateTime( entity.getUpdateTime() );
        tmCatalogDto.setUpdatedBy( entity.getUpdatedBy() );
        tmCatalogDto.setCatalogId( entity.getCatalogId() );
        tmCatalogDto.setEnable( entity.getEnable() );
        tmCatalogDto.setName( entity.getName() );
        tmCatalogDto.setStatus( entity.getStatus() );
        tmCatalogDto.setYear( entity.getYear() );

        return tmCatalogDto;
    }

    @Override
    public List<TmCatalogDto> toDto(List<TmCatalog> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmCatalogDto> list = new ArrayList<TmCatalogDto>( entityList.size() );
        for ( TmCatalog tmCatalog : entityList ) {
            list.add( toDto( tmCatalog ) );
        }

        return list;
    }

    @Override
    public TmCatalog toEntity(TmCatalogDto dto) {
        if ( dto == null ) {
            return null;
        }

        TmCatalog tmCatalog = new TmCatalog();

        tmCatalog.setCreateBy( dto.getCreateBy() );
        tmCatalog.setCreateTime( dto.getCreateTime() );
        tmCatalog.setUpdateTime( dto.getUpdateTime() );
        tmCatalog.setUpdatedBy( dto.getUpdatedBy() );
        tmCatalog.setCatalogId( dto.getCatalogId() );
        tmCatalog.setEnable( dto.getEnable() );
        tmCatalog.setName( dto.getName() );
        tmCatalog.setStatus( dto.getStatus() );
        tmCatalog.setYear( dto.getYear() );

        return tmCatalog;
    }

    @Override
    public List<TmCatalog> toEntity(List<TmCatalogDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TmCatalog> list = new ArrayList<TmCatalog>( dtoList.size() );
        for ( TmCatalogDto tmCatalogDto : dtoList ) {
            list.add( toEntity( tmCatalogDto ) );
        }

        return list;
    }
}
