package com.hzwangda.aigov.modules.appControl.service.impl;

import com.hzwangda.aigov.modules.appControl.domain.Z08SelfAppSpace;
import com.hzwangda.aigov.modules.appControl.domain.Z08SelfApplication;
import com.hzwangda.aigov.modules.appControl.dto.SelfApplicationCriteria;
import com.hzwangda.aigov.modules.appControl.repository.SelfAppSpaceRepository;
import com.hzwangda.aigov.modules.appControl.repository.SelfApplicationRepository;
import com.hzwangda.aigov.modules.appControl.service.SelfApplicationService;
import com.wangda.oa.modules.workflow.enums.application.AppStatusEnum;
import com.wangda.oa.utils.QueryHelp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/23
 * @description
 */
@Service
@Slf4j
public class SelfApplicationServiceImpl implements SelfApplicationService {
    @Resource
    private SelfApplicationRepository selfApplicationRepository;

    @Resource
    private SelfAppSpaceRepository selfAppSpaceRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Z08SelfApplication addOrUpdateBasics(Z08SelfApplication z08SelfApplication) {
        Z08SelfApplication app = new Z08SelfApplication();
        if(z08SelfApplication.getId() != null) {
            app = selfApplicationRepository.findById(z08SelfApplication.getId()).orElseGet(Z08SelfApplication::new);
        }else{
            Z08SelfAppSpace appSpace = selfAppSpaceRepository.findById(z08SelfApplication.getAppSpaceId()).orElseGet(Z08SelfAppSpace::new);
            app.setAppSpace(appSpace);
            app.setAppStatus(AppStatusEnum.ONLINE);
        }
        app.copy(z08SelfApplication);
        Z08SelfApplication save = selfApplicationRepository.save(app);
        return save;
    }

    @Override
    public Z08SelfApplication getById(Long id) {
        Z08SelfApplication Z08SelfApplication = selfApplicationRepository.findById(id).orElseGet(Z08SelfApplication::new);
        return Z08SelfApplication;
    }

    @Override
    public Page<Z08SelfApplication> queryListPage(SelfApplicationCriteria criteria, Pageable pageable) {
        Specification<Z08SelfApplication> sp = (root, cq, cb) -> {
            Predicate pe = QueryHelp.getPredicate(root, criteria, cb);
          /*  if(criteria.getAppSpaceId() != null) {
                Long spaceId = criteria.getAppSpaceId();
                // 查询应用权限为这个应用空间的
                pe = cb.and(pe, cb.equal(root.get("appSpaceId"),spaceId));
            }*/
            cq.orderBy(cb.asc(root.get("sort")));
            return pe;
        };

        Page<Z08SelfApplication> Z08SelfApplicationPage = selfApplicationRepository.findAll(sp, pageable);
        return Z08SelfApplicationPage;
    }

    @Override
    public Boolean delete(Long id) {
        selfApplicationRepository.deleteById(id);
        return true;
    }



    @Override
    public List<Z08SelfApplication> getByZ08SelfApplicationName(String Z08SelfApplicationName) {
        List<Z08SelfApplication> Z08SelfApplications = selfApplicationRepository.findByNameLike("%" + Z08SelfApplicationName + "%");
        return Z08SelfApplications;
    }

    @Override
    public List<Z08SelfApplication> getByType(String type) {
        List<Z08SelfApplication> Z08SelfApplications = selfApplicationRepository.findByType(type);
        return Z08SelfApplications;
    }

    @Override
    public List<Z08SelfApplication> getByApplicationName(String applicationName) {
        List<Z08SelfApplication> applications = selfApplicationRepository.findByNameLike("%" + applicationName + "%");
        return applications;
    }



}
