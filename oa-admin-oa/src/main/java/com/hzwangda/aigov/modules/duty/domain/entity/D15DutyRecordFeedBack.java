package com.hzwangda.aigov.modules.duty.domain.entity;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "d15_duty_record_feedback")
@Data
public class D15DutyRecordFeedBack extends BaseDomain {

    @ApiModelProperty(value = "反馈单位")
    private String unit;
    @ApiModelProperty(value = "反馈人")
    private String username;
    @ApiModelProperty(value = "联系方式")
    private String phone;
    @ApiModelProperty(value = "反馈内容")
    @Column(length = 2000)
    private String content;
    @ApiModelProperty(value = "主表id")
    private String number;
}
