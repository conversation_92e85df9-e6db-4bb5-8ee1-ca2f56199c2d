package com.hzwangda.aigov.modules.addresslist.mapstruct;

import com.hzwangda.aigov.modules.addresslist.domain.dto.UnitAddressListGroupDto;
import com.hzwangda.aigov.modules.addresslist.domain.entity.UnitAddressListGroup;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class UnitAddressListGroupMapperImpl implements UnitAddressListGroupMapper {

    @Override
    public UnitAddressListGroupDto toDto(UnitAddressListGroup entity) {
        if ( entity == null ) {
            return null;
        }

        UnitAddressListGroupDto unitAddressListGroupDto = new UnitAddressListGroupDto();

        unitAddressListGroupDto.setCreateBy( entity.getCreateBy() );
        unitAddressListGroupDto.setCreateTime( entity.getCreateTime() );
        unitAddressListGroupDto.setUpdateTime( entity.getUpdateTime() );
        unitAddressListGroupDto.setUpdatedBy( entity.getUpdatedBy() );
        unitAddressListGroupDto.setGroupName( entity.getGroupName() );
        unitAddressListGroupDto.setGroupUsername( entity.getGroupUsername() );
        unitAddressListGroupDto.setId( entity.getId() );
        unitAddressListGroupDto.setSort( entity.getSort() );

        return unitAddressListGroupDto;
    }

    @Override
    public List<UnitAddressListGroupDto> toDto(List<UnitAddressListGroup> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<UnitAddressListGroupDto> list = new ArrayList<UnitAddressListGroupDto>( entityList.size() );
        for ( UnitAddressListGroup unitAddressListGroup : entityList ) {
            list.add( toDto( unitAddressListGroup ) );
        }

        return list;
    }

    @Override
    public UnitAddressListGroup toEntity(UnitAddressListGroupDto dto) {
        if ( dto == null ) {
            return null;
        }

        UnitAddressListGroup unitAddressListGroup = new UnitAddressListGroup();

        unitAddressListGroup.setCreateBy( dto.getCreateBy() );
        unitAddressListGroup.setCreateTime( dto.getCreateTime() );
        unitAddressListGroup.setUpdateTime( dto.getUpdateTime() );
        unitAddressListGroup.setUpdatedBy( dto.getUpdatedBy() );
        unitAddressListGroup.setGroupName( dto.getGroupName() );
        unitAddressListGroup.setGroupUsername( dto.getGroupUsername() );
        unitAddressListGroup.setId( dto.getId() );
        unitAddressListGroup.setSort( dto.getSort() );

        return unitAddressListGroup;
    }

    @Override
    public List<UnitAddressListGroup> toEntity(List<UnitAddressListGroupDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<UnitAddressListGroup> list = new ArrayList<UnitAddressListGroup>( dtoList.size() );
        for ( UnitAddressListGroupDto unitAddressListGroupDto : dtoList ) {
            list.add( toEntity( unitAddressListGroupDto ) );
        }

        return list;
    }
}
