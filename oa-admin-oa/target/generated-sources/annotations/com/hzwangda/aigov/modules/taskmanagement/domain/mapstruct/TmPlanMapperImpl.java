package com.hzwangda.aigov.modules.taskmanagement.domain.mapstruct;

import com.hzwangda.aigov.modules.taskmanagement.domain.dto.TmPlanDto;
import com.hzwangda.aigov.modules.taskmanagement.domain.entity.TmPlan;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class TmPlanMapperImpl implements TmPlanMapper {

    @Override
    public TmPlanDto toDto(TmPlan entity) {
        if ( entity == null ) {
            return null;
        }

        TmPlanDto tmPlanDto = new TmPlanDto();

        tmPlanDto.setCreateBy( entity.getCreateBy() );
        tmPlanDto.setCreateTime( entity.getCreateTime() );
        tmPlanDto.setUpdateTime( entity.getUpdateTime() );
        tmPlanDto.setUpdatedBy( entity.getUpdatedBy() );
        tmPlanDto.setContent( entity.getContent() );
        tmPlanDto.setId( entity.getId() );
        tmPlanDto.setTask( entity.getTask() );
        tmPlanDto.setTitle( entity.getTitle() );

        return tmPlanDto;
    }

    @Override
    public List<TmPlanDto> toDto(List<TmPlan> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmPlanDto> list = new ArrayList<TmPlanDto>( entityList.size() );
        for ( TmPlan tmPlan : entityList ) {
            list.add( toDto( tmPlan ) );
        }

        return list;
    }

    @Override
    public TmPlan toEntity(TmPlanDto dto) {
        if ( dto == null ) {
            return null;
        }

        TmPlan tmPlan = new TmPlan();

        tmPlan.setCreateBy( dto.getCreateBy() );
        tmPlan.setCreateTime( dto.getCreateTime() );
        tmPlan.setUpdateTime( dto.getUpdateTime() );
        tmPlan.setUpdatedBy( dto.getUpdatedBy() );
        tmPlan.setContent( dto.getContent() );
        tmPlan.setId( dto.getId() );
        tmPlan.setTask( dto.getTask() );
        tmPlan.setTitle( dto.getTitle() );

        return tmPlan;
    }

    @Override
    public List<TmPlan> toEntity(List<TmPlanDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TmPlan> list = new ArrayList<TmPlan>( dtoList.size() );
        for ( TmPlanDto tmPlanDto : dtoList ) {
            list.add( toEntity( tmPlanDto ) );
        }

        return list;
    }
}
