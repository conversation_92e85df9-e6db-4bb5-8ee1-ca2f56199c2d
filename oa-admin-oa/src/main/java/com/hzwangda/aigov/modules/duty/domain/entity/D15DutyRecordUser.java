package com.hzwangda.aigov.modules.duty.domain.entity;

import com.wangda.boot.platform.idWorker.IdWorker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "d15_duty_record_user")
@ApiModel("值班记录转发人员表")
@DynamicInsert
public class D15DutyRecordUser {
    @Id
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "主表id")
    private Long recordId;

    private String username;

    private String nickName;

    @ApiModelProperty(value = "1签转 2抄送")
    private Integer type;

    @ApiModelProperty(value = "签转类别: 属地sd,处室cs,转分发zf")
    private String zfType;

    @ApiModelProperty(value = "是否已阅")
    @Column(columnDefinition = "int default 0")
    private Integer readFlag;

    @Temporal(TemporalType.TIMESTAMP)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date readTime;

    @ApiModelProperty(value = "是否已发送消息")
    @Column(columnDefinition = "int default 0")
    private Integer msgFlag;

    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }
}

