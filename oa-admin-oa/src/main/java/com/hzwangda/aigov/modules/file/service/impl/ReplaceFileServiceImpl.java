package com.hzwangda.aigov.modules.file.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.file.entity.ReplaceFileLog;
import com.hzwangda.aigov.modules.file.repository.ReplaceFileLogRepository;
import com.hzwangda.aigov.modules.file.service.ReplaceFileService;
import com.wangda.oa.config.FileProperties;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.repository.LocalStorageRepository;
import com.wangda.oa.utils.FileUtil;
import com.wangda.oa.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;

@Service
@Slf4j
public class ReplaceFileServiceImpl implements ReplaceFileService {

    @Resource
    private FileProperties properties;
    @Resource
    private LocalStorageRepository localStorageRepository;
    @Resource
    private ReplaceFileLogRepository replaceFileLogRepository;

    @Override
    public Object replaceFile(Long id, MultipartFile file, HttpServletRequest request, HttpServletResponse response) {
        File f = FileUtil.upload(file, properties.getPath().getPath());
        if (f==null) {
            throw new BadRequestException("上传失败!");
        }
        LocalStorage localStorage = localStorageRepository.findById(id).orElseThrow(() -> new BadRequestException("找不到原文件！"));
        String path = localStorage.getPath();
        log.info("文件id："+id);
        log.info("原文件路径："+path);
        String path1 = f.getPath();
        log.info("现文件路径："+path1);
        localStorage.setPath(path1);
        localStorage.setRealName(f.getName());
        String suffix = FileUtil.getExtensionName(f.getName());
        localStorage.setSuffix(suffix);
        localStorageRepository.save(localStorage);

        ReplaceFileLog replaceFileLog = new ReplaceFileLog();
        replaceFileLog.setStorageId(id);
        replaceFileLog.setOriginPath(path);
        replaceFileLog.setPath(path1);
        replaceFileLogRepository.save(replaceFileLog);
        return "success";
    }

    @Override
    public Page<ReplaceFileLog> logs(Long id, String name, Pageable pageable) {
        Page<ReplaceFileLog> all = replaceFileLogRepository.findAll((root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.isNotNull(root.get("id"));
            if (id != null) {
                predicate = criteriaBuilder.and(
                        predicate,
                        criteriaBuilder.equal(root.get("storageId"), id)
                );
            }
            if (StringUtils.isNotEmpty(name)) {
                predicate = criteriaBuilder.and(
                        predicate,
                        criteriaBuilder.or(
                                criteriaBuilder.like(root.get("originPath"), "%" + name + "%"),
                                criteriaBuilder.like(root.get("path"), "%" + name + "%")
                        )
                );
            }
            return predicate;
        }, pageable);
        return all;
    }
}
