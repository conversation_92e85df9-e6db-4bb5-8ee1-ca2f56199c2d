package com.hzwangda.aigov.modules.GoodsReceive.domain;


import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@Table(name = "goods_receive")
public class GoodsReceive extends BaseBpmDomain {
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name="main_id")
    @ApiModelProperty(value = "领用记录")
    private List<GoodsCategory>  goodsCategories;

    @Column(columnDefinition = "text")
    @ApiModelProperty(value = "领用说明")
    private String lysm;

    @Column(columnDefinition = "text")
    @ApiModelProperty(value = "备注")
    private String remark;

    @Column(columnDefinition = "text")
    @ApiModelProperty(value = "物品管理员意见")
    private String wpglyyj;

    @Column(columnDefinition = "text")
    @ApiModelProperty(value = "办公室负责人意见")
    private String fzrYj;

    @Column(columnDefinition = "text")
    @ApiModelProperty(value = "分管领导意见")
    private String leaderYj;

    public void setGoodsCategories(List<GoodsCategory> goodsCategories) {
        if (this.goodsCategories==null) {
            this.goodsCategories = new ArrayList<>();
        }
        this.goodsCategories.clear();
        this.goodsCategories.addAll(goodsCategories);
    }
}
