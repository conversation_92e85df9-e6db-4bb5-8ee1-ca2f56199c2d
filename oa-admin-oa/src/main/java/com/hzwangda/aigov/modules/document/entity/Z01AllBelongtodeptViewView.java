package com.hzwangda.aigov.modules.document.entity;

import lombok.Data;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Immutable
@Subselect("select * from all_belongtodept_view")
@Entity
@Data
public class Z01AllBelongtodeptViewView implements Serializable {
    @Id
    private Long id;

    private String belongToDept;

    private String bpmInstanceId;

}
