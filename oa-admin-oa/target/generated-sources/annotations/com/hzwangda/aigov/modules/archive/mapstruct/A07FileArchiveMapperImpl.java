package com.hzwangda.aigov.modules.archive.mapstruct;

import com.hzwangda.aigov.modules.archive.entity.A07FileArchive;
import com.hzwangda.aigov.modules.archive.entity.A07FileArchive.A07FileArchiveBuilder;
import com.hzwangda.aigov.modules.archive.entity.FileArchiveDto;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07DocumentMapperUtil;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A07FileArchiveMapperImpl implements A07FileArchiveMapper {

    @Autowired
    private A07DocumentMapperUtil a07DocumentMapperUtil;

    @Override
    public List<FileArchiveDto> toDto(List<A07FileArchive> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<FileArchiveDto> list = new ArrayList<FileArchiveDto>( entityList.size() );
        for ( A07FileArchive a07FileArchive : entityList ) {
            list.add( toDto( a07FileArchive ) );
        }

        return list;
    }

    @Override
    public A07FileArchive toEntity(FileArchiveDto dto) {
        if ( dto == null ) {
            return null;
        }

        A07FileArchiveBuilder a07FileArchive = A07FileArchive.builder();

        a07FileArchive.applicationReason( dto.getApplicationReason() );
        a07FileArchive.archiveNo( dto.getArchiveNo() );
        a07FileArchive.boxNo( dto.getBoxNo() );
        a07FileArchive.bpmProcessKey( dto.getBpmProcessKey() );
        a07FileArchive.carrierNumber( dto.getCarrierNumber() );
        a07FileArchive.cwrq( dto.getCwrq() );
        a07FileArchive.docId( dto.getDocId() );
        a07FileArchive.docNo( dto.getDocNo() );
        a07FileArchive.docType( dto.getDocType() );
        a07FileArchive.erpFileId( dto.getErpFileId() );
        a07FileArchive.erpNum( dto.getErpNum() );
        a07FileArchive.fbcc( dto.getFbcc() );
        a07FileArchive.fileDate( dto.getFileDate() );
        a07FileArchive.fileNumber( dto.getFileNumber() );
        a07FileArchive.filePage( dto.getFilePage() );
        a07FileArchive.fj( a07DocumentMapperUtil.toConvertToId( dto.getFj() ) );
        a07FileArchive.fondsNumber( dto.getFondsNumber() );
        a07FileArchive.fwjg( dto.getFwjg() );
        a07FileArchive.keywords( dto.getKeywords() );
        a07FileArchive.limitTime( dto.getLimitTime() );
        a07FileArchive.no( dto.getNo() );
        a07FileArchive.note( dto.getNote() );
        a07FileArchive.pageNo( dto.getPageNo() );
        a07FileArchive.procInstId( dto.getProcInstId() );
        a07FileArchive.qfr( dto.getQfr() );
        a07FileArchive.remark( dto.getRemark() );
        a07FileArchive.responsiblePerson( dto.getResponsiblePerson() );
        a07FileArchive.resultJson( dto.getResultJson() );
        a07FileArchive.secretLevel( dto.getSecretLevel() );
        a07FileArchive.sendTime( dto.getSendTime() );
        a07FileArchive.sender( dto.getSender() );
        a07FileArchive.status( dto.getStatus() );
        a07FileArchive.subject( dto.getSubject() );
        a07FileArchive.year( dto.getYear() );
        a07FileArchive.yfjg( dto.getYfjg() );
        a07FileArchive.yfrq( dto.getYfrq() );

        return a07FileArchive.build();
    }

    @Override
    public List<A07FileArchive> toEntity(List<FileArchiveDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A07FileArchive> list = new ArrayList<A07FileArchive>( dtoList.size() );
        for ( FileArchiveDto fileArchiveDto : dtoList ) {
            list.add( toEntity( fileArchiveDto ) );
        }

        return list;
    }

    @Override
    public FileArchiveDto toDto(A07FileArchive entity) {
        if ( entity == null ) {
            return null;
        }

        FileArchiveDto fileArchiveDto = new FileArchiveDto();

        fileArchiveDto.setCreateBy( entity.getCreateBy() );
        fileArchiveDto.setCreateDate( entity.getCreateDate() );
        fileArchiveDto.setCreatorId( entity.getCreatorId() );
        fileArchiveDto.setEnabled( entity.getEnabled() );
        fileArchiveDto.setId( entity.getId() );
        fileArchiveDto.setModifiedBy( entity.getModifiedBy() );
        fileArchiveDto.setModifiedDate( entity.getModifiedDate() );
        fileArchiveDto.setModifiedId( entity.getModifiedId() );
        fileArchiveDto.setVersion( entity.getVersion() );
        fileArchiveDto.setApplicationReason( entity.getApplicationReason() );
        fileArchiveDto.setArchiveNo( entity.getArchiveNo() );
        fileArchiveDto.setBoxNo( entity.getBoxNo() );
        fileArchiveDto.setBpmProcessKey( entity.getBpmProcessKey() );
        fileArchiveDto.setCarrierNumber( entity.getCarrierNumber() );
        fileArchiveDto.setCwrq( entity.getCwrq() );
        fileArchiveDto.setDocId( entity.getDocId() );
        fileArchiveDto.setDocNo( entity.getDocNo() );
        fileArchiveDto.setDocType( entity.getDocType() );
        fileArchiveDto.setErpFileId( entity.getErpFileId() );
        fileArchiveDto.setErpNum( entity.getErpNum() );
        fileArchiveDto.setFbcc( entity.getFbcc() );
        fileArchiveDto.setFileDate( entity.getFileDate() );
        fileArchiveDto.setFileNumber( entity.getFileNumber() );
        fileArchiveDto.setFilePage( entity.getFilePage() );
        fileArchiveDto.setFondsNumber( entity.getFondsNumber() );
        fileArchiveDto.setFwjg( entity.getFwjg() );
        fileArchiveDto.setKeywords( entity.getKeywords() );
        fileArchiveDto.setLimitTime( entity.getLimitTime() );
        fileArchiveDto.setNo( entity.getNo() );
        fileArchiveDto.setNote( entity.getNote() );
        fileArchiveDto.setPageNo( entity.getPageNo() );
        fileArchiveDto.setProcInstId( entity.getProcInstId() );
        fileArchiveDto.setQfr( entity.getQfr() );
        fileArchiveDto.setRemark( entity.getRemark() );
        fileArchiveDto.setResponsiblePerson( entity.getResponsiblePerson() );
        fileArchiveDto.setResultJson( entity.getResultJson() );
        fileArchiveDto.setSecretLevel( entity.getSecretLevel() );
        fileArchiveDto.setSendTime( entity.getSendTime() );
        fileArchiveDto.setSender( entity.getSender() );
        fileArchiveDto.setStatus( entity.getStatus() );
        fileArchiveDto.setSubject( entity.getSubject() );
        fileArchiveDto.setYear( entity.getYear() );
        fileArchiveDto.setYfjg( entity.getYfjg() );
        fileArchiveDto.setYfrq( entity.getYfrq() );

        fileArchiveDto.setFj( a07DocumentMapperUtil.toFj(entity.getFj()) );

        return fileArchiveDto;
    }
}
