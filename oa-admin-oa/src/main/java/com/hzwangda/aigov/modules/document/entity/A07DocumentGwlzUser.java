package com.hzwangda.aigov.modules.document.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 公文用户关联表
 *
 * <AUTHOR>
 * @date 2021/6/15 上午10:18
 */
@Data
@Entity
@Table(name = "a07_document_GwlzUser")
public class A07DocumentGwlzUser extends BaseDomain implements Serializable {


    @ApiModelProperty(value = "公文ID")
    @JoinColumn(name = "gwid", referencedColumnName = "id")
    private String gwid;

    @Column(name = "qsr")
    @ApiModelProperty(value = "签收人")
    private String qsr;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @Column(name = "username")
    @ApiModelProperty(value = "用户名，手输的姓名则无值")
    private String username;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "签收时间")
    private Date qsrq;

    @Column(name = "qszt")
    @ApiModelProperty(value = "签收状态(0:未签收,1:已签收)")
    private Integer qszt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "签收时间")
    private Date cyrq;

    @Column(name = "cyzt")
    @ApiModelProperty(value = "签收状态(0:未签收,1:已签收)")
    private Integer cyzt=0;

    @Column(name = "zrzt")
    @ApiModelProperty(value = "转入内部收文状态(0:未传入,1:已转入)")
    private Integer zrzt=0;

    @Column(name = "qslx")
    @ApiModelProperty(value = "签收类型(zs：主送,cs:抄送)")
    private String qslx;
    @Column(name = "o_id")
    @ApiModelProperty(value = "用户、部门id")
    private Long oId;

    public void copy(A07DocumentGwlzUser source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }

    @Override
    public Long getId() {
        if (Objects.nonNull(this.oId)) {
            return this.oId;
        } else {
            return super.getId();
        }
    }

    @PrePersist
    public void preCreateEntity() {
        if (Objects.isNull(oId)) {
            this.oId = this.id;
        }
        this.id = null;
        super.preCreateEntity();
    }
}
