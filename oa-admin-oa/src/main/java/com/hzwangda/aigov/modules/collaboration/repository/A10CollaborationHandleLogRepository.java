package com.hzwangda.aigov.modules.collaboration.repository;

import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationHandleLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface A10CollaborationHandleLogRepository extends JpaRepository<A10CollaborationHandleLog, Long>, JpaSpecificationExecutor<A10CollaborationHandleLog> {

    Page<A10CollaborationHandleLog> findByInfoId(Long infoId, Pageable pageable);

    List<A10CollaborationHandleLog> findByInfoIdAndRemarkIsNotNullOrderByHandleTimeDesc(Long infoId);
}
