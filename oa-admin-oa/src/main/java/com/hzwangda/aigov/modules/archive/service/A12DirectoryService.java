package com.hzwangda.aigov.modules.archive.service;


import com.hzwangda.aigov.modules.archive.dto.A12DirectoryDto;
import com.hzwangda.aigov.modules.archive.entity.A12Directory;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/7 下午4:37
 **/
public interface A12DirectoryService {

    /**
     * 根据部门id查找
     *
     * @param deptId
     * @return
     */
    A12DirectoryDto getDirectoryByDeptId(Long deptId);

    /**
     * 查询目录树列表
     *
     * @return
     */
    List<A12DirectoryDto> getMyDirectory() throws Exception;

    /**
     * 新增或修改目录树接口
     *
     * @param resources
     * @return
     */
    Boolean createOrUpdate(A12Directory resources);

    /**
     * 删除文件档案目录接口
     *
     * @param ids
     * @return
     */
    Boolean delete(Long[] ids);

}
