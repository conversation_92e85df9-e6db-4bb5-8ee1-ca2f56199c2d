package com.hzwangda.aigov.modules.addresslist.controller;

import com.hzwangda.aigov.modules.addresslist.service.AddressListService;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.extension.bo.CommonControlsBO;
import com.wangda.oa.modules.extension.bo.UserListBO;
import com.wangda.oa.modules.extension.dto.org.UserListDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: zhangzhanlong
 * @date: 2022/8/5 13:20
 * @description: 通讯录控件
 */
@RestController
@RequestMapping("addressList")
@Api(tags = "通讯录")
public class AddressListController {
    @Resource
    private AddressListService addressListService;

    /**
     * 主单位之下部门
     * @return
     */
    @ApiOperation(value = "查询主单位之下通用控件", notes = "查询主单位之下通用控件")
    @RequestMapping(value = "/getOrgList", method = RequestMethod.POST)
    public ResultJson<Object> getOrgList(@RequestBody CommonControlsBO bo) {
        return addressListService.getOrgList(bo);

    }

    /**
     * 主单位之下根据部门id查询人员
     * @return
     */
    @ApiOperation(value = "查询主单位之下通用控件", notes = "查询主单位之下通用控件")
    @RequestMapping(value = "/getOrgUserList", method = RequestMethod.POST)
    public ResultJson<Object> getOrgUserList(@RequestBody CommonControlsBO bo) {
        return addressListService.getOrgUserList(bo);
    }

    /**
     * 根据用户名查询用户集合
     * @return
     */
    @ApiOperation(value = "根据用户名查询用户集合", notes = "根据用户名查询用户集合")
    @RequestMapping(value = "/getUserList", method = RequestMethod.POST)
    public ResultJson<List<UserListDto>> getUserList(@RequestBody UserListBO bo) {
        return addressListService.getUserList(bo);
    }

}
