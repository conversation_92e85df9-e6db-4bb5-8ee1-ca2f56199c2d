package com.hzwangda.aigov.modules.duty.domain.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class D15DutyRecordCriteria {
    @ApiModelProperty(value = "开始值班日期")
    @Query(type = Query.Type.BETWEEN)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private List<Date> dutyTime;

    @ApiModelProperty(value = "模糊搜索")
    @Query(type = Query.Type.INNER_LIKE, blurry = "nickName,content")
    private String searchKeys;

    @ApiModelProperty(value = "是否完成")
    @Query
    private Integer finished;

    @ApiModelProperty(value = "待阅办todo,未完成unfinished,全部all")
    private String type;

}
