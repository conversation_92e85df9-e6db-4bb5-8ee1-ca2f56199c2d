package com.hzwangda.aigov.modules.archive.controller;

import com.hzwangda.aigov.modules.archive.dto.A12ArchivesAddListDto;
import com.hzwangda.aigov.modules.archive.dto.A12ArchivesCriteria;
import com.hzwangda.aigov.modules.archive.service.A12ArchivesService;
import com.wangda.oa.annotation.Log;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2021/9/7 下午7:35
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "文件档案")
@RequestMapping("/api/archives")
@CrossOrigin
public class A12ArchivesController {

    private final A12ArchivesService a12ArchivesService;

    @GetMapping
    @Log("查询文件档案")
    @ApiOperation("查询文件档案")
    public ResponseEntity<Object> query(A12ArchivesCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a12ArchivesService.query(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping("/createOrUpdate")
    @Log("新增文件档案")
    @ApiOperation("新增文件档案")
    public ResponseEntity<Object> createOrUpdate(@Validated @RequestBody A12ArchivesAddListDto resources) {
        return new ResponseEntity<>(a12ArchivesService.createOrUpdate(resources), HttpStatus.CREATED);
    }

    @Log("删除文件档案")
    @ApiOperation("删除文件档案")
    @PostMapping("/delete")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        a12ArchivesService.delete(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
