package com.hzwangda.aigov.modules.archive.dto;

import com.hzwangda.aigov.modules.archive.entity.A12Archives;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/14 上午10:15
 **/
@Data
public class A12ArchivesAddListDto {

    @ApiModelProperty(value = "新增或修改附件集合")
    List<A12Archives> list;

    @ApiModelProperty(value = "目录id")
    private Long directoryId;
}
