package com.hzwangda.aigov.modules.form.mapper;

import com.hzwangda.aigov.modules.form.convert.IdToDeptConvert;
import com.hzwangda.aigov.modules.form.convert.IdToUserConvert;
import com.hzwangda.aigov.modules.form.domain.A23ReportData;
import com.hzwangda.aigov.modules.form.domain.A23ReportFormInstantiate;
import com.hzwangda.aigov.modules.form.dto.A23ReportFormDataDto;
import com.hzwangda.aigov.modules.form.dto.A23ReportFormInstantiateDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A23ReportDataMapperImpl implements A23ReportDataMapper {

    @Autowired
    private IdToUserConvert idToUserConvert;
    @Autowired
    private IdToDeptConvert idToDeptConvert;

    @Override
    public List<A23ReportFormDataDto> toDto(List<A23ReportData> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A23ReportFormDataDto> list = new ArrayList<A23ReportFormDataDto>( entityList.size() );
        for ( A23ReportData a23ReportData : entityList ) {
            list.add( toDto( a23ReportData ) );
        }

        return list;
    }

    @Override
    public List<A23ReportData> toEntity(List<A23ReportFormDataDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A23ReportData> list = new ArrayList<A23ReportData>( dtoList.size() );
        for ( A23ReportFormDataDto a23ReportFormDataDto : dtoList ) {
            list.add( toEntity( a23ReportFormDataDto ) );
        }

        return list;
    }

    @Override
    public A23ReportFormDataDto toDto(A23ReportData entity) {
        if ( entity == null ) {
            return null;
        }

        A23ReportFormDataDto a23ReportFormDataDto = new A23ReportFormDataDto();

        a23ReportFormDataDto.setDept( idToDeptConvert.convert( entity.getDeptId() ) );
        a23ReportFormDataDto.setCreate( idToUserConvert.convert( entity.getCreateBy() ) );
        a23ReportFormDataDto.setCreateBy( entity.getCreateBy() );
        a23ReportFormDataDto.setCreateTime( entity.getCreateTime() );
        a23ReportFormDataDto.setUpdateTime( entity.getUpdateTime() );
        a23ReportFormDataDto.setUpdatedBy( entity.getUpdatedBy() );
        a23ReportFormDataDto.setId( entity.getId() );
        a23ReportFormDataDto.setInstantiate( a23ReportFormInstantiateToA23ReportFormInstantiateDto( entity.getInstantiate() ) );

        return a23ReportFormDataDto;
    }

    @Override
    public A23ReportData toEntity(A23ReportFormDataDto dto) {
        if ( dto == null ) {
            return null;
        }

        A23ReportData a23ReportData = new A23ReportData();

        a23ReportData.setDeptId( idToDeptConvert.convert( dto.getDept() ) );
        a23ReportData.setCreateBy( idToUserConvert.convert( dto.getCreate() ) );
        a23ReportData.setCreateTime( dto.getCreateTime() );
        a23ReportData.setUpdateTime( dto.getUpdateTime() );
        a23ReportData.setUpdatedBy( dto.getUpdatedBy() );
        a23ReportData.setId( dto.getId() );
        a23ReportData.setInstantiate( a23ReportFormInstantiateDtoToA23ReportFormInstantiate( dto.getInstantiate() ) );

        return a23ReportData;
    }

    protected A23ReportFormInstantiateDto a23ReportFormInstantiateToA23ReportFormInstantiateDto(A23ReportFormInstantiate a23ReportFormInstantiate) {
        if ( a23ReportFormInstantiate == null ) {
            return null;
        }

        A23ReportFormInstantiateDto a23ReportFormInstantiateDto = new A23ReportFormInstantiateDto();

        a23ReportFormInstantiateDto.setBizType( a23ReportFormInstantiate.getBizType() );
        a23ReportFormInstantiateDto.setEnable( a23ReportFormInstantiate.getEnable() );
        a23ReportFormInstantiateDto.setEndTime( a23ReportFormInstantiate.getEndTime() );
        a23ReportFormInstantiateDto.setForm( a23ReportFormInstantiate.getForm() );
        a23ReportFormInstantiateDto.setFormJson( a23ReportFormInstantiate.getFormJson() );
        a23ReportFormInstantiateDto.setId( a23ReportFormInstantiate.getId() );
        a23ReportFormInstantiateDto.setReportType( a23ReportFormInstantiate.getReportType() );
        a23ReportFormInstantiateDto.setStartTime( a23ReportFormInstantiate.getStartTime() );
        a23ReportFormInstantiateDto.setTitle( a23ReportFormInstantiate.getTitle() );

        return a23ReportFormInstantiateDto;
    }

    protected A23ReportFormInstantiate a23ReportFormInstantiateDtoToA23ReportFormInstantiate(A23ReportFormInstantiateDto a23ReportFormInstantiateDto) {
        if ( a23ReportFormInstantiateDto == null ) {
            return null;
        }

        A23ReportFormInstantiate a23ReportFormInstantiate = new A23ReportFormInstantiate();

        a23ReportFormInstantiate.setBizType( a23ReportFormInstantiateDto.getBizType() );
        a23ReportFormInstantiate.setEnable( a23ReportFormInstantiateDto.getEnable() );
        a23ReportFormInstantiate.setEndTime( a23ReportFormInstantiateDto.getEndTime() );
        a23ReportFormInstantiate.setForm( a23ReportFormInstantiateDto.getForm() );
        a23ReportFormInstantiate.setFormJson( a23ReportFormInstantiateDto.getFormJson() );
        a23ReportFormInstantiate.setId( a23ReportFormInstantiateDto.getId() );
        a23ReportFormInstantiate.setReportType( a23ReportFormInstantiateDto.getReportType() );
        a23ReportFormInstantiate.setStartTime( a23ReportFormInstantiateDto.getStartTime() );
        a23ReportFormInstantiate.setTitle( a23ReportFormInstantiateDto.getTitle() );

        return a23ReportFormInstantiate;
    }
}
