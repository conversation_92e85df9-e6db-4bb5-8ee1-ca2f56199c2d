package com.hzwangda.aigov.modules.taskmanagement.domain.mapstruct;

import com.hzwangda.aigov.modules.taskmanagement.domain.dto.TmPolicyDto;
import com.hzwangda.aigov.modules.taskmanagement.domain.entity.TmPolicy;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class TmPolicyMapperImpl implements TmPolicyMapper {

    @Override
    public TmPolicyDto toDto(TmPolicy entity) {
        if ( entity == null ) {
            return null;
        }

        TmPolicyDto tmPolicyDto = new TmPolicyDto();

        tmPolicyDto.setContent( entity.getContent() );
        tmPolicyDto.setContentType( entity.getContentType() );
        tmPolicyDto.setContentUrl( entity.getContentUrl() );
        tmPolicyDto.setCreateBy( entity.getCreateBy() );
        tmPolicyDto.setCreateTime( entity.getCreateTime() );
        tmPolicyDto.setImplementationDate( entity.getImplementationDate() );
        tmPolicyDto.setInterpretation( entity.getInterpretation() );
        tmPolicyDto.setInterpretationType( entity.getInterpretationType() );
        tmPolicyDto.setInterpretationUrl( entity.getInterpretationUrl() );
        tmPolicyDto.setPolicyId( entity.getPolicyId() );
        tmPolicyDto.setPolicyLevel( entity.getPolicyLevel() );
        tmPolicyDto.setPolicyLink( entity.getPolicyLink() );
        tmPolicyDto.setPolicyType( entity.getPolicyType() );
        tmPolicyDto.setPublishDate( entity.getPublishDate() );
        tmPolicyDto.setPublishDept( entity.getPublishDept() );
        tmPolicyDto.setReferenceNumber( entity.getReferenceNumber() );
        tmPolicyDto.setTitle( entity.getTitle() );
        tmPolicyDto.setUpdateTime( entity.getUpdateTime() );

        return tmPolicyDto;
    }

    @Override
    public List<TmPolicyDto> toDto(List<TmPolicy> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmPolicyDto> list = new ArrayList<TmPolicyDto>( entityList.size() );
        for ( TmPolicy tmPolicy : entityList ) {
            list.add( toDto( tmPolicy ) );
        }

        return list;
    }

    @Override
    public TmPolicy toEntity(TmPolicyDto dto) {
        if ( dto == null ) {
            return null;
        }

        TmPolicy tmPolicy = new TmPolicy();

        tmPolicy.setCreateBy( dto.getCreateBy() );
        tmPolicy.setCreateTime( dto.getCreateTime() );
        tmPolicy.setUpdateTime( dto.getUpdateTime() );
        tmPolicy.setContent( dto.getContent() );
        tmPolicy.setContentType( dto.getContentType() );
        tmPolicy.setContentUrl( dto.getContentUrl() );
        tmPolicy.setImplementationDate( dto.getImplementationDate() );
        tmPolicy.setInterpretation( dto.getInterpretation() );
        tmPolicy.setInterpretationType( dto.getInterpretationType() );
        tmPolicy.setInterpretationUrl( dto.getInterpretationUrl() );
        tmPolicy.setPolicyId( dto.getPolicyId() );
        tmPolicy.setPolicyLevel( dto.getPolicyLevel() );
        tmPolicy.setPolicyLink( dto.getPolicyLink() );
        tmPolicy.setPolicyType( dto.getPolicyType() );
        tmPolicy.setPublishDate( dto.getPublishDate() );
        tmPolicy.setPublishDept( dto.getPublishDept() );
        tmPolicy.setReferenceNumber( dto.getReferenceNumber() );
        tmPolicy.setTitle( dto.getTitle() );

        return tmPolicy;
    }

    @Override
    public List<TmPolicy> toEntity(List<TmPolicyDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TmPolicy> list = new ArrayList<TmPolicy>( dtoList.size() );
        for ( TmPolicyDto tmPolicyDto : dtoList ) {
            list.add( toEntity( tmPolicyDto ) );
        }

        return list;
    }
}
