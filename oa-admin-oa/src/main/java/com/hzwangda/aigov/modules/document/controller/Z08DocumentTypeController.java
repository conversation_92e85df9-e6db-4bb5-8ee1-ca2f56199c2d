package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.dto.MaxWhCriteria;
import com.hzwangda.aigov.modules.document.dto.Z08DocumentTypeQueryCriteria;
import com.hzwangda.aigov.modules.document.entity.Z08DocumentType;
import com.hzwangda.aigov.modules.document.service.Z08DocumentTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: zhangzhanlong
 * @date: 2022/10/26 9:35
 * @description:
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "文种维护")
@RequestMapping("/api/aigov/documenttype")
@CrossOrigin
public class Z08DocumentTypeController {
    private final Z08DocumentTypeService documentTypeService;

    @ApiOperation("文种新增")
    @PostMapping(value = "/saveDocumentType")
    public ResponseEntity<Object> saveDocumentType(@RequestBody Z08DocumentType documentType) {
        return new ResponseEntity<>(documentTypeService.saveDocumentType(documentType), HttpStatus.OK);
    }

    @ApiOperation("分页查询文种")
    @GetMapping(value = "/getDocumentTypePage")
    public ResponseEntity<Object> getDocumentTypePage(Z08DocumentTypeQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(documentTypeService.getDocumentTypePage(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("查询文种集合")
    @GetMapping(value = "/getDocumentTypeList")
    public ResponseEntity<Object> getDocumentTypeList(Z08DocumentTypeQueryCriteria criteria) {
        return new ResponseEntity<>(documentTypeService.getDocumentTypeList(criteria), HttpStatus.OK);
    }

    @ApiOperation("根据id查询文种")
    @GetMapping(value = "/getOneById")
    public ResponseEntity<Object> getOneById(@RequestParam("id") Long id) {
        return new ResponseEntity<>(documentTypeService.getOneById(id), HttpStatus.OK);
    }

    @ApiOperation("根据id删除文种")
    @PostMapping(value = "/deleteById")
    public ResponseEntity<Object> deleteById(@RequestParam("id") Long id) {
        documentTypeService.deleteById(id);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("根据代字查询")
    @GetMapping(value = "/getDzList")
    public ResponseEntity<Object> getDzList(String dz) {
        return new ResponseEntity<>(documentTypeService.getDzList(dz), HttpStatus.OK);
    }

    @ApiOperation("根据代字和应用查询")
    @GetMapping(value = "/getByDzAndAppId")
    public ResponseEntity<Z08DocumentType> getByDzAndAppId(String dz, Long appId) {
        return new ResponseEntity<>(documentTypeService.getByDzAndAppId(dz, appId), HttpStatus.OK);
    }

    @ApiOperation("根据应用id查询代字")
    @GetMapping(value = "/getByAppId")
    public ResponseEntity<List<Z08DocumentType>> getByAppId(Long appId) {
        return new ResponseEntity<>(documentTypeService.getByAppId(appId), HttpStatus.OK);
    }

    @ApiOperation("根据流程实例id和单位查询代字")
    @GetMapping(value = "/getByProcInstIdAndDept")
    public ResponseEntity<List<String>> getByProcInstIdAndDept(@RequestParam("procInstId") String procInstId) {
        return new ResponseEntity<>(documentTypeService.getByProcInstIdAndDept(procInstId), HttpStatus.OK);
    }

    @ApiOperation("更新最大文号")
    @PostMapping(value = "/updateMaxWh")
    public ResponseEntity updateMaxWh(@RequestBody @Valid MaxWhCriteria maxWhCriteria) {
        return ResponseEntity.ok(documentTypeService.updateMaxWh(maxWhCriteria.getDz(), maxWhCriteria.getNh(), maxWhCriteria.getWh(), maxWhCriteria.getBelongToDept()));
    }

}
