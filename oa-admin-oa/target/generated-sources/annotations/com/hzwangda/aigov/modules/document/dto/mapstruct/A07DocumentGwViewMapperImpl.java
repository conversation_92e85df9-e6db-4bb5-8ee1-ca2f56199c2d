package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07DocumentGwViewDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwView;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A07DocumentGwViewMapperImpl implements A07DocumentGwViewMapper {

    @Override
    public List<A07DocumentGwViewDto> toDto(List<A07DocumentGwView> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A07DocumentGwViewDto> list = new ArrayList<A07DocumentGwViewDto>( entityList.size() );
        for ( A07DocumentGwView a07DocumentGwView : entityList ) {
            list.add( toDto( a07DocumentGwView ) );
        }

        return list;
    }

    @Override
    public A07DocumentGwView toEntity(A07DocumentGwViewDto dto) {
        if ( dto == null ) {
            return null;
        }

        A07DocumentGwView a07DocumentGwView = new A07DocumentGwView();

        a07DocumentGwView.setBpmInstanceId( dto.getBpmInstanceId() );
        a07DocumentGwView.setBpmStatus( dto.getBpmStatus() );
        a07DocumentGwView.setCreateTime( dto.getCreateTime() );
        a07DocumentGwView.setBt( dto.getBt() );
        a07DocumentGwView.setCjr( dto.getCjr() );
        a07DocumentGwView.setId( dto.getId() );
        a07DocumentGwView.setModuleType( dto.getModuleType() );

        return a07DocumentGwView;
    }

    @Override
    public List<A07DocumentGwView> toEntity(List<A07DocumentGwViewDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A07DocumentGwView> list = new ArrayList<A07DocumentGwView>( dtoList.size() );
        for ( A07DocumentGwViewDto a07DocumentGwViewDto : dtoList ) {
            list.add( toEntity( a07DocumentGwViewDto ) );
        }

        return list;
    }

    @Override
    public A07DocumentGwViewDto toDto(A07DocumentGwView entity) {
        if ( entity == null ) {
            return null;
        }

        A07DocumentGwViewDto a07DocumentGwViewDto = new A07DocumentGwViewDto();

        a07DocumentGwViewDto.setBpmInstanceId( entity.getBpmInstanceId() );
        a07DocumentGwViewDto.setBpmStatus( entity.getBpmStatus() );
        a07DocumentGwViewDto.setBt( entity.getBt() );
        a07DocumentGwViewDto.setCjr( entity.getCjr() );
        a07DocumentGwViewDto.setCreateTime( entity.getCreateTime() );
        a07DocumentGwViewDto.setId( entity.getId() );
        a07DocumentGwViewDto.setModuleType( entity.getModuleType() );

        splitTime( entity, a07DocumentGwViewDto );

        return a07DocumentGwViewDto;
    }
}
