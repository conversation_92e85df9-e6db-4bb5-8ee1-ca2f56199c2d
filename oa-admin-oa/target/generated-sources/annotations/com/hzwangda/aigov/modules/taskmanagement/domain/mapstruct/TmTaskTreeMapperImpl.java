package com.hzwangda.aigov.modules.taskmanagement.domain.mapstruct;

import com.hzwangda.aigov.modules.taskmanagement.domain.dto.TmTaskTreeDto;
import com.hzwangda.aigov.modules.taskmanagement.domain.entity.TmTaskTree;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class TmTaskTreeMapperImpl implements TmTaskTreeMapper {

    @Override
    public TmTaskTreeDto toDto(TmTaskTree entity) {
        if ( entity == null ) {
            return null;
        }

        TmTaskTreeDto tmTaskTreeDto = new TmTaskTreeDto();

        tmTaskTreeDto.setId( entity.getId() );
        tmTaskTreeDto.setName( entity.getName() );
        tmTaskTreeDto.setPid( entity.getPid() );
        tmTaskTreeDto.setType( entity.getType() );

        return tmTaskTreeDto;
    }

    @Override
    public List<TmTaskTreeDto> toDto(List<TmTaskTree> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmTaskTreeDto> list = new ArrayList<TmTaskTreeDto>( entityList.size() );
        for ( TmTaskTree tmTaskTree : entityList ) {
            list.add( toDto( tmTaskTree ) );
        }

        return list;
    }

    @Override
    public TmTaskTree toEntity(TmTaskTreeDto dto) {
        if ( dto == null ) {
            return null;
        }

        TmTaskTree tmTaskTree = new TmTaskTree();

        tmTaskTree.setId( dto.getId() );
        tmTaskTree.setName( dto.getName() );
        tmTaskTree.setPid( dto.getPid() );
        tmTaskTree.setType( dto.getType() );

        return tmTaskTree;
    }

    @Override
    public List<TmTaskTree> toEntity(List<TmTaskTreeDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TmTaskTree> list = new ArrayList<TmTaskTree>( dtoList.size() );
        for ( TmTaskTreeDto tmTaskTreeDto : dtoList ) {
            list.add( toEntity( tmTaskTreeDto ) );
        }

        return list;
    }
}
