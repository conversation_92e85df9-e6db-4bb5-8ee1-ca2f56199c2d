package com.hzwangda.aigov.modules.collaboration.controller;

import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationAssignee;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationHandleLog;
import com.hzwangda.aigov.modules.collaboration.service.A10CollaborationHandleLogService;
import com.wangda.oa.annotation.Log;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: oa-mgt-server
 * @description: 工作协同对外接口
 * @author: liux
 * @create: 2021-08-12 18:47
 */
@Api(tags = "工作协同")
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/api/aigov/collaboration/handleLog")
public class A10CollaborationHandleLogController {
    @Resource
    private A10CollaborationHandleLogService collaborationHandleLogService;

    @PostMapping("/handle")
    @Log("工作协同-发送、签收、转交、评论")
    @ApiOperation("工作协同-发送、签收、转交、评论")
    public ResponseEntity<Long> handle(@Validated @RequestBody A10CollaborationHandleLog log) {
        return new ResponseEntity<Long>(collaborationHandleLogService.doHandle(log), HttpStatus.OK);
    }

    @Log("分页查询-所有工作协同操作记录")
    @ApiOperation("分页查询-所有工作协同操作记录")
    @GetMapping(value = "/list")
    public ResponseEntity<Page<A10CollaborationHandleLog>> list(Long infoId, Pageable pageable) {
        return new ResponseEntity(collaborationHandleLogService.queryHandleLog(infoId, pageable), HttpStatus.OK);
    }

    @ApiOperation("查询未签收")
    @GetMapping("queryUnsignedAssignees")
    public ResponseEntity<List<A10CollaborationAssignee>> queryUnsignedAssignees(@RequestParam Long infoId) {
        return ResponseEntity.ok(collaborationHandleLogService.queryUnsignedAssignees(infoId));
    }

    @ApiOperation("查询签收列表")
    @GetMapping("queryAssignees")
    public ResponseEntity<Page<A10CollaborationAssignee>> queryAssignees(@RequestParam Long infoId, @RequestParam String status, @RequestParam String keyword, Pageable pageable) {
        return ResponseEntity.ok(collaborationHandleLogService.queryAssignees(infoId, status, keyword, pageable));
    }
}
