package com.hzwangda.aigov.modules.form.service.impl;

import com.hzwangda.aigov.modules.form.domain.A23ReportForm;
import com.hzwangda.aigov.modules.form.dto.A23ReportFormDto;
import com.hzwangda.aigov.modules.form.dto.ReportFormQueryCriteria;
import com.hzwangda.aigov.modules.form.mapper.ReportFormMapper;
import com.hzwangda.aigov.modules.form.repository.A23ReportFormRepository;
import com.hzwangda.aigov.modules.form.service.ReportFromService;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.ValidationUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.Set;

@RequiredArgsConstructor
@Service
public class ReportFromServiceImpl implements ReportFromService {
    private final A23ReportFormRepository reportFormRepository;
    private final ReportFormMapper mapper;

    @Override
    @Transactional(rollbackFor = Exception.class)

    public A23ReportForm save(A23ReportForm resources) {
        return reportFormRepository.save(resources);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)

    public void delete(Set<Long> ids) {
        for (Long id : ids) {
            reportFormRepository.deleteById(id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)

    public A23ReportForm update(A23ReportForm resources) {
        A23ReportForm reportForm = reportFormRepository.findById(resources.getId()).orElseGet(A23ReportForm::new);
        ValidationUtil.isNull(reportForm.getId(), "ReportFrom", "id", resources.getId());
        return reportFormRepository.save(resources);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<A23ReportFormDto> query(ReportFormQueryCriteria criteria, Pageable pageable, boolean isAdmin) {
        if (isAdmin) {
            return reportFormRepository.findAll((root, query, builder) -> QueryHelp.getPredicate(root, criteria, builder), pageable).map(mapper::toDto);

        } else {
            reportFormRepository.findAll((root, query, builder) -> {
                Predicate predicate = QueryHelp.getPredicate(root, criteria, builder);
                return builder.and(predicate, builder.equal(root.get("creatorId"), SecurityUtils.getCurrentUserId()));
            });
        }
        return null;
    }

    @Override
    @Transactional(readOnly = true)
    public A23ReportForm queryById(Long id) {
        return reportFormRepository.findById(id).orElseGet(A23ReportForm::new);

    }
}
