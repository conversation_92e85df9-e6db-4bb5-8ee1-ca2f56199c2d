package com.hzwangda.aigov.modules.document.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.bpm.repository.A07DocumentGwlzRepository;
import com.hzwangda.aigov.bpm.repository.A07DocumentgwlzUserRepository;
import com.hzwangda.aigov.docconvert.common.domain.entity.SysStorageConversion;
import com.hzwangda.aigov.docconvert.common.repository.SysStorageConversionRepository;
import com.hzwangda.aigov.docconvert.common.service.FileConversionService;
import com.hzwangda.aigov.modules.collaboration.domain.criteria.SmsInfo;
import com.hzwangda.aigov.modules.document.dto.*;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07DocExchangeFwMapper;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07DocExchangeSignMapper;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07DocExchangeSwMapper;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07DocumentGwlzMapper;
import com.hzwangda.aigov.modules.document.entity.A07DocExchangeSw;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlz;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlzUser;
import com.hzwangda.aigov.modules.document.enums.TableClassNameEnum;
import com.hzwangda.aigov.modules.document.repository.A07DocExchangeSwRepository;
import com.hzwangda.aigov.modules.document.service.A07DocExchangeService;
import com.hzwangda.aigov.modules.document.service.A07GwService;
import com.hzwangda.aigov.modules.form.domain.A23ReportData;
import com.hzwangda.aigov.modules.form.domain.A23ReportFormInstantiate;
import com.hzwangda.aigov.modules.form.repository.A23ReportDataRepository;
import com.hzwangda.aigov.modules.form.repository.A23ReportFormInstantiateRepository;
import com.hzwangda.aigov.modules.form.utils.ExportUtil;
import com.hzwangda.aigov.modules.task.base.TaskConstants;
import com.hzwangda.aigov.oa.bo.MasSendContentBO;
import com.hzwangda.aigov.oa.bo.MasUserBO;
import com.hzwangda.aigov.oa.constant.AuthorityConstant;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.hzwangda.aigov.oa.repository.WdSysOptionRepository;
import com.hzwangda.aigov.oa.service.MasBusinessService;
import com.hzwangda.aigov.oa.util.OaUtil;
import com.hzwangda.aigov.oa.util.SMSFormatUtil;
import com.hzwangda.aigov.tables.domain.IdClass;
import com.hzwangda.aigov.tables.service.TablesService;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.boot.platform.utils.PageUtil;
import com.wangda.oa.backlog.bo.BacklogDeleteBO;
import com.wangda.oa.backlog.service.WdBacklogService;
import com.wangda.oa.config.ElPermissionConfig;
import com.wangda.oa.config.FileProperties;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.extension.domain.WdSysOption;
import com.wangda.oa.modules.extension.dto.org.OrgListDto;
import com.wangda.oa.modules.security.service.dto.JwtUserDto;
import com.wangda.oa.modules.system.domain.Dept;
import com.wangda.oa.modules.system.domain.DeptUserBind;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.DeptRepository;
import com.wangda.oa.modules.system.repository.DeptUserBindRepository;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.workflow.domain.form.FormTemplate;
import com.wangda.oa.modules.workflow.dto.AlreadyUserListBO;
import com.wangda.oa.modules.workflow.dto.BacklogDto;
import com.wangda.oa.modules.workflow.dto.BacklogUserDto;
import com.wangda.oa.modules.workflow.repository.form.WdFormTemplateRepository;
import com.wangda.oa.modules.workflow.service.application.ApplicationService;
import com.wangda.oa.repository.LocalStorageRepository;
import com.wangda.oa.service.IStorageService;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.service.dto.LocalStorageDto;
import com.wangda.oa.utils.FileUtil;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Tuple;
import javax.persistence.criteria.*;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class A07DocExchangeServiceImpl implements A07DocExchangeService {

    @Resource
    private ZwddProperties zwddProperties;
    @Resource
    private A07DocumentGwlzRepository a07DocumentgwlzRepository;
    @Resource
    private A07DocExchangeFwMapper a07DocExchangeFwMapper;
    @Resource
    private A07DocExchangeSwMapper a07DocExchangeSwMapper;
    @Resource
    private A07DocumentGwlzMapper a07DocumentGwlzMapper;
    @Resource
    private A07DocumentgwlzUserRepository a07DocumentgwlzUserRepository;
    @Resource
    private A07DocExchangeSwRepository a07DocExchangeSwRepository;
    @Resource
    private A07DocExchangeSignMapper a07DocExchangeSignMapper;
    @Resource
    private WdFormTemplateRepository wdFormTemplateRepository;
    @Resource
    private MasBusinessService masBusinessService;
    @Resource
    private UserRepository userRepository;
    @Resource
    private WdSysOptionRepository wdSysOptionRepository;
    @Resource
    private DeptRepository deptRepository;
    @Resource
    private A23ReportDataRepository a23ReportDataRepository;
    @Resource
    private A23ReportFormInstantiateRepository a23ReportFormInstantiateRepository;
    @Resource
    private LocalStorageRepository localStorageRepository;
    @Resource
    private ElPermissionConfig elPermissionConfig;
    @Resource
    private WdBacklogService wdBacklogService;

    @Resource
    private FileConversionService conversionService;

    @Resource
    private TablesService tablesService;

    @Resource
    private A07GwService gwService;
    @Resource
    private ApplicationService applicationService;
    @Resource
    private FileProperties properties;

    @Resource
    private DeptUserBindRepository deptUserBindRepository;
    @PersistenceContext
    private EntityManager em;
    @Autowired
    private StorageManageService storageManageService;
    @Autowired
    private IStorageService iStorageService;
    @Autowired
    private SysStorageConversionRepository sysStorageConversionRepository;


    @Override
    public ResultJson openDoc(String docid) {
        Map result = new HashMap();
        List<FormTemplate> list = wdFormTemplateRepository.findByClassNameLike("%A07DocumentGwlz%");
        if (list.size() >= 1) {
            FormTemplate form = list.stream().filter(item -> item.getTitle().equals("公文交换表单") || item.getId() == 881450695884800L).findFirst().orElse(list.get(0));
            result.put("data", form.getFormJson());
        } else if (list.size() == 0) {
            throw new BadRequestException("公文交换表单模板不存在！");
        }

        if (StringUtils.isEmpty(docid)) {
            A07DocumentGwlz a07DocumentGwlz = new A07DocumentGwlz();
            a07DocumentgwlzRepository.save(a07DocumentGwlz);
            result.put("id", a07DocumentGwlz.getId());
        } else {


            A07DocumentGwlz a07DocumentGwlz = a07DocumentgwlzRepository.findById(Long.valueOf(docid)).get();
            if (a07DocumentGwlz != null) {
                Long instantiateId = a07DocumentGwlz.getReportFormInstantiateId();
                if (instantiateId != null) {
                    a07DocumentGwlz.setSccj(a23ReportFormInstantiateRepository.findById(instantiateId).get());
                }
//                A07DocumentGwlzDto a07DocumentGwlzDto = a07DocumentGwlzMapper.toDto(a07DocumentGwlz);
                result.put("value", a07DocumentGwlz);

             /*   //待办删除
                List<AlreadyUserListBO> alreadyUserList = new ArrayList<>();
                AlreadyUserListBO alreadyUser = new AlreadyUserListBO();
                alreadyUser.setTransactionTime(System.currentTimeMillis() / 1000);
                alreadyUser.setUserId(SecurityUtils.getBindDeptUserName());
                alreadyUserList.add(alreadyUser);

                BacklogDto backlogDto = new BacklogDto();
                backlogDto.setAppId("OA");
                backlogDto.setModuleCode("documentGwlz");
                backlogDto.setModuleName("公文交换");
                backlogDto.setLogo("gwjh");
                backlogDto.setTitle(a07DocumentGwlz.getBt());
                backlogDto.setExtensionJson("{}");
                backlogDto.setPcUrl(zwddProperties.getWebUrl() + "#/document/docExchange/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=3&id=" + a07DocumentGwlz.getId());
                //backlogDto.setPcUrl(zwddProperties.getWebUrl()+"/#/meetingMgt/meetingNote/feedBackIndex");
                backlogDto.setUrl(zwddProperties.getAppUrl() + "#/document/docExchange/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=3&id=" + a07DocumentGwlz.getId());
                int urgent = 0;
                if("加急".equals(a07DocumentGwlz.getHj())) {
                    urgent = 1;
                }
                backlogDto.setUrgent(urgent);
                backlogDto.setBizId(docid);
                backlogDto.setUserId(a07DocumentGwlz.getAddUser());
                backlogDto.setAlreadyUserList(alreadyUserList);
                Boolean bool = OaUtil.pushToBacklog(backlogDto);
                System.out.println(bool);*/
            }
        }
        return ResultJson.generateResult(result);
    }

    @Override
    public Page<A07DocExchangeListFwDto> getDocExchangeFwList(A07DocExchangeQueryCriteria criteria, Pageable pageable) {
        Specification<A07DocumentGwlz> specification = (root, criteriaQuery, criteriaBuilder) -> {
            ArrayList<Predicate> andList = new ArrayList<>();

            Predicate predicate = QueryHelp.getPredicate(root, criteria, criteriaBuilder);
            andList.add(predicate);
            if (criteria.getStatus() == 1) {
                if (Objects.nonNull(criteria.getGwzt()) && criteria.getGwzt().size() == 0) {
                    andList.add(criteriaBuilder.and(root.get("gwzt").in("1", "2", "3")));
                }
                //排序：未全部签收，已全部签收，发文日期
                Subquery<A07DocumentGwlz> subquery = criteriaQuery.subquery(A07DocumentGwlz.class);
                Root<A07DocumentGwlzUser> subRoot = subquery.from(A07DocumentGwlzUser.class);
                //必须有查询字段
                subquery.select(subRoot.get("id")).where(criteriaBuilder.equal(subRoot.get("qszt"), "0"));
                criteriaQuery.orderBy(
                        criteriaBuilder.asc(criteriaBuilder.selectCase()
                                .when(criteriaBuilder.exists(subquery), 0)
                                .otherwise(1)),
                        criteriaBuilder.desc(root.get("fwrq"))
                );

                if (!elPermissionConfig.check(AuthorityConstant.DOC_EXCHANGE_ADMIN)) {
                    andList.add(criteriaBuilder.equal(root.get("addUser"), SecurityUtils.getBindDeptUserName()));
                }
            } else {
                andList.add(criteriaBuilder.equal(root.get("gwzt"), "0"));
                criteriaQuery.orderBy(criteriaBuilder.desc(root.get("modifiedDate")));

                andList.add(criteriaBuilder.equal(root.get("addUser"), SecurityUtils.getBindDeptUserName()));
            }


            return criteriaBuilder.and(andList.toArray(new Predicate[andList.size()]));
        };

        Page<A07DocumentGwlz> all = a07DocumentgwlzRepository.findAll(specification, pageable);
        Page<A07DocExchangeListFwDto> map = all.map(a07DocumentGwlz -> {
            A07DocExchangeListFwDto a07DocExchangeListFwDto = a07DocExchangeFwMapper.toDto(a07DocumentGwlz);
            if (!"0".equals(a07DocExchangeListFwDto.getGwzt())) {
                List<A07DocumentGwlzUser> unSignedList = a07DocumentgwlzUserRepository.findByGwidAndQszt(a07DocExchangeListFwDto.getId() + "", 0);
                a07DocExchangeListFwDto.setStatus(unSignedList.size() > 0 ? "未全部签收" : "已全部签收");
                a07DocExchangeListFwDto.setNoSignRecords(unSignedList);
            }
            return a07DocExchangeListFwDto;
        });

        return map;
    }

    /**
     * 草稿和未签收已撤回的筛掉
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public Page<A07DocExchangeListSwDto> getDocExchangeSwList(A07DocExchangeQueryCriteria criteria, Pageable pageable) {
        String CurrentUsername = SecurityUtils.getBindDeptUserName();
//        if (elPermissionConfig.check(AuthorityConstant.DOC_EXCHANGE_ADMIN)) {
//            CurrentUsername = "sjyt";
//        }
        String finalCurrentUsername = CurrentUsername;
        Specification<A07DocExchangeSw> specification = (root, criteriaQuery, criteriaBuilder) -> {
            ArrayList<Predicate> andList = new ArrayList<>();
            Join<A07DocExchangeSw, A07DocumentGwlzUser> join = root.join("zsdwDepts", JoinType.LEFT);
            Predicate predicate = QueryHelp.getPredicate(root, criteria, criteriaBuilder);
            andList.add(predicate);

            if (criteria.getStatus() == 1) {
                andList.add(criteriaBuilder.and(join.get("qszt").in("1", "3")));
            }
            if (criteria.getStatus() == 0) {
                andList.add(criteriaBuilder.and(join.get("qszt").in("0")));
            }
            andList.add(
                    criteriaBuilder.and(
                            criteriaBuilder.equal(join.get("qsr"), finalCurrentUsername),
                            criteriaBuilder.or(
                                    criteriaBuilder.and(criteriaBuilder.equal(join.get("qszt"), 0), criteriaBuilder.equal(root.get("gwzt"), 1)),
                                    criteriaBuilder.notEqual(join.get("qszt"), 0)
                            )
                    )
            );
            criteriaQuery.orderBy(criteriaBuilder.desc(root.get("modifiedDate")));
            // criteriaBuilder.desc(root.get("modifiedDate"));
          /*  criteriaQuery.orderBy(criteriaBuilder.desc(join.get("qsrq")),
                    criteriaBuilder.desc(join.get("createDate")));*/


            criteriaQuery.distinct(true);
            return criteriaBuilder.and(andList.toArray(new Predicate[andList.size()]));
        };
        Page<A07DocExchangeSw> all = a07DocExchangeSwRepository.findAll(specification, pageable);

        Page<A07DocExchangeListSwDto> map = all.map(a07DocExchangeSw -> {
            A07DocExchangeListSwDto a07DocExchangeListSwDto = a07DocExchangeSwMapper.toDto(a07DocExchangeSw);
            A07DocumentGwlzUser a07DocumentGwlzUser = a07DocumentgwlzUserRepository.findFirstByGwidAndQsr(String.valueOf(a07DocExchangeSw.getId()), finalCurrentUsername);
            a07DocExchangeListSwDto.setGwid(a07DocExchangeSw.getId());
            a07DocExchangeListSwDto.setId(a07DocumentGwlzUser.getId());
            a07DocExchangeListSwDto.setQslx(a07DocumentGwlzUser.getQslx());
            if (Objects.nonNull(a07DocumentGwlzUser.getZrzt())) {
                a07DocExchangeListSwDto.setZrzt(a07DocumentGwlzUser.getZrzt() == 0 ? "未转入" : "已转入");
            }
            a07DocExchangeListSwDto.setQszt(a07DocumentGwlzUser.getQszt() == 0 ? "未签收" : a07DocumentGwlzUser.getQszt() == 1 ? "已签收" : "已退回");
            a07DocExchangeListSwDto.setLwrq(a07DocumentGwlzUser.getCreateDate());
            a07DocExchangeListSwDto.setQsrq(a07DocumentGwlzUser.getQsrq());
            a07DocExchangeListSwDto.setRevoke(a07DocExchangeSw.getGwzt().equals("2") ? 1 : 0);
            return a07DocExchangeListSwDto;
        });
        return map;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveGwlz(A07DocumentGwlz resources, Integer type) {

        if (type == 0) {
            resources.setGwzt("0");
        } else {
            resources.setGwzt("1");
        }
        if (type == 2) {
            //短信通知
            List<A07DocumentGwlzUser> zsdwDepts = resources.getZsdwDepts() == null ? new ArrayList<>() : resources.getZsdwDepts();
            if (resources.getCsdwDepts() != null) {
                zsdwDepts.addAll(resources.getCsdwDepts());
            }
            //短信发送至绑定了该单位账号的所有人
            List<MasUserBO> userBOList = new ArrayList<>();
            zsdwDepts.stream().forEach(a07DocumentGwlzUser -> {
                if (StringUtils.isNotEmpty(a07DocumentGwlzUser.getQsr())) {
                    List<DeptUserBind> list = deptUserBindRepository.findByDeptUserName(a07DocumentGwlzUser.getQsr());
                    List<MasUserBO> masUserList = list.stream().filter(deptUserBind -> {
                        return Objects.nonNull(deptUserBind.getNotifyFlag()) && deptUserBind.getNotifyFlag();
                    }).map(deptUserBind -> {
                        MasUserBO masUserBO = new MasUserBO();
                        if (Objects.nonNull(list)) {
                            User user = userRepository.findById(deptUserBind.getUserId()).orElseGet(User::new);
                            masUserBO.setId(user.getUsername());
                            masUserBO.setNickName(user.getNickName() + "[" + a07DocumentGwlzUser.getUsername() + "]");
                            if (Objects.nonNull(user.getPhone())) {
                                masUserBO.setPhone(user.getPhone());
                            }
                        }

                        return masUserBO;
                    }).collect(Collectors.toList());
                    userBOList.addAll(masUserList);
                }
            });

            MasSendContentBO bo = new MasSendContentBO();
            WdSysOption firstByKey = wdSysOptionRepository.getFirstByKey("A07Doc.SMS");
            Assert.notNull(firstByKey, "短信模板没有配置!");
            String value = firstByKey.getValue();
            Map map = new HashMap();

            map.put("username", SecurityUtils.getBindDeptName());
            map.put("bt", resources.getBt());
            String content = SMSFormatUtil.processTemplate(value, map);
            bo.setContent(content);
            bo.setUserBOList(userBOList);
            bo.setSendDate(new Date());
            bo.setTiming(0);
            masBusinessService.sendSMContent(bo, 0, 0);


        }
        if (resources.getId() != null) {
            A07DocumentGwlz a07DocumentGwlz = a07DocumentgwlzRepository.findById(resources.getId()).orElseGet(A07DocumentGwlz::new);
            if (a07DocumentGwlz.getAddUser() == null) {
                resources.setCreatorId(SecurityUtils.getBindDeptUserId());
                resources.setAddUser(SecurityUtils.getBindDeptUserName());
            }
            StorageBiz zw = resources.getZw();
            if (zw != null) {
                zw.setBizId(resources.getId().toString());
                resources.setZwId(zw.getStorageId());
            }
            List<StorageBiz> fj = resources.getFj();
            if (fj != null) {
                for (int i = 0; i < fj.size(); i++) {
                    fj.get(i).setBizId(resources.getId().toString());
                }
            }
            List<StorageBiz> sjcj = resources.getSjcj();
            if (sjcj != null) {
                for (int i = 0; i < sjcj.size(); i++) {
                    sjcj.get(i).setBizId(resources.getId().toString());
                }
            }
            a07DocumentGwlz.copy(resources);
            //附件删光bugfix
            if (Objects.isNull(resources.getFj()) || resources.getFj().size() == 0) {
                a07DocumentGwlz.getFj().clear();
            }

            resources = a07DocumentGwlz;
        } else {
            resources.setCreatorId(SecurityUtils.getBindDeptUserId());
            resources.setAddUser(SecurityUtils.getBindDeptUserName());
        }

        // resources.setModifiedId(SecurityUtils.getBindDeptUserId());
        //resources.setModifiedDate(new Date());

        StorageBiz zw = resources.getZw();
        if (zw != null) {
            resources.setZwId(zw.getStorageId());
        }
        A07DocumentGwlz save = a07DocumentgwlzRepository.save(resources);

        if (type == 1 || type == 2) {
            List<A07DocumentGwlzUser> zsdwDepts = resources.getZsdwDepts() == null ? new ArrayList<>() : resources.getZsdwDepts();
            if (resources.getCsdwDepts() != null) {
                zsdwDepts.addAll(resources.getCsdwDepts());
            }
            //推送公文交换到待办
            List<BacklogUserDto> backlogUserList = new ArrayList<>();
            for (A07DocumentGwlzUser dto : zsdwDepts) {
                BacklogUserDto backlogUser = new BacklogUserDto();
                if ("cs".equals(dto.getQslx())) {
                    backlogUser.setCc(1);
                } else {
                    backlogUser.setCc(0);
                }
                backlogUser.setUserId(dto.getQsr());
                backlogUser.setCreateDate(System.currentTimeMillis() / 1000);
                backlogUserList.add(backlogUser);
            }
            BacklogDto backlogDto = new BacklogDto();
            backlogDto.setAppId("OA");
            backlogDto.setBizId(resources.getId().toString());
            backlogDto.setModuleCode("documentGwlz");
            backlogDto.setModuleName("公文交换");
            backlogDto.setLogo("gwjh");
            backlogDto.setTitle(resources.getBt());
            backlogDto.setExtensionJson("{}");
            backlogDto.setPcUrl(zwddProperties.getWebUrl() + "#/document/docExchange/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=3&id=" + resources.getId());
            //backlogDto.setPcUrl(zwddProperties.getWebUrl()+"/#/meetingMgt/meetingNote/feedBackIndex");
            backlogDto.setUrl(zwddProperties.getAppUrl() + "#/document/docExchange/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=3&id=" + resources.getId());
            int urgent = 0;
            if ("加急".equals(resources.getHj())) {
                urgent = 1;
            }
            backlogDto.setUrgent(urgent);
            backlogDto.setUserId(resources.getAddUser());
            backlogDto.setBacklogUserList(backlogUserList);
            Boolean bool = OaUtil.pushToBacklog(backlogDto);
        }
        return save.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long editGwlz(A07DocumentGwlz resources) {
        A07DocumentGwlz gwlzNew = a07DocumentgwlzRepository.findById(resources.getId()).orElseGet(A07DocumentGwlz::new);
        BeanUtil.copyProperties(resources, gwlzNew, CopyOptions.create().setIgnoreNullValue(true));
        if (Objects.isNull(resources.getZw())) {
            gwlzNew.setZwId(null);
            gwlzNew.setZw(null);
        }
        return a07DocumentgwlzRepository.save(gwlzNew).getId();
    }

    @Override
    public A07DocumentGwlzDto getGwlzInfo(Long id) {
        A07DocumentGwlz a07DocumentGwlz = a07DocumentgwlzRepository.findById(id).get();
        if (a07DocumentGwlz != null) {
            return a07DocumentGwlzMapper.toDto(a07DocumentGwlz);
        }
        return null;
    }

    /**
     * 未签收在上，签收时间
     *
     * @param criteria
     * @return
     */
    @Override
    public Map<String, Object> getSignRecords(A07DocExchangeSignCriteria criteria, Pageable pageable) {


        Page<A07DocumentGwlzUser> page = a07DocumentgwlzUserRepository.findAll((root, criteriaQuery, criteriaBuilder) -> {
            criteriaQuery.orderBy(criteriaBuilder.asc(root.get("qszt")), criteriaBuilder.desc(root.get("qsrq")));
            return criteriaBuilder.and(QueryHelp.getPredicate(root, criteria, criteriaBuilder));
        }, pageable);

        Map<String, Object> map = PageUtil.toPage(page.map(a07DocExchangeSignMapper::toDto));

        List<A07DocumentGwlzUser> list = a07DocumentgwlzUserRepository.findAll((root, query, criteriaBuilder) -> {
            return criteriaBuilder.equal(root.get("gwid"), criteria.getGwid());
        });
        map.put("zbl", list.size());
        map.put("ytj", list.stream().filter(a07DocumentGwlzUser -> {
            return a07DocumentGwlzUser.getQszt() == TaskConstants.NUMBER_ONE;
        }).count());
        map.put("wtj", list.stream().filter(a07DocumentGwlzUser -> {
            return a07DocumentGwlzUser.getQszt() == TaskConstants.NUMBER_ZERO;
        }).count());
        map.put("ycy", list.stream().filter(a07DocumentGwlzUser -> {
            return a07DocumentGwlzUser.getCyzt() == TaskConstants.NUMBER_ONE;
        }).count());
        map.put("wcy", list.stream().filter(a07DocumentGwlzUser -> {
            return a07DocumentGwlzUser.getCyzt() == TaskConstants.NUMBER_ZERO;
        }).count());
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchDel(Long[] id) {
        a07DocumentgwlzRepository.deleteAllByIdIn(id);

        //删除待办
        for (Long bizId : id) {
//        DeleteByBizIdBo deleteByBizIdBo = DeleteByBizIdBo.builder().bizId(bizId).build();
//        OaUtil.deleteBackLog(deleteByBizIdBo);
            BacklogDeleteBO backlogDeleteBO = new BacklogDeleteBO();
            backlogDeleteBO.setBizId(bizId.toString());
            wdBacklogService.deleteBacklog(backlogDeleteBO);
        }

        return "删除成功!";
    }

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    @Transactional
    public String sign(Long id) {
        A07DocumentGwlz a07DocumentGwlz = a07DocumentgwlzRepository.findById(id).get();
        String currentUsername = SecurityUtils.getBindDeptUserName();

        List<A07DocumentGwlzUser> list = a07DocumentgwlzUserRepository.findByGwidAndQsr(String.valueOf(id), currentUsername);
        if (list != null && list.size() > 0) {
            list.stream().forEach(p -> {
                p.setQsrq(new Date());
                p.setQszt(1);
            });
            a07DocumentgwlzUserRepository.saveAll(list);

            List<AlreadyUserListBO> alreadyUserList = new ArrayList<>();
            AlreadyUserListBO alreadyUser = new AlreadyUserListBO();
            alreadyUser.setTransactionTime(System.currentTimeMillis() / 1000);
            alreadyUser.setUserId(SecurityUtils.getBindDeptUserName());
            alreadyUserList.add(alreadyUser);

            BacklogDto backlogDto = new BacklogDto();
            backlogDto.setAppId("OA");
            backlogDto.setModuleCode("documentGwlz");
            backlogDto.setModuleName("公文交换");
            backlogDto.setTitle(a07DocumentGwlz.getBt());
            backlogDto.setExtensionJson("{}");
            backlogDto.setPcUrl(zwddProperties.getWebUrl() + "#/document/docExchange/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=3&id=" + a07DocumentGwlz.getId());
            //backlogDto.setPcUrl(zwddProperties.getWebUrl()+"/#/meetingMgt/meetingNote/feedBackIndex");
            backlogDto.setUrl(zwddProperties.getAppUrl() + "#/document/docExchange/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=3&id=" + a07DocumentGwlz.getId());
            int urgent = 0;
            if ("加急".equals(a07DocumentGwlz.getHj())) {
                urgent = 1;
            }
            backlogDto.setUrgent(urgent);
            backlogDto.setBizId(id.toString());
            backlogDto.setUserId(a07DocumentGwlz.getAddUser());
            backlogDto.setAlreadyUserList(alreadyUserList);
            Boolean bool = OaUtil.pushToBacklog(backlogDto);
            entityManager.createNativeQuery("DELETE FROM wd_backlog_transactor WHERE id IN ( SELECT wbt.id FROM a07_document_gwlz_user adgu LEFT JOIN wd_backlog wb ON adgu.gwid = wb.biz_id LEFT JOIN wd_backlog_transactor wbt ON wb.id = wbt.backlog_id AND adgu.qsr = wbt.transactor_id WHERE adgu.qszt = 1 AND wbt.transactor_id IS NOT NULL )").executeUpdate();
            entityManager.flush();
            return "签收成功!";
        }
        return "找不到数据";
    }

    @Override
    public String revoke(Long id) {
        A07DocumentGwlz a07DocumentGwlz = a07DocumentgwlzRepository.findById(id).get();
        if (a07DocumentGwlz != null && a07DocumentGwlz.getGwzt().equals("1")) {
            a07DocumentGwlz.setGwzt("2");
            a07DocumentgwlzRepository.save(a07DocumentGwlz);

            //撤回时， 消除未签收单位的待办
            List<A07DocumentGwlzUser> unSignList = a07DocumentgwlzUserRepository.findByGwidAndQszt(id.toString(), 0);
            List<AlreadyUserListBO> alreadyUserList = unSignList.stream().map(a07DocumentGwlzUser -> {
                AlreadyUserListBO alreadyUser = new AlreadyUserListBO();
                alreadyUser.setTransactionTime(System.currentTimeMillis() / 1000);
                alreadyUser.setUserId(a07DocumentGwlzUser.getQsr());
                return alreadyUser;
            }).collect(Collectors.toList());

            BacklogDto backlogDto = new BacklogDto();
            backlogDto.setAppId("OA");
            backlogDto.setModuleCode("documentGwlz");
            backlogDto.setModuleName("公文交换");
            backlogDto.setTitle(a07DocumentGwlz.getBt());
            backlogDto.setExtensionJson("{}");
            backlogDto.setPcUrl(zwddProperties.getWebUrl() + "#/document/docExchange/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=3&id=" + a07DocumentGwlz.getId());
            //backlogDto.setPcUrl(zwddProperties.getWebUrl()+"/#/meetingMgt/meetingNote/feedBackIndex");
            backlogDto.setUrl(zwddProperties.getAppUrl() + "#/document/docExchange/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=3&id=" + a07DocumentGwlz.getId());
            int urgent = 0;
            if ("加急".equals(a07DocumentGwlz.getHj())) {
                urgent = 1;
            }
            backlogDto.setUrgent(urgent);
            backlogDto.setBizId(id.toString());
            backlogDto.setUserId(a07DocumentGwlz.getAddUser());
            backlogDto.setAlreadyUserList(alreadyUserList);
            Boolean bool = OaUtil.pushToBacklog(backlogDto);

            return "撤回成功!";
        }
        return null;
    }


    @Override
    public String back(Long id) {
        A07DocumentGwlz a07DocumentGwlz = a07DocumentgwlzRepository.findById(id).get();
        if (a07DocumentGwlz != null && a07DocumentGwlz.getGwzt().equals("1")) {
            a07DocumentGwlz.setGwzt("3");
            a07DocumentgwlzRepository.save(a07DocumentGwlz);

            //撤回时， 消除未签收单位的待办
            //  List<A07DocumentGwlzUser> unSignList =a07DocumentgwlzUserRepository.findByGwidAndQszt(id.toString(),0);
            List<A07DocumentGwlzUser> unSignList = a07DocumentgwlzUserRepository.findByGwidAndQsr(id.toString(), SecurityUtils.getBindDeptUserName());
            List<AlreadyUserListBO> alreadyUserList = unSignList.stream().map(a07DocumentGwlzUser -> {
                AlreadyUserListBO alreadyUser = new AlreadyUserListBO();
                alreadyUser.setTransactionTime(System.currentTimeMillis() / 1000);
                alreadyUser.setUserId(a07DocumentGwlzUser.getQsr());
                //退回更新签收状态为已退回
                a07DocumentGwlzUser.setQszt(3);
                return alreadyUser;
            }).collect(Collectors.toList());

            //退回更新签收状态为已退回
            a07DocumentgwlzUserRepository.saveAll(unSignList);

            BacklogDto backlogDto = new BacklogDto();
            backlogDto.setAppId("OA");
            backlogDto.setModuleCode("documentGwlz");
            backlogDto.setModuleName("公文交换");
            backlogDto.setTitle(a07DocumentGwlz.getBt());
            backlogDto.setExtensionJson("{}");
            backlogDto.setPcUrl(zwddProperties.getWebUrl() + "#/document/docExchange/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=3&id=" + a07DocumentGwlz.getId());
            //backlogDto.setPcUrl(zwddProperties.getWebUrl()+"/#/meetingMgt/meetingNote/feedBackIndex");
            backlogDto.setUrl(zwddProperties.getAppUrl() + "#/document/docExchange/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=3&id=" + a07DocumentGwlz.getId());
            int urgent = 0;
            if ("加急".equals(a07DocumentGwlz.getHj())) {
                urgent = 1;
            }
            backlogDto.setUrgent(urgent);
            backlogDto.setBizId(id.toString());
            backlogDto.setUserId(a07DocumentGwlz.getAddUser());
            backlogDto.setAlreadyUserList(alreadyUserList);
            Boolean bool = OaUtil.pushToBacklog(backlogDto);

            return "退回成功!";
        }
        return null;
    }

    @Override
    public List<A07DocExchangeListSwDto> getHyGwList(Integer num, String type) {
        Specification<A07DocExchangeSw> specification = (root, criteriaQuery, criteriaBuilder) -> {
            ArrayList<Predicate> andList = new ArrayList<>();
            Join<A07DocExchangeSw, A07DocumentGwlzUser> join = root.join("zsdwDepts", JoinType.LEFT);
            if (StringUtils.isNotEmpty(type)) {
                andList.add(criteriaBuilder.equal(root.get("gwzl"), type));
            }
            andList.add(
                    criteriaBuilder.and(
                            criteriaBuilder.equal(join.get("qsr"), SecurityUtils.getBindDeptUserName()),
                            criteriaBuilder.or(
                                    criteriaBuilder.and(criteriaBuilder.equal(join.get("qszt"), 0), criteriaBuilder.equal(root.get("gwzt"), 1)),
                                    criteriaBuilder.notEqual(join.get("qszt"), 0)
                            )
                    )
            );
            criteriaQuery.orderBy(
                    criteriaBuilder.desc(join.get("qsrq")),
                    criteriaBuilder.desc(join.get("createDate")));

            criteriaQuery.distinct(true);
            return criteriaBuilder.and(andList.toArray(new Predicate[andList.size()]));
        };
        List<A07DocExchangeSw> list = a07DocExchangeSwRepository.findAll(specification);
        if (num != null && num <= list.size()) {
            list = list.subList(0, num);
        }
        List<A07DocExchangeListSwDto> collect = list.stream().map(a07DocExchangeSw -> {
            A07DocExchangeListSwDto a07DocExchangeListSwDto = a07DocExchangeSwMapper.toDto(a07DocExchangeSw);
            A07DocumentGwlzUser a07DocumentGwlzUser = a07DocumentgwlzUserRepository.findFirstByGwidAndQsr(String.valueOf(a07DocExchangeSw.getId()), String.valueOf(SecurityUtils.getBindDeptUserName()));
            a07DocExchangeListSwDto.setGwid(a07DocExchangeSw.getId());
            if (a07DocumentGwlzUser != null) {
                a07DocExchangeListSwDto.setId(a07DocumentGwlzUser.getId());
                a07DocExchangeListSwDto.setQslx(a07DocumentGwlzUser.getQslx());
                a07DocExchangeListSwDto.setQszt(a07DocumentGwlzUser.getQszt() == 0 ? "未签收" : "已签收");
                a07DocExchangeListSwDto.setLwrq(a07DocumentGwlzUser.getCreateDate());
                a07DocExchangeListSwDto.setQsrq(a07DocumentGwlzUser.getQsrq());
            }
            return a07DocExchangeListSwDto;
        }).collect(Collectors.toList());
        return collect;
    }

    @Override
    public ResultJson<List<OrgListDto>> getDeptTree(Long id) {
        List<A07DocumentGwlzUser> list = a07DocumentgwlzUserRepository.findByGwid(String.valueOf(id));
        if (list.size() == 0) {
            return null;
        }
        Long reportFormInstantiateId = a07DocumentgwlzRepository.findById(id).get().getReportFormInstantiateId();
        List<A23ReportData> byInstantiateId = a23ReportDataRepository.findByInstantiateId(reportFormInstantiateId);
        if (Objects.isNull(reportFormInstantiateId)) {
            return ResultJson.generateResult(null);
        }
        Map<String, List<A23ReportData>> collect = byInstantiateId.stream().collect(Collectors.groupingBy(A23ReportData::getCreateBy));

        List<String> usernames = list.stream().map(a07DocumentGwlzUser -> a07DocumentGwlzUser.getQsr()).collect(Collectors.toList());

        List<User> userVoList = userRepository.findByUsernameIn(usernames);
        List<OrgListDto> children = userVoList.stream().map(user -> {

            OrgListDto orgListDto = new OrgListDto();
            //userid和部门id相同会触发死循环，导致栈溢出，赋予随机id
            if (user.getId().equals(user.getDept().getId())) {
                orgListDto.setId(user.getId() + 99);
            } else {
                orgListDto.setId(user.getId());
            }
            orgListDto.setUserName(user.getUsername());
            orgListDto.setNickName(user.getNickName());
            orgListDto.setPid(user.getDept().getId());
            orgListDto.setType(0);

            int num = 0;
            List<A23ReportData> a23ReportData = collect.get(user.getUsername());
            if (a23ReportData != null) {
                num = a23ReportData.size();
            }
            // 暂用sort字段来显示填报数量
            orgListDto.setSort(num);
            orgListDto.setOrder(user.getSort());
            return orgListDto;
        }).collect(Collectors.toList());
//        Long[] deptIds = userVoList.stream().map(UserVo::getDeptId).toArray(Long[]::new);
        return ResultJson.generateResult(buildTree(children));


    }

    private Set<OrgListDto> getAllNodes(List<OrgListDto> nodes, Set<OrgListDto> result) {
        Long[] pids = nodes.stream().map(OrgListDto::getPid).toArray(Long[]::new);
        List<Dept> depts = deptRepository.findAllByIdInAndPidIsNotNull(pids);
        if (depts.size() > 0) {
            List<OrgListDto> collect = depts.stream().map(dept -> {
                OrgListDto orgListDto = new OrgListDto();
                orgListDto.setId(dept.getId());
                orgListDto.setNickName(dept.getName());
                orgListDto.setPid(dept.getPid());
                orgListDto.setType(1);
                orgListDto.setOrder(dept.getDeptSort());
                return orgListDto;
            }).collect(Collectors.toList());
            result.addAll(collect);
            return getAllNodes(collect, result);
        }
        return result;
    }

    private List<OrgListDto> build(List<OrgListDto> list, Map<Optional<Long>, List<OrgListDto>> collect) {
        list.stream().forEach(orgListDto -> {
            List<OrgListDto> children = collect.get(Optional.ofNullable(orgListDto.getId()));
            if (children != null) {
                children = children.stream().sorted(Comparator.comparing(OrgListDto::getOrder)).collect(Collectors.toList());

                orgListDto.setChildren(children);
                build(children, collect);
                orgListDto.setSort(children.stream().mapToInt(OrgListDto::getSort).sum());

            }
        });
        return list;
    }

    private List<OrgListDto> buildTree(List<OrgListDto> children) {
        // 先获取所有数据
        Set<OrgListDto> allNodes = getAllNodes(children, children.stream().collect(Collectors.toSet()));
        // 构建树
        Map<Optional<Long>, List<OrgListDto>> collect = allNodes.stream().sorted(Comparator.comparing(OrgListDto::getOrder, Comparator.nullsFirst(Integer::compareTo))).collect(Collectors.groupingBy(orgListDto -> Optional.ofNullable(orgListDto.getPid())));
        List<Dept> byPidIsNull = deptRepository.findByPidIsNull();
        List<OrgListDto> result = new ArrayList<>();
        byPidIsNull.stream().forEach(dept -> {
            List<OrgListDto> orgListDtos = collect.get(Optional.ofNullable(dept.getId()));
            if (orgListDtos != null) {
                OrgListDto orgListDto = new OrgListDto();
                orgListDto.setId(dept.getId());
                orgListDto.setNickName(dept.getName());
                orgListDto.setPid(dept.getPid());
                orgListDto.setType(1);
                orgListDto.setChildren(build(orgListDtos, collect));

                result.addAll(Arrays.asList(orgListDto));
            }
        });
        return result;


    }

    @Override
    public ResultJson<Map<String, Integer>> getNum(Long infoId) {
        List<A07DocumentGwlzUser> list = a07DocumentgwlzUserRepository.findByGwid(String.valueOf(infoId));
        Map<String, Integer> map = new HashMap<>();
        //应填报单位
        map.put("should", list.size());
        Long reportFormInstantiateId = a07DocumentgwlzRepository.findById(infoId).get().getReportFormInstantiateId();
        List<A23ReportData> a23ReportDataList = a23ReportDataRepository.findByInstantiateId(reportFormInstantiateId);
        long count = a23ReportDataList.stream().map(A23ReportData::getCreateBy).distinct().count();
        //已填报单位
        map.put("reported", (int) count);
        //填报数量
        map.put("num", a23ReportDataList.size());
        return ResultJson.generateResult(map);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultJson<String> smsUrge(Set<Long> ids) {

        List<A07DocumentGwlz> list = a07DocumentgwlzRepository.findAllByIdIn(ids);
        list.stream().forEach(a07DocumentGwlz -> {
            String formUsername = "";
            String addUser = a07DocumentGwlz.getAddUser();
            if (StringUtils.isNotEmpty(addUser)) {
                User user = userRepository.findByUsername(addUser);
                formUsername = user.getNickName();
            } else {
                Optional<User> byId = userRepository.findById(a07DocumentGwlz.getCreatorId());
                if (byId.isPresent()) {
                    formUsername = byId.get().getNickName();
                }
            }
            //短信通知
            List<A07DocumentGwlzUser> zsdwDepts = a07DocumentGwlz.getZsdwDepts() == null ? new ArrayList<>() : a07DocumentGwlz.getZsdwDepts();
            if (a07DocumentGwlz.getCsdwDepts() != null) {
                zsdwDepts.addAll(a07DocumentGwlz.getCsdwDepts());
            }
            List<MasUserBO> userBOList = zsdwDepts.stream()
                    .filter(a07DocumentGwlzUser -> a07DocumentGwlzUser.getQszt() == 0)
                    .map(a07DocumentGwlzUser -> {
                        MasUserBO masUserBO = new MasUserBO();
                        masUserBO.setId(a07DocumentGwlzUser.getQsr());
                        masUserBO.setNickName(a07DocumentGwlzUser.getUsername());
                        if (StringUtils.isNotEmpty(a07DocumentGwlzUser.getQsr())) {
                            User user = userRepository.findByUsername(a07DocumentGwlzUser.getQsr());
                            if (user != null) {
                                masUserBO.setPhone(user.getPhone());
                            }
                        }
                        return masUserBO;
                    }).collect(Collectors.toList());

            MasSendContentBO bo = new MasSendContentBO();
            WdSysOption firstByKey = wdSysOptionRepository.getFirstByKey("A07Doc.SMS");
            Assert.notNull(firstByKey, "短信模板没有配置!");
            String value = firstByKey.getValue();
            Map map = new HashMap();
            map.put("username", formUsername);
            map.put("bt", a07DocumentGwlz.getBt());
            String content = SMSFormatUtil.processTemplate(value, map);
            bo.setContent(content);
            bo.setUserBOList(userBOList);
            bo.setSendDate(new Date());
            bo.setTiming(0);
            masBusinessService.sendSMContent(bo, 0, 0);
        });

        return ResultJson.generateResult("催收成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultJson<JSONObject> doTransferToDeptOA(Long id, Long appId) {
        A07DocumentGwlz a07DocumentGwlz = a07DocumentgwlzRepository.findById(id).orElseThrow(() -> new BadRequestException("数据不存在"));
        log.info("公文流转对象{{}}", a07DocumentGwlz);
        IdClass idClass = new IdClass();
        idClass.setTargetClassName(TableClassNameEnum.GWJH.getValue());
        idClass.setAppId(appId);
        Object o = tablesService.mapToSourceClass(idClass, a07DocumentGwlz);

       /* StorageBiz zw = a07DocumentGwlz.getZw();
        //若有正文签章文件则取其签章文件
        if(!Objects.isNull(zw)) {
            StorageConversionDto storageConversionDto = conversionService.queryFileConversionInfo(Long.parseLong(zw.getStorageId()));
            Map<String, StorageConversionInfoDto> map = storageConversionDto.getConversionStorage();
            if(!Objects.isNull(map)) {
                StorageConversionInfoDto signFile = map.get("sign");
                if(!Objects.isNull(signFile)) {
                    zw = new StorageBiz();
                    zw.setStorageId(String.valueOf(signFile.getId()));
                }
            }

        }

        List<StorageBiz> fj = a07DocumentGwlz.getFj();
        String fwdw = a07DocumentGwlz.getFwdw();
        String bt = a07DocumentGwlz.getBt();
        ReferenceNumber gwwh = a07DocumentGwlz.getGwwh();

        JSONObject result = new JSONObject();
        //标题
        result.put("bt", bt);
        //来文单位
        result.put("lwdw", fwdw);
        //来文编号
        if(gwwh != null) {
            JSONObject bh = new JSONObject();
            bh.put("dz", gwwh.getDz());
            bh.put("nh", gwwh.getNh());
            bh.put("xh", gwwh.getXh());
            result.put("lwbh", bh);
        }
        //附件
        List<JSONObject> storageIds = new ArrayList<>();
        if(zw != null) {
            createStorage(storageIds, zw);
        }
        fj.stream().forEach(storageBiz -> {
            createStorage(storageIds, storageBiz);
        });
        result.put("fj", storageIds);*/

        return ResultJson.generateResult(o);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultJson<JSONObject> markTransferToDeptOA(Long id) {
        A07DocumentGwlzUser a07DocumentGwlzUser = a07DocumentgwlzUserRepository.findFirstByGwidAndQsr(String.valueOf(id), SecurityUtils.getBindDeptUserName());
        a07DocumentGwlzUser.setZrzt(TaskConstants.NUMBER_ONE);
        return ResultJson.generateResult(a07DocumentgwlzUserRepository.save(a07DocumentGwlzUser));
    }

    private void createStorage(List<JSONObject> storageIds, StorageBiz storageBiz) {
        LocalStorage localStorage = localStorageRepository.findById(new Long(storageBiz.getStorageId())).get();
        if (localStorage != null) {
            LocalStorage storage = new LocalStorage();
            storage.copy(localStorage);
            storage.setId(null);
            JSONObject s = new JSONObject();
            s.put("storageId", localStorageRepository.save(storage).getId());
            storageIds.add(s);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long fwToGw(String procInstId) {

        Map<String, Object> appIdAndFormData = gwService.getAppIdAndFormData(procInstId);
        if (CollUtil.isEmpty(appIdAndFormData)) {
            throw new BadRequestException("表单数据为空！");
        }
        IdClass idClass = new IdClass();
        idClass.setTargetClassName(TableClassNameEnum.GWJH.getValue());
        idClass.setAppId(Long.valueOf(appIdAndFormData.get("appId").toString()));
        Object formData = appIdAndFormData.get("formData");
        Object o = tablesService.mapToTargetClass(idClass, formData);
        //TODO 对好了下面的再修改
        /*String sfgk = "";
        ReferenceNumber gwwh = new ReferenceNumber();
        String hj = "";
        String bt = "";
        StorageBiz zw = null;
        List<StorageBiz> fj = null;
        SysDeptUserMain zsdw = null;
        SysDeptUserMain csdw = null;
        String fwdw = "";
        String fjsm = "";
        String fwlxr = "";
        A07DocumentGwView documentGwView = a07DocumentGwViewRepository.findByBpmInstanceId(procInstId.toString());
        if("ty_documentPost".equals(documentGwView.getModuleType())) {
            A01DocumentTyFw a07DocumentFw = a01DocumentTyFwRepository.findByBpmInstanceId(procInstId.toString());
            sfgk = a07DocumentFw.getGklx();
            switch(sfgk) {
                case "公开":
                    sfgk = "主动公开";
                    break;
                case "不公开":
                    sfgk = "不予公开";
                    break;
                default:
                    sfgk = "依申请公开";
            }
          *//*  BeanUtils.copyProperties(a07DocumentFw.getGwwh(),gwwh);
            gwwh.setId(IdWorker.generateId());*//*
            gwwh = a07DocumentFw.getGwwh();
            hj = a07DocumentFw.getHj();
            bt = a07DocumentFw.getBt();
            zw = a07DocumentFw.getZw();
            zsdw = a07DocumentFw.getZsdw();
            csdw = a07DocumentFw.getCsdw();
            fwdw = a07DocumentFw.getNgdw();
            fj = a07DocumentFw.getFj();
            fjsm = a07DocumentFw.getBz();
            fwlxr = a07DocumentFw.getNgr();
        }else {
            return null;
        }
        A07DocumentGwlz a07DocumentGwlz = new A07DocumentGwlz();
        a07DocumentGwlz.setGwzt("0");
        a07DocumentGwlz.setGwzl("公文");
        a07DocumentGwlz.setSfgk(sfgk);
        a07DocumentGwlz.setGwwh(gwwh);
        if(StringUtils.isNotEmpty(hj)) {
            if("普通".equals(hj)) {
                a07DocumentGwlz.setHj("平件");
            }else {
                a07DocumentGwlz.setHj("加急");
            }
        }
    *//*    String yjQf = a07DocumentFw.getYjQf();
        if(StringUtils.isNotEmpty(yjQf)) {
            JSONArray jsonArray = JSONObject.parseArray(yjQf);
            String nickName = jsonArray.stream().map(o ->
                    ((JSONObject) o).getString("nickName")
            ).collect(Collectors.joining());
            a07DocumentGwlz.setYjQfr(nickName);
        }*//*
        a07DocumentGwlz.setBt(bt);
        //若有正文签章文件则取其签章文件
        if(!Objects.isNull(zw)) {
            StorageConversionDto storageConversionDto = conversionService.queryFileConversionInfo(Long.parseLong(zw.getStorageId()));
            Map<String, StorageConversionInfoDto> map = storageConversionDto.getConversionStorage();
            if(!Objects.isNull(map)) {
                StorageConversionInfoDto signFile = map.get("sign");
                if(!Objects.isNull(signFile)) {
                    zw = new StorageBiz();
                    zw.setStorageId(String.valueOf(signFile.getId()));
                    zw.setBizType("A07DocumentGwlz.zw");
                }
            }
            a07DocumentGwlz.setZw(zw);
            a07DocumentGwlz.setZwId(zw.getStorageId());
        }
        if(zsdw != null) {
            List<SysDeptUserBiz> data = zsdw.getData();
            List<A07DocumentGwlzUser> zs = data.stream().map(sysDeptUserBiz -> {
                A07DocumentGwlzUser user = new A07DocumentGwlzUser();
                user.setQsr(sysDeptUserBiz.getUserName());
                user.setUsername(sysDeptUserBiz.getNickName());
                user.setQszt(0);
                user.setQslx("zs");
                user.setOId(sysDeptUserBiz.getOId());
                return user;
            }).collect(Collectors.toList());
            a07DocumentGwlz.setZsdwDepts(zs);
            a07DocumentGwlz.setZsdwms(zsdw.getMs());
        }
        if(csdw != null) {
            List<SysDeptUserBiz> data = csdw.getData();
            List<A07DocumentGwlzUser> cs = data.stream().map(sysDeptUserBiz -> {
                A07DocumentGwlzUser user = new A07DocumentGwlzUser();
                user.setQsr(sysDeptUserBiz.getUserName());
                user.setUsername(sysDeptUserBiz.getNickName());
                user.setQszt(0);
                user.setQslx("cs");
                user.setOId(sysDeptUserBiz.getOId());
                return user;
            }).collect(Collectors.toList());
            a07DocumentGwlz.setCsdwDepts(cs);
            a07DocumentGwlz.setCsdwms(csdw.getMs());
        }
        a07DocumentGwlz.setFwdw(fwdw);
        if(fj != null) {
            List<StorageBiz> list = fj.stream().map(storage -> {
                LocalStorage localStorage = localStorageRepository.findById(new Long(storage.getStorageId())).get();
                LocalStorage s = new LocalStorage();
                s.copy(localStorage);
                s.setId(null);
                LocalStorage save = localStorageRepository.save(s);
                StorageBiz storageBiz = new StorageBiz();
                storageBiz.setStorageId(save.getId().toString());
                storageBiz.setBizType("A07DocumentGwlz.fj");
                return storageBiz;
            }).collect(Collectors.toList());
            a07DocumentGwlz.setFj(list);
        }
        a07DocumentGwlz.setFjsm(fjsm);
        a07DocumentGwlz.setFwlxr(fwlxr);

//        a07DocumentGwlz.setZbmtjyh();
//        a07DocumentGwlz.setLy();
//        a07DocumentGwlz.setFkdlb();
//        a07DocumentGwlz.setOaname();
//        a07DocumentGwlz.setLybs();
//        a07DocumentGwlz.setLyxtdm();
        a07DocumentGwlz.setCreatorId(SecurityUtils.getBindDeptUserId());
        a07DocumentGwlz.setAddUser(SecurityUtils.getBindDeptUserName());
        A07DocumentGwlz save = a07DocumentgwlzRepository.save(a07DocumentGwlz);
        return save.getId();*/
        return null;
    }


    @Override
    public Map getSms(Long id) {
        WdSysOption firstByKey = wdSysOptionRepository.getFirstByKey("A07Doc.SMS");
        Assert.notNull(firstByKey, "短信模板没有配置!");
        String value = firstByKey.getValue();

        A07DocumentGwlz a07DocumentGwlz = a07DocumentgwlzRepository.findById(id).get();
        Map map = new HashMap();
        map.put("username", ((JwtUserDto) SecurityUtils.getCurrentUser()).getUser().getNickName());
        map.put("bt", a07DocumentGwlz.getBt());
        String content = SMSFormatUtil.processTemplate(value, map);

        //签收人
        List<Map<String, Object>> users = new ArrayList<>();
        Map<String, Object> unsign = new HashMap<>(3);
        Map<String, Object> signed = new HashMap<>(3);
        users.add(unsign);
        users.add(signed);
        List<A07DocumentGwlzUser> zsdwDepts = a07DocumentGwlz.getZsdwDepts() != null ? a07DocumentGwlz.getZsdwDepts() : new ArrayList<>();
        List<A07DocumentGwlzUser> csdwDepts = a07DocumentGwlz.getCsdwDepts();
        if (csdwDepts != null) {
            zsdwDepts.addAll(csdwDepts);
        }
        Set<Map> collect = zsdwDepts.stream().filter(a07DocumentGwlzUser -> a07DocumentGwlzUser.getQszt() == 0)
                .map(a07DocumentGwlzUser -> {
                    Map node = new HashMap(2);
                    node.put("id", a07DocumentGwlzUser.getQsr());
                    node.put("label", a07DocumentGwlzUser.getUsername());
                    return node;
                }).collect(Collectors.toSet());

        unsign.put("id", -1);
        unsign.put("label", "未签收");
        unsign.put("children", collect);

        Set<Map> collect1 = zsdwDepts.stream().filter(a07DocumentGwlzUser -> a07DocumentGwlzUser.getQszt() == 1)
                .map(a07DocumentGwlzUser -> {
                    Map node = new HashMap(2);
                    node.put("id", a07DocumentGwlzUser.getQsr());
                    node.put("label", a07DocumentGwlzUser.getUsername());
                    return node;
                }).collect(Collectors.toSet());
        signed.put("id", -2);
        signed.put("label", "已签收");
        signed.put("children", collect1);

        Map result = new HashMap(2);
        result.put("content", content);
        result.put("users", users);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String smsUrge(SmsInfo smsInfo) {
        MasSendContentBO bo = new MasSendContentBO();
        bo.setContent(smsInfo.getInfo());
        List<String> names = smsInfo.getNames();
        List<User> users = userRepository.findByUsernameIn(names);
        List<MasUserBO> userBOList = users.stream().map(user -> {
            MasUserBO masUserBO = new MasUserBO();
            masUserBO.setId(user.getUsername());
            masUserBO.setNickName(user.getNickName());
            masUserBO.setPhone(user.getPhone());
            return masUserBO;
        }).collect(Collectors.toList());
        bo.setUserBOList(userBOList);
        bo.setSendDate(new Date());
        bo.setTiming(0);
        masBusinessService.sendSMContent(bo, 0, 0);
        return "催收成功";
    }

    @Override
    public void exportAll(Long id, HttpServletRequest request, HttpServletResponse response) throws IOException {
        Optional<A07DocumentGwlz> byId = a07DocumentgwlzRepository.findById(id);
        if (byId.isPresent()) {
            A07DocumentGwlz a07DocumentGwlz = byId.get();
            Long reportFormInstantiateId = a07DocumentGwlz.getReportFormInstantiateId();
            A23ReportFormInstantiate a23ReportFormInstantiate = a23ReportFormInstantiateRepository.findById(reportFormInstantiateId).get();
            List<A23ReportData> list = a23ReportDataRepository.findByInstantiateId(reportFormInstantiateId);

            String formJson = a23ReportFormInstantiate.getFormJson();
            JSONObject formJsonObj = JSONObject.parseObject(formJson);

            ServletOutputStream out = response.getOutputStream();
            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet();
            XSSFRow titleRow = sheet.createRow(0);

            try {
                int columnIndex = 0;

                for (int i = 0; i < list.size(); i++) {
                    A23ReportData a23ReportData = list.get(i);
                    String formData = a23ReportData.getFormData();
                    JSONObject jsonObject = JSONObject.parseObject(formData);
                    XSSFCellStyle cellStyle = ExportUtil.getBasicCellStyle(workbook);
                    titleRow.setRowStyle(cellStyle);
                    if (i == 0) {
                        XSSFCell cell = titleRow.createCell(columnIndex++);
                        cell.setCellValue("单位");
                        Set<String> titleSet = jsonObject.keySet();
                        Iterator<String> iterator = titleSet.iterator();
                        while (iterator.hasNext()) {
                            titleRow.createCell(columnIndex++).setCellValue(iterator.next());
                        }
                    }

                    XSSFRow row = sheet.createRow(i + 1);
                    for (int j = 0; j < columnIndex; j++) {
                        if (j == 0) {
                            String createBy = a23ReportData.getCreateBy();
                            String nickName = userRepository.findByUsername(createBy).getNickName();
                            row.createCell(j).setCellValue(nickName);
                        } else {
                            String key = titleRow.getCell(j).getStringCellValue();
                            row.createCell(j).setCellValue(jsonObject.getString(key));
                        }
                    }

                }

                response.setCharacterEncoding(request.getCharacterEncoding());
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(a07DocumentGwlz.getBt(), request.getCharacterEncoding()));

            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                workbook.write(out);
                out.flush();
                workbook.close();
                out.close();
            }
        }
    }

    @SneakyThrows
    @Override
    public void exportFw(A07DocExchangeQueryCriteria criteria, HttpServletRequest request, HttpServletResponse response) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Tuple> cq = cb.createTupleQuery();
        Root<A07DocumentGwlz> root = cq.from(A07DocumentGwlz.class);

        Predicate predicate = QueryHelp.getPredicate(root, criteria, cb);
        if (criteria.getStatus() == 1) {
            if (Objects.nonNull(criteria.getGwzt()) && criteria.getGwzt().size() == 0) {
                predicate = cb.and(
                        predicate,
                        root.get("gwzt").in("1", "2", "3")
                );
            }
            //排序：未全部签收，已全部签收，发文日期
            Subquery<A07DocumentGwlz> subquery = cq.subquery(A07DocumentGwlz.class);
            Root<A07DocumentGwlzUser> subRoot = subquery.from(A07DocumentGwlzUser.class);
            //必须有查询字段
            subquery.select(subRoot.get("id")).where(cb.equal(subRoot.get("qszt"), "0"));


            if (!elPermissionConfig.check(AuthorityConstant.DOC_EXCHANGE_ADMIN)) {
                predicate = cb.and(
                        predicate,
                        cb.equal(root.get("addUser"), SecurityUtils.getBindDeptUserName())
                );
            }
        } else {
            predicate = cb.and(
                    predicate,
                    cb.equal(root.get("gwzt"), "0"),
                    cb.equal(root.get("addUser"), SecurityUtils.getBindDeptUserName())
            );

        }
        cq.where(predicate);

        cq.multiselect(cb.count(root.get("id")));
        long total = em.createQuery(cq).getSingleResult().get(0, Long.class);
        int pageSize = 1000;

        cq.multiselect(
                root.get("bt").alias("bt"),
                root.get("fwrq").alias("fwrq"),
                root.get("gwzl").alias("gwzl"),
                root.get("fwdw").alias("fwdw"),
                root.get("zsdwms").alias("zsdwms"),
                root.get("csdwms").alias("csdwms")
        );
        if (criteria.getStatus() == 1) {
            cq.orderBy(cb.asc(root.get("fwrq")));
        } else {
            cq.orderBy(cb.desc(root.get("modifiedDate")));
        }
        //创建excel模板文件
        ExcelWriter writer = ExcelUtil.getWriter(true);

        for (int i = 0; i * pageSize < total; i++) {
            if (i>0) {
                writer.setSheet("表"+(i+1));
            }
            List<Map> responseList = em.createQuery(cq).setFirstResult(i * pageSize).setMaxResults(pageSize).getResultList().stream().map(tuple -> {
                Map map = new LinkedHashMap();
                map.put("标题", tuple.get("bt"));
                map.put("发文日期", DateUtil.formatDate((Date) tuple.get("fwrq")));
                map.put("文件类型", tuple.get("gwzl"));
                map.put("发文单位", tuple.get("fwdw"));
                map.put("主送单位", tuple.get("zsdwms"));
                map.put("抄送单位", tuple.get("csdwms"));
                return map;
            }).collect(Collectors.toList());

            writer.setColumnWidth(0, 40);
            writer.setColumnWidth(1, 20);
            writer.setColumnWidth(3, 20);
            writer.setColumnWidth(4, 40);
            writer.setColumnWidth(5, 40);
            writer.setRowHeight(-1, 30);
            writer.write(responseList, true);
        }

        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=Template.xlsx");
        ServletOutputStream os = response.getOutputStream();
        writer.flush(os, true);
        // 关闭writer，释放内存
        writer.close();
        //此处记得关闭输出Servlet流
        IoUtil.close(os);
    }

    @Override
    public void exportFwZwZip(A07DocExchangeQueryCriteria queryCriteria, HttpServletRequest request, HttpServletResponse response) {
     log.info("进入文件导出！！！！");
        Specification<A07DocumentGwlz> specification = (root, criteriaQuery, cb) -> {
            ArrayList<Predicate> andList = new ArrayList<>();
            Predicate predicate = QueryHelp.getPredicate(root, queryCriteria, cb);
            andList.add(predicate);
                andList.add(cb.notEqual(root.get("gwzl"), "公文"));
                String bindDeptUserName = "";
                if(StringUtils.isBlank(queryCriteria.getDeptCode())){
                    bindDeptUserName=  SecurityUtils.getBindDeptUserName();
                }else{
                    bindDeptUserName=queryCriteria.getDeptCode();
                }
                andList.add(cb.equal(root.get("addUser"),bindDeptUserName ));
            return cb.and(andList.toArray(new Predicate[andList.size()]));
        };

        List<A07DocumentGwlz> gwlzs = a07DocumentgwlzRepository.findAll(specification);
        log.info("需要导出数据量！！！！{}",gwlzs.size());
        long time = System.currentTimeMillis();
        String zipPath = properties.getPath().getPath() + File.separator + "zip" + File.separator;
        String basePath = properties.getPath().getPath() + File.separator + "zip" + File.separator + time + File.separator;
        FileUtil.mkdir(basePath);
        for(A07DocumentGwlz gwlz : gwlzs) {
            log.info("开始循环{}",gwlz.getBt());
            String subjectPath = basePath + gwlz.getBt() + File.separator;
            if(gwlz.getZw()!=null) {
                FileUtil.mkdir(subjectPath);
                log.info("有正文！！！！{}",gwlz.getBt());
                processStorageBizList(gwlz.getZw(), subjectPath);
            }
        }
        log.info("开始压缩！！！！{}",gwlzs.size());
        File zip = ZipUtil.zip(basePath, zipPath + "公文交换正文批量导出" + DateUtil.now() + ".zip");
        FileUtil.downloadFile(request, response, zip, zip.getName(), true);
    }
    /**
     * @param storageBiz:
     * @param basePath:
     * @description: 处理附件下载
     * @author: zzl
     * @date: 2024/6/19 15:59
     * @return: void
     **/
    private void processStorageBizList(StorageBiz storageBiz, String basePath) {
        List<SysStorageConversion> list = sysStorageConversionRepository.findByOriginalStorageId(Long.valueOf(storageBiz.getStorageId()));
        List<SysStorageConversion> collect = list.stream().filter(conversion -> "pdf".equals(conversion.getConversionType())).collect(Collectors.toList());
        Long storageId = Long.valueOf(storageBiz.getStorageId());
        if(!collect.isEmpty()){
            storageId= collect.get(0).getConversionStorageId();
        }
        LocalStorageDto storageDto = storageManageService.findById(storageId);
            InputStream inputStream = null;
            FileOutputStream outputStream = null;
            try {
                inputStream = iStorageService.getInputStream(storageDto.getPath());
                String originalFileName = storageDto.getName();
                String truncatedFileName;
                if(originalFileName.length() > 60) {
                    truncatedFileName = originalFileName.substring(0, 60) + "...";
                }else {
                    truncatedFileName = originalFileName;
                }
                File file = new File(basePath, truncatedFileName );
                outputStream = new FileOutputStream(file);
                IOUtils.copy(inputStream, outputStream);
            }catch(IOException e) {
                throw new RuntimeException(e);
            }finally {
                IOUtils.closeQuietly(outputStream);
                IOUtils.closeQuietly(inputStream);
            }
    }
}
