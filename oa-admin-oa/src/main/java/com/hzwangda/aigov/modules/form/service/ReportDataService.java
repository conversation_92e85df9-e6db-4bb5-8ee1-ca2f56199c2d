package com.hzwangda.aigov.modules.form.service;

import com.hzwangda.aigov.modules.form.domain.A23ReportData;
import com.hzwangda.aigov.modules.form.dto.A23ReportFormDataDto;
import com.hzwangda.aigov.modules.form.dto.ReportDataQueryCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Set;

public interface ReportDataService {

    A23ReportData findById(Long id);

    A23ReportData create(A23ReportData resources);

    void delete(Set<Long> ids);

    void deleteByInstId(String InstId);

    Page<A23ReportFormDataDto> query(ReportDataQueryCriteria criteria, Pageable pageable);

    Page<A23ReportFormDataDto> queryByMy(ReportDataQueryCriteria criteria, Pageable pageable);

    Page<A23ReportFormDataDto> queryAll(ReportDataQueryCriteria criteria, Pageable pageable);

}
