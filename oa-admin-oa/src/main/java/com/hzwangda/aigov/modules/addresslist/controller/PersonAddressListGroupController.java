package com.hzwangda.aigov.modules.addresslist.controller;

import com.hzwangda.aigov.modules.addresslist.domain.entity.PersonAddressListGroup;
import com.hzwangda.aigov.modules.addresslist.service.PersonAddressListGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author: zhangzhanlong
 * @date: 2022/8/5 13:20
 * @description: 个人通讯录分组
 */
@RestController
@RequestMapping("personAddressListGroup")
@Api(tags = "个人通讯录分组")
public class PersonAddressListGroupController {
    @Resource
    private PersonAddressListGroupService groupService;

    @ApiOperation(value = "个人通讯录分组查询全部")
    @GetMapping("/queryList")
    public ResponseEntity queryList(@RequestParam("groupName") String groupName) {
        return ResponseEntity.ok(groupService.queryList(groupName));
    }

    @ApiOperation(value = "个人通讯录分组查询单个")
    @GetMapping("/queryOne")
    public ResponseEntity queryOne(@RequestParam("id") Long id) {
        return ResponseEntity.ok(groupService.queryOne(id));
    }

    @ApiOperation(value = "个人通讯录分组新增更改")
    @PostMapping("/save")
    public ResponseEntity save(@RequestBody PersonAddressListGroup group) {
        return ResponseEntity.ok(groupService.save(group));
    }

    @ApiOperation(value = "个人通讯录分组删除")
    @PostMapping("/del")
    public ResponseEntity del(@RequestBody Long id) {
        return ResponseEntity.ok(groupService.del(id));
    }
}
