package com.hzwangda.aigov.modules.document.service;

import com.hzwangda.aigov.modules.document.dto.A07FgjycfQueryCriteria;
import com.hzwangda.aigov.modules.zjedu.info.domain.criteria.D3InfoUseLogQueryCriteria;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface A07FgjycfspService {

    List<Map<String, Integer>> getList(A07FgjycfQueryCriteria criteria) throws ParseException;

    List<Map<String, Integer>> getListzx(A07FgjycfQueryCriteria criteria) throws ParseException;

    void exportExcel(HttpServletResponse response, A07FgjycfQueryCriteria queryCriteria) throws IOException, ParseException;

    void exportExcelzx(HttpServletResponse response,A07FgjycfQueryCriteria queryCriteria) throws IOException, ParseException;

}
