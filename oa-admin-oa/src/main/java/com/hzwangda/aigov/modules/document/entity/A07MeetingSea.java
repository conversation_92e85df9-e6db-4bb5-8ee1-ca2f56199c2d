package com.hzwangda.aigov.modules.document.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 会海会议
 *
 * <AUTHOR>
 * @date 2021/7/15 上午11:18
 */
@Data
@Entity
@Table(name = "a07_meeting_sea")
public class A07MeetingSea extends BaseDomain implements Serializable {

    @Column(length = 500)
    @ApiModelProperty(value = "标题")
    private String subject;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "会议开始时间")
    private Timestamp startDate;

    @ApiModelProperty(value = "会议经费")
    private Double meetingMoney;

    @Column(length = 64)
    @ApiModelProperty(value = "几类会议")
    private String wfType;

    @ApiModelProperty(value = "参会人员")
    private Integer attendancePeople;

    @Column(length = 1000)
    @ApiModelProperty(value = "会议申报单位")
    private String mainOrg2;

    @Column(length = 1000)
    @ApiModelProperty(value = "会议时长")
    private String meetingPeriod;

    @Column(length = 50)
    @ApiModelProperty(value = "是否视频会议")
    private String isNetMeeting;

    @Column(length = 50)
    @ApiModelProperty(value = "是否已推送")
    private String isPost = "0";

    @Column(length = 1000)
    @ApiModelProperty(value = "备注说明")
    private String remark;

    @Column(length = 50)
    @ApiModelProperty(value = "审批附件的文件名")
    private String fileName;

    @Column(length = 4000)
    @ApiModelProperty(value = "附件地址")
    private String attach;

    @ApiModelProperty(value = "主送单位名称")
    @OneToMany(targetEntity = A07DocumentGwlzUser.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "gwid", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private List<A07DocumentGwlzUser> mainTo;

    @ApiModelProperty(value = "主送单位描述")
    @Column(length = 5000)
    private String mainToms;

    @ApiModelProperty(value = "主送单位JSON")
    private String MainToJson;

    @ApiModelProperty(value = "抄送单位")
    @OneToMany(targetEntity = A07DocumentGwlzUser.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "gwid", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private List<A07DocumentGwlzUser> CopyTo;

    @ApiModelProperty(value = "抄送单位JSON")
    private String CopyToJson;

    @Column(length = 200)
    @ApiModelProperty(value = "adduser")
    private String adduser;

    @ApiModelProperty(value = "addtime")
    private Date addtime;

    @Column(length = 200)
    @ApiModelProperty(value = "modifyuser")
    private String modifyuser;

    @ApiModelProperty(value = "modifytime")
    private Date modifytime;

    @Column(length = 200)
    @ApiModelProperty(value = "Prop1")
    private String prop1;

    @Column(length = 200)
    @ApiModelProperty(value = "Prop2")
    private String prop2;

    @Column(length = 200)
    @ApiModelProperty(value = "Prop3")
    private String prop3;

    @Column(length = 200)
    @ApiModelProperty(value = "Prop4")
    private String prop4;

    @Column(length = 200)
    @ApiModelProperty(value = "Prop5")
    private String prop5;

    @Column(length = 200)
    @ApiModelProperty(value = "会议发送状态(0待发,1已发,2撤回)")
    private String sendStatus;

    @Column(length = 200)
    @ApiModelProperty(value = "会议状态（未全部签收，已全部签收）")
    private String status;

    @Column(length = 100)
    @ApiModelProperty(value = "联系人")
    private String fromPerson;

    @Column(length = 200)
    @ApiModelProperty(value = "联系方式")
    private String fromPersonMobile;

    @Column(length = 200)
    @ApiModelProperty(value = "紧急程度(特急,加急,平急)")
    private String urgentLevel;

    @Column(length = 200)
    @ApiModelProperty(value = "会议归属")
    private String ascription;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A07MeetingSea.fj'")
    private List<StorageBiz> fj;

    public void copy(A07MeetingSea source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }

    public void setMainTo(List<A07DocumentGwlzUser> mainTo) {
        if (mainTo != null) {
            if (this.mainTo == null) {
                this.mainTo = new ArrayList<>();
            }
            this.mainTo.clear();
            this.mainTo.addAll(mainTo);
        }
    }

    public void setFj(List<StorageBiz> fj) {
        if (fj != null) {
            if (this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }
}
