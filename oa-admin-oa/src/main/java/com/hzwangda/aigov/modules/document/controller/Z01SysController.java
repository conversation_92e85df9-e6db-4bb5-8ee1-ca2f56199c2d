package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.service.Z01SysService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: zhang<PERSON><PERSON><PERSON>
 * @date: 2023/2/14 13:27
 * @description: 系统接口
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "系统接口")
@RequestMapping("/api/sys")
public class Z01SysController {

    private final Z01SysService sysService;

    @ApiOperation("根据输入反查符合用户")
    @GetMapping(value = "/getUserList")
    public ResponseEntity<Object> getUserList(String elStr, String username) {
        return new ResponseEntity<>(sysService.getUserList(elStr, username), HttpStatus.OK);
    }

}
