package com.hzwangda.aigov.modules.document.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import com.bstek.ureport.export.ExportConfigure;
import com.bstek.ureport.export.ExportManager;
import com.google.common.collect.Maps;
import com.hzwangda.aigov.bpm.repository.A07DocumentgwlzUserRepository;
import com.hzwangda.aigov.modules.collaboration.domain.criteria.SmsInfo;
import com.hzwangda.aigov.modules.document.dto.*;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07HhInfoAllMapper;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07HhInfoFwMapper;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07HhInfoSignMapper;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A07HhInfoSwMapper;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlzUser;
import com.hzwangda.aigov.modules.document.entity.A07HhInfoTypeTabs;
import com.hzwangda.aigov.modules.document.entity.A07MeetingSea;
import com.hzwangda.aigov.modules.document.repository.A07MeetingSeaRepository;
import com.hzwangda.aigov.modules.document.service.A07HhInfoService;
import com.hzwangda.aigov.oa.bo.MasSendContentBO;
import com.hzwangda.aigov.oa.bo.MasUserBO;
import com.hzwangda.aigov.oa.constant.AuthorityConstant;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.hzwangda.aigov.oa.repository.WdSysOptionRepository;
import com.hzwangda.aigov.oa.service.MasBusinessService;
import com.hzwangda.aigov.oa.util.OaUtil;
import com.hzwangda.aigov.oa.util.SMSFormatUtil;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.backlog.bo.BacklogDeleteBO;
import com.wangda.oa.backlog.service.WdBacklogService;
import com.wangda.oa.config.ElPermissionConfig;
import com.wangda.oa.config.FileProperties;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.extension.domain.WdSysOption;
import com.wangda.oa.modules.security.service.dto.JwtUserDto;
import com.wangda.oa.modules.system.domain.DeptUserBind;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.DeptUserBindRepository;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.workflow.domain.form.FormTemplate;
import com.wangda.oa.modules.workflow.dto.AlreadyUserListBO;
import com.wangda.oa.modules.workflow.dto.BacklogDto;
import com.wangda.oa.modules.workflow.dto.BacklogUserDto;
import com.wangda.oa.modules.workflow.repository.form.WdFormTemplateRepository;
import com.wangda.oa.service.IStorageService;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.SneakyThrows;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.*;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class A07HhInfoServiceImpl implements A07HhInfoService {

    @Resource
    private ZwddProperties zwddProperties;
    @Resource
    private A07DocumentgwlzUserRepository a07DocumentgwlzUserRepository;
    @Resource
    private WdFormTemplateRepository wdFormTemplateRepository;
    @Resource
    private MasBusinessService masBusinessService;
    @Resource
    private UserRepository userRepository;
    @Resource
    private WdSysOptionRepository wdSysOptionRepository;

    @Resource
    private ElPermissionConfig elPermissionConfig;
    @Resource
    private WdBacklogService wdBacklogService;

    @Resource
    private A07MeetingSeaRepository a07MeetingSeaRepository;
    @Resource
    private A07HhInfoFwMapper a07HhInfoFwMapper;
    @Resource
    private A07HhInfoSwMapper a07HhInfoSwMapper;
    @Resource
    private A07HhInfoSignMapper a07HhInfoSignMapper;
    @Resource
    private A07HhInfoAllMapper a07HhInfoAllMapper;
    @Resource
    private FileProperties properties;
    @Resource
    private ExportManager exportManager;
    @Resource
    private IStorageService storageService;
    @Resource
    private DeptUserBindRepository deptUserBindRepository;

    @Override
    public ResultJson openDoc(String docid) {
        Map result = new HashMap();
        List<FormTemplate> list = wdFormTemplateRepository.findByClassNameLike("%A07MeetingSea%");
        if(list.size() >= 1) {
            FormTemplate form = list.stream().filter(item -> item.getTitle().equals("会海会议详情单") || item.getId() == 1336957330898944L).findFirst().orElse(list.get(0));
            result.put("data", form.getFormJson());
        }else if(list.size() == 0) {
            throw new BadRequestException("会海会议详情单模板不存在！");
        }

        if(StringUtils.isEmpty(docid)) {

        }else {

            A07MeetingSea a07MeetingSea = a07MeetingSeaRepository.findById(Long.valueOf(docid)).get();
            if(a07MeetingSea != null) {

                result.put("value", a07MeetingSea);

                //待办删除
//                List<AlreadyUserListBO> alreadyUserList = new ArrayList<>();
//                AlreadyUserListBO alreadyUser = new AlreadyUserListBO();
//                alreadyUser.setTransactionTime(System.currentTimeMillis() / 1000);
//                alreadyUser.setUserId(SecurityUtils.getBindDeptUserName());
//                alreadyUserList.add(alreadyUser);
//
//                BacklogDto backlogDto = new BacklogDto();
//                backlogDto.setAppId("OA");
//                backlogDto.setLogo("hhhy");
//                backlogDto.setModuleCode("meetingSea");
//                backlogDto.setModuleName("会海会议");
//                //标题
//                backlogDto.setTitle(a07MeetingSea.getSubject());
//                backlogDto.setExtensionJson("{}");
//                backlogDto.setPcUrl(zwddProperties.getWebUrl() + "#/meeting/meetingSea/openDoc?sidebarHide=false&navbar=false&isAdd=false&sw=sw&from=1&hideBtn=toSign&id=" + a07MeetingSea.getId());
//                //backlogDto.setPcUrl(zwddProperties.getWebUrl()+"/#/meetingMgt/meetingNote/feedBackIndex");
//                backlogDto.setUrl(zwddProperties.getAppUrl() + "#/document/meetingSea/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=2&id=" + a07MeetingSea.getId());
//                int urgent = 0;
//                if("加急".equals(a07MeetingSea.getUrgentLevel())) {
//                    urgent = 1;
//                }else if("特急".equals(a07MeetingSea.getUrgentLevel())) {
//                    urgent = 2;
//                }
//                backlogDto.setUrgent(urgent);
//                backlogDto.setBizId(docid);
//                backlogDto.setUserId(a07MeetingSea.getAdduser());
//                backlogDto.setAlreadyUserList(alreadyUserList);
//                Boolean bool = OaUtil.pushToBacklog(backlogDto);
//                System.out.println(bool);
            }
        }
        return ResultJson.generateResult(result);
    }

    @Override
    public Page<A07HhInfoListFwDto> getHhInfoFwList(A07HhInfoQueryCriteria criteria, Pageable pageable) {
        Specification<A07MeetingSea> specification = (root, criteriaQuery, criteriaBuilder) -> {
            ArrayList<Predicate> andList = new ArrayList<>();
            Predicate predicate = QueryHelp.getPredicate(root, criteria, criteriaBuilder);
            andList.add(predicate);
            if(criteria.getStatus() == 1) {
                //已发 || 撤回
                andList.add(criteriaBuilder.and(root.get("sendStatus").in("1", "2")));

                Subquery<A07MeetingSea> subquery = criteriaQuery.subquery(A07MeetingSea.class);
                Root<A07DocumentGwlzUser> subRoot = subquery.from(A07DocumentGwlzUser.class);
                //必须有查询字段
                subquery.select(subRoot.get("id")).where(criteriaBuilder.equal(subRoot.get("qszt"), "0"));
                criteriaQuery.orderBy(
                        criteriaBuilder.asc(criteriaBuilder.selectCase()
                                .when(criteriaBuilder.exists(subquery), 0)
                                .otherwise(1)),
                        criteriaBuilder.desc(root.get("startDate"))
                );

                if(!elPermissionConfig.check(AuthorityConstant.DOC_EXCHANGE_ADMIN)) {
                    andList.add(criteriaBuilder.equal(root.get("adduser"), SecurityUtils.getBindDeptUserName()));
                }
            }else {
                andList.add(criteriaBuilder.equal(root.get("sendStatus"), "0"));
                criteriaQuery.orderBy(criteriaBuilder.desc(root.get("modifiedDate")));

                andList.add(criteriaBuilder.equal(root.get("adduser"), SecurityUtils.getBindDeptUserName()));
            }

            return criteriaBuilder.and(andList.toArray(new Predicate[0]));
        };

        Page<A07MeetingSea> all = a07MeetingSeaRepository.findAll(specification, pageable);
        Page<A07HhInfoListFwDto> map = all.map(a07MeetingSea -> {
            A07HhInfoListFwDto a07HhInfoListFwDto = a07HhInfoFwMapper.toDto(a07MeetingSea);
            if(!a07HhInfoListFwDto.getSendStatus().equals("0")) {
                List<A07DocumentGwlzUser> unSignedList = a07DocumentgwlzUserRepository.findByGwidAndQszt(a07HhInfoListFwDto.getId() + "", 0);
                a07HhInfoListFwDto.setStatus(unSignedList.size() > 0 ? "未全部签收" : "已全部签收");
                a07MeetingSea.setStatus(unSignedList.size() > 0 ? "1" : "2");
                a07MeetingSeaRepository.save(a07MeetingSea);
            }
            return a07HhInfoListFwDto;
        });
        return map;
    }

    /**
     * 草稿和未签收已撤回的筛掉
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public Page<A07HhInfoListSwDto> getDocExchangeSwList(A07HhInfoQueryCriteria criteria, Pageable pageable) {
        String CurrentUsername = SecurityUtils.getBindDeptUserName();
        Specification<A07MeetingSea> specification = (root, criteriaQuery, criteriaBuilder) -> {
            ArrayList<Predicate> andList = new ArrayList<>();
            Join<A07MeetingSea, A07DocumentGwlzUser> join = root.join("mainTo", JoinType.LEFT);
            Predicate predicate = QueryHelp.getPredicate(root, criteria, criteriaBuilder);
            andList.add(predicate);

            andList.add(
                    criteriaBuilder.and(
                            criteriaBuilder.equal(join.get("qsr"), CurrentUsername),
                            criteriaBuilder.equal(join.get("qszt"), criteria.getStatus()),
                            criteriaBuilder.or(
                                    criteriaBuilder.and(criteriaBuilder.equal(join.get("qszt"), 0), criteriaBuilder.equal(root.get("sendStatus"), "1")),
                                    criteriaBuilder.notEqual(join.get("qszt"), 0)
                            )
                    )
            );
            criteriaQuery.orderBy(criteriaBuilder.desc(join.get("qsrq")),
                    criteriaBuilder.desc(join.get("createDate")));


            // criteriaQuery.distinct(true);
            return criteriaBuilder.and(andList.toArray(new Predicate[andList.size()]));
        };
        Page<A07MeetingSea> all = a07MeetingSeaRepository.findAll(specification, pageable);

        Page<A07HhInfoListSwDto> map = all.map(a07MeetingSea -> {
            A07HhInfoListSwDto a07HhInfoListSwDto = a07HhInfoSwMapper.toDto(a07MeetingSea);
            A07DocumentGwlzUser a07DocumentGwlzUser = a07DocumentgwlzUserRepository.findFirstByGwidAndQsr(String.valueOf(a07MeetingSea.getId()), CurrentUsername);
            a07HhInfoListSwDto.setGwid(a07MeetingSea.getId());
            a07HhInfoListSwDto.setId(a07DocumentGwlzUser.getId());
            a07HhInfoListSwDto.setWfType(a07MeetingSea.getWfType());
            a07HhInfoListSwDto.setMainOrg2(a07MeetingSea.getMainOrg2());
            a07HhInfoListSwDto.setSignDate(a07DocumentGwlzUser.getQsrq());
            a07HhInfoListSwDto.setStatus(a07DocumentGwlzUser.getQszt() == 0 ? "未签收" : "已签收");
            a07HhInfoListSwDto.setStartDate(a07DocumentGwlzUser.getCreateDate());
            return a07HhInfoListSwDto;
        });
        return map;
    }

    @Override
    public Page<A07HhInfoListAllDto> getDocExchangeAllList(A07HhInfoQueryCriteria criteria, Pageable pageable) {
        String currentUsername = SecurityUtils.getBindDeptUserName();
        Specification<A07MeetingSea> specification = (root, criteriaQuery, criteriaBuilder) -> {
            ArrayList<Predicate> andList = new ArrayList<>();
            Join<A07MeetingSea, A07DocumentGwlzUser> join = root.join("mainTo", JoinType.LEFT);
            Predicate predicate = QueryHelp.getPredicate(root, criteria, criteriaBuilder);

            //只看已发送的，撤回的和待发的都不查
            andList.add(criteriaBuilder.equal(root.get("sendStatus"), "1"));
            andList.add(predicate);

            //按照会议日程开始时间
            criteriaQuery.orderBy(criteriaBuilder.desc(root.get("startDate")));

//            criteriaQuery.distinct(true);
            return criteriaBuilder.and(andList.toArray(new Predicate[0]));
        };
        Page<A07MeetingSea> all = a07MeetingSeaRepository.findAll(specification, pageable);

        Page<A07HhInfoListAllDto> map = all.map(a07MeetingSea -> {
            A07HhInfoListAllDto a07HhInfoListAllDto = a07HhInfoAllMapper.toDto(a07MeetingSea);
            A07DocumentGwlzUser a07DocumentGwlzUser = a07DocumentgwlzUserRepository.findFirstByGwidAndQsr(String.valueOf(a07MeetingSea.getId()), currentUsername);
            if(Objects.isNull(a07DocumentGwlzUser)) {
                List<A07DocumentGwlzUser> byGwid = a07DocumentgwlzUserRepository.findByGwid(a07MeetingSea.getId() + "");
                if(byGwid.size() == 1) {
                    a07DocumentGwlzUser = byGwid.get(0);
                }else if(byGwid.size() > 1) {
                    a07DocumentGwlzUser = byGwid.stream().sorted(Comparator.comparing(current -> {
                        A07DocumentGwlzUser gwlzUser = (A07DocumentGwlzUser) current;
                        return gwlzUser.getCreateDate().compareTo(a07MeetingSea.getCreateDate());
                    }).reversed()).collect(Collectors.toList()).get(0);
                }
            }
            a07HhInfoListAllDto.setGwid(a07MeetingSea.getId());
            a07HhInfoListAllDto.setId(a07DocumentGwlzUser.getId());
            a07HhInfoListAllDto.setSignDate(a07DocumentGwlzUser.getQsrq());
            a07HhInfoListAllDto.setStatus(a07DocumentGwlzUser.getQszt() == 0 ? "未签收" : "已签收");
            return a07HhInfoListAllDto;
        });
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveGwlz(A07MeetingSea resources, Integer type) {

        if(type == 0) {
            resources.setSendStatus("0");
        }else {
            resources.setSendStatus("1");
        }
        if(type == 2) {
            //短信通知
            List<A07DocumentGwlzUser> zsdwDepts = resources.getMainTo() == null ? new ArrayList<>() : resources.getMainTo();
            if(resources.getCopyTo() != null) {
                zsdwDepts.addAll(resources.getCopyTo());
            }
            //短信发送至绑定了该单位账号的所有人
            List<MasUserBO> userBOList = new ArrayList<>();
            zsdwDepts.stream().forEach(a07DocumentGwlzUser -> {
                if (StringUtils.isNotEmpty(a07DocumentGwlzUser.getQsr())) {
                    List<DeptUserBind> list = deptUserBindRepository.findByDeptUserName(a07DocumentGwlzUser.getQsr());
                    List<MasUserBO> masUserList = list.stream().filter(deptUserBind -> {return Objects.nonNull(deptUserBind.getNotifyFlag())&&deptUserBind.getNotifyFlag();})
                            .map(deptUserBind -> {
                        MasUserBO masUserBO = new MasUserBO();
                        if (Objects.nonNull(list)) {
                            User user = userRepository.findById(deptUserBind.getUserId()).orElseGet(User::new);
                            masUserBO.setId(user.getUsername());
                            masUserBO.setNickName(user.getNickName()+"["+a07DocumentGwlzUser.getUsername()+"]");
                            if (Objects.nonNull(user.getPhone())) {
                                masUserBO.setPhone(user.getPhone());
                            }
                        }

                        return masUserBO;
                    }).collect(Collectors.toList());
                    userBOList.addAll(masUserList);
                }
            });

            MasSendContentBO bo = new MasSendContentBO();
            WdSysOption firstByKey = wdSysOptionRepository.getFirstByKey("A07Doc.SMS");
            Assert.notNull(firstByKey, "短信模板没有配置!");
            String value = firstByKey.getValue();
            Map map = new HashMap();
            map.put("username", SecurityUtils.getBindDeptName());
            map.put("bt", resources.getSubject());
            String content = SMSFormatUtil.processTemplate(value, map);
            bo.setContent(content);
            bo.setUserBOList(userBOList);
            bo.setSendDate(new Date());
            bo.setTiming(0);
            masBusinessService.sendSMContent(bo, 0, 0);

        }
        if(resources.getId() != null) {
            A07MeetingSea a07MeetingSea = a07MeetingSeaRepository.findById(resources.getId()).orElseGet(A07MeetingSea::new);
            List<StorageBiz> fj = resources.getFj();
            if(fj != null) {
                for(int i = 0; i < fj.size(); i++) {
                    fj.get(i).setBizId(resources.getId().toString());
                }
            }
            a07MeetingSea.copy(resources);

            resources = a07MeetingSea;
        }else {
            resources.setCreatorId(SecurityUtils.getCurrentUserId());
            resources.setAdduser(SecurityUtils.getBindDeptUserName());
        }
        resources.setModifiedId(SecurityUtils.getCurrentUserId());
        resources.setModifiedDate(new Date());
        A07MeetingSea save = a07MeetingSeaRepository.save(resources);

        if(type == 1 || type == 2) {
            List<A07DocumentGwlzUser> zsdwDepts = resources.getMainTo() == null ? new ArrayList<>() : resources.getMainTo();
            if(resources.getCopyTo() != null) {
                zsdwDepts.addAll(resources.getCopyTo());
            }
            //推送公文交换到待办
            List<BacklogUserDto> backlogUserList = new ArrayList<>();
            for(A07DocumentGwlzUser dto : zsdwDepts) {
                BacklogUserDto backlogUser = new BacklogUserDto();
                if("cs".equals(dto.getQslx())) {
                    backlogUser.setCc(1);
                }else {
                    backlogUser.setCc(0);
                }
                backlogUser.setUserId(dto.getQsr());
                backlogUser.setCreateDate(System.currentTimeMillis() / 1000);
                backlogUserList.add(backlogUser);
            }
            BacklogDto backlogDto = new BacklogDto();
            backlogDto.setAppId("OA");
            backlogDto.setBizId(resources.getId().toString());
            backlogDto.setModuleCode("meetingSea");
            backlogDto.setModuleName("会海会议");
            backlogDto.setTitle(resources.getSubject());
            backlogDto.setExtensionJson("{}");
            backlogDto.setPcUrl(zwddProperties.getWebUrl() + "#/document/meetingSea/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=2&id=" + resources.getId());
            //backlogDto.setPcUrl(zwddProperties.getWebUrl()+"/#/meetingMgt/meetingNote/feedBackIndex");
            backlogDto.setUrl(zwddProperties.getAppUrl() + "#/document/meetingSea/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=2&id=" + resources.getId());
            int urgent = 0;
            if("加急".equals(resources.getUrgentLevel())) {
                urgent = 1;
            }else if("特急".equals(resources.getUrgentLevel())) {
                urgent = 2;
            }
            backlogDto.setUrgent(urgent);
            backlogDto.setUserId(resources.getAdduser());
            backlogDto.setBacklogUserList(backlogUserList);
            Boolean bool = OaUtil.pushToBacklog(backlogDto);
        }
        return save.getId();
    }

    @Override
    public A07MeetingSea getMeetingInfo(Long id) {
        Optional<A07MeetingSea> meetingSea = a07MeetingSeaRepository.findById(id);
        return meetingSea.orElse(null);
    }

    /**
     * 未签收在上，签收时间
     * @param criteria
     * @return
     */
    @Override
    public List<A07HhInfoSignDto> getSignRecords(A07HhInfoSignCriteria criteria) {
        Specification<A07DocumentGwlzUser> specification = (root, criteriaQuery, criteriaBuilder) -> {
            criteriaQuery.orderBy(criteriaBuilder.asc(root.get("qszt")), criteriaBuilder.desc(root.get("qsrq")));
            return criteriaBuilder.and(QueryHelp.getPredicate(root, criteria, criteriaBuilder));
        };
        List<A07DocumentGwlzUser> all = a07DocumentgwlzUserRepository.findAll(specification);
        List<A07HhInfoSignDto> list = all.stream().map(a07HhInfoSignMapper::toDto).collect(Collectors.toList());
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchDel(Long[] id) {
        a07MeetingSeaRepository.deleteAllByIdIn(id);

        //删除待办
        String bizId = id.toString();
        BacklogDeleteBO backlogDeleteBO = new BacklogDeleteBO();
        backlogDeleteBO.setBizId(bizId);
        wdBacklogService.deleteBacklog(backlogDeleteBO);

        List<String> ids = new ArrayList<>();
        for(Long l : id) {
            ids.add(l + "");
        }
        a07DocumentgwlzUserRepository.deleteByGwidIn(ids);
        return "删除成功!";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchPush(Long[] id) {
        List<A07MeetingSea> allById = a07MeetingSeaRepository.findAllById(Arrays.asList(id));

        Map<String, String> stringPathMap = genPdf("/meeting", id);

        //保存文件路径
        allById.forEach(sea -> {
            sea.setIsPost("1");
            if(StringUtils.isNotEmpty(sea.getAttach())) {
                FileUtil.del(sea.getAttach());
            }
            String path = stringPathMap.get(sea.getId() + "");
            sea.setAttach(path);
        });
        a07MeetingSeaRepository.saveAll(allById);

        return "更新成功!";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchUnPush(Long[] id) {
        List<A07MeetingSea> allById = a07MeetingSeaRepository.findAllById(Arrays.asList(id));
        allById.forEach(sea -> {
            sea.setIsPost("0");
            FileUtil.del(sea.getAttach());
            sea.setAttach(null);
        });
        a07MeetingSeaRepository.saveAll(allById);

        return "更新成功!";
    }

    private Map<String, String> genPdf(String filePath, Long[] id) {
        Map<String, String> filePathMap = new HashMap<>();
        for(Long i : id) {
            ExportConfigure exportConfigure = new ExportConfigure() {
                @SneakyThrows
                @Override
                public OutputStream getOutputStream() {
                    String path = properties.getPath().getPath() + File.separator + filePath + File.separator;
                    FileUtil.mkdir(path);
                    File pdfFile = new File(path + "会海-" + System.currentTimeMillis() + ".pdf");
                    LocalStorage localStorage = storageService.create(pdfFile.getName(), pdfFile);

                    filePathMap.put(i + "", "/api/localStorage/downloadFile/" + localStorage.getId());
                    return new FileOutputStream(pdfFile);
                }

                @Override
                public String getFile() {
                    return "file:meeting.ureport.xml";
                }

                @Override
                public Map<String, Object> getParameters() {
                    Map<String, Object> params = Maps.newHashMap();
                    params.put("id", String.valueOf(i));
                    return params;
                }
            };
            exportManager.exportPdf(exportConfigure);
        }
        return filePathMap;
    }

    @Override
    public String sign(Long id) {
        A07MeetingSea a07MeetingSea = a07MeetingSeaRepository.findById(id).get();
        String currentUsername = SecurityUtils.getBindDeptUserName();
        List<A07DocumentGwlzUser> list = a07DocumentgwlzUserRepository.findByGwidAndQsztAndQsr(String.valueOf(id), 0, currentUsername);
        if(list != null && list.size() > 0) {
            list.stream().forEach(p -> {
                p.setQsrq(new Date());
                p.setQszt(1);
            });
            a07DocumentgwlzUserRepository.saveAll(list);

            BacklogDto backlogDto = new BacklogDto();
            backlogDto.setAppId("OA");
            backlogDto.setModuleCode("meetingSea");
            backlogDto.setModuleName("会海会议");
            backlogDto.setTitle(a07MeetingSea.getSubject());
            backlogDto.setExtensionJson("{}");
            backlogDto.setPcUrl(zwddProperties.getWebUrl() + "#/document/meetingSea/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=2&id=" + a07MeetingSea.getId());
            //backlogDto.setPcUrl(zwddProperties.getWebUrl()+"/#/meetingMgt/meetingNote/feedBackIndex");
            backlogDto.setUrl(zwddProperties.getAppUrl() + "#/document/meetingSea/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=2&id=" + a07MeetingSea.getId());
            int urgent = 0;
            if("加急".equals(a07MeetingSea.getUrgentLevel())) {
                urgent = 1;
            }else if("特急".equals(a07MeetingSea.getUrgentLevel())) {
                urgent = 2;
            }
            backlogDto.setUrgent(urgent);
            backlogDto.setBizId(id.toString());
            backlogDto.setUserId(a07MeetingSea.getAdduser());
            List<AlreadyUserListBO> alreadyUserList = new ArrayList<>();
            AlreadyUserListBO alreadyUser = new AlreadyUserListBO();
            alreadyUser.setTransactionTime(System.currentTimeMillis() / 1000);
            alreadyUser.setUserId(SecurityUtils.getBindDeptUserName());
            alreadyUserList.add(alreadyUser);
            backlogDto.setAlreadyUserList(alreadyUserList);
            Boolean bool = OaUtil.pushToBacklog(backlogDto);
            return "签收成功!";
        }
        return "找不到数据";
    }

    @Override
    public String revoke(Long id) {
        A07MeetingSea a07MeetingSea = a07MeetingSeaRepository.findById(id).get();
        if(a07MeetingSea != null && a07MeetingSea.getSendStatus().equals("1")) {
            a07MeetingSea.setSendStatus("2");
            a07MeetingSeaRepository.save(a07MeetingSea);

            //撤回时， 消除未签收单位的待办
            List<A07DocumentGwlzUser> unSignList = a07DocumentgwlzUserRepository.findByGwidAndQszt(id.toString(), 0);
            List<AlreadyUserListBO> alreadyUserList = unSignList.stream().map(a07DocumentGwlzUser -> {
                AlreadyUserListBO alreadyUser = new AlreadyUserListBO();
                alreadyUser.setTransactionTime(System.currentTimeMillis() / 1000);
                alreadyUser.setUserId(a07DocumentGwlzUser.getQsr());
                return alreadyUser;
            }).collect(Collectors.toList());

            BacklogDto backlogDto = new BacklogDto();
            backlogDto.setAppId("OA");
            backlogDto.setModuleCode("meetingSea");
            backlogDto.setModuleName("会海会议");
            backlogDto.setTitle(a07MeetingSea.getSubject());
            backlogDto.setExtensionJson("{}");
            backlogDto.setPcUrl(zwddProperties.getWebUrl() + "#/document/meetingSea/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=2&id=" + a07MeetingSea.getId());
            //backlogDto.setPcUrl(zwddProperties.getWebUrl()+"/#/meetingMgt/meetingNote/feedBackIndex");
            backlogDto.setUrl(zwddProperties.getAppUrl() + "#/document/meetingSea/openDoc?sidebarHide=false&navbar=false&isAdd=false&from=2&id=" + a07MeetingSea.getId());
            int urgent = 0;
            if("加急".equals(a07MeetingSea.getUrgentLevel())) {
                urgent = 1;
            }else if("特急".equals(a07MeetingSea.getUrgentLevel())) {
                urgent = 2;
            }
            backlogDto.setUrgent(urgent);
            backlogDto.setBizId(id.toString());
            backlogDto.setUserId(a07MeetingSea.getAdduser());
            backlogDto.setAlreadyUserList(alreadyUserList);
            Boolean bool = OaUtil.pushToBacklog(backlogDto);

            return "撤回成功!";
        }
        return null;
    }

    @Override
    public List<A07HhInfoTypeTabs> getTypeTabs() {
        Long deptUserId = SecurityUtils.getBindDeptUserId();
        //1328850603497472 区政府办公室（区外办、区大数据局、区委生态文明办）
        //1328850585475072 区委办公室[区档案局、区委机要局、区委保密办、区委党史研究室]、区台办
        List<A07HhInfoTypeTabs> tabsList = new ArrayList<>();

        if(deptUserId.equals(1328850603497472L) || deptUserId.equals(1328850585475072L)) {
            tabsList.add(new A07HhInfoTypeTabs("一类会议", "一类会议"));
            tabsList.add(new A07HhInfoTypeTabs("二类会议", "二类会议"));
        }
        tabsList.add(new A07HhInfoTypeTabs("三类会议", "三类会议"));
        tabsList.add(new A07HhInfoTypeTabs("其他会议", "其他会议"));
        return tabsList;
    }

    @Override
    public Map getSms(Long id) {
        WdSysOption firstByKey = wdSysOptionRepository.getFirstByKey("A07Doc.SMS");
        Assert.notNull(firstByKey, "短信模板没有配置!");
        String value = firstByKey.getValue();

        A07MeetingSea a07MeetingSea = a07MeetingSeaRepository.findById(id).get();
        Map map = new HashMap();
        map.put("username", ((JwtUserDto) SecurityUtils.getCurrentUser()).getUser().getNickName());
        map.put("bt", a07MeetingSea.getSubject());
        String content = SMSFormatUtil.processTemplate(value, map);

        //签收人
        List<Map<String, Object>> users = new ArrayList<>();
        Map<String, Object> unsign = new HashMap<>(3);
        Map<String, Object> signed = new HashMap<>(3);
        users.add(unsign);
        users.add(signed);
        List<A07DocumentGwlzUser> zsdwDepts = a07MeetingSea.getMainTo() != null ? a07MeetingSea.getMainTo() : new ArrayList<>();
        List<A07DocumentGwlzUser> csdwDepts = a07MeetingSea.getCopyTo();
        if(csdwDepts != null) {
            zsdwDepts.addAll(csdwDepts);
        }
        Set<Map> collect = zsdwDepts.stream().filter(a07DocumentGwlzUser -> a07DocumentGwlzUser.getQszt() == 0)
                .map(a07DocumentGwlzUser -> {
                    Map node = new HashMap(2);
                    node.put("id", a07DocumentGwlzUser.getId());
                    node.put("label", a07DocumentGwlzUser.getUsername());
                    return node;
                }).collect(Collectors.toSet());

        unsign.put("id", -1);
        unsign.put("label", "未签收");
        unsign.put("children", collect);

        Set<Map> collect1 = zsdwDepts.stream().filter(a07DocumentGwlzUser -> a07DocumentGwlzUser.getQszt() == 1)
                .map(a07DocumentGwlzUser -> {
                    Map node = new HashMap(2);
                    node.put("id", a07DocumentGwlzUser.getId());
                    node.put("label", a07DocumentGwlzUser.getUsername());
                    return node;
                }).collect(Collectors.toSet());
        signed.put("id", -2);
        signed.put("label", "已签收");
        signed.put("children", collect1);

        Map result = new HashMap(2);
        result.put("content", content);
        result.put("users", users);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String smsUrge(SmsInfo smsInfo) {
        MasSendContentBO bo = new MasSendContentBO();
        bo.setContent(smsInfo.getInfo());
        List<String> names = smsInfo.getNames();
        List<User> users = userRepository.findByUsernameIn(names);
        List<MasUserBO> userBOList = users.stream().map(user -> {
            MasUserBO masUserBO = new MasUserBO();
            masUserBO.setId(user.getUsername());
            masUserBO.setNickName(user.getNickName());
            masUserBO.setPhone(user.getPhone());
            return masUserBO;
        }).collect(Collectors.toList());
        bo.setUserBOList(userBOList);
        bo.setSendDate(new Date());
        bo.setTiming(0);
        masBusinessService.sendSMContent(bo, 0, 0);
        return "催收成功";
    }

}
