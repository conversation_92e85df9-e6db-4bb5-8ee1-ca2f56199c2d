package com.hzwangda.aigov.modules.GoodsReceive.domain;


import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "goods_category")
public class GoodsCategoryDto extends BaseBpmDomain {
    @ManyToOne
    @JoinColumn(name="main_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @JSONField(serialize = false)
    private GoodsReceive goodsReceive;

    @Column
    @ApiModelProperty(value = "物品分类")
    private String wpfl;

    @Column
    @ApiModelProperty(value = "用途分类")
    private String ytfl;

    @Column
    @ApiModelProperty(value = "物品名称")
    private String mcxh;

    @ApiModelProperty(value = "领用数量")
    private Integer lysl;

    @ApiModelProperty(value = "领用天数")
    private Integer borrowDay;

    @ApiModelProperty(value = "归还状态")
    private String returnStatus;


}
