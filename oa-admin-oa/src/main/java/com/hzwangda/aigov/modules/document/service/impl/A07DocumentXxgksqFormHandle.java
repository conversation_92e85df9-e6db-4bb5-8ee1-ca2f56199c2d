package com.hzwangda.aigov.modules.document.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.document.entity.A07DocumentXxgksq;
import com.hzwangda.aigov.modules.document.repository.A07DocumentXxgksqRepository;
import com.wangda.oa.modules.workflow.dto.BacklogListDto;
import com.wangda.oa.modules.workflow.dto.MyWorkDto;
import com.wangda.oa.modules.workflow.enums.MyWorkStatusEnum;
import com.wangda.oa.modules.workflow.service.FlowFormHandle;
import com.wangda.oa.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/13
 * @description 信息公开申请表单处理
 */
@Component
@Slf4j
public class A07DocumentXxgksqFormHandle implements FlowFormHandle {

    @Autowired
    private A07DocumentXxgksqRepository a07DocumentXxgksqRepository;

    @Autowired
    private HistoryService historyService;

    @Override
    public void handleFormForMyWork(BacklogListDto myWork, MyWorkDto myWorkDto) {
        myWorkDto.setId(myWork.getId());
        myWorkDto.setProcessInstanceId(myWork.getBizId());
        myWorkDto.setTitle(myWork.getTitle());
        myWorkDto.setType(myWork.getModuleName());
        myWorkDto.setCreateTime(myWork.getCreateDate());
        myWorkDto.setPcUrl(myWork.getPcUrl());
        myWorkDto.setLogo(myWork.getLogo());
        if (Objects.nonNull(myWork.getUrgent())) {
            // 0:非急件,1:急件
            if (myWork.getUrgent().equals(1)) {
                myWorkDto.setHj("急");
            } else {
                myWorkDto.setHj("普通");
            }
        }

        A07DocumentXxgksq a07DocumentXxgksq = a07DocumentXxgksqRepository.findFirstByBpmInstanceId(myWork.getBizId());
        if (Objects.nonNull(a07DocumentXxgksq)) {
            myWorkDto.setTitle(a07DocumentXxgksq.getSxxxnrms());
            myWorkDto.setInstruction(a07DocumentXxgksq.getYjZrnb());
            myWorkDto.setHj("普通");

            List<HistoricTaskInstance> historicTaskInstanceList = historyService.createHistoricTaskInstanceQuery().processInstanceId(myWork.getBizId()).taskAssignee(SecurityUtils.getCurrentUsername()).orderByHistoricTaskInstanceStartTime().desc().list();
            HistoricTaskInstance historicTaskInstance = historicTaskInstanceList.get(0);
            myWorkDto.setCreateTime(historicTaskInstance.getCreateTime());
            if (Objects.isNull(historicTaskInstance.getEndTime())) {
                myWorkDto.setStatus(MyWorkStatusEnum.BLZ);
            } else {
                myWorkDto.setStatus(MyWorkStatusEnum.YBL);
            }
        }
    }

    @Override
    public String handleSubjectRule(JSONObject formDataObj, String subjectRule) {
        return null;
    }

    @Override
    public Object handleFormRecord(String procInstanceId, String taskDefKey, JSONObject bpmFormData) {
        // 如果表单数据有值且不需要处理，直接返回
        if (Objects.nonNull(bpmFormData)) {
            return bpmFormData;
        }
        A07DocumentXxgksq document = a07DocumentXxgksqRepository.findFirstByBpmInstanceId(procInstanceId);
        return JSONObject.toJSON(document);
    }

    @Override
    public void deleteFormRecord(String instanceId) {
        a07DocumentXxgksqRepository.deleteByBpmInstanceId(instanceId);
    }
}
