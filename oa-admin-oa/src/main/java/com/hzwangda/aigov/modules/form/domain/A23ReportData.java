package com.hzwangda.aigov.modules.form.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;

@Entity
@Data
@Table(name = "a23_report_data")
public class A23ReportData extends BaseEntity {
    @Id
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    @Lob
    @ApiModelProperty("表单数据")
    private String formData;
    @ApiModelProperty("关联业务的id")
    private String bizId;
    @ApiModelProperty("部门id")
    private String deptId;
    @ManyToOne
    @JoinColumn(name = "instantiate_id")
    private A23ReportFormInstantiate instantiate;
    @Transient
    @ApiModelProperty("表单实例id")
    private Long instId;

    @Column(
            name = "create_by",
            updatable = false
    )
    @ApiModelProperty(
            value = "创建人",
            hidden = true
    )
    private String createBy;
    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }
}
