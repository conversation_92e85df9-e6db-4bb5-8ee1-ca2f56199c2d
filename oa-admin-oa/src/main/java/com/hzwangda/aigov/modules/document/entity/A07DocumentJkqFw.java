package com.hzwangda.aigov.modules.document.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Where;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 动态信息-发文
 * <AUTHOR>
 * @date 2021/6/15 上午10:18
 */
@Data
@Entity
@Table(name = "a07_document_jkq_fw")
public class A07DocumentJkqFw extends BaseBpmDomain implements Serializable {

    @Column(name = "old_id")
    @ApiModelProperty(value = "老数据id(匹配是否存在)")
    private Long oldId;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @ApiModelProperty(value = "文号")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "wh_id", referencedColumnName = "id")
    private ReferenceNumber gwwh;

    @Column(name = "mj")
    @ApiModelProperty(value = "密级(绝密,机密,普通)")
    private String mj;

    @Column(name = "hj")
    @ApiModelProperty(value = "紧急程度(紧急,普通)")
    private String hj;

    @Column(name = "gklx")
    @ApiModelProperty(value = "公开类型(公开,不公开,依申请公开)")
    private String gklx;

    @Column(name = "wjlx")
    @ApiModelProperty(value = "文件类型(规范性文件,非规范性文件,其他)")
    private String wjlx;

    @Column(name = "wjxz")
    @ApiModelProperty(value = "文件性质(行政规范性文件,非行政规范性文件)")
    private String wjxz;

    @Column(name = "bt")
    @ApiModelProperty(value = "标题")
    private String bt;

    @Lob
    @Column(name = "yj_qf")
    @ApiModelProperty(value = "签发-json格式")
    private String yjQf;

    @Lob
    @Column(name = "yj_sy")
    @ApiModelProperty(value = "审阅-json格式")
    private String yjSy;

    @Lob
    @Column(name = "yj_sh")
    @ApiModelProperty(value = "审核-json格式")
    private String yjSh;

    @Lob
    @Column(name = "yj_hg")
    @ApiModelProperty(value = "核稿-json格式")
    private String yjHg;

    @Lob
    @Column(name = "yj_zwgkyj")
    @ApiModelProperty(value = "政务公开意见-json格式")
    private String yjZwgkyj;

    @Lob
    @Column(name = "yj_hq")
    @ApiModelProperty(value = "会签-json格式")
    private String yjHq;

    @ApiModelProperty(value = "正文")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "zw", referencedColumnName = "id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private StorageBiz zw;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A07DocumentJkqFw.fj'")
    private List<StorageBiz> fj;

    @ApiModelProperty(value = "主送单位")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "zsdw", referencedColumnName = "id")
    private SysDeptUserMain zsdw;

    @ApiModelProperty(value = "抄送单位")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "csdw", referencedColumnName = "id")
    private SysDeptUserMain csdw;

    @Column(name = "zbbm")
    @ApiModelProperty(value = "主办部门")
    private String zbbm;

    @Column(name = "ngr")
    @ApiModelProperty(value = "拟稿人")
    private String ngr;

    @Column(name = "ngdw")
    @ApiModelProperty(value = "拟稿部门")
    private String ngdw;

    @Column(name = "yffs")
    @ApiModelProperty(value = "印发份数")
    private String yffs;

    @Column(name = "ngrq")
    @ApiModelProperty(value = "拟稿日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date ngrq;

    @Column(name = "jdr")
    @ApiModelProperty(value = "校对人")
    private String jdr;

    @Column(name = "lxdh")
    @ApiModelProperty(value = "联系电话")
    private String lxdh;

    @Column(name = "dyr")
    @ApiModelProperty(value = "打印人")
    private String dyr;

    @Column(name = "hgr")
    @ApiModelProperty(value = "核稿人")
    private String hgr;

    @Column(name = "bz", length = 2000)
    @ApiModelProperty(value = "备注")
    private String bz;

    @Column(name = "hqdw")
    @ApiModelProperty(value = "会签单位")
    private String hqdw;

    @Column(name = "zcwj")
    @ApiModelProperty(value = "政策文件是否允许公开")
    private String zcwj;

    @ApiModelProperty(value = "会签附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A07DocumentJkqFw.hqfj'")
    private List<StorageBiz> hqfj;

    @Column(name = "headtitle")
    @ApiModelProperty(value = "发文头部标题")
    private String headtitle;

    public void setZw(StorageBiz zw) {
        if(Objects.nonNull(zw)) {
            this.zw = zw;
        }
        if(Objects.nonNull(this.zw) && StringUtils.isNotBlank(this.zw.getStorageId())) {
            if(this.id != null) {
                this.zw.setBizId(this.id.toString());
            }
        }else {
            this.zw = null;
        }
    }

    public void setFj(List<StorageBiz> fj) {
        if(fj != null) {
            if(this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }

    public void setHqfj(List<StorageBiz> hqfj) {
        if(hqfj != null) {
            if(this.hqfj == null) {
                this.hqfj = new ArrayList<>();
            }
            this.hqfj.clear();
            this.hqfj.addAll(hqfj);
        }
    }

    @Override
    @PrePersist
    @PreUpdate
    public void preCreateEntity() {
        super.preCreateEntity();
        if(!CollectionUtils.isEmpty(this.fj)) {
            for(StorageBiz storageBiz : this.fj) {
                if(Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                }
            }
        }
        if(!CollectionUtils.isEmpty(this.hqfj)) {
            for(StorageBiz storageBiz : this.hqfj) {
                if(Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                }
            }
        }
        if(Objects.nonNull(this.zw) && StringUtils.isNotBlank(this.zw.getStorageId())) {
            this.zw.setBizId(this.id.toString());
        }else {
            this.zw = null;
        }
    }
}
