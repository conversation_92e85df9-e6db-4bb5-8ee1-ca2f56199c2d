package com.hzwangda.aigov.modules.matter.service.mapstruct;

import com.hzwangda.aigov.modules.matter.domain.MatterCommonProblem;
import com.hzwangda.aigov.modules.matter.service.dto.MatterCommonProblemDto;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class MatterCommonProblemMapperImpl implements MatterCommonProblemMapper {

    @Override
    public MatterCommonProblemDto toDto(MatterCommonProblem entity) {
        if ( entity == null ) {
            return null;
        }

        MatterCommonProblemDto matterCommonProblemDto = new MatterCommonProblemDto();

        matterCommonProblemDto.setCreateBy( entity.getCreateBy() );
        if ( entity.getCreateDate() != null ) {
            matterCommonProblemDto.setCreateDate( new Timestamp( entity.getCreateDate().getTime() ) );
        }
        matterCommonProblemDto.setCreatorId( entity.getCreatorId() );
        matterCommonProblemDto.setEnabled( entity.getEnabled() );
        matterCommonProblemDto.setId( entity.getId() );
        matterCommonProblemDto.setMatterAnswer( entity.getMatterAnswer() );
        matterCommonProblemDto.setMatterId( entity.getMatterId() );
        matterCommonProblemDto.setMatterProblem( entity.getMatterProblem() );
        if ( entity.getModifiedDate() != null ) {
            matterCommonProblemDto.setModifiedDate( new Timestamp( entity.getModifiedDate().getTime() ) );
        }
        matterCommonProblemDto.setModifiedId( entity.getModifiedId() );
        matterCommonProblemDto.setVersion( entity.getVersion() );

        return matterCommonProblemDto;
    }

    @Override
    public List<MatterCommonProblemDto> toDto(List<MatterCommonProblem> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MatterCommonProblemDto> list = new ArrayList<MatterCommonProblemDto>( entityList.size() );
        for ( MatterCommonProblem matterCommonProblem : entityList ) {
            list.add( toDto( matterCommonProblem ) );
        }

        return list;
    }

    @Override
    public MatterCommonProblem toEntity(MatterCommonProblemDto dto) {
        if ( dto == null ) {
            return null;
        }

        MatterCommonProblem matterCommonProblem = new MatterCommonProblem();

        matterCommonProblem.setCreateBy( dto.getCreateBy() );
        matterCommonProblem.setCreateDate( dto.getCreateDate() );
        matterCommonProblem.setCreatorId( dto.getCreatorId() );
        matterCommonProblem.setEnabled( dto.getEnabled() );
        matterCommonProblem.setId( dto.getId() );
        matterCommonProblem.setModifiedDate( dto.getModifiedDate() );
        matterCommonProblem.setModifiedId( dto.getModifiedId() );
        matterCommonProblem.setVersion( dto.getVersion() );
        matterCommonProblem.setMatterAnswer( dto.getMatterAnswer() );
        matterCommonProblem.setMatterId( dto.getMatterId() );
        matterCommonProblem.setMatterProblem( dto.getMatterProblem() );

        return matterCommonProblem;
    }

    @Override
    public List<MatterCommonProblem> toEntity(List<MatterCommonProblemDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MatterCommonProblem> list = new ArrayList<MatterCommonProblem>( dtoList.size() );
        for ( MatterCommonProblemDto matterCommonProblemDto : dtoList ) {
            list.add( toEntity( matterCommonProblemDto ) );
        }

        return list;
    }
}
