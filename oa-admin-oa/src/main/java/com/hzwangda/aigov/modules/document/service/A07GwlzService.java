package com.hzwangda.aigov.modules.document.service;

import com.hzwangda.aigov.modules.document.dto.A07DocumentGwlzDto;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwlzQueryCriteria;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwlzUserDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlz;
import com.hzwangda.aigov.modules.workflow.bo.GwlzUserListBO;
import org.springframework.data.domain.Pageable;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface A07GwlzService {

    /**
     * 公文流转列表
     *
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> gwlzList(A07DocumentGwlzQueryCriteria criteria, Pageable pageable);

    /**
     * 新增或修改公文流转
     *
     * @param resources
     * @return
     */
    A07DocumentGwlz createOrUpdate(A07DocumentGwlz resources);

    /**
     * 用户公文流转列表
     *
     * @param gwlzUserListBO
     * @return
     */
    Object getGwlzUserList(GwlzUserListBO gwlzUserListBO);

    /**
     * 公文流转详情
     *
     * @param id
     * @return
     */
    A07DocumentGwlz getGwlzInfo(Long id);

    /**
     * 公文流转详情dto
     *
     * @param id
     * @return
     */
    A07DocumentGwlzDto getGwlzInfoDto(Long id);

    /**
     * 公文流转用户详情
     *
     * @param id
     * @return
     */
    A07DocumentGwlzUserDto getGwlzUserInfo(Long id);

    /**
     * 公文流转签收
     *
     * @param id
     * @return
     */
    Boolean gwlzQs(Long id);

    /**
     * 公文流转查阅
     *
     * @param id
     * @return
     */
    Boolean gwlzCy(Long id);

    /**
     * 公文流转来文删除
     *
     * @param id
     * @return
     */
    Boolean gwlzLwSc(Long id);

    /**
     * 公文流转删除
     *
     * @param id
     * @return
     */
    Boolean gwlzSc(Long id);

    /**
     * 公文流转撤回
     *
     * @param id
     * @return
     */
    Boolean gwlzCh(Long id);

}
