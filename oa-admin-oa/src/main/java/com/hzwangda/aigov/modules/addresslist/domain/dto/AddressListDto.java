package com.hzwangda.aigov.modules.addresslist.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @updateTime 2022/8/8 10:49
 */

@Data
public class AddressListDto {
    @ApiModelProperty(value = "部门id")
    private Long id;
    @ApiModelProperty(value = "部门名称")
    private String deptName;
    @ApiModelProperty(value = "用户名")
    private String userName;
    @ApiModelProperty(value = "手机号")
    private String phone;
    @ApiModelProperty(value = "树id")
    private String treeId;
    @ApiModelProperty(value = "部门名称")
    private String nickName;
    @ApiModelProperty(value = "type(0:人,1:部门)")
    private Integer type;
    @ApiModelProperty(value = "序号")
    private Integer sort;
    @ApiModelProperty(value = "父id")
    private Long pid;
    @ApiModelProperty(value = "负责人")
    private Map<String, Object> highest = new HashMap<>();
    @ApiModelProperty(value = "所属人员或者组织")
    private List<AddressListDto> children;
    private List<AddressListDto> children1;
    private String extId;
    private String customId;
    @ApiModelProperty(value = "条线id")
    private Long lineId;
}
