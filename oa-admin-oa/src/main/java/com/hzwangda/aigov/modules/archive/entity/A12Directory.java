package com.hzwangda.aigov.modules.archive.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @Date 2021/9/7 下午3:54
 **/
@Entity
@Table(name = "a12_directory")
@Data
@ApiModel(value = "文件档案目录")
public class A12Directory extends BaseEntity {
    @Id
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "父id")
    private Long pid;

    @ApiModelProperty(value = "部门id(顶级处室使用)")
    private Long deptId;

    @ApiModelProperty(value = "序号")
    private Integer sort;

    public void copy(A12Directory a12Directory) {
        BeanUtil.copyProperties(a12Directory, this, CopyOptions.create().setIgnoreNullValue(true));
    }

    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }
}
