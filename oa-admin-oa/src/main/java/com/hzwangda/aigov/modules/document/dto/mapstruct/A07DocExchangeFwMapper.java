package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07DocExchangeListFwDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlz;
import com.wangda.oa.base.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", uses = A07DocumentMapperUtil.class, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface A07DocExchangeFwMapper extends BaseMapper<A07DocExchangeListFwDto, A07DocumentGwlz> {

    List<A07DocExchangeListFwDto> toListDto(List<A07DocumentGwlz> list);


}
