package com.hzwangda.aigov.modules.document.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Immutable
@Subselect("select * from sw_all_view")
@Entity
@Data
public class SwAllView extends BaseBpmDomain implements Serializable {
    @Id
    private Long id;

    @Column(name = "bt")
    @ApiModelProperty(value = "标题")
    private String bt;

    @Column(name = "bz")
    @ApiModelProperty(value = "备注")
    private String bz;

    @Column(name = "file_status")
    @ApiModelProperty(value = "归档状态 0待归档，1预归档,2暂不归档，3不归档,4已归档，5归档中，-1移交失败")
    private String fileStatus;

    @Column(name = "hj")
    @ApiModelProperty(value = "缓急")
    private String hj;

    @Column(name = "lwdw")
    @ApiModelProperty(value = "来文单位")
    private String lwdw;

    @Column(name = "lwfs")
    @ApiModelProperty(value = "来文方式")
    private String lwfs;

    @Column(name = "lwqfr")
    @ApiModelProperty(value = "来文签发人")
    private String lwqfr;

    @ApiModelProperty(value = "来文日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date lwrq;

    @Column(name = "lwwh")
    @ApiModelProperty(value = "来文文号")
    private String lwwh;

    @Column(name = "lxrdh")
    @ApiModelProperty(value = "联系人电话")
    private String lxrdh;

    @ApiModelProperty(value = "签收人")
    private String qsr;

    @Column(name = "swlx")
    @ApiModelProperty(value = "收文类型")
    private String swlx;

    @JSONField(format = "yyyy-MM-dd")
    @ApiModelProperty(value = "收文日期")
    private Date swrq;

    @Column(name = "yj_blqk")
    @ApiModelProperty(value = "意见办理情况")
    private String yjBlqk;

    @Column(name = "yj_bljg")
    @ApiModelProperty(value = "意见办理结果")
    private String yjBljg;

    @Column(name = "yj_ldqp")
    @ApiModelProperty(value = "意见领导签批")
    private String yjLdqp;

    @Column(name = "yj_nb")
    @ApiModelProperty(value = "意见拟办")
    private String yjNb;

    @ApiModelProperty(value = "收文编号")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "swbh_id", referencedColumnName = "id")
    private ReferenceNumber swbh;

    @ApiModelProperty(value = "正文")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "zw", referencedColumnName = "id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private StorageBiz zw;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type like '%.fj%'")
    private List<StorageBiz> fj;

    @ApiModelProperty(value = "类型")
    private String moduleType;


    @ApiModelProperty(value = "正文附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type like '%.zwfj'")
    private List<StorageBiz> zwfj;
}
