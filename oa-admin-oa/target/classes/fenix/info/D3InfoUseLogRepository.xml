<?xml version="1.0" encoding="UTF-8"?>
<fenixs namespace="com.hzwangda.aigov.modules.zjedu.info.repository.D3InfoUseLogRepository">
    <fenix id="findByDeptToScore">
        select max(d.contributeUsername) as contributeUsername,
        d.contributeDeptName as  contributeDeptName,
        sum(d.score) as sumScore,
        count(d.id) as useCount
        from D3InfoUseLog d
        <trimWhere>
            <andEqual field="d.contributeDeptName" value="contributeDeptName" />
        </trimWhere>
        group by d.contributeDeptName
    </fenix>
    <fenix id="findByDeptUseLogTypeAndContributeDeptName">
        select max(d.contributeUsername) as contributeUsername,
        d.contributeDeptName as contributeDeptName,
        d.logType as logType,
        count(d.id) as count,
        sum(d.score) as score
        from D3InfoUseLog d
        <trimWhere>
            <andEqual field="d.contributeDeptName" value="contributeDeptName" match="contributeDeptName != empty"/>
            <andBetween  field="d.useDate" start="startTime" end="endTime" match="startTime != null &amp;&amp; startTime != empty &amp;&amp; endTime != null &amp;&amp; endTime != empty"/>
        </trimWhere>
        group by d.contributeDeptName,d.logType
    </fenix>
</fenixs>
