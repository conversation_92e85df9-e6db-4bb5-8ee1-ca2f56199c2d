package com.hzwangda.aigov.modules.zjedu.meeting.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35MeetingDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35MeetingNoticeDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35Meeting;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35MeetingNotice;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35MeetingNoticeUser;
import com.wangda.oa.modules.system.service.mapstruct.UserMapperUtil;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class C35MeetingNoticeMapperImpl implements C35MeetingNoticeMapper {

    @Autowired
    private UserMapperUtil userMapperUtil;

    @Override
    public C35MeetingNoticeDto toDto(C35MeetingNotice entity) {
        if ( entity == null ) {
            return null;
        }

        C35MeetingNoticeDto c35MeetingNoticeDto = new C35MeetingNoticeDto();

        c35MeetingNoticeDto.setCreateBy( entity.getCreateBy() );
        c35MeetingNoticeDto.setCreateTime( entity.getCreateTime() );
        c35MeetingNoticeDto.setUpdateTime( entity.getUpdateTime() );
        c35MeetingNoticeDto.setUpdatedBy( entity.getUpdatedBy() );
        c35MeetingNoticeDto.setDescription( entity.getDescription() );
        c35MeetingNoticeDto.setFile( entity.getFile() );
        c35MeetingNoticeDto.setHost( entity.getHost() );
        c35MeetingNoticeDto.setId( entity.getId() );
        c35MeetingNoticeDto.setMeeting( c35MeetingToC35MeetingDto( entity.getMeeting() ) );
        c35MeetingNoticeDto.setName( entity.getName() );
        List<C35MeetingNoticeUser> list = entity.getViewUsers();
        if ( list != null ) {
            c35MeetingNoticeDto.setViewUsers( new ArrayList<C35MeetingNoticeUser>( list ) );
        }

        return c35MeetingNoticeDto;
    }

    @Override
    public List<C35MeetingNoticeDto> toDto(List<C35MeetingNotice> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<C35MeetingNoticeDto> list = new ArrayList<C35MeetingNoticeDto>( entityList.size() );
        for ( C35MeetingNotice c35MeetingNotice : entityList ) {
            list.add( toDto( c35MeetingNotice ) );
        }

        return list;
    }

    @Override
    public C35MeetingNotice toEntity(C35MeetingNoticeDto dto) {
        if ( dto == null ) {
            return null;
        }

        C35MeetingNotice c35MeetingNotice = new C35MeetingNotice();

        c35MeetingNotice.setCreateBy( dto.getCreateBy() );
        c35MeetingNotice.setCreateTime( dto.getCreateTime() );
        c35MeetingNotice.setUpdateTime( dto.getUpdateTime() );
        c35MeetingNotice.setUpdatedBy( dto.getUpdatedBy() );
        c35MeetingNotice.setDescription( dto.getDescription() );
        c35MeetingNotice.setFile( dto.getFile() );
        c35MeetingNotice.setHost( dto.getHost() );
        c35MeetingNotice.setId( dto.getId() );
        c35MeetingNotice.setMeeting( c35MeetingDtoToC35Meeting( dto.getMeeting() ) );
        c35MeetingNotice.setName( dto.getName() );
        List<C35MeetingNoticeUser> list = dto.getViewUsers();
        if ( list != null ) {
            c35MeetingNotice.setViewUsers( new ArrayList<C35MeetingNoticeUser>( list ) );
        }

        return c35MeetingNotice;
    }

    @Override
    public List<C35MeetingNotice> toEntity(List<C35MeetingNoticeDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<C35MeetingNotice> list = new ArrayList<C35MeetingNotice>( dtoList.size() );
        for ( C35MeetingNoticeDto c35MeetingNoticeDto : dtoList ) {
            list.add( toEntity( c35MeetingNoticeDto ) );
        }

        return list;
    }

    protected C35MeetingDto c35MeetingToC35MeetingDto(C35Meeting c35Meeting) {
        if ( c35Meeting == null ) {
            return null;
        }

        C35MeetingDto c35MeetingDto = new C35MeetingDto();

        c35MeetingDto.setCreateBy( c35Meeting.getCreateBy() );
        c35MeetingDto.setCreateTime( c35Meeting.getCreateTime() );
        c35MeetingDto.setUpdateTime( c35Meeting.getUpdateTime() );
        c35MeetingDto.setUpdatedBy( c35Meeting.getUpdatedBy() );
        c35MeetingDto.setAddress( c35Meeting.getAddress() );
        c35MeetingDto.setEndTime( c35Meeting.getEndTime() );
        c35MeetingDto.setId( c35Meeting.getId() );
        c35MeetingDto.setStartTime( c35Meeting.getStartTime() );
        c35MeetingDto.setStatus( c35Meeting.getStatus() );
        c35MeetingDto.setSubject( c35Meeting.getSubject() );
        c35MeetingDto.setType( c35Meeting.getType() );
        c35MeetingDto.setUserIds( c35Meeting.getUserIds() );
        c35MeetingDto.setViewUsers( userMapperUtil.toConvertToSimpleUserDto( c35Meeting.getViewUsers() ) );

        return c35MeetingDto;
    }

    protected C35Meeting c35MeetingDtoToC35Meeting(C35MeetingDto c35MeetingDto) {
        if ( c35MeetingDto == null ) {
            return null;
        }

        C35Meeting c35Meeting = new C35Meeting();

        c35Meeting.setCreateBy( c35MeetingDto.getCreateBy() );
        c35Meeting.setCreateTime( c35MeetingDto.getCreateTime() );
        c35Meeting.setUpdateTime( c35MeetingDto.getUpdateTime() );
        c35Meeting.setUpdatedBy( c35MeetingDto.getUpdatedBy() );
        c35Meeting.setAddress( c35MeetingDto.getAddress() );
        c35Meeting.setEndTime( c35MeetingDto.getEndTime() );
        c35Meeting.setId( c35MeetingDto.getId() );
        c35Meeting.setStartTime( c35MeetingDto.getStartTime() );
        c35Meeting.setStatus( c35MeetingDto.getStatus() );
        c35Meeting.setSubject( c35MeetingDto.getSubject() );
        c35Meeting.setType( c35MeetingDto.getType() );
        c35Meeting.setUserIds( c35MeetingDto.getUserIds() );
        c35Meeting.setViewUsers( userMapperUtil.toConvertToUserName( c35MeetingDto.getViewUsers() ) );

        return c35Meeting;
    }
}
