package com.hzwangda.aigov.modules.matter.service.mapstruct;

import com.hzwangda.aigov.modules.matter.domain.MatterAppMaterials;
import com.hzwangda.aigov.modules.matter.service.dto.MatterAppMaterialsDto;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class MatterAppMaterialsMapperImpl implements MatterAppMaterialsMapper {

    @Override
    public MatterAppMaterialsDto toDto(MatterAppMaterials entity) {
        if ( entity == null ) {
            return null;
        }

        MatterAppMaterialsDto matterAppMaterialsDto = new MatterAppMaterialsDto();

        matterAppMaterialsDto.setCreateBy( entity.getCreateBy() );
        matterAppMaterialsDto.setBlankFormUrl( entity.getBlankFormUrl() );
        if ( entity.getCreateDate() != null ) {
            matterAppMaterialsDto.setCreateDate( new Timestamp( entity.getCreateDate().getTime() ) );
        }
        matterAppMaterialsDto.setCreatorId( entity.getCreatorId() );
        matterAppMaterialsDto.setEnabled( entity.getEnabled() );
        matterAppMaterialsDto.setId( entity.getId() );
        matterAppMaterialsDto.setIsNecessity( entity.getIsNecessity() );
        matterAppMaterialsDto.setIsTolerance( entity.getIsTolerance() );
        matterAppMaterialsDto.setMaterialsName( entity.getMaterialsName() );
        matterAppMaterialsDto.setMaterialsType( entity.getMaterialsType() );
        matterAppMaterialsDto.setMatterId( entity.getMatterId() );
        if ( entity.getModifiedDate() != null ) {
            matterAppMaterialsDto.setModifiedDate( new Timestamp( entity.getModifiedDate().getTime() ) );
        }
        matterAppMaterialsDto.setModifiedId( entity.getModifiedId() );
        matterAppMaterialsDto.setNumberMateria( entity.getNumberMateria() );
        matterAppMaterialsDto.setSampleSheetUrl( entity.getSampleSheetUrl() );
        matterAppMaterialsDto.setSpecification( entity.getSpecification() );
        matterAppMaterialsDto.setVersion( entity.getVersion() );

        return matterAppMaterialsDto;
    }

    @Override
    public List<MatterAppMaterialsDto> toDto(List<MatterAppMaterials> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MatterAppMaterialsDto> list = new ArrayList<MatterAppMaterialsDto>( entityList.size() );
        for ( MatterAppMaterials matterAppMaterials : entityList ) {
            list.add( toDto( matterAppMaterials ) );
        }

        return list;
    }

    @Override
    public MatterAppMaterials toEntity(MatterAppMaterialsDto dto) {
        if ( dto == null ) {
            return null;
        }

        MatterAppMaterials matterAppMaterials = new MatterAppMaterials();

        matterAppMaterials.setCreateBy( dto.getCreateBy() );
        matterAppMaterials.setCreateDate( dto.getCreateDate() );
        matterAppMaterials.setCreatorId( dto.getCreatorId() );
        matterAppMaterials.setEnabled( dto.getEnabled() );
        matterAppMaterials.setId( dto.getId() );
        matterAppMaterials.setModifiedDate( dto.getModifiedDate() );
        matterAppMaterials.setModifiedId( dto.getModifiedId() );
        matterAppMaterials.setVersion( dto.getVersion() );
        matterAppMaterials.setBlankFormUrl( dto.getBlankFormUrl() );
        matterAppMaterials.setIsNecessity( dto.getIsNecessity() );
        matterAppMaterials.setIsTolerance( dto.getIsTolerance() );
        matterAppMaterials.setMaterialsName( dto.getMaterialsName() );
        matterAppMaterials.setMaterialsType( dto.getMaterialsType() );
        matterAppMaterials.setMatterId( dto.getMatterId() );
        matterAppMaterials.setNumberMateria( dto.getNumberMateria() );
        matterAppMaterials.setSampleSheetUrl( dto.getSampleSheetUrl() );
        matterAppMaterials.setSpecification( dto.getSpecification() );

        return matterAppMaterials;
    }

    @Override
    public List<MatterAppMaterials> toEntity(List<MatterAppMaterialsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MatterAppMaterials> list = new ArrayList<MatterAppMaterials>( dtoList.size() );
        for ( MatterAppMaterialsDto matterAppMaterialsDto : dtoList ) {
            list.add( toEntity( matterAppMaterialsDto ) );
        }

        return list;
    }
}
