package com.hzwangda.aigov.modules.document.entity;

import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.Where;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.util.List;

/**
 * 协同
 *
 * <AUTHOR>
 * @date 2021/6/21下午8:29
 */
@Data
@Entity
@Table(name = "a08_gzxt")
public class A08Gzxt extends BaseBpmDomain {

    @Column(name = "bt")
    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "接收单位/人员")
    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "jsry", referencedColumnName = "id")
    private SysDeptUserMain jsry;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @Column(name = "nr", length = 2000)
    @ApiModelProperty(value = "内容")
    private String nr;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='a08Gzxt.fj'")
    private List<StorageBiz> fj;

    @Column(name = "yj_lz", length = 2000)
    @ApiModelProperty(value = "流转意见")
    private String yjLz;

    @PrePersist
    public void preCreateEntity() {
        super.preCreateEntity();
        if (!CollectionUtils.isEmpty(this.fj)) {
            for (StorageBiz storageBiz : this.fj) {
                storageBiz.setBizId(this.id.toString());
            }
        }
    }
}
