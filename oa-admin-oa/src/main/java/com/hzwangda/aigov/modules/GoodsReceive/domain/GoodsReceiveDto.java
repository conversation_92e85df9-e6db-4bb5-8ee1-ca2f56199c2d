package com.hzwangda.aigov.modules.GoodsReceive.domain;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsReceiveDto{

    private Long id;

    @ApiModelProperty(value = "物品名称")
    private String goodName;

    @ApiModelProperty(value = "审批或查看url")
    private String url;

    @ApiModelProperty(value = "领用数量")
    private int goodsNum;

    @ApiModelProperty(value = "领用类型  申领  借用")
    private String useType;

    @ApiModelProperty(value = "物品类型")
    private String goodType;

    @ApiModelProperty(value = "审批状态")
    private String status;

    @ApiModelProperty(value = "领取状态 待领取 已领取")
    private String getStatus;

    @ApiModelProperty(value = "当前审批人")
    private String assigneeName;

    @ApiModelProperty(value = "物品管理员")
    private String goodAdmin;

    @ApiModelProperty(value = "借用天数")
    private String borrowTime;
}
