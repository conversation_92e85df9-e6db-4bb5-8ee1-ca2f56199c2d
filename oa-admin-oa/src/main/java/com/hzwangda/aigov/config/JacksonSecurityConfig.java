package com.hzwangda.aigov.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.DefaultTyping;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Jackson安全配置
 * 修复jackson-databind安全漏洞，禁用危险的DefaultTyping功能
 * 
 * <AUTHOR> Fix
 * @date 2025-01-16
 */
@Configuration
public class JacksonSecurityConfig {

    /**
     * 配置安全的ObjectMapper
     * 禁用DefaultTyping以防止反序列化漏洞
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        
        // 禁用DefaultTyping功能，防止反序列化安全漏洞
        // DefaultTyping.NON_FINAL 和其他类型可能导致远程代码执行
        mapper.deactivateDefaultTyping();
        
        // 配置其他安全选项
        mapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false);
        
        return mapper;
    }
}
