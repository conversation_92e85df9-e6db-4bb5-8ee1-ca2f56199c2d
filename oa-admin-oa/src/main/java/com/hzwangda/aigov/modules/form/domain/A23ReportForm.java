package com.hzwangda.aigov.modules.form.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "a23_report_form")
public class A23ReportForm extends BaseEntity {
    @Id
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    private Boolean enabled = true;
    @ApiModelProperty("表单名称")
    private String title;
    @ApiModelProperty("表单结构JSON")
    @Lob
    private String formJson;
    @ApiModelProperty("备注")
    private String remark;

    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }

}
