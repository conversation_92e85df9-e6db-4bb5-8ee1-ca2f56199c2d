package com.hzwangda.aigov.auth.config;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.system.repository.UserRepositoryCustom;
import com.wangda.oa.utils.StringUtils;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Data
@Component
public class HzAuthRequestConfig {

    @Value("${hz-auth.url}")
    private String url;

    @Resource
    private UserRepositoryCustom userRepositoryCustom;

    /**
     * 鉴权
     */
    public HzAuthResponse auth(String token) {
        HttpResponse execute = HttpUtil.createGet(url)
                .form("access_token", token)
                .execute();
        String body = execute.body();
        execute.close();
        HzAuthResponse response = new HzAuthResponse();
        response.setSuccess(false);
        if (StringUtils.isNotEmpty(body)) {
            JSONObject json = JSONObject.parseObject(body);
            if (json.containsKey("error")) {
                throw new BadRequestException(json.getString("error") + ":" + json.getString("message"));
            } else {
                String accountId = json.getString("accountId");
                // TODO 校验accountId是否合法
                if (checkAccountId(accountId)) {
                    response.setSuccess(true);
                    response.setAccountId(accountId);
                } else {
                    response.setMessage("用户不存在");
                }
            }
        }
        return response;

    }

    private boolean checkAccountId(String accountId) {
        Boolean b = userRepositoryCustom.existsByUsername(accountId);
        return b;
    }

}