package com.hzwangda.aigov.modules.document.dto;

import com.wangda.oa.modules.workflow.dto.workflow.SearchDtoCriteria;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class A07SearchDtoCriteria extends SearchDtoCriteria {
    @ApiModelProperty(value = "标题")
    private String bt;
    @ApiModelProperty(value = "收文类型")
    private String swlx;
    @ApiModelProperty(value = "2:查询已办已完结,1:查询已办未完结,0:查询待办,不传查所有（查已办列表状态使用）")
    private Integer type;
    @ApiModelProperty(value = "类别，如：行政服务，目前用在服务保障中的")
    private String category;
}
