package com.hzwangda.aigov.modules.form.mapper;

import com.hzwangda.aigov.modules.form.convert.IdToUserConvert;
import com.hzwangda.aigov.modules.form.domain.A23ReportForm;
import com.hzwangda.aigov.modules.form.dto.A23ReportFormDto;
import com.wangda.oa.base.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", uses = IdToUserConvert.class, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ReportFormMapper extends BaseMapper<A23ReportFormDto, A23ReportForm> {

    @Override
    @Mapping(target = "create", source = "createBy")
    A23ReportFormDto toDto(A23ReportForm entity);
}
