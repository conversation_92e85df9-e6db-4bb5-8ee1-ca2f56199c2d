package com.hzwangda.aigov.modules.duty.domain.entity;

import com.wangda.boot.platform.idWorker.IdWorker;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;

import javax.persistence.*;
import java.util.Date;

@Entity
@Data
@Table(name = "d15_duty_record_deal")
public class D15DutyRecordDeal {

    @Id
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "处理情况")
    private String content;

    @JoinColumn(name = "recordId", referencedColumnName = "id")
    private Long recordId;

    @ApiModelProperty(value = "处理人姓名")
    private String username;

    @CreatedDate
    @ApiModelProperty(value = "处理时间")
    private Date time;

    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }
}
