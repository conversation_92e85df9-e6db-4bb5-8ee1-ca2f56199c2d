package com.hzwangda.aigov.modules.document.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.BaseDeptBpmDomain;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Where;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 收文单
 * <AUTHOR>
 * @date 2021/6/21下午8:29
 */
@Data
@Entity
@Table(name = "a07_document_czj_sw")
public class A07DocumentCzjSw extends BaseDeptBpmDomain {

    @Column(name = "old_id")
    @ApiModelProperty(value = "老数据id(匹配是否存在)")
    private Long oldId;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @ApiModelProperty(value = "收文编号")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "swbh_id", referencedColumnName = "id")
    private ReferenceNumber swbh;

    @Column(name = "lwwh")
    @ApiModelProperty(value = "来文文号")
    private String lwwh;

    @Column(name = "hj")
    @ApiModelProperty(value = "缓急")
    private String hj;

    @Column(name = "swrq")
    @ApiModelProperty(value = "收文日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date swrq;

    @Column(name = "lwdw")
    @ApiModelProperty(value = "来文单位")
    private String lwdw;

    @Column(name = "lwfs")
    @ApiModelProperty(value = "来文方式")
    private String lwfs;

    @Column(name = "lwqfr")
    @ApiModelProperty(value = "来文签发人")
    private String lwqfr;

    @Column(name = "swlx")
    @ApiModelProperty(value = "收文类型")
    private String swlx;

    @Column(name = "cllx")
    @ApiModelProperty(value = "处理类型")
    private String cllx;

    @Column(name = "bt")
    @ApiModelProperty(value = "标题")
    private String bt;

    @Column(name = "qsr")
    @ApiModelProperty(value = "签收人")
    private String qsr;

    @Column(name = "lxrdh")
    @ApiModelProperty(value = "联系人电话")
    private String lxrdh;

    @Column(name = "lwrq")
    @ApiModelProperty(value = "来文日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date lwrq;

    @ApiModelProperty(value = "正文")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "zw", referencedColumnName = "id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private StorageBiz zw;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A07DocumentCzjSw.fj'")
    private List<StorageBiz> fj;

    @ApiModelProperty(value = "拟办意见")
    @Lob
    private String yjNb;

    @ApiModelProperty(value = "领导签批")
    @Lob
    private String yjLdqp;

    @ApiModelProperty(value = "办理情况")
    @Lob
    private String yjBlqk;

    @ApiModelProperty(value = "办理结果")
    @Lob
    private String yjBljg;

    @ApiModelProperty(value = "传阅意见")
    @Lob
    private String yjCy;

    @Column(name = "bz", length = 2000)
    @ApiModelProperty(value = "备注")
    private String bz;


    public void setFj(List<StorageBiz> fj) {
        if(fj != null) {
            if(this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }

    @Override
    @PrePersist
    @PreUpdate
    public void preCreateEntity() {
        super.preCreateEntity();
        if(!CollectionUtils.isEmpty(this.fj)) {
            for(StorageBiz storageBiz : this.fj) {
                if(Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                }
            }
        }
    }

    public void setZw(StorageBiz zw) {
        if(Objects.nonNull(zw)) {
            this.zw = zw;
        }
        if(Objects.nonNull(this.zw) && StringUtils.isNotBlank(this.zw.getStorageId())) {
            if(this.id != null) {
                this.zw.setBizId(this.id.toString());
            }
        }else {
            this.zw = null;
        }
    }
}
