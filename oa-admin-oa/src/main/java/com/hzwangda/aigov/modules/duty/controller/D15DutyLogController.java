package com.hzwangda.aigov.modules.duty.controller;

import com.hzwangda.aigov.modules.duty.domain.dto.D15DutyLogCriteria;
import com.hzwangda.aigov.modules.duty.domain.entity.D15DutyLog;
import com.hzwangda.aigov.modules.duty.service.D15DutyLogService;
import com.wangda.oa.annotation.rest.AnonymousDeleteMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/d15DutyLog")
@Api(tags = "值班交接班日志记录")
public class D15DutyLogController {

    @Resource
    private D15DutyLogService d15DutyLogService;

    @GetMapping(value = "queryList")
    @ApiOperation("列表查询")
    public ResponseEntity<Page<D15DutyLog>> queryList(D15DutyLogCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(d15DutyLogService.queryList(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping(value = "save")
    @ApiOperation("保存日志")
    public ResponseEntity<Long> save(@RequestBody D15DutyLog d15DutyLog) {
        return new ResponseEntity<>(d15DutyLogService.save(d15DutyLog), HttpStatus.OK);
    }

    @GetMapping("/queryById")
    @ApiOperation("交班信息")
    public ResponseEntity<D15DutyLog> queryById(@RequestParam Long id) {
        return new ResponseEntity<>(d15DutyLogService.queryById(id), HttpStatus.OK);
    }

    @PostMapping(value = "confirm")
    @ApiOperation("交班同意或拒绝")
    public ResponseEntity<Long> confirm(@RequestBody D15DutyLog d15DutyLog) {
        return new ResponseEntity<>(d15DutyLogService.confirm(d15DutyLog), HttpStatus.OK);
    }

    @AnonymousDeleteMapping("/delete")
    @ApiOperation("删除")
    public ResponseEntity<String> delete(@RequestParam Long id) {
        return new ResponseEntity<>(d15DutyLogService.delete(id), HttpStatus.OK);
    }
}
