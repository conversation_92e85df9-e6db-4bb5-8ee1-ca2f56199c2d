package com.hzwangda.aigov.modules.document.controller;


import com.hzwangda.aigov.modules.collaboration.domain.criteria.SmsInfo;
import com.hzwangda.aigov.modules.document.dto.*;
import com.hzwangda.aigov.modules.document.entity.A07HhInfoTypeTabs;
import com.hzwangda.aigov.modules.document.entity.A07MeetingSea;
import com.hzwangda.aigov.modules.document.service.A07HhInfoService;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.annotation.AnonymousAccess;
import com.wangda.oa.annotation.Log;
import com.wangda.oa.annotation.rest.AnonymousGetMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/a07HhInfo")
@Api(tags = "会海会议")
@CrossOrigin
public class A07HhInfoController {
    @Resource
    private A07HhInfoService a07HhInfoService;


    @AnonymousAccess
    @Log("会海会议-新建")
    @ApiOperation("获取会议表单")
    @GetMapping(value = "/openDoc")
    public ResultJson start(@RequestParam(required = false) String id) {
        return a07HhInfoService.openDoc(id);
    }

    @Log("分页查询-发文")
    @ApiOperation("分页查询-发文")
    @GetMapping(value = "/getDocExchangeFwList")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Page<A07HhInfoListFwDto>> getHhInfoFwList(A07HhInfoQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07HhInfoService.getHhInfoFwList(criteria, pageable), HttpStatus.OK);
    }

    @Log("分页查询-收文")
    @ApiOperation("分页查询-收文")
    @GetMapping(value = "/getDocExchangeSwList")
    public ResponseEntity<Page<A07HhInfoListSwDto>> getHhInfoSwList(A07HhInfoQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07HhInfoService.getDocExchangeSwList(criteria, pageable), HttpStatus.OK);
    }

    @Log("分页查询-全部")
    @ApiOperation("分页查询-全部")
    @PostMapping(value = "/getDocExchangeAllList")
    public ResponseEntity<Page<A07HhInfoListAllDto>> getDocExchangeAllList(@RequestBody A07HhInfoQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07HhInfoService.getDocExchangeAllList(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping("/saveGwlz")
    @Log("新增或修改公文流转")
    @ApiOperation("新增或修改gwlz")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Long> saveHhHy(@Validated @RequestBody A07HhInfoSaveCriteria resources) {
        return new ResponseEntity<>(a07HhInfoService.saveGwlz(resources.getA07MeetingSea(), resources.getType()), HttpStatus.OK);
    }

    @Log("公文流转详情")
    @ApiOperation("公文流转详情")
    @GetMapping(value = "/getGwlzInfo")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<A07MeetingSea> getMeetingInfo(@RequestParam Long id) {
        return new ResponseEntity<>(a07HhInfoService.getMeetingInfo(id), HttpStatus.OK);
    }

    @Log("签收记录列表")
    @ApiOperation("签收记录列表")
    @GetMapping("/getSignRecords")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<List<A07HhInfoSignDto>> getSignRecords(A07HhInfoSignCriteria criteria) {
        return new ResponseEntity<>(a07HhInfoService.getSignRecords(criteria), HttpStatus.OK);
    }

    @Log("批量推送")
    @ApiOperation("批量推送")
    @PostMapping("/batchPush")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<String> batchPush(@NotNull @RequestBody Long[] id) {
        return new ResponseEntity<>(a07HhInfoService.batchPush(id), HttpStatus.OK);
    }

    @Log("批量解除推送")
    @ApiOperation("批量解除推送")
    @PostMapping("/batchUnPush")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<String> batchUnPush(@NotNull @RequestBody Long[] id) {
        return new ResponseEntity<>(a07HhInfoService.batchUnPush(id), HttpStatus.OK);
    }

    @Log("批量删除")
    @ApiOperation("批量删除")
    @PostMapping("/batchDel")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<String> batchDel(@NotNull @RequestBody Long[] id) {
        return new ResponseEntity<>(a07HhInfoService.batchDel(id), HttpStatus.OK);
    }

    @Log("签收")
    @ApiOperation("签收")
    @PostMapping("/sign")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<String> sign(@RequestBody Long id) {
        return new ResponseEntity<>(a07HhInfoService.sign(id), HttpStatus.OK);
    }

    @Log("外网发布数据集合")
    @ApiOperation("外网发布数据集合")
    @AnonymousGetMapping(value = "/getTypeTabs")
    public ResponseEntity<List<A07HhInfoTypeTabs>> getTypeTabs() {
        return new ResponseEntity<>(a07HhInfoService.getTypeTabs(), HttpStatus.OK);
    }

    @Log("撤回")
    @ApiOperation("撤回")
    @PostMapping("/revoke")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<String> revoke(@RequestBody Long id) {
        return new ResponseEntity<>(a07HhInfoService.revoke(id), HttpStatus.OK);
    }

    @Log("短信催收")
    @ApiOperation("短信催收")
    @PostMapping("/smsUrge")
    public ResponseEntity smsUrge(@RequestBody SmsInfo smsInfo) {
        return ResponseEntity.ok(a07HhInfoService.smsUrge(smsInfo));
    }

    @ApiOperation("获取短信内容")
    @GetMapping("/getSms")
    public ResponseEntity<Map> getSms(@RequestParam Long id) {
        return ResponseEntity.ok(a07HhInfoService.getSms(id));
    }
}
