package com.hzwangda.aigov.modules.addresslist.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.hzwangda.aigov.modules.addresslist.domain.criteria.PersonAddressListQueryCriteria;
import com.hzwangda.aigov.modules.addresslist.domain.dto.AddressListDto;
import com.hzwangda.aigov.modules.addresslist.domain.dto.PersonAddressListDto;
import com.hzwangda.aigov.modules.addresslist.domain.entity.PersonAddressList;
import com.hzwangda.aigov.modules.addresslist.domain.entity.PersonAddressListGroup;
import com.hzwangda.aigov.modules.addresslist.mapstruct.PersonAddressListMapper;
import com.hzwangda.aigov.modules.addresslist.repository.PersonAddressListGroupRepository;
import com.hzwangda.aigov.modules.addresslist.repository.PersonAddressListRepository;
import com.hzwangda.aigov.modules.addresslist.service.PersonAddressListService;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.extension.bo.UserListBO;
import com.wangda.oa.modules.extension.dto.org.UserListDto;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;

/**
 * @author: zhangzhanlong
 * @date: 2022/8/5 13:23
 * @description: 个人通讯录
 */
@Service
@RequiredArgsConstructor
public class PersonAddressListServiceImpl implements PersonAddressListService {
    private final PersonAddressListGroupRepository addressListGroupRepository;
    private final PersonAddressListRepository addressListRepository;
    private final PersonAddressListMapper addressListMapper;

    @Override
    public Object queryList(PersonAddressListQueryCriteria criteria, Pageable pageable) {
        Specification<PersonAddressList> sp = (root, criteriaQuery, cb) -> {
            Predicate pe = QueryHelp.getPredicate(root, criteria, cb);

            pe = cb.and(pe, cb.equal(root.get("createBy"), SecurityUtils.getCurrentUsername()));
            criteriaQuery.orderBy(
                    cb.asc(root.get("sort"))
            );
            return pe;
        };
        Page<PersonAddressList> addressLists = addressListRepository.findAll(sp, pageable);
        return PageUtil.toPage(addressLists.map(addressListMapper::toDto));
    }

    @Override
    public Object queryOne(Long id) {
        PersonAddressList addressList = addressListRepository.findById(id).orElseGet(PersonAddressList::new);
        return addressList;
    }

    @Override
    public Object create(PersonAddressListDto addressListDto) {
        PersonAddressList addressList = addressListMapper.toEntity(addressListDto);
        PersonAddressList save = addressListRepository.save(addressList);
        return save;
    }

    @Override
    public Object update(PersonAddressListDto addressListDto) {
        if (addressListDto.getId() == null) {
            throw new BadRequestException("id为空！");
        }
        PersonAddressList addressList = addressListMapper.toEntity(addressListDto);
        PersonAddressList save = addressListRepository.save(addressList);
        return save;
    }

    @Override
    public Object del(Long id) {
        addressListRepository.deleteById(id);
        return "删除成功";
    }

    @Override
    public Object queryModule(PersonAddressListQueryCriteria criteria) {
        Specification<PersonAddressListGroup> spGroup = (root, criteriaQuery, cb) -> {
            Predicate pe = cb.and(cb.equal(root.get("createBy"), SecurityUtils.getCurrentUsername()));
            criteriaQuery.orderBy(
                    cb.asc(root.get("sort"))
            );
            return pe;
        };

        Specification<PersonAddressList> sp = (root, criteriaQuery, cb) -> {
            Predicate pe = QueryHelp.getPredicate(root, criteria, cb);
            pe = cb.and(pe, cb.equal(root.get("createBy"), SecurityUtils.getCurrentUsername()));
            criteriaQuery.orderBy(
                    cb.asc(root.get("sort"))
            );
            return pe;
        };
        List<PersonAddressListGroup> addressListGroups = addressListGroupRepository.findAll(spGroup);
        List<PersonAddressList> addressLists = addressListRepository.findAll(sp);
        if (CollectionUtil.isEmpty(addressListGroups)) {
            return ResultJson.generateResult();
        }
        return ResultJson.generateResult(build(addressListGroups, addressListGroups.get(0).getId(), addressLists));
    }

    /**
     * 递归查子部门
     *
     * @param groupList
     * @param groupId
     * @param personAddressLists
     * @return
     */
    private List<AddressListDto> build(List<PersonAddressListGroup> groupList, Long groupId, List<PersonAddressList> personAddressLists) {
        List<AddressListDto> list = new ArrayList<>();
        for (PersonAddressListGroup group : groupList) {
            //传入的pid为空则查询pid为空符合,传入的pid不为空则需要pid不为空且相同符合
            Boolean flag = (groupId == null && group.getId() == null) ||
                    (groupId != null && group.getId() != null && group.getId().longValue() == groupId.longValue());

            AddressListDto orgListDto = new AddressListDto();
            orgListDto.setId(group.getId());
            orgListDto.setDeptName(group.getGroupName());
            orgListDto.setNickName(group.getGroupName());
            orgListDto.setSort(group.getSort());
            orgListDto.setType(1);
            orgListDto.setExtId(group.getId().toString());

            Iterator<PersonAddressList> addressListDtoIterator = personAddressLists.iterator();
            while (addressListDtoIterator.hasNext()) {
                PersonAddressList addressListDto = addressListDtoIterator.next();
                if (addressListDto.getGroupId().longValue() == group.getId().longValue()) {
                    AddressListDto listDto = new AddressListDto();
                    listDto.setId(addressListDto.getId());
                    listDto.setDeptName(group.getGroupName());
                    listDto.setNickName(addressListDto.getNickName());
                    listDto.setUserName(addressListDto.getUsername());
                    listDto.setType(0);
                    listDto.setExtId(group.getGroupName() + addressListDto.getId());
                    listDto.setSort(addressListDto.getSort());
                    listDto.setPhone(addressListDto.getPhone());
                    //人员赋值
                    if (orgListDto.getChildren() == null) {
                        orgListDto.setChildren(new ArrayList<>());
                    }
                    orgListDto.getChildren().add(listDto);

                }
            }

            list.add(orgListDto);
        }
        list.sort(Comparator.comparing(AddressListDto::getSort, Comparator.nullsLast(Integer::compareTo)));
        return list;
    }

    @Override
    public ResultJson<List<UserListDto>> getUserList(UserListBO bo) {
        List<UserListDto> userListDtoList = new ArrayList<>();
        Specification<PersonAddressList> sp = (root, criteriaQuery, cb) -> {
            Predicate pe = cb.and(cb.equal(root.get("createBy"), SecurityUtils.getCurrentUsername()));
            if (StringUtils.isNotBlank(bo.getName())) {
                pe = cb.and(pe, cb.like(root.get("nickName"), "%" + bo.getName() + "%"));
            }

            criteriaQuery.orderBy(
                    cb.asc(root.get("sort"))
            );
            return pe;
        };
        List<PersonAddressList> addressLists = addressListRepository.findAll(sp);
        if (!CollectionUtils.isEmpty(addressLists)) {
            for (PersonAddressList user : addressLists) {
                UserListDto userListDto = new UserListDto();
                userListDto.setId(user.getId().toString());
                userListDto.setUserName(user.getUsername());
                userListDto.setNickName(user.getNickName());
                userListDto.setExtId(user.getGroupName() + user.getId());
                userListDto.setType(0);
                userListDtoList.add(userListDto);
            }
        }
        return ResultJson.generateResult(userListDtoList);
    }
}
