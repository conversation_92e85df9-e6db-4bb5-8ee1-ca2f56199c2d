package com.hzwangda.aigov.modules.document.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.document.entity.A07DocumentFw;
import com.hzwangda.aigov.modules.document.repository.A07DocumentFwRepository;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.workflow.dto.BacklogListDto;
import com.wangda.oa.modules.workflow.dto.MyWorkDto;
import com.wangda.oa.modules.workflow.enums.MyWorkStatusEnum;
import com.wangda.oa.modules.workflow.service.FlowFormHandle;
import com.wangda.oa.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/13
 * @description
 */
@Component
@Slf4j
public class A07DocumentFwFormHandle implements FlowFormHandle {

    @Autowired
    private A07DocumentFwRepository a07DocumentFwRepository;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private UserService userService;

    @Override
    public void handleFormForMyWork(BacklogListDto myWork, MyWorkDto myWorkDto) {

        myWorkDto.setId(myWork.getId());
        myWorkDto.setProcessInstanceId(myWork.getBizId());
        myWorkDto.setTitle(myWork.getTitle());
        myWorkDto.setType(myWork.getModuleName());
        myWorkDto.setCreateTime(myWork.getCreateDate());
        myWorkDto.setPcUrl(myWork.getPcUrl());
        myWorkDto.setLogo(myWork.getLogo());
        if(Objects.nonNull(myWork.getUrgent())) {
            // 0:非急件,1:急件
            if(myWork.getUrgent().equals(1)) {
                myWorkDto.setHj("急");
            }else {
                myWorkDto.setHj("普通");
            }
        }

        A07DocumentFw a07DocumentFw = a07DocumentFwRepository.findFirstByBpmInstanceId(myWork.getBizId());
        if(Objects.nonNull(a07DocumentFw)) {
            myWorkDto.setTitle(a07DocumentFw.getBt());
            myWorkDto.setHj(a07DocumentFw.getHj());

            List<HistoricTaskInstance> historicTaskInstanceList = historyService.createHistoricTaskInstanceQuery().processInstanceId(myWork.getBizId()).taskAssignee(SecurityUtils.getCurrentUsername()).orderByHistoricTaskInstanceStartTime().desc().list();
            HistoricTaskInstance historicTaskInstance = historicTaskInstanceList.get(0);
            myWorkDto.setCreateTime(historicTaskInstance.getCreateTime());
            if(Objects.isNull(historicTaskInstance.getEndTime())) {
                myWorkDto.setStatus(MyWorkStatusEnum.BLZ);
            }else {
                myWorkDto.setStatus(MyWorkStatusEnum.YBL);
            }
        }

    }

    @Override
    public String handleSubjectRule(JSONObject formDataObj, String subjectRule) {
        return null;
    }

    @Override
    public Object handleFormRecord(String procInstanceId, String taskDefKey, JSONObject bpmFormData) {
        // 如果表单数据有值且不需要处理，直接返回
        if(Objects.nonNull(bpmFormData)) {
            return bpmFormData;
        }
        A07DocumentFw documentFw = a07DocumentFwRepository.findFirstByBpmInstanceId(procInstanceId);
        // 初始化默认值
        if(Objects.isNull(documentFw)) {
            documentFw = new A07DocumentFw();
            UserDto userInfoDto = userService.findById(SecurityUtils.getCurrentUserId());
            documentFw.setNgr(userInfoDto.getNickName());

            ReferenceNumber referenceNumber = new ReferenceNumber();
            referenceNumber.setNh(LocalDate.now().getYear());
            documentFw.setGwwh(referenceNumber);
            /* documentFw.setWwfblmCode("");*/

            UserDto userDto = userService.findById(SecurityUtils.getCurrentUserId());
            Date date = new Date();
            documentFw.setHj("普通");//缓急为普通
            //documentFw.setNgr(userDto.getNickName());//默认为当前用户
           /* documentFw.setYfrq(new Date());//默认为当前时间
            documentFw.setZbbm(userDto.getDept().getName());//默认为当前用户所属部门
            documentFw.setYfrq(date);//默认为当前时间*/
            // documentFw.setJdr(userDto.getNickName());
        }
        return JSONObject.toJSON(documentFw);
    }

    @Override
    @Transactional
    public void deleteFormRecord(String instanceId) {
        a07DocumentFwRepository.deleteByBpmInstanceId(instanceId);
    }
}
