package com.hzwangda.aigov.modules.file.rest;

import com.hzwangda.aigov.modules.file.entity.ReplaceFileLog;
import com.hzwangda.aigov.modules.file.service.ReplaceFileService;
import com.wangda.oa.annotation.AnonymousAccess;
import com.wangda.oa.annotation.rest.AnonymousGetMapping;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/api/file")
public class ReplaceFileController {
    @Resource
    private ReplaceFileService replaceFileService;

    @AnonymousAccess
    @ApiOperation(value = "替换文件")
    @PostMapping("/replaceFile")
    public ResponseEntity replaceFile(@RequestParam Long id, @RequestParam MultipartFile file, HttpServletRequest request, HttpServletResponse response) {
        return ResponseEntity.ok(replaceFileService.replaceFile(id, file, request, response));
    }

    @AnonymousGetMapping("/logs")
    @ApiOperation(value = "日志")
    public ResponseEntity<Page<ReplaceFileLog>> logs(Long id, String name, Pageable pageable) {
        return ResponseEntity.ok(replaceFileService.logs(id, name, pageable));
    }
}
