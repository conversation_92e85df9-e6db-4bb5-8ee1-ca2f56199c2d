package com.hzwangda.aigov.modules.duty.repository;

import com.hzwangda.aigov.modules.duty.domain.entity.D15DutyLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Date;
import java.util.List;

public interface D15DutyLogRepository extends JpaRepository<D15DutyLog, Long>, JpaSpecificationExecutor<D15DutyLog> {
    List<D15DutyLog> findByDutyTimeAndType(Date dutyTime, Integer type);

    List<D15DutyLog> findByType(Integer type);

    Boolean existsByDutyTimeAndUsername(Date dutyTime, String username);
}
