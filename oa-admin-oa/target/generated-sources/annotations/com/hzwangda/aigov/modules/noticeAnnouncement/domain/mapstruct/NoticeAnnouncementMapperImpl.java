package com.hzwangda.aigov.modules.noticeAnnouncement.domain.mapstruct;

import com.hzwangda.aigov.modules.noticeAnnouncement.domain.dto.NoticeAnnouncementDto;
import com.hzwangda.aigov.modules.noticeAnnouncement.domain.entity.NoticeAnnouncement;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class NoticeAnnouncementMapperImpl implements NoticeAnnouncementMapper {

    @Override
    public List<NoticeAnnouncementDto> toDto(List<NoticeAnnouncement> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<NoticeAnnouncementDto> list = new ArrayList<NoticeAnnouncementDto>( entityList.size() );
        for ( NoticeAnnouncement noticeAnnouncement : entityList ) {
            list.add( toDto( noticeAnnouncement ) );
        }

        return list;
    }

    @Override
    public NoticeAnnouncement toEntity(NoticeAnnouncementDto dto) {
        if ( dto == null ) {
            return null;
        }

        NoticeAnnouncement noticeAnnouncement = new NoticeAnnouncement();

        noticeAnnouncement.setCreateBy( dto.getCreateBy() );
        noticeAnnouncement.setCreateDate( dto.getCreateDate() );
        noticeAnnouncement.setId( dto.getId() );
        noticeAnnouncement.setSendDate( dto.getSendDate() );
        noticeAnnouncement.setStatus( dto.getStatus() );
        noticeAnnouncement.setTitle( dto.getTitle() );
        noticeAnnouncement.setType( dto.getType() );

        return noticeAnnouncement;
    }

    @Override
    public List<NoticeAnnouncement> toEntity(List<NoticeAnnouncementDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<NoticeAnnouncement> list = new ArrayList<NoticeAnnouncement>( dtoList.size() );
        for ( NoticeAnnouncementDto noticeAnnouncementDto : dtoList ) {
            list.add( toEntity( noticeAnnouncementDto ) );
        }

        return list;
    }

    @Override
    public NoticeAnnouncementDto toDto(NoticeAnnouncement entity) {
        if ( entity == null ) {
            return null;
        }

        NoticeAnnouncementDto noticeAnnouncementDto = new NoticeAnnouncementDto();

        noticeAnnouncementDto.setCreateBy( entity.getCreateBy() );
        noticeAnnouncementDto.setCreateDate( entity.getCreateDate() );
        noticeAnnouncementDto.setId( entity.getId() );
        noticeAnnouncementDto.setSendDate( entity.getSendDate() );
        noticeAnnouncementDto.setStatus( entity.getStatus() );
        noticeAnnouncementDto.setTitle( entity.getTitle() );
        noticeAnnouncementDto.setType( entity.getType() );

        return noticeAnnouncementDto;
    }
}
