package com.hzwangda.aigov.modules.news.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class A06NewsApprovalListDto {

    @ApiModelProperty(value = "流程标识(电子公告:electronicBulletin，今日择报:newspaperToday,工作交流:workCommunication,政策文件:policyDocument)")
    private String processIdentifier;

    @ApiModelProperty(value = "状态(0:待处理,1:已处理)")
    private int status;

    @ApiModelProperty(value = "标题")
    private String title;
}
