package com.hzwangda.aigov.modules.document.listener;

import com.hzwangda.aigov.modules.document.entity.A07DocumentFw;
import com.hzwangda.aigov.modules.document.repository.A07DocumentFwRepository;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class A07DocumentFwZcwjExecutionListener implements ExecutionListener {

    @Autowired
    private A07DocumentFwRepository a07DocumentFwRepository;


    @Override
    public void notify(DelegateExecution execution) {
        String procinstid = execution.getProcessInstanceId();
        A07DocumentFw fw = a07DocumentFwRepository.findFirstByBpmInstanceId(procinstid);
       /* if("是".equals(fw.getNwfb())){
            a07DocumentFwRepository.updateZcwjByBpmInstanceId(procinstid);
        }*/
    }
}
