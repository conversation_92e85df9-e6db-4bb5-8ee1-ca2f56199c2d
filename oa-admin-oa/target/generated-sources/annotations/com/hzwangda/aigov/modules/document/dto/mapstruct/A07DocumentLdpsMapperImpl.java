package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07DocumentLdpsDto;
import com.hzwangda.aigov.modules.document.entity.A07DocumentLdps;
import com.hzwangda.aigov.modules.document.entity.A07DocumentLdps.A07DocumentLdpsBuilder;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A07DocumentLdpsMapperImpl implements A07DocumentLdpsMapper {

    @Autowired
    private A07DocumentMapperUtil a07DocumentMapperUtil;

    @Override
    public List<A07DocumentLdpsDto> toDto(List<A07DocumentLdps> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A07DocumentLdpsDto> list = new ArrayList<A07DocumentLdpsDto>( entityList.size() );
        for ( A07DocumentLdps a07DocumentLdps : entityList ) {
            list.add( toDto( a07DocumentLdps ) );
        }

        return list;
    }

    @Override
    public A07DocumentLdps toEntity(A07DocumentLdpsDto dto) {
        if ( dto == null ) {
            return null;
        }

        A07DocumentLdpsBuilder a07DocumentLdps = A07DocumentLdps.builder();

        a07DocumentLdps.bljg( dto.getBljg() );
        a07DocumentLdps.bz( dto.getBz() );
        a07DocumentLdps.fkrq( dto.getFkrq() );
        a07DocumentLdps.hj( dto.getHj() );
        a07DocumentLdps.ldpsnr( dto.getLdpsnr() );
        a07DocumentLdps.lxr( dto.getLxr() );
        a07DocumentLdps.lxrdh( dto.getLxrdh() );
        a07DocumentLdps.psj( a07DocumentMapperUtil.toConvertToId( dto.getPsj() ) );
        a07DocumentLdps.psxh( dto.getPsxh() );
        a07DocumentLdps.swqr( dto.getSwqr() );
        a07DocumentLdps.xyfk( dto.getXyfk() );
        a07DocumentLdps.yjBgsnb( dto.getYjBgsnb() );
        a07DocumentLdps.yjBlqk( dto.getYjBlqk() );
        a07DocumentLdps.yjDwjnr( dto.getYjDwjnr() );
        a07DocumentLdps.yjTldps( dto.getYjTldps() );

        return a07DocumentLdps.build();
    }

    @Override
    public List<A07DocumentLdps> toEntity(List<A07DocumentLdpsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A07DocumentLdps> list = new ArrayList<A07DocumentLdps>( dtoList.size() );
        for ( A07DocumentLdpsDto a07DocumentLdpsDto : dtoList ) {
            list.add( toEntity( a07DocumentLdpsDto ) );
        }

        return list;
    }

    @Override
    public A07DocumentLdpsDto toDto(A07DocumentLdps entity) {
        if ( entity == null ) {
            return null;
        }

        A07DocumentLdpsDto a07DocumentLdpsDto = new A07DocumentLdpsDto();

        a07DocumentLdpsDto.setBelongToDept( entity.getBelongToDept() );
        a07DocumentLdpsDto.setBpmInstanceId( entity.getBpmInstanceId() );
        a07DocumentLdpsDto.setBpmProcessKey( entity.getBpmProcessKey() );
        a07DocumentLdpsDto.setBpmStatus( entity.getBpmStatus() );
        a07DocumentLdpsDto.setBpmSubject( entity.getBpmSubject() );
        a07DocumentLdpsDto.setCreateBy( entity.getCreateBy() );
        a07DocumentLdpsDto.setCreateTime( entity.getCreateTime() );
        a07DocumentLdpsDto.setId( entity.getId() );
        List<String> list = entity.getParticipateUser();
        if ( list != null ) {
            a07DocumentLdpsDto.setParticipateUser( new ArrayList<String>( list ) );
        }
        a07DocumentLdpsDto.setUpdateBy( entity.getUpdateBy() );
        a07DocumentLdpsDto.setUpdateTime( entity.getUpdateTime() );
        a07DocumentLdpsDto.setVersion( entity.getVersion() );
        a07DocumentLdpsDto.setBljg( entity.getBljg() );
        a07DocumentLdpsDto.setBz( entity.getBz() );
        a07DocumentLdpsDto.setFkrq( entity.getFkrq() );
        a07DocumentLdpsDto.setHj( entity.getHj() );
        a07DocumentLdpsDto.setLdpsnr( entity.getLdpsnr() );
        a07DocumentLdpsDto.setLxr( entity.getLxr() );
        a07DocumentLdpsDto.setLxrdh( entity.getLxrdh() );
        a07DocumentLdpsDto.setPsxh( entity.getPsxh() );
        a07DocumentLdpsDto.setSwqr( entity.getSwqr() );
        a07DocumentLdpsDto.setXyfk( entity.getXyfk() );
        a07DocumentLdpsDto.setYjBgsnb( entity.getYjBgsnb() );
        a07DocumentLdpsDto.setYjBlqk( entity.getYjBlqk() );
        a07DocumentLdpsDto.setYjDwjnr( entity.getYjDwjnr() );
        a07DocumentLdpsDto.setYjTldps( entity.getYjTldps() );

        a07DocumentLdpsDto.setPsj( a07DocumentMapperUtil.toPsj(entity.getPsj()) );

        splitTime( entity, a07DocumentLdpsDto );

        return a07DocumentLdpsDto;
    }
}
