package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.dto.Z08DocumentTypeQueryCriteria;
import com.hzwangda.aigov.modules.document.entity.Z08DocumentSwType;
import com.hzwangda.aigov.modules.document.service.Z08DocumentSwTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: zhangzhan<PERSON>
 * @date: 2022/10/26 9:35
 * @description:
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "收文文种维护")
@RequestMapping("/api/aigov/swtype")
@CrossOrigin
public class Z08DocumentSwTypeController {
    private final Z08DocumentSwTypeService documentTypeService;

    @ApiOperation("文种新增")
    @PostMapping(value = "/saveDocumentType")
    public ResponseEntity<Object> saveDocumentType(@RequestBody Z08DocumentSwType documentType) {
        return new ResponseEntity<>(documentTypeService.saveDocumentType(documentType), HttpStatus.OK);
    }

    @ApiOperation("分页查询文种")
    @GetMapping(value = "/getDocumentTypePage")
    public ResponseEntity<Object> getDocumentTypePage(Z08DocumentTypeQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(documentTypeService.getDocumentTypePage(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("查询文种集合")
    @GetMapping(value = "/getDocumentTypeList")
    public ResponseEntity<Object> getDocumentTypeList(Z08DocumentTypeQueryCriteria criteria) {
        return new ResponseEntity<>(documentTypeService.getDocumentTypeList(criteria), HttpStatus.OK);
    }

    @ApiOperation("根据id查询文种")
    @GetMapping(value = "/getOneById")
    public ResponseEntity<Object> getOneById(@RequestParam("id") Long id) {
        return new ResponseEntity<>(documentTypeService.getOneById(id), HttpStatus.OK);
    }

    @ApiOperation("根据id删除文种")
    @PostMapping(value = "/deleteById")
    public ResponseEntity<Object> deleteById(@RequestParam("id") Long id) {
        documentTypeService.deleteById(id);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("根据代字查询")
    @GetMapping(value = "/getDzList")
    public ResponseEntity<Object> getDzList(String dz) {
        return new ResponseEntity<>(documentTypeService.getDzList(dz), HttpStatus.OK);
    }

    @ApiOperation("根据代字和单位查询")
    @GetMapping(value = "/getByDzAndDeptId")
    public ResponseEntity<Z08DocumentSwType> getByDzAndDeptId(String dz, String deptId) {
        return new ResponseEntity<>(documentTypeService.getByDzAndDeptId(dz, deptId), HttpStatus.OK);
    }

    @ApiOperation("根据单位查询代字")
    @GetMapping(value = "/getByDeptId")
    public ResponseEntity<List<Z08DocumentSwType>> getByDeptId(String deptId) {
        return new ResponseEntity<>(documentTypeService.getByDeptId(deptId), HttpStatus.OK);
    }
}
