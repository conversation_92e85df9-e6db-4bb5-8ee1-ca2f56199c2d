package com.hzwangda.aigov.modules.form.mapper;

import com.hzwangda.aigov.modules.form.convert.IdToDeptConvert;
import com.hzwangda.aigov.modules.form.convert.IdToUserConvert;
import com.hzwangda.aigov.modules.form.domain.A23ReportData;
import com.hzwangda.aigov.modules.form.dto.A23ReportFormDataDto;
import com.wangda.oa.base.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", uses = {IdToUserConvert.class, IdToDeptConvert.class}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface A23ReportDataMapper extends BaseMapper<A23ReportFormDataDto, A23ReportData> {

    @Override
    @Mappings({@Mapping(target = "dept", source = "deptId"), @Mapping(target = "create", source = "createBy")})
    A23ReportFormDataDto toDto(A23ReportData entity);

    @Mappings({@Mapping(target = "deptId", source = "dept"), @Mapping(target = "createBy", source = "create")})
    @Override
    A23ReportData toEntity(A23ReportFormDataDto dto);
}
