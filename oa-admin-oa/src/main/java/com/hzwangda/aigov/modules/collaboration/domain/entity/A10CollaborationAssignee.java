package com.hzwangda.aigov.modules.collaboration.domain.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.annotation.JSONField;
import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @program: oa-mgt-server
 * @description: 工作协同-操作记录详细接收人
 * @author: liux
 * @create: 2021-08-15 18:14
 */
@Entity
@Getter
@Setter
@Table(name = "a10_collaboration_assignee")
@ApiModel(value = "工作协同-操作日志-接收人")
public class A10CollaborationAssignee extends BaseEntity {
    @Id
    @Column(name = "id")
    @NotNull(groups = {Update.class})
    @ApiModelProperty(value = "id")
    private Long id;

    @Column(name = "info_id")
    @ApiModelProperty(value = "工作协同id")
    private Long infoId;

    @Column(name = "log_id")
    @ApiModelProperty(value = "操作记录id")
    private Long logId;

    @Column(name = "handle_user")
    @ApiModelProperty(value = "处理人username")
    private String handleUser;

    @Column(name = "from_user")
    @ApiModelProperty(value = "发送人username，冗余记录来自于谁")
    private String fromUser;

    @Column(name = "status")
    // @Convert(converter = CollaborationAssigneeStatusEnum.class)
    @ApiModelProperty(value = "状态[草稿(draft)，未读(new),已读/未签收(unsign),已签收(signed)]")
    // private CollaborationAssigneeStatusEnum status;
    private String status;

    @Column(name = "read_first_time")
    @ApiModelProperty(value = "首读时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date readingTime;

    @ApiModelProperty(value = "转办或办结时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @Column(name = "signed_time")
    @ApiModelProperty(value = "签收时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date signedTime;

    @Transient
    @ApiModelProperty(value = "操作人nickName")
    private String handleUserName;


    @Transient
    @ApiModelProperty(value = "操作人的部门id")
    private Long deptId;

    @ApiModelProperty(value = "委托人前缀部门名称")
    private String prefixDeptName;

    public void copy(A10CollaborationAssignee a10CollaborationAssignee) {
        BeanUtil.copyProperties(a10CollaborationAssignee, this, CopyOptions.create().setIgnoreNullValue(true));
    }

    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }
}
