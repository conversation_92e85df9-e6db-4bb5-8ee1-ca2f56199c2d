package com.hzwangda.aigov.modules.addresslist.domain.dto;

import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: zhangzhanlong
 * @date: 2022/8/5 10:47
 * @description: 单位通讯录
 */
@Data
public class UnitAddressListDto extends BaseEntity {

    private Long id;

    @ApiModelProperty(value = "分类Id")
    private Long groupId;

    @ApiModelProperty(value = "分类名称")
    private String groupName;

    @ApiModelProperty(value = "姓名")
    private String nickName;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "单位/部门Id")
    private Long unitId;

    @ApiModelProperty(value = "单位/部门名称")
    private String unitName;

    @ApiModelProperty(value = "职位")
    private String jobName;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "办电")
    private String officeTel;

    @ApiModelProperty(value = "备注")
    private String remark;
}
