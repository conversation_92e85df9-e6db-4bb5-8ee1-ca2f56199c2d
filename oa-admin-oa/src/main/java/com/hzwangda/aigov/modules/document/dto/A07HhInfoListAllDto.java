package com.hzwangda.aigov.modules.document.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "会海会议-列表")
public class A07HhInfoListAllDto {
    private Long id;

    @ApiModelProperty(value = "流转单id")
    private Long gwid;

    @ApiModelProperty(value = "标题")
    private String subject;

    @ApiModelProperty(value = "状态(未全部签收，已全部签收)")
    private String status;

    @ApiModelProperty(value = "会议种类")
    private String wfType;

    @ApiModelProperty(value = "会议发送状态(0待发,1已发,2撤回)")
    private String sendStatus;

    @ApiModelProperty(value = "会议申报单位")
    private String mainOrg2;

    @ApiModelProperty(value = "是否已推送")
    private String isPost;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "会议开始时间")
    private Date startDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "签收日期")
    private Date signDate;

    @ApiModelProperty(value = "紧急程度(特急,加急,平急)")
    private String urgentLevel;
}
