package com.hzwangda.aigov.modules.GoodsReceive.service;

import com.hzwangda.aigov.modules.GoodsReceive.domain.GoodsInventory;
import com.hzwangda.aigov.modules.GoodsReceive.dto.GoodsInventoryCriteria;

import java.util.List;

public interface GoodsInventoryService {
    List<GoodsInventory> findAllByPid(GoodsInventoryCriteria goodsInventoryCriteria);

    List<GoodsInventory> findAllByPidIsNull(GoodsInventoryCriteria goodsInventoryCriteria);

    Long save(GoodsInventory goodsInventory);

    List<GoodsInventory> findAll(GoodsInventoryCriteria goodsInventoryCriteria);

    Object delByIds(Long[] ids);

    List<GoodsInventory> findChildrenByName(String name, Boolean leaf, GoodsInventoryCriteria goodsInventoryCriteria);
}
