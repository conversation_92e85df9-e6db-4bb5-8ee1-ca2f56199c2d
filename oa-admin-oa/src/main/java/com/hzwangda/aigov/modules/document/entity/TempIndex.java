package com.hzwangda.aigov.modules.document.entity;

import com.wangda.boot.platform.idWorker.IdWorker;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.Table;

/**
 * 记录es更新索引的临时表
 */
@Data
@Entity
@Table(name = "temp_index")
public class TempIndex {

    @Id
    private Long id;

    private String fwId;

    private String swId;

    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }
}
