package com.hzwangda.aigov.modules.taskmanagement.domain.mapstruct;

import com.hzwangda.aigov.modules.taskmanagement.domain.dto.TmEvaluationDto;
import com.hzwangda.aigov.modules.taskmanagement.domain.entity.TmEvaluation;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class TmEvaluationMapperImpl implements TmEvaluationMapper {

    @Override
    public TmEvaluationDto toDto(TmEvaluation entity) {
        if ( entity == null ) {
            return null;
        }

        TmEvaluationDto tmEvaluationDto = new TmEvaluationDto();

        tmEvaluationDto.setCloseCase( entity.getCloseCase() );
        tmEvaluationDto.setCompleteLimit( entity.getCompleteLimit() );
        tmEvaluationDto.setCreateBy( entity.getCreateBy() );
        tmEvaluationDto.setCreateTime( entity.getCreateTime() );
        tmEvaluationDto.setDept( entity.getDept() );
        tmEvaluationDto.setEvaluationId( entity.getEvaluationId() );
        tmEvaluationDto.setProblemDescribe( entity.getProblemDescribe() );
        tmEvaluationDto.setProblemName( entity.getProblemName() );
        tmEvaluationDto.setRectificationOf( entity.getRectificationOf() );
        tmEvaluationDto.setRemark( entity.getRemark() );
        tmEvaluationDto.setTask( entity.getTask() );
        tmEvaluationDto.setUpdateTime( entity.getUpdateTime() );

        return tmEvaluationDto;
    }

    @Override
    public List<TmEvaluationDto> toDto(List<TmEvaluation> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmEvaluationDto> list = new ArrayList<TmEvaluationDto>( entityList.size() );
        for ( TmEvaluation tmEvaluation : entityList ) {
            list.add( toDto( tmEvaluation ) );
        }

        return list;
    }

    @Override
    public TmEvaluation toEntity(TmEvaluationDto dto) {
        if ( dto == null ) {
            return null;
        }

        TmEvaluation tmEvaluation = new TmEvaluation();

        tmEvaluation.setCreateBy( dto.getCreateBy() );
        tmEvaluation.setCreateTime( dto.getCreateTime() );
        tmEvaluation.setUpdateTime( dto.getUpdateTime() );
        tmEvaluation.setCloseCase( dto.getCloseCase() );
        tmEvaluation.setCompleteLimit( dto.getCompleteLimit() );
        tmEvaluation.setDept( dto.getDept() );
        tmEvaluation.setEvaluationId( dto.getEvaluationId() );
        tmEvaluation.setProblemDescribe( dto.getProblemDescribe() );
        tmEvaluation.setProblemName( dto.getProblemName() );
        tmEvaluation.setRectificationOf( dto.getRectificationOf() );
        tmEvaluation.setRemark( dto.getRemark() );
        tmEvaluation.setTask( dto.getTask() );

        return tmEvaluation;
    }

    @Override
    public List<TmEvaluation> toEntity(List<TmEvaluationDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TmEvaluation> list = new ArrayList<TmEvaluation>( dtoList.size() );
        for ( TmEvaluationDto tmEvaluationDto : dtoList ) {
            list.add( toEntity( tmEvaluationDto ) );
        }

        return list;
    }
}
