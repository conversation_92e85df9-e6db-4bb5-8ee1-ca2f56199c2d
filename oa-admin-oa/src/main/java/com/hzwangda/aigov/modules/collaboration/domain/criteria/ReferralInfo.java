package com.hzwangda.aigov.modules.collaboration.domain.criteria;

import com.hzwangda.aigov.oa.domain.StorageBiz;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel(value = "转办信息")
@Data
public class ReferralInfo {

    @ApiModelProperty(value = "主送")
    private List<String> sendTo;

    @ApiModelProperty(value = "标题")
    private String subject;

    @ApiModelProperty(value = "交办内容")
    private String content;

    @NotNull(message = "bizType不能为空")
    @ApiModelProperty(value = "业务类名")
    private String bizType;

    @NotNull(message = "bizLink不能为空")
    @ApiModelProperty(value = "链接")
    private String bizLink;

    @NotNull(message = "bizId不能为空")
    @ApiModelProperty(value = "业务表id")
    private Long bizId;

    @Transient
    private List<StorageBiz> attachments;

    private String className;
}
