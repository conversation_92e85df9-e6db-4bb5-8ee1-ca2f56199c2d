package com.hzwangda.aigov.modules.document.dto.tzb;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import retrofit2.http.GET;

import java.util.List;

@Data
@ApiModel("直通车领导批示接受")
public class A07LdpsReceiveDto {

    private String code;

    private String date;

    private FormData formData;

    private Zw zw;

    private List<Attach> attachs;

    private String appId;

    @Getter
    @Setter
    public class  FormData{
        public String title;
        public String content;
        public String pushUnit;
    }

    @Getter
    @Setter
    public class  Zw{
        @ApiModelProperty("正文名称，含后缀")
        public String fileName;
        @ApiModelProperty("文件类型，目前固定正文类型为pdf")
        public String fileExt;
        @ApiModelProperty("文件的下载地址")
        public String fileUrl;
    }

    @Getter
    @Setter
    public class Attach{
        public String attName;
        public String attUrl;
        @ApiModelProperty("附件的字节大小")
        public String size;
    }


}
