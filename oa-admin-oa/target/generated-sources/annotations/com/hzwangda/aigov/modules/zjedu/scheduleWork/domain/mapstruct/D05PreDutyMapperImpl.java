package com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.dto.D05PreDutyDto;
import com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.entity.D05PreDuty;
import com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.entity.D05PreDuty.D05PreDutyBuilder;
import com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.entity.D05PreSchedule;
import com.hzwangda.aigov.modules.zjedu.scheduleWork.domain.entity.D05Schedule;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class D05PreDutyMapperImpl implements D05PreDutyMapper {

    @Override
    public D05PreDutyDto toDto(D05PreDuty entity) {
        if ( entity == null ) {
            return null;
        }

        D05PreDutyDto d05PreDutyDto = new D05PreDutyDto();

        d05PreDutyDto.setCreateBy( entity.getCreateBy() );
        d05PreDutyDto.setCreateTime( entity.getCreateTime() );
        d05PreDutyDto.setUpdateTime( entity.getUpdateTime() );
        d05PreDutyDto.setUpdatedBy( entity.getUpdatedBy() );
        d05PreDutyDto.setDepartment( entity.getDepartment() );
        d05PreDutyDto.setDutyDate( entity.getDutyDate() );
        d05PreDutyDto.setEndTime( entity.getEndTime() );
        d05PreDutyDto.setId( entity.getId() );
        d05PreDutyDto.setPhoneBook( entity.getPhoneBook() );
        d05PreDutyDto.setSchedule( d05PreScheduleToD05Schedule( entity.getSchedule() ) );
        d05PreDutyDto.setScheduleOreder( entity.getScheduleOreder() );
        d05PreDutyDto.setShiftLeader( entity.getShiftLeader() );
        d05PreDutyDto.setShiftLeaderTel( entity.getShiftLeaderTel() );
        d05PreDutyDto.setStartTime( entity.getStartTime() );
        d05PreDutyDto.setStatus( entity.getStatus() );
        d05PreDutyDto.setTel( entity.getTel() );
        d05PreDutyDto.setUserCode( entity.getUserCode() );
        d05PreDutyDto.setUserName( entity.getUserName() );

        return d05PreDutyDto;
    }

    @Override
    public List<D05PreDutyDto> toDto(List<D05PreDuty> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<D05PreDutyDto> list = new ArrayList<D05PreDutyDto>( entityList.size() );
        for ( D05PreDuty d05PreDuty : entityList ) {
            list.add( toDto( d05PreDuty ) );
        }

        return list;
    }

    @Override
    public D05PreDuty toEntity(D05PreDutyDto dto) {
        if ( dto == null ) {
            return null;
        }

        D05PreDutyBuilder d05PreDuty = D05PreDuty.builder();

        d05PreDuty.department( dto.getDepartment() );
        d05PreDuty.dutyDate( dto.getDutyDate() );
        d05PreDuty.endTime( dto.getEndTime() );
        d05PreDuty.id( dto.getId() );
        d05PreDuty.phoneBook( dto.getPhoneBook() );
        try {
            d05PreDuty.schedule( d05ScheduleToD05PreSchedule( dto.getSchedule() ) );
        }
        catch ( ParseException e ) {
            throw new RuntimeException( e );
        }
        d05PreDuty.scheduleOreder( dto.getScheduleOreder() );
        d05PreDuty.shiftLeader( dto.getShiftLeader() );
        d05PreDuty.shiftLeaderTel( dto.getShiftLeaderTel() );
        d05PreDuty.startTime( dto.getStartTime() );
        d05PreDuty.status( dto.getStatus() );
        d05PreDuty.tel( dto.getTel() );
        d05PreDuty.userCode( dto.getUserCode() );
        d05PreDuty.userName( dto.getUserName() );

        return d05PreDuty.build();
    }

    @Override
    public List<D05PreDuty> toEntity(List<D05PreDutyDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<D05PreDuty> list = new ArrayList<D05PreDuty>( dtoList.size() );
        for ( D05PreDutyDto d05PreDutyDto : dtoList ) {
            list.add( toEntity( d05PreDutyDto ) );
        }

        return list;
    }

    protected D05Schedule d05PreScheduleToD05Schedule(D05PreSchedule d05PreSchedule) {
        if ( d05PreSchedule == null ) {
            return null;
        }

        D05Schedule d05Schedule = new D05Schedule();

        d05Schedule.setCreateBy( d05PreSchedule.getCreateBy() );
        d05Schedule.setCreateTime( d05PreSchedule.getCreateTime() );
        d05Schedule.setUpdateTime( d05PreSchedule.getUpdateTime() );
        d05Schedule.setUpdatedBy( d05PreSchedule.getUpdatedBy() );
        d05Schedule.setDepartment( d05PreSchedule.getDepartment() );
        d05Schedule.setDeptUserName( d05PreSchedule.getDeptUserName() );
        if ( d05PreSchedule.getEndTime() != null ) {
            d05Schedule.setEndTime( new SimpleDateFormat().format( d05PreSchedule.getEndTime() ) );
        }
        d05Schedule.setId( d05PreSchedule.getId() );
        d05Schedule.setIsNextDay( d05PreSchedule.getIsNextDay() );
        d05Schedule.setScheduleOreder( d05PreSchedule.getScheduleOreder() );
        if ( d05PreSchedule.getStartTime() != null ) {
            d05Schedule.setStartTime( new SimpleDateFormat().format( d05PreSchedule.getStartTime() ) );
        }
        d05Schedule.setUseDate( d05PreSchedule.getUseDate() );

        return d05Schedule;
    }

    protected D05PreSchedule d05ScheduleToD05PreSchedule(D05Schedule d05Schedule) throws ParseException {
        if ( d05Schedule == null ) {
            return null;
        }

        D05PreSchedule d05PreSchedule = new D05PreSchedule();

        d05PreSchedule.setCreateBy( d05Schedule.getCreateBy() );
        d05PreSchedule.setCreateTime( d05Schedule.getCreateTime() );
        d05PreSchedule.setUpdateTime( d05Schedule.getUpdateTime() );
        d05PreSchedule.setUpdatedBy( d05Schedule.getUpdatedBy() );
        d05PreSchedule.setDepartment( d05Schedule.getDepartment() );
        d05PreSchedule.setDeptUserName( d05Schedule.getDeptUserName() );
        if ( d05Schedule.getEndTime() != null ) {
            d05PreSchedule.setEndTime( new SimpleDateFormat().parse( d05Schedule.getEndTime() ) );
        }
        d05PreSchedule.setId( d05Schedule.getId() );
        d05PreSchedule.setIsNextDay( d05Schedule.getIsNextDay() );
        d05PreSchedule.setScheduleOreder( d05Schedule.getScheduleOreder() );
        if ( d05Schedule.getStartTime() != null ) {
            d05PreSchedule.setStartTime( new SimpleDateFormat().parse( d05Schedule.getStartTime() ) );
        }
        d05PreSchedule.setUseDate( d05Schedule.getUseDate() );

        return d05PreSchedule;
    }
}
