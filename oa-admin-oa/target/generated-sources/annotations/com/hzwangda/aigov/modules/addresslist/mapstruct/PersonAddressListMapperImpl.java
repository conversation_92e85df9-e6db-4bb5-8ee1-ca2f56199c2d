package com.hzwangda.aigov.modules.addresslist.mapstruct;

import com.hzwangda.aigov.modules.addresslist.domain.dto.PersonAddressListDto;
import com.hzwangda.aigov.modules.addresslist.domain.entity.PersonAddressList;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class PersonAddressListMapperImpl implements PersonAddressListMapper {

    @Override
    public PersonAddressListDto toDto(PersonAddressList entity) {
        if ( entity == null ) {
            return null;
        }

        PersonAddressListDto personAddressListDto = new PersonAddressListDto();

        personAddressListDto.setCreateBy( entity.getCreateBy() );
        personAddressListDto.setCreateTime( entity.getCreateTime() );
        personAddressListDto.setUpdateTime( entity.getUpdateTime() );
        personAddressListDto.setUpdatedBy( entity.getUpdatedBy() );
        personAddressListDto.setGroupId( entity.getGroupId() );
        personAddressListDto.setGroupName( entity.getGroupName() );
        personAddressListDto.setId( entity.getId() );
        personAddressListDto.setJobName( entity.getJobName() );
        personAddressListDto.setNickName( entity.getNickName() );
        personAddressListDto.setOfficeTel( entity.getOfficeTel() );
        personAddressListDto.setPhone( entity.getPhone() );
        personAddressListDto.setRemark( entity.getRemark() );
        personAddressListDto.setUnitId( entity.getUnitId() );
        personAddressListDto.setUnitName( entity.getUnitName() );
        personAddressListDto.setUsername( entity.getUsername() );

        return personAddressListDto;
    }

    @Override
    public List<PersonAddressListDto> toDto(List<PersonAddressList> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<PersonAddressListDto> list = new ArrayList<PersonAddressListDto>( entityList.size() );
        for ( PersonAddressList personAddressList : entityList ) {
            list.add( toDto( personAddressList ) );
        }

        return list;
    }

    @Override
    public PersonAddressList toEntity(PersonAddressListDto dto) {
        if ( dto == null ) {
            return null;
        }

        PersonAddressList personAddressList = new PersonAddressList();

        personAddressList.setCreateBy( dto.getCreateBy() );
        personAddressList.setCreateTime( dto.getCreateTime() );
        personAddressList.setUpdateTime( dto.getUpdateTime() );
        personAddressList.setUpdatedBy( dto.getUpdatedBy() );
        personAddressList.setGroupId( dto.getGroupId() );
        personAddressList.setGroupName( dto.getGroupName() );
        personAddressList.setId( dto.getId() );
        personAddressList.setJobName( dto.getJobName() );
        personAddressList.setNickName( dto.getNickName() );
        personAddressList.setOfficeTel( dto.getOfficeTel() );
        personAddressList.setPhone( dto.getPhone() );
        personAddressList.setRemark( dto.getRemark() );
        personAddressList.setUnitId( dto.getUnitId() );
        personAddressList.setUnitName( dto.getUnitName() );
        personAddressList.setUsername( dto.getUsername() );

        return personAddressList;
    }

    @Override
    public List<PersonAddressList> toEntity(List<PersonAddressListDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<PersonAddressList> list = new ArrayList<PersonAddressList>( dtoList.size() );
        for ( PersonAddressListDto personAddressListDto : dtoList ) {
            list.add( toEntity( personAddressListDto ) );
        }

        return list;
    }
}
