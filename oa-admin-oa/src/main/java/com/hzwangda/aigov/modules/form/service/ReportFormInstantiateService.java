package com.hzwangda.aigov.modules.form.service;

import com.hzwangda.aigov.modules.form.domain.A23ReportFormInstantiate;
import com.hzwangda.aigov.modules.form.dto.A23ReportFormInstantiateDto;
import com.hzwangda.aigov.modules.form.dto.ReportFormInstQueryCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

public interface ReportFormInstantiateService {

    Page<A23ReportFormInstantiateDto> query(ReportFormInstQueryCriteria criteria, Pageable pageable);

    void delete(Set<Long> ids);

    A23ReportFormInstantiateDto update(A23ReportFormInstantiate resources);

    A23ReportFormInstantiateDto create(A23ReportFormInstantiate resources);

    A23ReportFormInstantiate queryById(Long l);

    void reset(Long id);

    List<A23ReportFormInstantiateDto> batchSave(Set<Long> ids);

    A23ReportFormInstantiateDto queryByIdToDto(Long id);
}
