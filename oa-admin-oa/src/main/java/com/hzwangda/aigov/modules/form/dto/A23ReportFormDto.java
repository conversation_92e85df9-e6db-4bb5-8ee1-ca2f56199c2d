package com.hzwangda.aigov.modules.form.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.wangda.oa.base.BaseDTO;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import lombok.Data;

@Data
public class A23ReportFormDto extends BaseDTO {
    A23ReportFormInstantiateDto instantiate;
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    private String title;
    private SimpleUserDto create;
    private String remark;

}
