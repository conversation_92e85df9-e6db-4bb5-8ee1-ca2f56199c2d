/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package ${package}.service.dto;

import lombok.Data;
<#if queryHasTimestamp>
    import java.sql.Timestamp;
</#if>
<#if queryHasBigDecimal>
    import java.math.BigDecimal;
</#if>
<#if betweens??>
    import java.util.List;
</#if>
<#if queryColumns??>
    import com.wangda.oa.annotation.Query;
</#if>

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date ${date}
**/
@Data
public class ${className}QueryCriteria{
<#if queryColumns??>
    <#list queryColumns as column>

        <#if column.queryType = '='>
            /** 精确 */
            @Query
            private ${column.columnType} ${column.changeColumnName};
        </#if>
        <#if column.queryType = 'Like'>
            /** 模糊 */
            @Query(type = Query.Type.INNER_LIKE)
            private ${column.columnType} ${column.changeColumnName};
        </#if>
        <#if column.queryType = '!='>
            /** 不等于 */
            @Query(type = Query.Type.NOT_EQUAL)
            private ${column.columnType} ${column.changeColumnName};
        </#if>
        <#if column.queryType = 'NotNull'>
            /** 不为空 */
            @Query(type = Query.Type.NOT_NULL)
            private ${column.columnType} ${column.changeColumnName};
        </#if>
        <#if column.queryType = '>='>
            /** 大于等于 */
            @Query(type = Query.Type.GREATER_THAN)
            private ${column.columnType} ${column.changeColumnName};
        </#if>
        <#if column.queryType = '<='>
            /** 小于等于 */
            @Query(type = Query.Type.LESS_THAN)
            private ${column.columnType} ${column.changeColumnName};
        </#if>
    </#list>
</#if>
<#if betweens??>
    <#list betweens as column>
        /** BETWEEN */
        @Query(type = Query.Type.BETWEEN)
        private List<${column.columnType}> ${column.changeColumnName};
    </#list>
</#if>
}
