package com.hzwangda.aigov.modules.document.entity.qfb;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.oa.domain.BaseDeptBpmDomain;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Where;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 区府办-会议通知收文
 * @updateTime 2023/1/10 14:10
 */

@Data
@Entity
@Table(name = "z01_qfb_hytz_sw")
public class Z01QfbHytzSw extends BaseDeptBpmDomain {
    @Column(name = "old_id")
    @ApiModelProperty(value = "老数据id(匹配是否存在)")
    private Long oldId;

    @Column(name = "lwwh")
    @ApiModelProperty(value = "来文文号")
    private String lwwh;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @Column(name = "hj")
    @ApiModelProperty(value = "缓急")
    private String hj;

    @Column(name = "swrq")
    @ApiModelProperty(value = "收文日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date swrq;

    @Column(name = "lwdw")
    @ApiModelProperty(value = "来文单位")
    private String lwdw;

    @Column(name = "lwfs")
    @ApiModelProperty(value = "来文方式")
    private String lwfs;

    @Column(name = "lwqfr")
    @ApiModelProperty(value = "来文签发人")
    private String lwqfr;

    @Column(name = "swlx")
    @ApiModelProperty(value = "收文类型")
    private String swlx;

    @Column(name = "wjlx")
    @ApiModelProperty(value = "文件类型")
    private String wjlx;

    @Column(name = "bt")
    @ApiModelProperty(value = "标题")
    private String bt;

    @Column(name = "qsr")
    @ApiModelProperty(value = "签收人")
    private String qsr;

    @Column(name = "lwrq")
    @ApiModelProperty(value = "来文日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date lwrq;

    @ApiModelProperty(value = "正文")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='Z01QfbHytzSw.zwfj'")
    private List<StorageBiz> zwfj;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='Z01QfbHytzSw.fj'")
    private List<StorageBiz> fj;

    @ApiModelProperty(value = "办理情况")
    @Column(columnDefinition = "text")
    private String blqk;

    @Column(name = "bz", length = 2000)
    @ApiModelProperty(value = "备注")
    private String bz;

    @ApiModelProperty(value = "拟办意见")
    @Column(columnDefinition = "text")
    private String yjNb;

    @ApiModelProperty(value = "领导签批")
    @Column(columnDefinition = "text")
    private String yjLdqp;


    public void setFj(List<StorageBiz> fj) {
        if(fj != null) {
            if(this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }

    public void setZwfj(List<StorageBiz> zwfj) {
        if(zwfj != null) {
            if(this.zwfj == null) {
                this.zwfj = new ArrayList<>();
            }
            this.zwfj.clear();
            this.zwfj.addAll(zwfj);
        }
    }

    @Override
    @PrePersist
    @PreUpdate
    public void preCreateEntity() {
        super.preCreateEntity();
        if(!CollectionUtils.isEmpty(this.fj)) {
            for(StorageBiz storageBiz : this.fj) {
                if(Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                }
            }
        }
        if(!CollectionUtils.isEmpty(this.zwfj)) {
            for(StorageBiz storageBiz : this.zwfj) {
                if(Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                }
            }
        }
    }
}
