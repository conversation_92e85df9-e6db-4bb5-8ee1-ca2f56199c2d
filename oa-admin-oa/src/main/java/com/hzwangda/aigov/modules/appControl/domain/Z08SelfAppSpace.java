package com.hzwangda.aigov.modules.appControl.domain;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/22
 * @description 自选应用空间
 */
@Getter
@Setter
@Entity
@ApiModel("自选应用空间")
@Table(name = "z08_self_app_space")
public class Z08SelfAppSpace extends BaseDomain {

    @Column(length = 128)
    @ApiModelProperty(value = "名称")
    private String name;

    @OneToMany
    @ApiModelProperty(value = "应用")
    @JoinColumn(name = "app_space_id")
    private List<Z08SelfApplication> applications;

    @Column(length = 32)
    @ApiModelProperty(value = "标识")
    private String identity;

    @ApiModelProperty(value = "序号")
    private Integer sort;


}
