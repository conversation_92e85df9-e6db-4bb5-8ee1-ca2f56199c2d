package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.dto.A07DocumentGwQueryCriteria;
import com.hzwangda.aigov.modules.document.service.A01ReadDocService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "公文待阅已阅")
@RequestMapping("/api/aigov/read")
@CrossOrigin
public class A01ReadDocController {

    private final A01ReadDocService readDocService;

    @ApiOperation("发文待阅已阅列表")
    @GetMapping(value = "/getFwReadList")
    public ResponseEntity<Object> getFwReadList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(readDocService.getFwReadList(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("收文待阅已阅列表")
    @GetMapping(value = "/getSwReadList")
    public ResponseEntity<Object> getSwReadList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(readDocService.getSwReadList(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("全部待阅已阅列表")
    @GetMapping(value = "/getAllReadList")
    public ResponseEntity<Object> getAllReadList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(readDocService.getAllReadList(criteria, pageable), HttpStatus.OK);
    }


    @ApiOperation("批量办理待阅")
    @PostMapping("/read")
    public ResponseEntity<String> read(@RequestBody List<String> bpmInstanceIds) {
        return ResponseEntity.ok(readDocService.read(bpmInstanceIds));
    }

    @ApiOperation("流转记录-抄送列表查询")
    @GetMapping(value = "/getFlowDocRead")
    public ResponseEntity<List<Map<String, String>>> getFlowDocRead(A07DocumentGwQueryCriteria criteria) {
        return ResponseEntity.ok(readDocService.getFlowDocRead(criteria));
    }

    @ApiOperation("流转记录-抄送列表查询数量")
    @GetMapping(value = "/getFlowDocReadCount")
    public ResponseEntity<Map<String, Integer>> getFlowDocReadCount(A07DocumentGwQueryCriteria criteria) {
        return ResponseEntity.ok(readDocService.getFlowDocReadCount(criteria));
    }

}
