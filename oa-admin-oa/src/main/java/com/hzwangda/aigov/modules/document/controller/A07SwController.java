package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.dto.*;
import com.hzwangda.aigov.modules.document.service.A07SwService;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.annotation.Log;
import com.wangda.oa.annotation.rest.AnonymousGetMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "收文管理")
@RequestMapping("/api/aigov/document/sw")
@CrossOrigin
public class A07SwController {

    private final A07SwService a07SwService;

    @ApiOperation("收文列表")
    @GetMapping(value = "/getSwList")
    public ResponseEntity<Object> getSwList(A07DocumentSwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07SwService.getSwList(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("领导批示办理单列表")
    @GetMapping(value = "/getLdpsList")
    public ResponseEntity<Object> getLdpsList(A07DocumentLdpsQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07SwService.getLdpsList(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("签批办理单列表")
    @GetMapping(value = "/getClqpList")
    public ResponseEntity<Object> getClqpList(A07DocumentClqpQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07SwService.getClqpList(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("信息公开申请列表")
    @GetMapping(value = "/getXxgksqList")
    public ResponseEntity<Object> getXxgksqList(A07DocumentXxgksqQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07SwService.getXxgksqList(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("收文详情")
    @PostMapping(value = "/getSwInfo")
    public ResponseEntity<A07DocumentSwDto> getSwInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07SwService.getSwInfo(id), HttpStatus.OK);
    }

    @ApiOperation("领导批示办理单详情")
    @PostMapping(value = "/getLdpsInfo")
    public ResponseEntity<A07DocumentLdpsDto> getLdpsInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07SwService.getLdpsInfo(id), HttpStatus.OK);
    }

    @ApiOperation("签批办理单详情")
    @PostMapping(value = "/getClqpInfo")
    public ResponseEntity<A07DocumentClqpDto> getClqpInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07SwService.getClqpInfo(id), HttpStatus.OK);
    }

    @ApiOperation("信息公开申请详情")
    @PostMapping(value = "/getXxgksqInfo")
    public ResponseEntity<A07DocumentXxgksqDto> getXxgksqInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07SwService.getXxgksqInfo(id), HttpStatus.OK);
    }

    @ApiOperation("收文检索")
    @GetMapping(value = "/getSwListRetrieve")
    public ResponseEntity<Object> getSwListRetrieve(A07DocumentGwRetrieveQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07SwService.getSwListRetrieve(criteria, pageable), HttpStatus.OK);
    }

    @Log("删除收文")
    @ApiOperation("删除收文")
    @PostMapping(value = "/deleteSw")
    public ResponseEntity<Boolean> deleteSw(@NotNull @RequestBody List<Long> ids) {
        return new ResponseEntity<>(a07SwService.deleteSw(ids), HttpStatus.OK);
    }

    @Log("删除领导批示办理单")
    @ApiOperation("删除领导批示办理单")
    @PostMapping(value = "/deleteLdps")
    public ResponseEntity<Boolean> deleteLdps(@NotNull @RequestBody List<Long> ids) {
        return new ResponseEntity<>(a07SwService.deleteLdps(ids), HttpStatus.OK);
    }

    @Log("删除签批办理")
    @ApiOperation("删除签批办理")
    @PostMapping(value = "/deleteClqp")
    public ResponseEntity<Boolean> deleteClqps(@NotNull @RequestBody List<Long> ids) {
        return new ResponseEntity<>(a07SwService.deleteClqps(ids), HttpStatus.OK);
    }

    @Log("删除信息公开申请")
    @ApiOperation("删除信息公开申请")
    @PostMapping(value = "/deleteXxgksq")
    public ResponseEntity<Boolean> deleteXxgksq(@NotNull @RequestBody List<Long> ids) {
        return new ResponseEntity<>(a07SwService.deleteXxgksq(ids), HttpStatus.OK);
    }

    @Log("根据年号获取最新的序号值")
    @ApiOperation("根据年号获取最新的序号值")
    @AnonymousGetMapping(value = "/getXhByNh")
    public ResponseEntity<Integer> getXhByNh(@RequestParam("dz") String dz, @RequestParam("nh") String nh) {

        return new ResponseEntity<>(a07SwService.getXhByNh(dz, nh), HttpStatus.OK);
    }

    @Log("根据用户输入的来文单位获取历史来文单位列表")
    @ApiOperation("根据用户输入的来文单位获取历史来文单位列表")
    @AnonymousGetMapping(value = "/getLwdwList")
    public ResponseEntity<List<Object>> getLwdwList(@RequestParam("query") String query) {
        return new ResponseEntity<>(a07SwService.getLwdwList(query), HttpStatus.OK);
    }

    @ApiOperation("单位收文转内部收文")
    @PostMapping("/unitSwToInSw")
    public ResultJson unitSwToInSw(@RequestParam("procInstId") String procInstId, @RequestParam("appId") Long appId) {
        return a07SwService.unitSwToInSw(procInstId, appId);
    }

    @ApiOperation("内部收文转内部发文")
    @PostMapping("/inSwToInFw")
    public ResultJson inSwToInFw(@RequestParam("procInstId") String procInstId, @RequestParam("appId") Long appId) {
        return a07SwService.inSwToInFw(procInstId, appId);
    }
}
