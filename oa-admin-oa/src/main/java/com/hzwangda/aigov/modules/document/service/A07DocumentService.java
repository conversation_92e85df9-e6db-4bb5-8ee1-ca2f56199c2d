package com.hzwangda.aigov.modules.document.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzwangda.aigov.modules.document.dto.*;
import com.hzwangda.aigov.modules.document.entity.*;
import com.hzwangda.aigov.modules.workflow.bo.GwlzUserListBO;
import com.hzwangda.aigov.modules.workflow.bo.GzxtUserListBO;
import com.hzwangda.aigov.modules.workflow.dto.MyConferenceListDto;
import com.hzwangda.aigov.modules.zjedu.domain.dto.DocumentAddressCriteria;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskDto;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface A07DocumentService {

    /**
     * 政策文件列表
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> getPolicyDocumentList(A07DocumentZcwjQueryCriteria criteria, Pageable pageable);

    /**
     * 政策文件列表
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> getPolicyDocumentListForAPP(A07DocumentZcwjQueryCriteria criteria, Pageable pageable);

    /**
     * 查询政策文件详情
     * @param id
     * @return
     */
    A07DocumentFwDto findByDocumentInfo(Long id);

    /**
     * 删除政策文件记录
     * @param ids
     * @return
     */
    Boolean deleteDocumentInfo(List<Long> ids);

    /**
     * 收文列表
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> getSwList(A07DocumentSwQueryCriteria criteria, Pageable pageable);

    /**
     * 收文列表检索
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> getSwListSearch(DocumentAddressCriteria criteria, Pageable pageable);

    /**
     * 收文详情
     * @param id
     * @return
     */
    A07DocumentSwDto getSwInfo(Long id);

    /**
     * 会签列表
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> getHqList(A07DocumentHqQueryCriteria criteria, Pageable pageable);

    /**
     * 领导批示办理单列表
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> getLdpsList(A07DocumentLdpsQueryCriteria criteria, Pageable pageable);

    /**
     * 签批办理单列表
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> getClqpList(A07DocumentClqpQueryCriteria criteria, Pageable pageable);

    /**
     * 协同列表
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> getXtList(A08GzxtQueryCriteria criteria, Pageable pageable);

    /**
     * 信息公开申请列表
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> getXxgksqList(A07DocumentXxgksqQueryCriteria criteria, Pageable pageable);


    /**
     * 会签详情
     * @param id
     * @return
     */
    A07DocumentHqDto getHqInfo(Long id);

    /**
     * 领导批示办理单详情
     * @param id
     * @return
     */
    A07DocumentLdpsDto getLdpsInfo(Long id);

    /**
     * 签批办理单详情
     * @param id
     * @return
     */
    A07DocumentClqpDto getClqpInfo(Long id);

    /**
     * 协同详情
     * @param id
     * @return
     */
    A08GzxtDto getXtInfo(Long id);

    /**
     * 信息公开申请详情
     * @param id
     * @return
     */
    A07DocumentXxgksqDto getXxgksqInfo(Long id);

    /**
     * 删除收文
     * @param ids
     * @return
     */
    Boolean deleteSw(List<Long> ids);

    /**
     * 删除会签
     * @param ids
     * @return
     */
    Boolean deleteHq(List<Long> ids);

    /**
     * 删除领导批示办理单
     * @param ids
     * @return
     */
    Boolean deleteLdps(List<Long> ids);

    /**
     * 删除签批办理
     * @param ids
     * @return
     */
    Boolean deleteClqp(List<Long> ids);

    /**
     * 删除协同
     * @param ids
     * @return
     */
    Boolean deleteXt(List<Long> ids);

    /**
     * 删除信息公开申请
     * @param ids
     * @return
     */
    Boolean deleteXxgksq(List<Long> ids);

    /**
     * 公文流转操作
     * @param resources
     * @return
     */
    A07DocumentGwlz createOrUpdate(A07DocumentGwlz resources);

    /**
     * 工作协同操作
     * @param resources
     * @return
     */
    A07DocumentGzxt createOrUpdateGzxt(A07DocumentGzxt resources);

    /**
     * 公文流转查询
     * @param criteria
     * @return
     */
    Map<String, Object> gwlzList(A07DocumentGwlzQueryCriteria criteria, Pageable pageable);

    /**
     * 外网发布数据集合
     * @return
     */
    List<A07DocumentInfoWwfb> getWwfbList();

    /**
     * 用户公文列表
     * @return
     */
    Object getGwlzUserList(GwlzUserListBO gwlzUserListBO);

    /**
     * 公文流转详情
     * @param id
     * @return
     */
    A07DocumentGwlz getGwlzInfo(Long id);

    /**
     * 公文流转用户详情
     * @param id
     * @return
     */
    A07DocumentGwlzUserDto getGwlzUserInfo(Long id);

    /**
     * 公文流转签收
     * @param id
     * @return
     */
    Boolean gwlzQs(Long id);

    /**
     * 公文流转来文删除
     * @param id
     * @return
     */
    Boolean gwlzLwSc(Long id);

    /**
     * 公文流转删除
     * @param id
     * @return
     */
    Boolean gwlzSc(Long id);

    /**
     * 公文流转撤回
     * @param id
     * @return
     */
    Boolean gwlzCh(Long id);

    /**
     * 用户工作协同列表
     * @return
     */
    MyConferenceListDto getGzxtUserList(GzxtUserListBO gzxtUserListBO);

    A07DocumentGwlzUser createOrUpdateUser(A07DocumentGwlzUser resources);

    /**
     * 收文检索列表
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> getSwListRetrieve(A07DocumentRetrieveQueryCriteria criteria, Pageable pageable);

    /**
     * 发文检索列表
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> getFwListRetrieve(A07DocumentRetrieveQueryCriteria criteria, Pageable pageable);

    /**
     * 服务事项检索
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Page<FlowTaskDto> getFwsxListRetrieve(A07DocumentRetrieveQueryCriteria criteria, Pageable pageable);

    /**
     * 公文管理列表
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> getGwList(A07DocumentGwQueryCriteria criteria, Pageable pageable);

    /**
     * 发文管理列表
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> getFwList(A07DocumentGwQueryCriteria criteria, Pageable pageable);

    /**
     * 全部发文列表-根据单位筛选
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */

    Map<String, Object> getAllFwList(A07AllFwQueryCriteria criteria, Pageable pageable);

    /**
     * 收文管理列表
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> getSwList(A07DocumentGwQueryCriteria criteria, Pageable pageable);

    /**
     * 全部发文列表-根据单位筛选
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */

    Map<String, Object> getAllSwList(A07AllSwQueryCriteria criteria, Pageable pageable);

    /**
     * Description: 通过流程实例查询领导批示表单数据
     * @param bpmInstanceId
     * @return: com.hzwangda.aigov.modules.document.entity.A07DocumentLdps
     * @Date: 2021/12/10 12:39
     * @Author: maogy
     * @throws:
     */
    A07DocumentLdps findByLdpsBpmInstanceId(String bpmInstanceId);

    /**
     * Description: 分页查询所有收文
     * @param criteria
     * @param pageable
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     * @Date: 2021/12/15 15:13
     * @Author: maogy
     * @throws:
     */
    Map<String, Object> queryPageSw(A07DocumentSwQueryCriteria criteria, Pageable pageable);


    /**
     * Description: 分页查询所有发文
     * @param criteria
     * @param pageable
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     * @Date: 2021/12/15 15:13
     * @Author: maogy
     * @throws:
     */
    Map<String, Object> queryPageFw(A07DocumentFwQueryCriteria criteria, Pageable pageable);

    /**
     * Description: 查询所有领导批示
     * @param criteria
     * @param pageable
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     * @Date: 2021/12/15 15:13
     * @Author: maogy
     * @throws:
     */
    Map<String, Object> queryPageLdps(A07DocumentLdpsQueryCriteria criteria, Pageable pageable);


    /**
     * Description: 分页查询所有领厅内呈阅
     * @param criteria
     * @param pageable
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     * @Date: 2021/12/15 15:13
     * @Author: maogy
     * @throws:
     */
    Map<String, Object> queryPageTncy(A07DocumentClqpQueryCriteria criteria, Pageable pageable);


    /**
     * Description: 分页查询所有会签
     * @param criteria
     * @param pageable
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     * @Date: 2021/12/15 15:13
     * @Author: maogy
     * @throws:
     */
    Map<String, Object> queryPageHq(A07DocumentHqQueryCriteria criteria, Pageable pageable);

    /**
     * 行政审批管理列表
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> getXzList(A07DocumentGwQueryCriteria criteria, Pageable pageable);

}
