package com.hzwangda.aigov.modules.collaboration.service;

import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.collaboration.domain.criteria.A10CollaborationInfoQueryCriteria;
import com.hzwangda.aigov.modules.collaboration.domain.criteria.SmsInfo;
import com.hzwangda.aigov.modules.collaboration.domain.dto.A10CollaborationInfo4ListDto;
import com.hzwangda.aigov.modules.collaboration.domain.dto.A10CollaborationInfo4ViewDto;
import com.hzwangda.aigov.modules.collaboration.domain.dto.A10CollaborationInfoDto;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationInfo;
import com.wangda.boot.platform.base.ResultJson;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Map;

public interface A10CollaborationInfoService {

    /**
     * 所有待签收协同
     *
     * @param resources
     * @param pageable
     * @return
     */
    Page<A10CollaborationInfo4ListDto> queryUnsignList(A10CollaborationInfoQueryCriteria resources, Pageable pageable);

    /**
     * 所有待签收协同
     *
     * @param resources
     * @param pageable
     * @return
     */
    Page<A10CollaborationInfo4ListDto> querySignedList(A10CollaborationInfoQueryCriteria resources, Pageable pageable);

    /**
     * 所有协同
     *
     * @param resources
     * @param pageable
     * @return
     */
    Page<A10CollaborationInfo4ListDto> queryAllList(A10CollaborationInfoQueryCriteria resources, Pageable pageable);

    /**
     * 获取工作协同详情-查看
     *
     * @param id
     * @return
     */
    A10CollaborationInfo4ViewDto queryOneCollaborationByIdForView(Long id,Long userType);

    /**
     * 获取工作协同详情以供编辑
     *
     * @param id
     * @return
     */
    A10CollaborationInfoDto queryOneCollaborationByIdForEdit(Long id);

    /**
     * 工作协同-保存或发送
     *
     * @param collaborationInfo
     * @return
     */
    Long doSave(A10CollaborationInfo collaborationInfo);



    /**
     * 工作协同-编辑
     *
     * @param collaborationInfo
     * @return
     */
    Long doEdit(A10CollaborationInfo collaborationInfo);

    /**
     * 工作协同-删除
     *
     * @param ids
     */
    void doDelete(Long[] ids);

    /**
     * 工作协同-保存或发送
     *
     * @param id
     */
    void doFirstRead(Long id);

    void sendBacklogFromUnsignedList();

    String smsUrge(SmsInfo smsInfo);

    Map getSms(Long id,Long userType);

    ResultJson<JSONObject> doTransferToDeptOA(Long id);
}
