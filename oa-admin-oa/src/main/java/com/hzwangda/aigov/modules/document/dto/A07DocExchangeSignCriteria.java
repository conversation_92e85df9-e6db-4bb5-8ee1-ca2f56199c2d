package com.hzwangda.aigov.modules.document.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class A07DocExchangeSignCriteria {
    @ApiModelProperty(value = "公文流转单id")
    @Query
    private String gwid;

    @ApiModelProperty(value = "签收状态(0:未签收,1:已签收)")
    @Query
    private Integer qszt;

    @ApiModelProperty(value = "查阅状态(0:未查阅,1:已查阅)")
    @Query
    private Integer cyzt;

    @ApiModelProperty(value = "搜索")
    @Query(propName = "username", type = Query.Type.INNER_LIKE)
    private String searchKeys;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "签收时间")
    private Timestamp qsrq;
}
