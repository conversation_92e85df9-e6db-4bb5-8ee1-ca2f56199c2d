/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.modules.document.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-06-25
 **/
@Data
public class A07AllFwQueryCriteria {

    @Query(joinName = "gwwh", propName = "nh")
    @ApiModelProperty(value = "年份")
    private String year;

    @Query(joinName = "gwwh")
    @ApiModelProperty(value = "文种")
    private String dz;

    @ApiModelProperty(value = "发文模板")
    private String fwmb;

    @ApiModelProperty(value = "发文环节")
    private String fwhj;

    @Query(joinName = "gwwh", propName = "xh")
    @ApiModelProperty(value = "发文序号")
    private String gwxh;

    @Query
    @ApiModelProperty(value = "所属单位")
    private String belongToDept;

    @ApiModelProperty(value = "待办创建开始结束时间")
    private List<String> timeRange;

    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "流程实例id")
    private List<String> bpmInstanceId;

    @ApiModelProperty(value = "流程定义key")
    @Query(propName = "bpmProcessKey")
    private String processDefinitionKey;

    @Query
    @ApiModelProperty(value = "流程状态")
    private String bpmStatus;

    @ApiModelProperty("拟稿人")
    private String ngr;
}
