package com.hzwangda.aigov.modules.document.dto.tzb;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

@Data
@ApiModel("直通车领导批示查询")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class A07LdpsQueryDto {

    @ApiModelProperty("文件下载地址 若多个，英文逗号隔开")
    private String attUrl;

    @ApiModelProperty("文件名字（含后缀）若多个，英文逗号隔开")
    private String attName;

    @ApiModelProperty("批示或办理单位名字")
    private String unitName;

    @ApiModelProperty("标记时间（流程创建时间）（yyyy-mm-dd hh:mm:ss）")
    private String signTime;

    @ApiModelProperty("批示或办理时间（批示创建时间）（yyyy-mm-dd hh:mm:ss）")
    private String createTime;

    @ApiModelProperty("批示或办理人")
    private String userName;

    @ApiModelProperty("批示或办理状态（DOING 处理中 NOT_ENTER  未查看   FINISH 已完成）")
    private String status;

    @ApiModelProperty("反馈意见")
    private String opinion;

    private String keyId;
}
