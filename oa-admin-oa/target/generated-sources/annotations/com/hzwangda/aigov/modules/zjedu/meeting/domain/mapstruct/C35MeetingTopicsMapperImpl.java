package com.hzwangda.aigov.modules.zjedu.meeting.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35MeetingTopicsDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35MeetingTopics;
import com.wangda.oa.modules.system.service.mapstruct.UserMapperUtil;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class C35MeetingTopicsMapperImpl implements C35MeetingTopicsMapper {

    @Autowired
    private UserMapperUtil userMapperUtil;

    @Override
    public C35MeetingTopicsDto toDto(C35MeetingTopics entity) {
        if ( entity == null ) {
            return null;
        }

        C35MeetingTopicsDto c35MeetingTopicsDto = new C35MeetingTopicsDto();

        c35MeetingTopicsDto.setCreateBy( entity.getCreateBy() );
        c35MeetingTopicsDto.setCreateTime( entity.getCreateTime() );
        c35MeetingTopicsDto.setUpdateTime( entity.getUpdateTime() );
        c35MeetingTopicsDto.setUpdatedBy( entity.getUpdatedBy() );
        c35MeetingTopicsDto.setId( entity.getId() );
        c35MeetingTopicsDto.setParentId( entity.getParentId() );
        c35MeetingTopicsDto.setStatus( entity.getStatus() );
        c35MeetingTopicsDto.setSubject( entity.getSubject() );
        c35MeetingTopicsDto.setTopicSort( entity.getTopicSort() );
        c35MeetingTopicsDto.setTopicsCollect( entity.getTopicsCollect() );
        c35MeetingTopicsDto.setTopicsNote( entity.getTopicsNote() );
        c35MeetingTopicsDto.setType( entity.getType() );
        c35MeetingTopicsDto.setViewType( entity.getViewType() );
        c35MeetingTopicsDto.setViewUsers( userMapperUtil.toConvertToSimpleUserDto( entity.getViewUsers() ) );

        return c35MeetingTopicsDto;
    }

    @Override
    public List<C35MeetingTopicsDto> toDto(List<C35MeetingTopics> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<C35MeetingTopicsDto> list = new ArrayList<C35MeetingTopicsDto>( entityList.size() );
        for ( C35MeetingTopics c35MeetingTopics : entityList ) {
            list.add( toDto( c35MeetingTopics ) );
        }

        return list;
    }

    @Override
    public C35MeetingTopics toEntity(C35MeetingTopicsDto dto) {
        if ( dto == null ) {
            return null;
        }

        C35MeetingTopics c35MeetingTopics = new C35MeetingTopics();

        c35MeetingTopics.setCreateBy( dto.getCreateBy() );
        c35MeetingTopics.setCreateTime( dto.getCreateTime() );
        c35MeetingTopics.setUpdateTime( dto.getUpdateTime() );
        c35MeetingTopics.setUpdatedBy( dto.getUpdatedBy() );
        c35MeetingTopics.setId( dto.getId() );
        c35MeetingTopics.setParentId( dto.getParentId() );
        c35MeetingTopics.setStatus( dto.getStatus() );
        c35MeetingTopics.setSubject( dto.getSubject() );
        c35MeetingTopics.setTopicSort( dto.getTopicSort() );
        c35MeetingTopics.setTopicsCollect( dto.getTopicsCollect() );
        c35MeetingTopics.setTopicsNote( dto.getTopicsNote() );
        c35MeetingTopics.setType( dto.getType() );
        c35MeetingTopics.setViewType( dto.getViewType() );
        c35MeetingTopics.setViewUsers( userMapperUtil.toConvertToUserName( dto.getViewUsers() ) );

        return c35MeetingTopics;
    }

    @Override
    public List<C35MeetingTopics> toEntity(List<C35MeetingTopicsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<C35MeetingTopics> list = new ArrayList<C35MeetingTopics>( dtoList.size() );
        for ( C35MeetingTopicsDto c35MeetingTopicsDto : dtoList ) {
            list.add( toEntity( c35MeetingTopicsDto ) );
        }

        return list;
    }
}
