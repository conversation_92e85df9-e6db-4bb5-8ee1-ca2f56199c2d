package com.hzwangda.aigov.modules.form.service.impl;

import com.hzwangda.aigov.modules.form.domain.A23ReportData;
import com.hzwangda.aigov.modules.form.domain.A23ReportFormInstantiate;
import com.hzwangda.aigov.modules.form.dto.A23ReportFormDataDto;
import com.hzwangda.aigov.modules.form.dto.ReportDataQueryCriteria;
import com.hzwangda.aigov.modules.form.mapper.A23ReportDataMapper;
import com.hzwangda.aigov.modules.form.repository.A23ReportDataRepository;
import com.hzwangda.aigov.modules.form.service.ReportDataService;
import com.hzwangda.aigov.modules.form.service.ReportFormInstantiateService;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.system.domain.Dept;
import com.wangda.oa.modules.system.repository.DeptRepository;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ReportDataServiceImpl implements ReportDataService {

    private final A23ReportDataRepository dataRepository;

    private final ReportFormInstantiateService instantiateService;

    private final UserService userService;

    private final A23ReportDataMapper mapper;

    private final DeptRepository deptRepository;

    @Override
    @Transactional(readOnly = true)
    public A23ReportData findById(Long id) {

        return dataRepository.findById(id).orElseThrow(() -> new BadRequestException("数据不存在"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public A23ReportData create(A23ReportData resources) {
        UserDto userdto = userService.findById(SecurityUtils.getCurrentUserId());

        Long id = userdto.getDept().getId();
        resources.setDeptId(id.toString());
        Long instId = resources.getInstId();
        A23ReportFormInstantiate instantiate = instantiateService.queryById(instId);
        resources.setInstantiate(instantiate);
        resources.setCreateBy(SecurityUtils.getBindDeptUserName());
        return dataRepository.save(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Set<Long> ids) {
        for (Long id : ids) {
            dataRepository.deleteById(id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByInstId(String InstId) {
        dataRepository.deleteByInstantiateId(InstId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<A23ReportFormDataDto> query(ReportDataQueryCriteria criteria, Pageable pageable) {
        return dataRepository.findAll((root, query, cb) -> QueryHelp.getPredicate(root, criteria, cb), pageable).map(mapper::toDto);
    }

    @Override
    public Page<A23ReportFormDataDto> queryByMy(ReportDataQueryCriteria criteria, Pageable pageable) {
        String currentUsername = SecurityUtils.getBindDeptUserName();
        return dataRepository.findAll((root, query, cb) -> cb.and(cb.equal(root.get("createBy").as(String.class), currentUsername), QueryHelp.getPredicate(root, criteria, cb)), pageable).map(mapper::toDto);
    }

    @Override
    public Page<A23ReportFormDataDto> queryAll(ReportDataQueryCriteria criteria, Pageable pageable) {
//        if(Objects.isNull(criteria.getDeptId()))return null;
        if(Objects.isNull(criteria.getInstId()))return null;
        return dataRepository.findAll((root, query, cb) -> {
            Predicate predicate = QueryHelp.getPredicate(root, criteria, cb);
            if (criteria.getDeptId() != null) {
                // 查找所有下级部门id
                List<Long> deptIds = new ArrayList<>();
                deptIds.add(criteria.getDeptId());
                findAllDeptIds(deptIds, deptIds);
                Predicate deptId = cb.and(cb.in(root.get("deptId")).value(deptIds.stream().map(String::valueOf).collect(Collectors.toList())));
                return cb.and(predicate, deptId);
            }
            return cb.and(predicate);
        }, pageable).map(mapper::toDto);
    }

    private void findAllDeptIds(List<Long> deptIds, List<Long> result) {
        List<Dept> deptList = deptRepository.findByPidInAndEnabled(deptIds);
        if (deptList.size() > 0) {
            List<Long> ids = deptList.stream().map(Dept::getId).collect(Collectors.toList());
            result.addAll(ids);
            findAllDeptIds(ids, result);
        }
    }
}
