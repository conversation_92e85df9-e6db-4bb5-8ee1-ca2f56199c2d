package com.hzwangda.aigov.modules.collaboration.service;

import com.hzwangda.aigov.modules.collaboration.domain.criteria.ReferralInfo;
import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationLink;

/**
 * <AUTHOR>
 */
public interface A10CollaborationLinkService {

    /**
     * 其他模块转协同
     *
     * @param referralInfo
     * @return
     */
    A10CollaborationLink referral(ReferralInfo referralInfo) throws NoSuchFieldException, ClassNotFoundException, IllegalAccessException;


    Object findById(String className, Long id) throws ClassNotFoundException;


    Long saveLink(String className, Long id, A10CollaborationLink link) throws ClassNotFoundException, NoSuchFieldException, IllegalAccessException;
}
