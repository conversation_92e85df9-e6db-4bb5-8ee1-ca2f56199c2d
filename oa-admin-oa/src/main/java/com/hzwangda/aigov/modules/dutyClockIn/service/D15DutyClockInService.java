package com.hzwangda.aigov.modules.dutyClockIn.service;

import com.hzwangda.aigov.modules.dutyClockIn.domain.dto.D15DutyClockInDto;
import com.hzwangda.aigov.modules.dutyClockIn.domain.entity.D15DutyClockIn;
import com.hzwangda.aigov.modules.dutyClockIn.domain.query.DutyClockInQueryCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @description:
 * @author: zzl
 * @date: 2025/4/1 15:20
 **/
public interface D15DutyClockInService {

    /**
     * @description:  查询当天是否打卡
     * @author: zzl
     * @date: 2025/4/1 15:54
     * @return: boolean
     **/
    Boolean queryToDayIsClockIn();
    /**
     * @description:  移动端打卡记录列表查询
     * @author: zzl
     * @date: 2025/4/1 15:55
     * @param pageable:  
     * @return: org.springframework.data.domain.Page<com.hzwangda.aigov.modules.dutyClockIn.domain.entity.D15DutyClockIn>
     **/
    Page<D15DutyClockIn> queryPageH5(Pageable pageable);

    /**
     * @description:  查询当前用户今天的打卡数据
     * @author: zzl
     * @date: 2025/4/1 16:06
     * @return: com.hzwangda.aigov.modules.dutyClockIn.domain.entity.D15DutyClockIn
     **/
    D15DutyClockIn queryByToDay();

    /**
     * @description:   新增或更新打卡
     * @author: zzl
     * @date: 2025/4/1 15:57
     * @param dto:  
     * @return: com.hzwangda.aigov.modules.dutyClockIn.domain.entity.D15DutyClockIn
     **/
    D15DutyClockIn save(D15DutyClockInDto dto);

    /**
     * @description:  根据id查询打卡数据
     * @author: zzl
     * @date: 2025/4/1 17:45
     * @return: com.hzwangda.aigov.modules.dutyClockIn.domain.entity.D15DutyClockIn
     **/
    D15DutyClockIn queryById(Long id);

    /**
     * @description:  根据身份查询团队打卡记录
     * @author: zzl
     * @date: 2025/4/3 11:25
     * @param criteria: 
     * @param pageable:  
     * @return: org.springframework.data.domain.Page<com.hzwangda.aigov.modules.dutyClockIn.domain.entity.D15DutyClockIn>
     **/
    Page<D15DutyClockIn> queryByIdentityPage(DutyClockInQueryCriteria criteria, Pageable pageable);

    /**
     * @description:  导出打卡数据
     * @author: zzl
     * @date: 2025/4/7 15:22
     * @param criteria:  
     * @return: void
     **/
    void exportDutyClockIn(HttpServletResponse response,DutyClockInQueryCriteria criteria) throws IOException;
}
