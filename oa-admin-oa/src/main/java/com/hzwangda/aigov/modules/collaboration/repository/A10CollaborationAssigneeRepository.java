package com.hzwangda.aigov.modules.collaboration.repository;

import com.hzwangda.aigov.modules.collaboration.domain.entity.A10CollaborationAssignee;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface A10CollaborationAssigneeRepository extends JpaRepository<A10CollaborationAssignee, Long>, JpaSpecificationExecutor<A10CollaborationAssignee> {
    List<A10CollaborationAssignee> findByInfoId(Long infoId);

    List<A10CollaborationAssignee> findByInfoIdAndHandleUserAndStatusIn(Long infoId, String handleUser, String[] statusArray);

    @Query("select distinct t.handleUser from A10CollaborationAssignee t where t.infoId=:infoId")
    List<String> findDistinctHandleUserByInfoId(Long infoId);

    List<A10CollaborationAssignee> findByInfoIdAndFromUserInAndStatusIn(Long infoId,String[] fromuser,String[] status);

    Integer countByInfoIdAndFromUserIn(Long infoId,String[] userNames);

    List<A10CollaborationAssignee> findByInfoIdAndStatus(Long infoId, String status);

    A10CollaborationAssignee findFirstByInfoIdAndStatusInOrderByCreateTimeDesc(Long infoId, String[] status);

    List<A10CollaborationAssignee> findByInfoIdAndStatusAndFromUser(Long infoId, String status, String user);
}
