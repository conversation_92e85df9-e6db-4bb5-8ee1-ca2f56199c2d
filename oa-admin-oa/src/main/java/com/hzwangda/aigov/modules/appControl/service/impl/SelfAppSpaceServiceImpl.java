package com.hzwangda.aigov.modules.appControl.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.hzwangda.aigov.modules.appControl.domain.Z08SelfAppSpace;
import com.hzwangda.aigov.modules.appControl.domain.Z08SelfApplication;
import com.hzwangda.aigov.modules.appControl.dto.SelfAppSpaceCriteria;
import com.hzwangda.aigov.modules.appControl.repository.SelfAppSpaceRepository;
import com.hzwangda.aigov.modules.appControl.service.SelfAppSpaceService;
import com.hzwangda.aigov.modules.rule.RuleExpressionExecuteCmd;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.security.service.dto.AuthorityDto;
import com.wangda.oa.modules.system.service.DeptService;
import com.wangda.oa.modules.system.service.DictDetailService;
import com.wangda.oa.modules.system.service.RoleService;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.DeptSmallDto;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowUserRuleDto;
import com.wangda.oa.modules.workflow.enums.application.AppStatusEnum;
import com.wangda.oa.modules.workflow.utils.FlowableUtils;
import com.wangda.oa.modules.workflow.utils.UserRuleUtils;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.ManagementService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/03
 * @description
 */
@Service
@Slf4j
public class SelfAppSpaceServiceImpl implements SelfAppSpaceService {
    @Resource
    SelfAppSpaceRepository selfAppSpaceRepository;

    @Resource
    private ManagementService managementService;

    @Resource
    private UserService userService;

    @Resource
    private RoleService roleService;

    @Resource
    private DeptService deptService;

    @Resource
    private DictDetailService dictDetailService;

    @Override
    public void addOrUpdate(Z08SelfAppSpace space) {

        Z08SelfAppSpace byIdentity = selfAppSpaceRepository.findByIdentity(space.getIdentity());
        Z08SelfAppSpace Z08SelfAppSpace = new Z08SelfAppSpace();
        if(space.getId() != null) {
            Z08SelfAppSpace = selfAppSpaceRepository.findById(space.getId()).orElseGet(Z08SelfAppSpace::new);
            if(Objects.nonNull(byIdentity) && !byIdentity.getId().equals(Z08SelfAppSpace.getId())) {
                throw new BadRequestException(Z08SelfAppSpace.getIdentity() + "标识已存在，请勿重复添加！");
            }
        }else {
            if(Objects.nonNull(byIdentity)) {
                throw new BadRequestException(Z08SelfAppSpace.getIdentity() + "标识已存在，请勿重复添加！");
            }
        }
        Z08SelfAppSpace.copy(space);
        selfAppSpaceRepository.save(Z08SelfAppSpace);
    }

    @Override
    public Z08SelfAppSpace getById(Long id) {
        Z08SelfAppSpace appSpace = selfAppSpaceRepository.findById(id).orElseGet(Z08SelfAppSpace::new);
        if(!Objects.isNull(appSpace)) {
            List<Z08SelfApplication> applications = appSpace.getApplications().stream().filter(application -> {
                return validatePermission(application.getAppliedRange(), SecurityUtils.getCurrentUsername());
            }).collect(Collectors.toList());
            //处理通配符
            applications.stream().forEach(app -> {
                if(StringUtils.isNotEmpty(app.getExternalLink())) {
                    app.setExternalLink(replaceGeneralSymbol(app.getExternalLink()));
                }
                if(StringUtils.isNotEmpty(app.getInternalLink())) {
                    app.setInternalLink(replaceGeneralSymbol(app.getInternalLink()));
                }
            });
            //对当前用户进行空间内的应用过滤
            appSpace.setApplications(applications);


        }
        return appSpace;
    }

    @Override
    public Z08SelfAppSpace getByKey(String key) {
        Z08SelfAppSpace appSpace = selfAppSpaceRepository.findByIdentity(key);
        if(!Objects.isNull(appSpace)) {
            List<Z08SelfApplication> applications = appSpace.getApplications().stream().filter(application -> {
                return AppStatusEnum.ONLINE.getValue().equals(application.getAppStatus().getValue()) && validatePermission(application.getAppliedRange(), SecurityUtils.getCurrentUsername());
            }).sorted(Comparator.comparing(Z08SelfApplication::getSort)).collect(Collectors.toList());
            //处理通配符
            applications.stream().forEach(app -> {
                if(StringUtils.isNotEmpty(app.getExternalLink())) {
                    app.setExternalLink(replaceGeneralSymbol(app.getExternalLink()));
                }
                if(StringUtils.isNotEmpty(app.getInternalLink())) {
                    app.setInternalLink(replaceGeneralSymbol(app.getInternalLink()));
                }
            });
            //对当前用户进行空间内的应用过滤
            appSpace.setApplications(applications);


        }
        return appSpace;
    }

    @Override
    public Page<Z08SelfAppSpace> queryListPage(SelfAppSpaceCriteria criteria, Pageable pageable) {

        List<Z08SelfAppSpace> Z08SelfAppSpaces = selfAppSpaceRepository.findAll((root, cq, cb) -> QueryHelp.getPredicate(root, criteria, cb));

        List<Long> Z08SelfAppSpaceIds = new ArrayList<>();
        for(Z08SelfAppSpace space : Z08SelfAppSpaces) {
           /* boolean b = iUserRuleService.validatePermission(space.get(), SecurityUtils.getCurrentUsername());
            if(b) {*/
            Z08SelfAppSpaceIds.add(space.getId());
            // }
        }
        if(CollUtil.isEmpty(Z08SelfAppSpaceIds)) {
            return Page.empty();
        }
        Specification<Z08SelfAppSpace> sp = (root, cq, cb) -> {
            Predicate p = QueryHelp.getPredicate(root, criteria, cb);
            if(Z08SelfAppSpaceIds.size() > 0) {
                p = cb.and(p, cb.in(root.get("id")).value(Z08SelfAppSpaceIds));
            }
            return p;
        };
        Page<Z08SelfAppSpace> Z08SelfAppSpacePage = selfAppSpaceRepository.findAll(sp, pageable);

        return Z08SelfAppSpacePage;
    }

    @Override
    public List<Z08SelfAppSpace> queryList(SelfAppSpaceCriteria criteria) {
        Specification<Z08SelfAppSpace> sp = (root, cq, cb) -> {
            Predicate p = QueryHelp.getPredicate(root, criteria, cb);
            return p;
        };
        List<Z08SelfAppSpace> all = selfAppSpaceRepository.findAll(sp);
        List<Z08SelfAppSpace> Z08SelfAppSpaceList = new ArrayList<>();
        for(Z08SelfAppSpace space : all) {
           /* boolean b = iUserRuleService.validatePermission(space.getAppliedRange(), SecurityUtils.getCurrentUsername());
            if(b) {*/
            Z08SelfAppSpaceList.add(space);
            // }
        }
        return Z08SelfAppSpaceList;
    }

    @Override
    public boolean delete(Long id) {
        selfAppSpaceRepository.deleteById(id);
        return true;
    }

    @Override
    @ApiOperation(value = "是否有该权限")
    public boolean validatePermission(String userRule, String username) {
        if(StringUtils.isEmpty(userRule)) {
            return true;
        }
        if(StringUtils.isEmpty(username)) {
            username = SecurityUtils.getCurrentUsername();
        }
        // 判断是否自定义表达式
        if(FlowableUtils.isExpressionRule(userRule)) {
            Boolean aBoolean = managementService.executeCommand(new RuleExpressionExecuteCmd(userRule, username));
            return aBoolean;
        }else {
            FlowUserRuleDto userRuleDto = UserRuleUtils.formatUserRule(userRule);
            if(CollUtil.isNotEmpty(userRuleDto.getUserList()) && userRuleDto.getUserList().contains(username)) {
                return true;
            }

            UserDto userDto = userService.findByName(username);
            if(CollUtil.isNotEmpty(userRuleDto.getRoleList())) {
                List<AuthorityDto> authorityDtos = roleService.mapToGrantedAuthorities(userDto);
                if(CollUtil.isNotEmpty(authorityDtos)) {
                    Set<String> authoritySet = authorityDtos.stream().map(authorityDto -> {
                        return authorityDto.getAuthority();
                    }).collect(Collectors.toSet());
                    authoritySet.retainAll(userRuleDto.getRoleList());
                    if(CollUtil.isNotEmpty(authoritySet)) {
                        return true;
                    }
                }
            }

            if(CollUtil.isNotEmpty(userRuleDto.getUnitList())) {
                List<String> deptCode = deptService.queryUserUnits(username);
                if(CollUtil.isNotEmpty(deptCode)) {
                    if(CollUtil.containsAny(userRuleDto.getUnitList(), deptCode)) {
                        return true;
                    }
                }
            }
            if(CollUtil.isNotEmpty(userRuleDto.getOrgList())) {
                // TODO: 下级部门的需要判断
                DeptSmallDto deptSmallDto = userDto.getDept();
                if(userRuleDto.getOrgList().contains(deptSmallDto.getDeptCode())) {
                    return true;
                }
            }
        }
        return false;
    }


    public String replaceGeneralSymbol(String resouce) {
        return resouce
                .replaceAll("\\{\\@userName\\}", SecurityUtils.getCurrentUsername())
                .replaceAll("\\{\\@userId\\}", String.valueOf(SecurityUtils.getCurrentUserId()));
    }
}
