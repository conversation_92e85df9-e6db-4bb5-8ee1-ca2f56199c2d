package com.hzwangda.aigov.modules.zjedu.meeting.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35MeetingDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35Meeting;
import com.wangda.oa.modules.system.service.mapstruct.UserMapperUtil;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class C35MeetingMapperImpl implements C35MeetingMapper {

    @Autowired
    private UserMapperUtil userMapperUtil;

    @Override
    public C35MeetingDto toDto(C35Meeting entity) {
        if ( entity == null ) {
            return null;
        }

        C35MeetingDto c35MeetingDto = new C35MeetingDto();

        c35MeetingDto.setCreateBy( entity.getCreateBy() );
        c35MeetingDto.setCreateTime( entity.getCreateTime() );
        c35MeetingDto.setUpdateTime( entity.getUpdateTime() );
        c35MeetingDto.setUpdatedBy( entity.getUpdatedBy() );
        c35MeetingDto.setAddress( entity.getAddress() );
        c35MeetingDto.setEndTime( entity.getEndTime() );
        c35MeetingDto.setId( entity.getId() );
        c35MeetingDto.setStartTime( entity.getStartTime() );
        c35MeetingDto.setStatus( entity.getStatus() );
        c35MeetingDto.setSubject( entity.getSubject() );
        c35MeetingDto.setType( entity.getType() );
        c35MeetingDto.setUserIds( entity.getUserIds() );
        c35MeetingDto.setViewUsers( userMapperUtil.toConvertToSimpleUserDto( entity.getViewUsers() ) );

        return c35MeetingDto;
    }

    @Override
    public List<C35MeetingDto> toDto(List<C35Meeting> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<C35MeetingDto> list = new ArrayList<C35MeetingDto>( entityList.size() );
        for ( C35Meeting c35Meeting : entityList ) {
            list.add( toDto( c35Meeting ) );
        }

        return list;
    }

    @Override
    public C35Meeting toEntity(C35MeetingDto dto) {
        if ( dto == null ) {
            return null;
        }

        C35Meeting c35Meeting = new C35Meeting();

        c35Meeting.setCreateBy( dto.getCreateBy() );
        c35Meeting.setCreateTime( dto.getCreateTime() );
        c35Meeting.setUpdateTime( dto.getUpdateTime() );
        c35Meeting.setUpdatedBy( dto.getUpdatedBy() );
        c35Meeting.setAddress( dto.getAddress() );
        c35Meeting.setEndTime( dto.getEndTime() );
        c35Meeting.setId( dto.getId() );
        c35Meeting.setStartTime( dto.getStartTime() );
        c35Meeting.setStatus( dto.getStatus() );
        c35Meeting.setSubject( dto.getSubject() );
        c35Meeting.setType( dto.getType() );
        c35Meeting.setUserIds( dto.getUserIds() );
        c35Meeting.setViewUsers( userMapperUtil.toConvertToUserName( dto.getViewUsers() ) );

        return c35Meeting;
    }

    @Override
    public List<C35Meeting> toEntity(List<C35MeetingDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<C35Meeting> list = new ArrayList<C35Meeting>( dtoList.size() );
        for ( C35MeetingDto c35MeetingDto : dtoList ) {
            list.add( toEntity( c35MeetingDto ) );
        }

        return list;
    }
}
