package com.hzwangda.aigov.modules.form.convert;

import com.wangda.oa.modules.system.service.DeptService;
import com.wangda.oa.modules.system.service.dto.DeptDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class IdToDeptConvert {

    private final DeptService service;

    public DeptDto convert(String deptId) {
        return service.findById(Long.valueOf(deptId));
    }

    public String convert(DeptDto deptDto) {
        return deptDto.getId().toString();
    }

}
