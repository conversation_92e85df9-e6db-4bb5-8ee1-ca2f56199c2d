package com.hzwangda.aigov.modules.document.dto.mapstruct;

import com.hzwangda.aigov.modules.document.dto.A07DocExchangeListSwDto;
import com.hzwangda.aigov.modules.document.entity.A07DocExchangeSw;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A07DocExchangeSwMapperImpl implements A07DocExchangeSwMapper {

    @Override
    public List<A07DocExchangeListSwDto> toDto(List<A07DocExchangeSw> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A07DocExchangeListSwDto> list = new ArrayList<A07DocExchangeListSwDto>( entityList.size() );
        for ( A07DocExchangeSw a07DocExchangeSw : entityList ) {
            list.add( toDto( a07DocExchangeSw ) );
        }

        return list;
    }

    @Override
    public A07DocExchangeSw toEntity(A07DocExchangeListSwDto dto) {
        if ( dto == null ) {
            return null;
        }

        A07DocExchangeSw a07DocExchangeSw = new A07DocExchangeSw();

        a07DocExchangeSw.setId( dto.getId() );
        a07DocExchangeSw.setBt( dto.getBt() );
        a07DocExchangeSw.setFwdw( dto.getFwdw() );
        a07DocExchangeSw.setGwzl( dto.getGwzl() );
        a07DocExchangeSw.setHj( dto.getHj() );

        return a07DocExchangeSw;
    }

    @Override
    public List<A07DocExchangeSw> toEntity(List<A07DocExchangeListSwDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A07DocExchangeSw> list = new ArrayList<A07DocExchangeSw>( dtoList.size() );
        for ( A07DocExchangeListSwDto a07DocExchangeListSwDto : dtoList ) {
            list.add( toEntity( a07DocExchangeListSwDto ) );
        }

        return list;
    }

    @Override
    public List<A07DocExchangeListSwDto> toListDto(List<A07DocExchangeSw> list) {
        if ( list == null ) {
            return null;
        }

        List<A07DocExchangeListSwDto> list1 = new ArrayList<A07DocExchangeListSwDto>( list.size() );
        for ( A07DocExchangeSw a07DocExchangeSw : list ) {
            list1.add( toDto( a07DocExchangeSw ) );
        }

        return list1;
    }

    @Override
    public A07DocExchangeListSwDto toDto(A07DocExchangeSw entity) {
        if ( entity == null ) {
            return null;
        }

        A07DocExchangeListSwDto a07DocExchangeListSwDto = new A07DocExchangeListSwDto();

        a07DocExchangeListSwDto.setBt( entity.getBt() );
        a07DocExchangeListSwDto.setFwdw( entity.getFwdw() );
        a07DocExchangeListSwDto.setGwzl( entity.getGwzl() );
        a07DocExchangeListSwDto.setHj( entity.getHj() );
        a07DocExchangeListSwDto.setId( entity.getId() );

        return a07DocExchangeListSwDto;
    }
}
