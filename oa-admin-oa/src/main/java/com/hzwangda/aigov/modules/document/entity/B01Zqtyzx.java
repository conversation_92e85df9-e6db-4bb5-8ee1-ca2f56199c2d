package com.hzwangda.aigov.modules.document.entity;

import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Where;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 知情同意执行
 * <AUTHOR>
 * @date 2021/6/15 上午10:18
 */
@Data
@Entity
@Table(name = "b01_zqtyzx")
public class B01Zqtyzx extends BaseBpmDomain implements Serializable {
    @Column(name = "bq")
    @ApiModelProperty(value = "病区")
    private String bq;

    @Column(name = "kz")
    @ApiModelProperty(value = "科组")
    private String kz;

    @Column(name = "zyh")
    @ApiModelProperty(value = "住院号")
    private String zyh;

    @Column(name = "czwt")
    @ApiModelProperty(value = "存在问题")
    private String czwt;

    @Column(name = "fzr")
    @ApiModelProperty(value = "负责人")
    private String fzr;

    @Column(name = "tysfs")
    @ApiModelProperty(value = "同意书份数")
    private String tysfs;

    @Column(name = "dcr")
    @ApiModelProperty(value = "督查人")
    private String dcr;

    @Column(name = "dcrq")
    @ApiModelProperty(value = "督查日期")
    private Date dcrq;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='B01Zqtyzx.fj'")
    private List<StorageBiz> fj;

    public void setFj(List<StorageBiz> fj) {
        if(fj != null) {
            if(this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }

    @Override
    @PreUpdate
    public void preCreateEntity() {
        super.preCreateEntity();
        if(!CollectionUtils.isEmpty(this.fj)) {
            for(StorageBiz storageBiz : this.fj) {
                if(Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                }
            }
        }
    }
}
