package com.hzwangda.aigov.modules.taskmanagement.domain.mapstruct;

import com.hzwangda.aigov.modules.taskmanagement.domain.dto.TmGoalDto;
import com.hzwangda.aigov.modules.taskmanagement.domain.entity.TmGoal;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class TmGoalMapperImpl implements TmGoalMapper {

    @Override
    public TmGoalDto toDto(TmGoal entity) {
        if ( entity == null ) {
            return null;
        }

        TmGoalDto tmGoalDto = new TmGoalDto();

        tmGoalDto.setCreateBy( entity.getCreateBy() );
        tmGoalDto.setCreateTime( entity.getCreateTime() );
        tmGoalDto.setDescribe( entity.getDescribe() );
        tmGoalDto.setEndDate( entity.getEndDate() );
        tmGoalDto.setGoalId( entity.getGoalId() );
        tmGoalDto.setStatus( entity.getStatus() );
        tmGoalDto.setSummary( entity.getSummary() );
        tmGoalDto.setTask( entity.getTask() );
        tmGoalDto.setUpdateTime( entity.getUpdateTime() );

        return tmGoalDto;
    }

    @Override
    public List<TmGoalDto> toDto(List<TmGoal> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmGoalDto> list = new ArrayList<TmGoalDto>( entityList.size() );
        for ( TmGoal tmGoal : entityList ) {
            list.add( toDto( tmGoal ) );
        }

        return list;
    }

    @Override
    public TmGoal toEntity(TmGoalDto dto) {
        if ( dto == null ) {
            return null;
        }

        TmGoal tmGoal = new TmGoal();

        tmGoal.setCreateBy( dto.getCreateBy() );
        tmGoal.setCreateTime( dto.getCreateTime() );
        tmGoal.setUpdateTime( dto.getUpdateTime() );
        tmGoal.setDescribe( dto.getDescribe() );
        tmGoal.setEndDate( dto.getEndDate() );
        tmGoal.setGoalId( dto.getGoalId() );
        tmGoal.setStatus( dto.getStatus() );
        tmGoal.setSummary( dto.getSummary() );
        tmGoal.setTask( dto.getTask() );

        return tmGoal;
    }

    @Override
    public List<TmGoal> toEntity(List<TmGoalDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TmGoal> list = new ArrayList<TmGoal>( dtoList.size() );
        for ( TmGoalDto tmGoalDto : dtoList ) {
            list.add( toEntity( tmGoalDto ) );
        }

        return list;
    }
}
