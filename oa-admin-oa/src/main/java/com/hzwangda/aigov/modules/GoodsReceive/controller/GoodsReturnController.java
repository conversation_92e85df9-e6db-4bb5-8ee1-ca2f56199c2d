package com.hzwangda.aigov.modules.GoodsReceive.controller;

import com.hzwangda.aigov.modules.GoodsReceive.dto.GoodsCriteria;
import com.hzwangda.aigov.modules.GoodsReceive.dto.QueryCriteria;
import com.hzwangda.aigov.modules.GoodsReceive.service.GoodsReturnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/GoodsReturn")
@Api(tags = "物品归还")
public class GoodsReturnController {

    @Resource
    public GoodsReturnService goodsReturnService;



    @ApiOperation(value = "物品归还列表")
    @GetMapping("/goodsReturnPageList")
    public ResponseEntity goodsReturnPageList (GoodsCriteria queryCriteria, Pageable pageable) {
        return ResponseEntity.ok(goodsReturnService.goodsReturnPageList (queryCriteria, pageable));
    }


}
