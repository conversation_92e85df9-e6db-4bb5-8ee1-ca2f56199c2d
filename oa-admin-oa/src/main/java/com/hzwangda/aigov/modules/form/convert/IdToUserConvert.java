package com.hzwangda.aigov.modules.form.convert;

import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class IdToUserConvert {

    private final UserRepository repository;

    public SimpleUserDto convert(String createBy) {
        User user = repository.findByUsername(createBy);
        SimpleUserDto simpleUserDto = new SimpleUserDto();
        simpleUserDto.setId(user.getId());
        simpleUserDto.setUserName(user.getUsername());
        simpleUserDto.setNickName(user.getNickName());
        return simpleUserDto;
    }

    public String convert(SimpleUserDto simpleUserDto) {
        return simpleUserDto.getUserName();
    }

}
