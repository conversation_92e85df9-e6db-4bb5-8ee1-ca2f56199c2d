package com.hzwangda.aigov.modules.collaboration.domain.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @program: oa-mgt-server
 * @description: 工作协同-用户操作记录
 * @author: liux
 * @create: 2021-08-12 10:38
 */
@Entity
@Getter
@Setter
@Table(name = "a10_collaboration_handle_log")
@ApiModel(value = "工作协同-操作日志")
public class A10CollaborationHandleLog extends BaseEntity {
    @Id
    @Column(name = "id")
    @NotNull(groups = {Update.class})
    @ApiModelProperty(value = "id")
    private Long id;

    @Column(name = "info_id")
    @ApiModelProperty(value = "工作协同id")
    private Long infoId;

    @Column(name = "handle_user")
    @ApiModelProperty(value = "操作人username")
    private String handleUser;

    @Column(name = "handle_time")
    @ApiModelProperty(value = "操作时间")
    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date handleTime;

    @Column(name = "handle_type")
    @ApiModelProperty(value = "操作类型：[创建（create），签收（sign），转发（forward），评论（comment）")
    private String handleType;

    @Column(name = "remark", length = 4000)
    @ApiModelProperty(value = "操作意见")
    private String remark;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "log_id")
    private List<A10CollaborationAssignee> assignees;

    @Transient
    @ApiModelProperty(value = "操作人nickName")
    private String handleUserName;
    @Transient
    @ApiModelProperty(value = "短信通知")
    private Boolean sms;
    @Transient
    @ApiModelProperty(value = "钉钉消息通知")
    private Boolean ding;

    @Transient
    @ApiModelProperty(value = "发起人的用户类型 1.个人用户，2.单位用户")
    private int userType=1;
    @ApiModelProperty(value = "操作人前缀部门名称")
    private String prefixDeptName;


    public void copy(A10CollaborationHandleLog a10CollaborationHandleLog) {
        BeanUtil.copyProperties(a10CollaborationHandleLog, this, CopyOptions.create().setIgnoreNullValue(true));
    }

    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }
}
