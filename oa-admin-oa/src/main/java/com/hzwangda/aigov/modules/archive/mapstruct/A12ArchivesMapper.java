package com.hzwangda.aigov.modules.archive.mapstruct;

import com.hzwangda.aigov.modules.archive.dto.A12ArchivesDto;
import com.hzwangda.aigov.modules.archive.entity.A12Archives;
import com.wangda.oa.base.BaseMapper;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @Date 2021/9/7
 **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface A12ArchivesMapper extends BaseMapper<A12ArchivesDto, A12Archives> {

    @AfterMapping
    default void splitTime(A12Archives entity, @MappingTarget A12ArchivesDto dto) {
    }
}
