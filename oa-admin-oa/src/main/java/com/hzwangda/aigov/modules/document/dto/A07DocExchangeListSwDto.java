package com.hzwangda.aigov.modules.document.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

@Data
@ApiModel(value = "公文交换-收文-列表")
public class A07DocExchangeListSwDto {
    private Long id;

    @ApiModelProperty(value = "公文流转单id")
    private Long gwid;

    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "签收状态(未签收/已签收)")
    private String qszt;

    @ApiModelProperty(value = "转入内部收文状态(未转入/已转入)")
    private String zrzt;

    @Column(name = "qslx")
    @ApiModelProperty(value = "签收类型(zs：主送,cs:抄送)")
    private String qslx;

    @ApiModelProperty(value = "公文种类(公文,会议)")
    private String gwzl;

    @ApiModelProperty(value = "发文单位")
    private String fwdw;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "来文日期")
    private Date lwrq;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "签收日期")
    private Date qsrq;

    @ApiModelProperty(value = "1撤回")
    private Integer revoke;

    @ApiModelProperty(value = "缓急程度")
    private String hj;
}
