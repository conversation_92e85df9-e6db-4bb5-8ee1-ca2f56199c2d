package com.hzwangda.aigov.modules.document.entity;

import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 住建局公务用车审批
 *
 * <AUTHOR>
 * @date 2021/7/15 上午11:18
 */
@Data
@Entity
@Table(name = "a07_document_zjjgwyc_sp")
public class A07DocumentZjjgwycSp extends BaseBpmDomain implements Serializable {

    @Column(name = "sq_bm", length = 2000)
    @ApiModelProperty(value = "申请部门")
    private String sqBm;

    @Column(name = "sqr")
    @ApiModelProperty(value = "申请人")
    private String sql;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @Column(name = "lxdh")
    @ApiModelProperty(value = "联系电话")
    private String lxdh;

    @Column(name = "ycsj")
    @ApiModelProperty(value = "用车时间")
    private String ycsj;

    @Column(name = "xclx")
    @ApiModelProperty(value = "行车路线")
    private String xclx;

    @Column(name = "ycsy")
    @ApiModelProperty(value = "用车事由")
    private String ycsy;

    @Column(name = "clap")
    @ApiModelProperty(value = "车辆安排")
    private String clap;

    @ApiModelProperty(value = "科室负责人意见")
    @Lob
    private String yjksfzr;

    @ApiModelProperty(value = "领导签批")
    @Lob
    private String yjLdqp;
}
