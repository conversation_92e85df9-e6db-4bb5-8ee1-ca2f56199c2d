package com.hzwangda.aigov.modules.archive.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.hzwangda.aigov.modules.archive.dto.A12DirectoryDto;
import com.hzwangda.aigov.modules.archive.entity.A12Directory;
import com.hzwangda.aigov.modules.archive.mapstruct.A12DirectoryMapper;
import com.hzwangda.aigov.modules.archive.repository.A12ArchivesRepository;
import com.hzwangda.aigov.modules.archive.repository.A12DirectoryRepository;
import com.hzwangda.aigov.modules.archive.service.A12DirectoryService;
import com.hzwangda.aigov.oa.constant.AuthorityConstant;
import com.wangda.oa.config.ElPermissionConfig;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.extension.domain.WdLine;
import com.wangda.oa.modules.extension.repository.WdLineRepository;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.system.service.mapstruct.UserMapper;
import com.wangda.oa.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/9/7 下午4:45
 **/
@Service
@RequiredArgsConstructor
public class A12DirectoryServiceImpl implements A12DirectoryService {

    private final A12DirectoryRepository a12DirectoryRepository;

    private final A12ArchivesRepository a12ArchivesRepository;

    private final UserRepository userRepository;

    private final UserMapper userMapper;

    private final WdLineRepository wdLineRepository;

    private final A12DirectoryMapper a12DirectoryMapper;

    private final ElPermissionConfig elPermissionConfig;

    @Override
    public A12DirectoryDto getDirectoryByDeptId(Long deptId) {
        return a12DirectoryMapper.toDto(a12DirectoryRepository.findDistinctFirstByDeptId(deptId));
    }

    @Override
    public List<A12DirectoryDto> getMyDirectory() throws Exception {
        User user = userRepository.findByUsername(SecurityUtils.getCurrentUsername());
        UserDto userDto = userMapper.toDto(user);
        if (userDto.getDept() == null) {
            throw new BadRequestException("用户部门不存在");
        }

        //根据人的id或者部门id查询条线记录
        List<WdLine> wdLineList = wdLineRepository.findByKeyIdIn(new ArrayList() {{
            add(userDto.getDept().getId());
            add(userDto.getId());
        }});
        if (CollectionUtil.isEmpty(wdLineList)) {
            return null;
            //throw new BadRequestException("该用户条线记录不存在");
        }

        //创建当前人在条线中可以看到的部门id集合
        List<Long> deptIds = new ArrayList() {{
            add(userDto.getDept().getId());
        }};
        //根据父类id和类型查询数据
        List<WdLine> list = wdLineRepository.findByPidInAndType(wdLineList.stream().map(WdLine::getId).filter(x -> x != null).collect(Collectors.toList()), 1);
        while (CollectionUtil.isNotEmpty(list)) {
            //存在子类则放入部门id集合
            List<Long> keyIds = list.stream().map(WdLine::getKeyId).filter(x -> x != null).collect(Collectors.toList());
            deptIds.addAll(keyIds);
            list = wdLineRepository.findByPidInAndType(keyIds, 1);
        }
        //办公室
        List<A12Directory> directoryList;
        if (deptIds.contains(10029L) || elPermissionConfig.check(AuthorityConstant.ARCHIVES_ADMIN)) {
            //办公室看到所有
            directoryList = a12DirectoryRepository.findAll(Sort.by("sort").ascending());
        } else {
            //根据部门id查询目录表
            directoryList = a12DirectoryRepository.findByDeptIdInOrderBySortAsc(deptIds);
        }
        List<A12DirectoryDto> directoryDtoList = a12DirectoryMapper.toDto(directoryList);
        directoryDtoList = buildChildrenTree(directoryDtoList);
        return directoryDtoList;
    }

    @Override
    public Boolean createOrUpdate(A12Directory resources) {
        if (resources.getId() != null) {
            A12Directory directory = a12DirectoryRepository.findById(resources.getId()).orElse(null);
            if (directory == null) {
                throw new BadRequestException("文件档案目录不存在！");
            }

            directory.copy(resources);
            a12DirectoryRepository.save(directory);
        } else {
            a12DirectoryRepository.save(resources);
        }
        return true;
    }

    @Override
    public Boolean delete(Long[] ids) {
        for (Long id : ids) {
            A12Directory directory = a12DirectoryRepository.findById(id).orElse(null);
            if (directory == null) {
                throw new BadRequestException("文件档案目录不存在！");
            }
            Integer count = a12DirectoryRepository.countByPid(id);
            if (count != null && count > 0) {
                throw new BadRequestException("文件档案存在子目录不能删除！");
            }
            Integer archivesCount = a12ArchivesRepository.countByDirectoryId(id);
            if (archivesCount != null && archivesCount > 0) {
                throw new BadRequestException("目录中存在文件不能删除！");
            }
            a12DirectoryRepository.delete(directory);
        }
        return true;
    }

    /**
     * 递归构建树
     *
     * @param directoryDtos
     * @return
     */
    private List<A12DirectoryDto> buildChildrenTree(List<A12DirectoryDto> directoryDtos) {
        for (A12DirectoryDto directoryDto : directoryDtos) {
            List<A12Directory> directories = a12DirectoryRepository.findByPidOrderBySortAsc(directoryDto.getId());
            if (CollectionUtil.isNotEmpty(directories)) {
                List<A12DirectoryDto> childrenDirectorieDto = a12DirectoryMapper.toDto(directories);
                directoryDto.setChild(childrenDirectorieDto);
                buildChildrenTree(childrenDirectorieDto);
            }
        }
        return directoryDtos;
    }
}
