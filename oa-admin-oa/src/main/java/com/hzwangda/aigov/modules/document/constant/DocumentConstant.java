package com.hzwangda.aigov.modules.document.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DocumentConstant {

    /**
     * 查询状态-待办
     */
    public static final int QUERY_STATUS_DB = 0;

    /**
     * 查询状态-待阅
     */
    public static final int QUERY_STATUS_DY = 3;

    /**
     * 检索-日期-不限
     */
    public static final int RETRIEVE_DATE_BX = 0;

    /**
     * 检索-日期-24小时内
     */
    public static final int RETRIEVE_DATE_24H = 1;

    /**
     * 检索-日期-近3天
     */
    public static final int RETRIEVE_DATE_3DAY = 2;

    /**
     * 检索-日期-近1个月
     */
    public static final int RETRIEVE_DATE_1M = 3;

    /**
     * 检索-日期-自定义
     */
    public static final int RETRIEVE_DATE_CUSTOM = 4;

    /**
     * 公文管理
     */
    public static final List<String> PROCESS_DEFINITION_KEY_LIST = new ArrayList() {{
        //区府办
        add("qfb_sw");
        add("qfb_cgd");
        // 统战部
        add("tzb_ldps");

        add("system_gdfq");
        add("qfb_documentFw");
        add("ty_documentPost");
        add("ty_docAddressee");
        add("documentPost");
        add("documentAddressee");
        add("nxfw_czdbbj");
        add("bigDataAddressee");
        add("meetingNotice");
        add("noticeAddressee");
        add("qfb_gzspAddressee");
        add("qfb_czdbxzbz");
        add("qfb_bigDatafzgljFw");
        add("a07_document_xzbdz_fw");
        add("tzbDocumentAddressee");
        add("documentLdps");
        add("documentQpbl");
        add("documentXxgksq");
        add("documentConfidential");
        add("documentSign");
        // 政协
        add("qzx_docAddressee");
        // 和孚
        add("hfz_docAddressee");
    }};

    /**
     * 行政管理
     */
    public static final List<String> PROCESS_DEFINITION_XZKEY_LIST = new ArrayList() {{
        add("zjj_gwycApprove");
        add("zjj_yyApprove");
        add("jkq_yyApprove");
        add("slz_yyApprove");
        add("hfz_yjglyyApprove");
        add("hfz_jzgcyyApprove");
        add("hfz_sjytyyApprove");
        add("hfz_lhgsyyApprove");
        add("hfz_lyfzyyApprove");
        add("hfz_gwjcyyApprove");
        add("hfz_rmzfyyApprove");
        add("hfz_wyhyyApprove");
        add("hfz_gwjcwyyApprove");
        add("hfz_gwpcApprove");
        add("hfz_czjsyyApprove");
        add("hfz_xwqyyyApprove");
        add("hfz_fhzjyyApprove");
        add("hfz_bmzxyyApprove");
        add("fgj_gzyyApprove");
        add("fgj_ycfApprove");
        add("fgj_ycfzxApprove");
        add("fgj_gzyyzxApprove");
        add("fgj_freeTeamWork");
        add("fgj_gwycApprove");
        add("fgj_yyzxApprove");
        add("fgj_qjzxApprove");
        add("fgj_gwycApprove");
        add("fgj_qjApprove");
        add("fgj_yyApprove");
        add("nxz_yyApprove");
        add("dag_yyApprove");
        add("dag_gwycApprove");
        add("xcjd_djwpApprove");
        add("xcjt_yyApprove");
        add("xcjt_gwjcApprove");
        add("xcjt_gwpcApprove");
        add("jgjd_gwjcApprove");
        add("jgjd_yyApprove");
        add("czj_djwpApprove");
        add("czj_yyApprove");
        add("czj_gwjcApprove");
    }};

    /**
     * 发文管理
     */
    public static final List<String> PROCESS_DEFINITION_KEY_FW_LIST = new ArrayList() {{
        // 区府办
        add("qfb_documentFw");
        add("qfb_bigDatafzgljPost");
        add("qfb_czdbxzbz");
        add("qfb_cgd");


        add("ty_documentPost");
        add("qfb_gzspAddressee");
        add("documentPost");
        add("nxfw_czdbbj");

        //和孚镇发文
        add("hfz_docPost");
    }};

    /**
     * 收文管理
     */
    public static final List<String> PROCESS_DEFINITION_KEY_SW_LIST = new ArrayList() {{
        //区府办
        add("qfb_sw");
        add("meetingNotice");
        add("noticeAddressee");
        // 统战部
        add("tzb_ldps");

        add("documentAddressee");
        add("ty_docAddressee");
        // 政协
        add("qzx_docAddressee");
        // 和孚
        add("hfz_docAddressee");

    }};

    /**
     * 行政服务
     */
    public static final List<String> PROCESS_DEFINITION_XZFW_KEY_LIST = new ArrayList() {{
        add("qfb_seal_use");//区府办用印
        add("qfb_leave");//区府办请假
        add("GoodsReceive");//物品领用
        add("ty_leave");//通用请假
        add("ty_seal_use");//通用用印
        add("system_gdfq");//工单申请
        add("businessTrip");//公差备案
        add("travelExpenses");//出差报销
        add("rentalCars");//用车审批
        add("leaveApproval");//引私请假
        add("workOvertime");//加班备案
        add("jobEntry");//入职管理
        add("jobLeave");//调离管理
        add("officeSupplies");//物品领用
        add("sealApproval");//用印审批
        add("toleranceReimbursement");//公差报销
        add("zqtyzx");//知情同意执行

        /*----------------------和孚及其他单位行政流程----------------------*/
        add("zjj_gwycApprove");
        add("zjj_yyApprove");
        add("jkq_yyApprove");
        add("slz_yyApprove");
        add("hfz_yjglyyApprove");
        add("hfz_jzgcyyApprove");
        add("hfz_sjytyyApprove");
        add("hfz_lhgsyyApprove");
        add("hfz_lyfzyyApprove");
        add("hfz_gwjcyyApprove");
        add("hfz_rmzfyyApprove");
        add("hfz_wyhyyApprove");
        add("hfz_gwjcwyyApprove");
        add("hfz_gwpcApprove");
        add("hfz_czjsyyApprove");
        add("hfz_xwqyyyApprove");
        add("hfz_fhzjyyApprove");
        add("hfz_bmzxyyApprove");
        add("fgj_gzyyApprove");
        add("fgj_ycfApprove");
        add("fgj_ycfzxApprove");
        add("fgj_gzyyzxApprove");
        add("fgj_freeTeamWork");
        add("fgj_gwycApprove");
        add("fgj_yyzxApprove");
        add("fgj_qjzxApprove");
        add("fgj_gwycApprove");
        add("fgj_qjApprove");
        add("fgj_yyApprove");
        add("nxz_yyApprove");
        add("dag_yyApprove");
        add("dag_gwycApprove");
        add("xcjd_djwpApprove");
        add("xcjt_yyApprove");
        add("xcjt_gwjcApprove");
        add("xcjt_gwpcApprove");
        add("jgjd_gwjcApprove");
        add("jgjd_yyApprove");
        add("czj_djwpApprove");
        add("czj_yyApprove");
        add("czj_gwjcApprove");
        /*----------------------和孚及其他单位行政流程----------------------*/

        /*-----驻外招商局行政流程-----*/
        add("zwzsLeave");
        /*-----驻外招商局行政流程-----*/
    }};
    /**
     * 信息服务
     */
    public static final List<String> PROCESS_DEFINITION_XXFW_KEY_LIST = new ArrayList() {{
        add("electronicBulletin");//电子公告
        add("newspaperToday");//今日择报
        add("workCommunication");//工作交流
    }};

    /**
     * 公文交换模块
     */
    public static final List<String> PROCESS_DEFINITION_DocExchange_KEY_LIST = new ArrayList() {{
        add("documentGwlz");//公文交换
    }};

}
