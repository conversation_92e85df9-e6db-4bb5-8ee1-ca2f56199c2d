package com.hzwangda.aigov.modules.appControl.repository;

import com.hzwangda.aigov.modules.appControl.domain.Z08SelfApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;


public interface SelfApplicationRepository extends JpaRepository<Z08SelfApplication, Long>, JpaSpecificationExecutor<Z08SelfApplication> {

    /**
     * 根据名称模糊查询
     */
    List<Z08SelfApplication> findByNameLike(String name);

    List<Z08SelfApplication> findByType(String type);
}
