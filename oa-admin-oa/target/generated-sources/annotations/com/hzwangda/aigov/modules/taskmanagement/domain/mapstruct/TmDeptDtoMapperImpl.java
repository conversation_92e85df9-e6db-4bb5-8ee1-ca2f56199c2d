package com.hzwangda.aigov.modules.taskmanagement.domain.mapstruct;

import com.hzwangda.aigov.modules.taskmanagement.domain.dto.TmDeptDto;
import com.wangda.oa.modules.system.domain.Dept;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class TmDeptDtoMapperImpl implements TmDeptDtoMapper {

    @Override
    public TmDeptDto toDto(Dept entity) {
        if ( entity == null ) {
            return null;
        }

        TmDeptDto tmDeptDto = new TmDeptDto();

        tmDeptDto.setDeptSort( entity.getDeptSort() );
        tmDeptDto.setId( entity.getId() );
        tmDeptDto.setName( entity.getName() );

        return tmDeptDto;
    }

    @Override
    public List<TmDeptDto> toDto(List<Dept> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmDeptDto> list = new ArrayList<TmDeptDto>( entityList.size() );
        for ( Dept dept : entityList ) {
            list.add( toDto( dept ) );
        }

        return list;
    }

    @Override
    public Dept toEntity(TmDeptDto dto) {
        if ( dto == null ) {
            return null;
        }

        Dept dept = new Dept();

        dept.setDeptSort( dto.getDeptSort() );
        dept.setId( dto.getId() );
        dept.setName( dto.getName() );

        return dept;
    }

    @Override
    public List<Dept> toEntity(List<TmDeptDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Dept> list = new ArrayList<Dept>( dtoList.size() );
        for ( TmDeptDto tmDeptDto : dtoList ) {
            list.add( toEntity( tmDeptDto ) );
        }

        return list;
    }
}
