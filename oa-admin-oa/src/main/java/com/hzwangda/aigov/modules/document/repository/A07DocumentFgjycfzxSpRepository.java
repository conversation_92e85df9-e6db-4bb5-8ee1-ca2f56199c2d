/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.modules.document.repository;

import com.hzwangda.aigov.modules.document.entity.A07DocumentFgjZyxt;
import com.hzwangda.aigov.modules.document.entity.A07DocumentFgjycfSp;
import com.hzwangda.aigov.modules.document.entity.A07DocumentFgjycfzxSp;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2021-06-04
 **/
@Repository
public interface A07DocumentFgjycfzxSpRepository extends JpaRepository<A07DocumentFgjycfzxSp, Long>, JpaSpecificationExecutor<A07DocumentFgjycfzxSp> {

    @Query(value = "SELECT distinct(jbry),count(jbry) from a07_document_fgjycfzx_sp where bpm_status='TG' and jb_start > :jbStart and jb_end < :jbEnd group by jbry" ,nativeQuery = true)
    List<Map<String,Integer>> findAllByBpmStatus(Timestamp jbStart, Timestamp jbEnd);

    @Query(value = "SELECT distinct(jbry),count(jbry) from a07_document_fgjycfzx_sp where bpm_status='TG' and jbry like %:jbry% and jb_start > :jbStart and jb_end < :jbEnd group by jbry" ,nativeQuery = true)
    List<Map<String,Integer>> findAllByBpmStatusAndJbry(String jbry,Timestamp jbStart,Timestamp jbEnd);

    @Query(value = "SELECT * from a07_document_fgjycf_sp where bpm_status='TG' and jb_start > :jbStart and jb_end < :jbEnd order by jb_start" ,nativeQuery = true)
    List<A07DocumentFgjycfzxSp> findByBpmStatus(Timestamp jbStart, Timestamp jbEnd);


}
