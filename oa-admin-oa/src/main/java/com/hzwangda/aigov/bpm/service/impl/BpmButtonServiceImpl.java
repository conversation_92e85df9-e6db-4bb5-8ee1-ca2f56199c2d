/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hzwangda.aigov.bpm.service.impl;

import com.hzwangda.aigov.bpm.domain.criteria.BpmButtonQueryCriteria;
import com.hzwangda.aigov.bpm.domain.entity.BpmButton;
import com.hzwangda.aigov.bpm.repository.BpmButtonRepository;
import com.hzwangda.aigov.bpm.service.BpmButtonService;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.ValidationUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2021-05-14
 **/
@Service
@RequiredArgsConstructor
public class BpmButtonServiceImpl implements BpmButtonService {

    private final BpmButtonRepository bpmButtonRepository;

    @Override
    public Map<String, Object> queryAll(BpmButtonQueryCriteria criteria, Pageable pageable) {
        Page<BpmButton> page = bpmButtonRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page);
    }

    @Override
    @Transactional
    public BpmButton findById(Long id) {
        BpmButton bpmButton = bpmButtonRepository.findById(id).orElseGet(BpmButton::new);
        ValidationUtil.isNull(bpmButton.getId(), "BpmButton", "id", id);
        return bpmButton;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BpmButton createOrUpdate(BpmButton resources) {
        return bpmButtonRepository.save(resources);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            bpmButtonRepository.deleteById(id);
        }
    }

}
