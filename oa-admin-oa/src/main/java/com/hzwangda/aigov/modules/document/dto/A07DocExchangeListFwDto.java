package com.hzwangda.aigov.modules.document.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGwlzUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "公文交换-发文-列表")
public class A07DocExchangeListFwDto {

    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    protected Date createDate;
    @LastModifiedDate
    @Column(
            name = "modified_date"
    )
    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    protected Date modifiedDate;
    @CreatedBy
    @Column(
            name = "creator_id"
    )
    protected Long creatorId;
    private Long id;
    @ApiModelProperty(value = "标题")
    private String bt;
    @ApiModelProperty(value = "公文状态(0待发,1已发,2撤回)")
    private String gwzt;
    @ApiModelProperty(value = "公文种类(公文,联合发文)")
    private String gwzl;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "发文日期")
    private Timestamp fwrq;

    private List<A07DocumentGwlzUser> noSignRecords;

    @ApiModelProperty(value = "缓急(特急,加急,平件)")
    private String hj;

    @ApiModelProperty(value = "状态(未全部签收，已全部签收)")
    private String status;
}
