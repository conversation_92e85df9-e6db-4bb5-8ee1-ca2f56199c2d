package com.hzwangda.aigov.modules.document.service;

import com.hzwangda.aigov.modules.document.dto.A07DocumentGwQueryCriteria;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface A01ReadDocService {


    Object getFwReadList(A07DocumentGwQueryCriteria criteria, Pageable pageable);

    Object getSwReadList(A07DocumentGwQueryCriteria criteria, Pageable pageable);

    Object getAllReadList(A07DocumentGwQueryCriteria criteria, Pageable pageable);

    String read(List<String> bpmInstanceIds);

    List<Map<String, String>> getFlowDocRead(A07DocumentGwQueryCriteria criteria);

    Map<String, Integer> getFlowDocReadCount(A07DocumentGwQueryCriteria criteria);
}
