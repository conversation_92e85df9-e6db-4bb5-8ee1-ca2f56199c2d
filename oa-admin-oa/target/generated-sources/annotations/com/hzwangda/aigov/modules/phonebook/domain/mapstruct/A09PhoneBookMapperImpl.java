package com.hzwangda.aigov.modules.phonebook.domain.mapstruct;

import com.hzwangda.aigov.modules.phonebook.domain.dto.A09PhoneBookDto;
import com.hzwangda.aigov.modules.phonebook.domain.entity.A09PhoneBook;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A09PhoneBookMapperImpl implements A09PhoneBookMapper {

    @Override
    public A09PhoneBookDto toDto(A09PhoneBook entity) {
        if ( entity == null ) {
            return null;
        }

        A09PhoneBookDto a09PhoneBookDto = new A09PhoneBookDto();

        a09PhoneBookDto.setCreateBy( entity.getCreateBy() );
        a09PhoneBookDto.setModifiedDate( entity.getModifiedDate() );
        a09PhoneBookDto.setOfficeDirectorName( entity.getOfficeDirectorName() );
        a09PhoneBookDto.setOfficeDirectorPhone( entity.getOfficeDirectorPhone() );
        a09PhoneBookDto.setSenderName( entity.getSenderName() );
        a09PhoneBookDto.setSenderPhone( entity.getSenderPhone() );
        a09PhoneBookDto.setUnitName( entity.getUnitName() );

        return a09PhoneBookDto;
    }

    @Override
    public List<A09PhoneBookDto> toDto(List<A09PhoneBook> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A09PhoneBookDto> list = new ArrayList<A09PhoneBookDto>( entityList.size() );
        for ( A09PhoneBook a09PhoneBook : entityList ) {
            list.add( toDto( a09PhoneBook ) );
        }

        return list;
    }

    @Override
    public A09PhoneBook toEntity(A09PhoneBookDto dto) {
        if ( dto == null ) {
            return null;
        }

        A09PhoneBook a09PhoneBook = new A09PhoneBook();

        a09PhoneBook.setCreateBy( dto.getCreateBy() );
        a09PhoneBook.setModifiedDate( dto.getModifiedDate() );
        a09PhoneBook.setOfficeDirectorName( dto.getOfficeDirectorName() );
        a09PhoneBook.setOfficeDirectorPhone( dto.getOfficeDirectorPhone() );
        a09PhoneBook.setSenderName( dto.getSenderName() );
        a09PhoneBook.setSenderPhone( dto.getSenderPhone() );
        a09PhoneBook.setUnitName( dto.getUnitName() );

        return a09PhoneBook;
    }

    @Override
    public List<A09PhoneBook> toEntity(List<A09PhoneBookDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A09PhoneBook> list = new ArrayList<A09PhoneBook>( dtoList.size() );
        for ( A09PhoneBookDto a09PhoneBookDto : dtoList ) {
            list.add( toEntity( a09PhoneBookDto ) );
        }

        return list;
    }
}
