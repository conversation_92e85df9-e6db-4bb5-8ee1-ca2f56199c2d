package com.hzwangda.aigov.modules.addresslist.mapstruct;

import com.hzwangda.aigov.modules.addresslist.domain.dto.UnitAddressListDto;
import com.hzwangda.aigov.modules.addresslist.domain.entity.UnitAddressList;
import com.hzwangda.aigov.modules.form.convert.IdToDeptConvert;
import com.hzwangda.aigov.modules.form.convert.IdToUserConvert;
import com.wangda.oa.base.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", uses = {IdToUserConvert.class, IdToDeptConvert.class}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UnitAddressListMapper extends BaseMapper<UnitAddressListDto, UnitAddressList> {


}
