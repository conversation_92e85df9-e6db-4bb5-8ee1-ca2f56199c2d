package com.hzwangda.aigov.modules.form.rest;

import com.hzwangda.aigov.modules.form.domain.A23ReportData;
import com.hzwangda.aigov.modules.form.dto.A23ReportFormDataDto;
import com.hzwangda.aigov.modules.form.dto.ReportDataQueryCriteria;
import com.hzwangda.aigov.modules.form.service.ReportDataService;
import com.wangda.oa.config.ElPermissionConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;
import java.util.Set;

@RequestMapping("api/report/data")
@RestController
@RequiredArgsConstructor
@Api(tags = "数据填报数据管理")
public class ReportDataController {

    private final ReportDataService reportDataService;

    @ApiOperation(value = "根据id查找记录", notes = "根据id查找记录")
    @GetMapping("{id}")
    public A23ReportData queryByInstId(@PathVariable Long id) {

        return reportDataService.findById(id);
    }

    @ApiOperation(value = "创建数据", notes = "创建数据")
    @PostMapping
    public A23ReportData create(@RequestBody A23ReportData resources) {

        return reportDataService.create(resources);
    }

    @ApiOperation(value = "删除数据", notes = "删除数据")
    @PostMapping("delete")
    public void delete(@RequestBody Set<Long> ids) {
        reportDataService.delete(ids);
    }

    @ApiOperation(value = "查询我填报的表单 如果是超级管理员则可以查看全部", notes = "查询我填报的表单 如果是超级管理员则可以查看全部")
    @GetMapping
    public Page<A23ReportFormDataDto> query(ReportDataQueryCriteria criteria, Pageable pageable) {
        if(Objects.isNull(criteria.getInstId()))return null;
        ElPermissionConfig elPermissionConfig = new ElPermissionConfig();
        Boolean admin = elPermissionConfig.check("admin");
        if (admin) {
            return reportDataService.query(criteria, pageable);
        } else {
            //查询自己属于自己的记录
            return reportDataService.queryByMy(criteria, pageable);
        }

    }


    @ApiOperation(value = "查询各单位填报数据")
    @GetMapping("queryAll")
    public Page<A23ReportFormDataDto> queryAll(ReportDataQueryCriteria criteria, Pageable pageable) {
        return reportDataService.queryAll(criteria, pageable);
    }
}
