package com.hzwangda.aigov.config;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/12/3
 * @description 多数据源配置
 */
// 未启用注解，即该配置不会被加载，不使用多数据源。
@Configuration
public class DynamicDataSourceConfig {

    /**
     * 创建 DataSource Bean
     **/
    @Primary
    @Bean(value = "primaryDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.druid.postgresql")
    public DataSource primaryDataSource() {
        DataSource dataSource = DruidDataSourceBuilder.create().build();
        return dataSource;
    }

    @Bean(value = "sqlserverDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.druid.sqlserver")
    @ConditionalOnProperty(prefix = "multi-data-source", value = "sqlserver.enable", havingValue = "true")
    public DataSource oracleDataSource() {
        DataSource dataSource = DruidDataSourceBuilder.create().build();
        return dataSource;
    }
}
