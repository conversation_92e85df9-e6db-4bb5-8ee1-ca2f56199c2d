package com.hzwangda.aigov.modules.document.enums;

import com.alibaba.fastjson.annotation.JSONType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@ToString
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@JSONType(serializeEnumAsJavaBean = true)
public enum TableClassNameEnum {
    /**
     * 权限标识
     */
    GWJH("公文交换", "com.hzwangda.aigov.modules.document.entity.A07DocumentGwlz");


    private String name;
    private String value;


    public static TableClassNameEnum getByValue(String value) {
        for(TableClassNameEnum className : TableClassNameEnum.values()) {
            if(className.value.equals(value)) {
                return className;
            }
        }
        return null;
    }
}
