package com.hzwangda.aigov.modules.document.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.modules.document.entity.SysDeptUserBiz;
import com.hzwangda.aigov.modules.workflow.domain.form.ReferenceNumber;
import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.dto.StorageBizDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 动态信息-发文
 * <AUTHOR>
 * @date 2021/6/15 上午10:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class A07AllFwDto extends BaseBpmDomain implements Serializable {

    @ApiModelProperty(value = "文号")
    private ReferenceNumber gwwh;

    @ApiModelProperty(value = "文号展示")
    private String gwwhStr;

    @ApiModelProperty(value = "类型")
    private String moduleType;

    @ApiModelProperty(value = "缓急(普通,急)")
    private String hj;

    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "签发-json格式")
    private String yjQf;

    @ApiModelProperty(value = "会签-json格式")
    private String yjHq;

    @ApiModelProperty(value = "分管主任审核意见-json格式")
    private String yjFgzrsh;

    @ApiModelProperty(value = "秘书科审核意见-json格式")
    private String yjMsksh;

    @ApiModelProperty(value = "处室审核意见-json格式")
    private String yjCssh;

    @ApiModelProperty(value = "正文")
    private StorageBizDto zw;

    @ApiModelProperty(value = "附件")
    private List<StorageBizDto> fj;

    @ApiModelProperty(value = "主送单位")
    private List<SysDeptUserBiz> zsdwDepts;

    @ApiModelProperty(value = "主送单位描述")
    private String zsdwms;

    @ApiModelProperty(value = "抄送单位")
    private List<SysDeptUserBiz> csdwDepts;

    @ApiModelProperty(value = "抄送单位描述")
    private String csdwms;

    @ApiModelProperty(value = "主办部门")
    private String zbbm;

    @ApiModelProperty(value = "拟稿人")
    private String ngr;

    @ApiModelProperty(value = "拟稿单位")
    private String ngdw;

    @ApiModelProperty(value = "印发份数")
    private Integer yffs;

    @ApiModelProperty(value = "印发日期")
    private Date yfrq;

    @ApiModelProperty(value = "内网发布(是,否)")
    private String nwfb;

    @ApiModelProperty(value = "校对人")
    private String jdr;

    @ApiModelProperty(value = "外网发布栏目code")
    private String wwfblmCode;

    @ApiModelProperty(value = "公开类型(主动公开,依申请公开,不予公开)")
    private String gklx;

    @ApiModelProperty(value = "理由")
    private String ly;

    @ApiModelProperty(value = "备注")
    private String bz;

    @ApiModelProperty(value = "会签单位")
    private String hqdw;

    @ApiModelProperty(value = "主题词")
    private String ztc;

    @ApiModelProperty("任务环节名称")
    private String taskName;

    @ApiModelProperty("任务处理人名称")
    private String assigneeName;

    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty("办理时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date dealDate;

    @ApiModelProperty(value = "办理期限")
    @JSONField(format = "yyyy-MM-dd")
    private Date deadlineDate;

    @ApiModelProperty(value = "归档状态 0待归档，1预归档,2暂不归档，3不归档,4已归档，5归档中，-1移交失败")
    private String fileStatus;

    @ApiModelProperty(value = "是否关注")
    private Boolean isConcern;

    @ApiModelProperty(value = "是否抄送0是1否")
    private Integer isCarbonCopy;

    @ApiModelProperty(value = "是否可以办理0否1是")
    private Integer isHandle;

    @ApiModelProperty(value = "是否逾期")
    private Boolean isOverdue;

    @ApiModelProperty(value = "是否锁定")
    private Boolean isSuspended;

    @ApiModelProperty(value = "是否分发")
    private Boolean isDistribute;

    @JSONField(serialize = false)
    @ApiModelProperty(value = "排序")
    private Date sorted;

    @JSONField(format = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "待阅已阅接收时间")
    private Date createDate;

    @ApiModelProperty(value = "最后办理时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date endTime;

    public void setId(BigInteger id) {
        this.id = id.longValue();
    }

    public void setIsOverdue(Object overdue) {
        if(Objects.isNull(overdue)) {
            this.isOverdue = false;
            return;
        }

        if(overdue instanceof Integer || overdue instanceof BigInteger) {
            this.isOverdue = BooleanUtils.toBoolean(((Number) overdue).intValue());
            return;
        }

        this.isOverdue = BooleanUtils.toBooleanObject(overdue.toString());
    }

    public void setIsSuspended(Object isSuspended) {
        if(Objects.isNull(isSuspended)) {
            this.isSuspended = false;
        }else if(isSuspended instanceof Integer || isSuspended instanceof BigInteger) {
            this.isSuspended = BooleanUtils.toBoolean(((Number) isSuspended).intValue());
        }else {
            this.isSuspended = BooleanUtils.toBoolean(isSuspended.toString());
        }
    }
}
