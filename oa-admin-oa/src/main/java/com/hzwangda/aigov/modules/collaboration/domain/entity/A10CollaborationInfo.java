package com.hzwangda.aigov.modules.collaboration.domain.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @program: oa-mgt-server
 * @description: 工作协同
 * @author: liux
 * @create: 2021-08-11 18:09
 */
@Entity
@Getter
@Setter
@Table(name = "a10_collaboration_info")
@ApiModel(value = "工作协同")
public class A10CollaborationInfo extends BaseEntity {

    @Id
    @NotNull(groups = {Update.class})
    @ApiModelProperty(value = "id")
    private Long id;

    @Column(name = "subject", length = 2000)
    @ApiModelProperty(value = "主题")
    private String subject;

    @Column(name = "urgent", length = 16)
    @ApiModelProperty(value = "紧急程度(是否加急，1：是，0：否)")
    private String urgent;

    @Column(name = "content", columnDefinition = "text")
    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "相关附件")
    @ElementCollection
    @CollectionTable(name = "sys_storage_biz", joinColumns = {
            @JoinColumn(name = "biz_id"),
    }, foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
    @Column(name = "storage_id")
    private List<Long> attachments;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A10CollaborationInfo.fj'")
    private List<StorageBiz> fj;


    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "info_id")
    @ApiModelProperty(value = "操作记录")
    private List<A10CollaborationHandleLog> handleLogs;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "info_id")
    @ApiModelProperty(value = "接收用户")
    private List<A10CollaborationAssignee> assignees;

    @Column(name = "status")
    @ApiModelProperty(value = "状态[草稿(draft),已发送(sent)]")
    private String status;

    @Column(name = "send_time")
    @ApiModelProperty(value = "发送时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    @Transient
    @ApiModelProperty(value = "短信通知")
    private Boolean sms;

    @Transient
    @ApiModelProperty(value = "是否浙政钉通知")
    private Boolean ding;

    @Transient
    @ApiModelProperty(value = "发起人的用户类型 1.个人用户，2.单位用户")
    private int userType=1;
    @OneToOne(mappedBy = "a10CollaborationInfo")
    private A10CollaborationLink link;

    @Column(
            name = "create_by",
            updatable = false
    )
    @ApiModelProperty(
            value = "创建人",
            hidden = true
    )
    private String createBy;

    public void setFj(List<StorageBiz> fj) {
        if(fj != null) {
            if(this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }
    public void copy(A10CollaborationInfo a10CollaborationInfo) {
        BeanUtil.copyProperties(a10CollaborationInfo, this, CopyOptions.create().setIgnoreNullValue(true));
    }

    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }
}
