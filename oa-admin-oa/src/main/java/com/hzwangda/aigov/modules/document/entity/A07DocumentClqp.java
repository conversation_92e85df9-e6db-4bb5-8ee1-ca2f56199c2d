package com.hzwangda.aigov.modules.document.entity;

import com.hzwangda.aigov.oa.domain.BaseBpmDomain;
import com.hzwangda.aigov.oa.domain.StorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Where;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 动态信息-签批办理单(厅内呈阅)
 * <AUTHOR>
 * @date 2021/6/15 上午10:18
 */
@Data
@Entity
@Table(name = "a07_document_clqp")
public class A07DocumentClqp extends BaseBpmDomain implements Serializable {

    @Column(name = "old_id")
    @ApiModelProperty(value = "老数据id(匹配是否存在)")
    private Long oldId;

    @ApiModelProperty(value = "归档状态")
    @Column(columnDefinition = "int default 0")
    private Integer fileStatus;

    @Column(name = "clmc")
    @ApiModelProperty(value = "材料名称")
    private String clmc;

    @Column(name = "yj_zyld", length = 2000)
    @ApiModelProperty(value = "主要领导意见-json格式")
    private String yjZyld;

    @Column(name = "yj_fgld", length = 2000)
    @ApiModelProperty(value = "分管领导意见-json格式")
    private String yjFgld;

    @Column(name = "yj_bgs", length = 2000)
    @ApiModelProperty(value = "办公室意见-json格式")
    private String yjBgs;

    @Column(name = "yj_hbcs", length = 2000)
    @ApiModelProperty(value = "会办处室意见-json格式")
    private String yjHbcs;

    @Column(name = "yj_ngr", length = 2000)
    @ApiModelProperty(value = "拟稿人意见-json格式")
    private String yjNgr;

    @Column(name = "yj_shr", length = 2000)
    @ApiModelProperty(value = "审核人意见-json格式")
    private String yjShr;

    @ApiModelProperty(value = "正文")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "zw", referencedColumnName = "id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private StorageBiz zw;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A07DocumentClqp.fj'")
    private List<StorageBiz> fj;

    public void setFj(List<StorageBiz> fj) {
        if(fj != null) {
            if(this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }

    public void setZw(StorageBiz zw) {
        if(Objects.nonNull(zw)) {
            this.zw = zw;
        }
        if(Objects.nonNull(this.zw) && StringUtils.isNotBlank(this.zw.getStorageId())) {
            if(this.id != null) {
                this.zw.setBizId(this.id.toString());
            }
        }else {
            this.zw = null;
        }
    }

    @Override
    @PrePersist
    @PreUpdate
    public void preCreateEntity() {
        super.preCreateEntity();
        if(!CollectionUtils.isEmpty(this.fj)) {
            for(StorageBiz storageBiz : this.fj) {
                if(Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                }
            }
        }
        if(Objects.nonNull(this.zw) && StringUtils.isNotBlank(this.zw.getStorageId())) {
            this.zw.setBizId(this.id.toString());
        }else {
            this.zw = null;
        }
    }
}
