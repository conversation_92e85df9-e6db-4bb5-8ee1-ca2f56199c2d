package com.hzwangda.aigov.modules.form.service.impl;

import com.google.common.collect.Lists;
import com.hzwangda.aigov.modules.form.domain.A23ReportForm;
import com.hzwangda.aigov.modules.form.domain.A23ReportFormInstantiate;
import com.hzwangda.aigov.modules.form.domain.A23ReportFormInstantiate.ReportType;
import com.hzwangda.aigov.modules.form.dto.A23ReportFormInstantiateDto;
import com.hzwangda.aigov.modules.form.dto.ReportFormInstQueryCriteria;
import com.hzwangda.aigov.modules.form.mapper.A23ReportFormInstantiateMapper;
import com.hzwangda.aigov.modules.form.repository.A23ReportDataRepository;
import com.hzwangda.aigov.modules.form.repository.A23ReportFormInstantiateRepository;
import com.hzwangda.aigov.modules.form.repository.A23ReportFormRepository;
import com.hzwangda.aigov.modules.form.service.ReportFormInstantiateService;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.ValidationUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

@RequiredArgsConstructor
@Service
public class ReportFormInstantiateServiceImpl implements ReportFormInstantiateService {

    private final A23ReportFormInstantiateRepository reportFormInstantiateRepository;

    private final A23ReportFormRepository formRepository;

    private final A23ReportFormInstantiateMapper mapper;

    private final A23ReportDataRepository reportDataRepository;

    @Transactional(readOnly = true)
    @Override
    public Page<A23ReportFormInstantiateDto> query(ReportFormInstQueryCriteria criteria, Pageable pageable) {
        return reportFormInstantiateRepository.findAll((root, query, cb) -> cb.and(cb.equal(root.get("enable"), true), QueryHelp.getPredicate(root, criteria, cb)), pageable).map(mapper::toDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Set<Long> ids) {
        for (Long id : ids) {
            reportDataRepository.deleteByInstantiateId(id.toString());
            reportFormInstantiateRepository.deleteById(id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public A23ReportFormInstantiateDto update(A23ReportFormInstantiate resources) {
        A23ReportFormInstantiate reportForm = reportFormInstantiateRepository.findById(resources.getId()).orElseGet(A23ReportFormInstantiate::new);
        ValidationUtil.isNull(reportForm.getId(), "A23ReportFormInstantiate", "id", resources.getId());
        return mapper.toDto(reportFormInstantiateRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public A23ReportFormInstantiateDto create(A23ReportFormInstantiate resources) {
        A23ReportForm form = formRepository.findById(resources.getForm().getId()).orElseThrow(() -> new BadRequestException("记录不存在"));
        resources.setForm(form);
        resources.setFormJson(form.getFormJson());
        resources.setTitle(form.getTitle());
        resources.setReportType(ReportType.single);
        resources.setCreateBy(SecurityUtils.getBindDeptUserName());
        return mapper.toDto(reportFormInstantiateRepository.save(resources));

    }

    @Override
    @Transactional(readOnly = true)
    public A23ReportFormInstantiate queryById(Long l) {

        return reportFormInstantiateRepository.findById(l).orElseThrow(() -> new BadRequestException("记录不存在"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reset(Long id) {
        A23ReportFormInstantiate instantiate = reportFormInstantiateRepository.findById(id).orElseThrow(() -> new BadRequestException("记录不存在"));
        instantiate.setEnable(!instantiate.getEnable());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<A23ReportFormInstantiateDto> batchSave(Set<Long> ids) {
        ArrayList<A23ReportFormInstantiateDto> lists = Lists.newArrayListWithCapacity(ids.size());
        for (Long id : ids) {
            A23ReportForm form = formRepository.findById(id).orElseThrow(() -> new BadRequestException("模版已被删除！请重新选择"));
            A23ReportFormInstantiate a23ReportFormInstantiate = new A23ReportFormInstantiate();
            a23ReportFormInstantiate.setReportType(ReportType.single);
            a23ReportFormInstantiate.setTitle(form.getTitle());
            a23ReportFormInstantiate.setFormJson(form.getFormJson());
            a23ReportFormInstantiate.setStartTime(new Date());
            a23ReportFormInstantiate.setCreateBy(SecurityUtils.getBindDeptUserName());
            reportFormInstantiateRepository.save(a23ReportFormInstantiate);
            lists.add(mapper.toDto(a23ReportFormInstantiate));
        }
        return lists;
    }

    @Override
    public A23ReportFormInstantiateDto queryByIdToDto(Long id) {
        return mapper.toDto(queryById(id));
    }
}
