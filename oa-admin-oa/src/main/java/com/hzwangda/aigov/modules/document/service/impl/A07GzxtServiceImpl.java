package com.hzwangda.aigov.modules.document.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzwangda.aigov.bpm.repository.A07DocumentgzxtRepository;
import com.hzwangda.aigov.modules.document.constant.DocumentConstant;
import com.hzwangda.aigov.modules.document.dto.A07SearchDtoCriteria;
import com.hzwangda.aigov.modules.document.dto.A08GzxtDto;
import com.hzwangda.aigov.modules.document.dto.A08GzxtQueryCriteria;
import com.hzwangda.aigov.modules.document.dto.FlowTaskInfoDto;
import com.hzwangda.aigov.modules.document.dto.mapstruct.A08GzxtMapper;
import com.hzwangda.aigov.modules.document.entity.A07DocumentGzxt;
import com.hzwangda.aigov.modules.document.entity.A08Gzxt;
import com.hzwangda.aigov.modules.document.repository.A08GzxtRepository;
import com.hzwangda.aigov.modules.document.service.A07GzxtService;
import com.hzwangda.aigov.modules.workflow.bo.GzxtUserListBO;
import com.hzwangda.aigov.modules.workflow.dto.AnnualConferenceRegistrationListDto;
import com.hzwangda.aigov.modules.workflow.dto.MyConferenceListDto;
import com.hzwangda.aigov.modules.workflow.enums.workflow.WorkflowType;
import com.hzwangda.aigov.oa.mapper.A07documentMapper;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.boot.platform.utils.PageUtil;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskDto;
import com.wangda.oa.modules.workflow.service.workflow.FlowTaskService;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import com.wangda.oa.utils.ValidationUtil;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class A07GzxtServiceImpl implements A07GzxtService {

    private final A07documentMapper a07documentMapper;

    private final A08GzxtRepository a08GzxtRepository;

    private final A08GzxtMapper a08GzxtMapper;

    private final A07DocumentgzxtRepository a07DocumentgzxtRepository;

    private final TaskService taskService;

    private final FlowTaskService flowTaskService;

    @Override
    public Map<String, Object> getXtList(A08GzxtQueryCriteria criteria, Pageable pageable) {
        A07SearchDtoCriteria searchDtoCriteria = new A07SearchDtoCriteria();
        searchDtoCriteria.setBt(criteria.getBt());
        searchDtoCriteria.setType(criteria.getStatus());
        Map<String, FlowTaskInfoDto> map = this.getProcessInstanceIdMap(criteria.getStatus(), WorkflowType.DOCUMENT_XT.getValue(), pageable, searchDtoCriteria);
        if (CollectionUtils.isEmpty(map)) {
            return com.wangda.boot.platform.utils.PageUtil.toPage(Page.empty());
        }
        criteria.setBpmInstanceId(map.keySet());
        Page<A08Gzxt> page = a08GzxtRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Page<A08GzxtDto> pageDto = page.map(a08GzxtMapper::toDto);
        pageDto.getContent().forEach(a08GzxtDto -> {
            FlowTaskInfoDto flowTaskInfoDto = map.get(a08GzxtDto.getBpmInstanceId());
            a08GzxtDto.setFlowTaskInfoDto(flowTaskInfoDto);
        });
        return PageUtil.toPage(pageDto);
    }

    @Override
    public A08GzxtDto getXtInfo(Long id) {
        A08Gzxt gzxt = a08GzxtRepository.findById(id).orElseGet(A08Gzxt::new);
        ValidationUtil.isNull(gzxt.getId(), "gzxt", "id", id);
        return a08GzxtMapper.toDto(gzxt);
    }

    @Override
    public Boolean deleteXt(List<Long> ids) {
        for (Long id : ids) {
            a08GzxtRepository.deleteById(id);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public A07DocumentGzxt createOrUpdateGzxt(A07DocumentGzxt resources) {
        if (resources.getId() == null) {
            return a07DocumentgzxtRepository.save(resources);
        } else {
            A07DocumentGzxt a07DocumentGzxt = a07DocumentgzxtRepository.findById(resources.getId()).orElseGet(A07DocumentGzxt::new);
            a07DocumentGzxt.copy(resources);
            return a07DocumentgzxtRepository.save(a07DocumentGzxt);
        }
    }

    @Override
    public MyConferenceListDto getGzxtUserList(GzxtUserListBO bo) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<AnnualConferenceRegistrationListDto> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(bo.getPageNo(), bo.getSize());
        IPage<AnnualConferenceRegistrationListDto> listDtoIPage = a07documentMapper.getUserGzxtList(page, bo);
        MyConferenceListDto dto = new MyConferenceListDto();
        dto.setList(listDtoIPage.getRecords());
        dto.setTotal(listDtoIPage.getTotal());
        return dto;
    }

    /**
     * 查询我的不同状态的流程实例id
     *
     * @param status
     * @param processDefinitionKey
     * @param pageable
     * @return
     */
    private Map<String, FlowTaskInfoDto> getProcessInstanceIdMap(Integer status, String processDefinitionKey, Pageable pageable, A07SearchDtoCriteria searchDto) {
        Map<String, List<FlowTaskInfoDto>> result;
        Map<String, FlowTaskInfoDto> returnResult = new HashMap<>();
        if (DocumentConstant.QUERY_STATUS_DB == status || DocumentConstant.QUERY_STATUS_DY == status) {
            //待办任务列表
            TaskQuery taskQuery = taskService.createTaskQuery()
                    .active()
                    .processDefinitionKey(processDefinitionKey)
                    .taskAssignee(SecurityUtils.getCurrentUsername()) // userName关联处理人
                    .includeProcessVariables()
                    .orderByTaskCreateTime().desc();
            if (StringUtils.isNotEmpty(searchDto.getBt())) {
                taskQuery.processVariableValueLikeIgnoreCase(ProcessConstants.BPM_FORM_TITLE, searchDto.getBt() + "%");
            }
            if (StringUtils.isNotEmpty(searchDto.getSwlx())) {
                if (DocumentConstant.QUERY_STATUS_DY == status) {
                    taskQuery.processVariableValueEquals(ProcessConstants.BPM_FORM_DOCUMENT_REVIEW, searchDto.getSwlx());
                } else {
                    taskQuery.processVariableValueNotEquals(ProcessConstants.BPM_FORM_DOCUMENT_REVIEW, searchDto.getSwlx());
                }
            }

            List<Task> taskList = taskQuery.listPage(pageable.getPageNumber() * pageable.getPageSize(), pageable.getPageSize());
            if (CollectionUtils.isEmpty(taskList)) {
                return returnResult;
            }
            result = taskList.stream().collect(Collectors.groupingBy(Task::getProcessInstanceId, Collectors.mapping(FlowTaskInfoDto::new, Collectors.toList())));
        } else {
            //根据查询条件改成已办未完结和已办已完结
            ResultJson resultJson = flowTaskService.finishedList(pageable.getPageNumber(), pageable.getPageSize(), processDefinitionKey, searchDto);
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<FlowTaskDto> page = (com.baomidou.mybatisplus.extension.plugins.pagination.Page<FlowTaskDto>) resultJson.getData();
            result = page.getRecords().stream().collect(Collectors.groupingBy(FlowTaskDto::getProcInsId, Collectors.mapping(FlowTaskInfoDto::new, Collectors.toList())));
        }
        for (Map.Entry<String, List<FlowTaskInfoDto>> m : result.entrySet()) {
            returnResult.put(m.getKey(), m.getValue() == null ? null : m.getValue().get(0));
        }
        return returnResult;
    }
}
