package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.dto.*;
import com.hzwangda.aigov.modules.document.entity.A07DocumentInfoWwfb;
import com.hzwangda.aigov.modules.document.service.A07FwService;
import com.wangda.oa.annotation.Log;
import com.wangda.oa.annotation.rest.AnonymousGetMapping;
import com.wangda.oa.annotation.rest.AnonymousPostMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "发文管理")
@RequestMapping("/api/aigov/document/fw")
@CrossOrigin
public class A07FwController {

    private final A07FwService a07FwService;

    @ApiOperation("发文列表")
    @GetMapping(value = "/getFwList")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Object> getFwList(A07DocumentFwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07FwService.getFwList(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("会签列表")
    @GetMapping(value = "/getHqList")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Object> getHqList(A07DocumentHqQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07FwService.getHqList(criteria, pageable), HttpStatus.OK);
    }

    @Log("发文详情")
    @ApiOperation("发文详情")
    @PostMapping(value = "/findByDocumentInfo")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<A07DocumentFwDto> findByDocumentInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07FwService.findByDocumentInfo(id), HttpStatus.OK);
    }

    @ApiOperation("会签详情")
    @PostMapping(value = "/getHqInfo")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<A07DocumentHqDto> getHqInfo(@NotNull @RequestBody Long id) {
        return new ResponseEntity<>(a07FwService.getHqInfo(id), HttpStatus.OK);
    }

    @ApiOperation("发文检索")
    @GetMapping(value = "/getFwListRetrieve")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Object> getFwListRetrieve(A07DocumentGwRetrieveQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07FwService.getFwListRetrieve(criteria, pageable), HttpStatus.OK);
    }

    @Log("删除会签")
    @ApiOperation("删除会签")
    @PostMapping(value = "/deleteHq")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Boolean> deleteHq(@NotNull @RequestBody List<Long> ids) {
        return new ResponseEntity<>(a07FwService.deleteHq(ids), HttpStatus.OK);
    }

    @Log("外网发布数据集合")
    @ApiOperation("外网发布数据集合")
    @AnonymousPostMapping(value = "/getWwfbList")
    public ResponseEntity<List<A07DocumentInfoWwfb>> getWwfbList() {
        return new ResponseEntity<>(a07FwService.getWwfbList(), HttpStatus.OK);
    }

    @Log("删除发文")
    @ApiOperation("删除发文")
    @PostMapping(value = "/deleteDocumentInfo")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Boolean> deleteDocumentInfo(@NotNull @RequestBody List<Long> ids) {
        return new ResponseEntity<>(a07FwService.deleteDocumentInfo(ids), HttpStatus.OK);
    }

    @Log("根据代字获取最新的序号值")
    @ApiOperation("根据代字获取最新的序号值")
    @AnonymousGetMapping(value = "/getXhByDz")
    public ResponseEntity<Integer> getXhByDz(@RequestParam("dz") String dz, @RequestParam("nh") String nh, String processInstanceId) {
        return new ResponseEntity<>(a07FwService.getXhByDz(dz, nh, processInstanceId), HttpStatus.OK);
    }


    @ApiOperation("分发发文")
    @PostMapping(value = "/saveFfGwlz")
    public ResponseEntity<Long> saveFfGwlz(@RequestParam("bpmInstanceId") String bpmInstanceId) {
        return new ResponseEntity<>(a07FwService.saveFfGwlz(bpmInstanceId), HttpStatus.OK);
    }

    @ApiOperation("发文信息公开")
    @PostMapping(value = "/publicDoc")
    public ResponseEntity<Object> publicDoc(@RequestParam("bpmInstanceId") String bpmInstanceId) {
        return a07FwService.publicDoc(bpmInstanceId);
    }

    @ApiOperation("发文新建权限过滤代字")
    @GetMapping(value = "/getFwCreateByDz")
    public ResponseEntity<List<Map<String, Object>>> getFwCreateByDz() {
        return new ResponseEntity<>(a07FwService.getFwCreateByDz(), HttpStatus.OK);
    }

    @ApiOperation("发文文种切换判断")
    @GetMapping(value = "/getFwDzSwitchover")
    public ResponseEntity<Object> getFwDzSwitchover(String procInstId, String appid) {
        return new ResponseEntity<>(a07FwService.getFwDzSwitchover(procInstId, appid), HttpStatus.OK);
    }

    @ApiOperation("发文文种切换数据查询")
    @GetMapping(value = "/getFwDzSwitchoverByProcInstId")
    public ResponseEntity<Object> getFwDzSwitchoverByProcInstId(String procInstId) {
        return new ResponseEntity<>(a07FwService.getFwDzSwitchoverByProcInstId(procInstId), HttpStatus.OK);
    }


    @Log("全部收发文增加待阅人")
    @ApiOperation("全部收发文增加待阅人")
    @PostMapping(value = "/saveTaskUserRead")
    public ResponseEntity<String> saveTaskUserRead(@RequestParam(value = "remark", required = false) String remark, @RequestParam("users") String users, @RequestParam("bpmInstanceId") String bpmInstanceId) {
        return new ResponseEntity<>(a07FwService.saveTaskUserRead(remark, users, bpmInstanceId), HttpStatus.OK);
    }


}
