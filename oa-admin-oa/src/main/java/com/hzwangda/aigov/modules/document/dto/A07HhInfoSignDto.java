package com.hzwangda.aigov.modules.document.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Timestamp;

@Data
@ApiModel(value = "会海会议签收")
public class A07HhInfoSignDto {

    @ApiModelProperty(value = "单位id")
    private String qsr;

    @ApiModelProperty(value = "单位名称")
    private String username;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "会议开始时间")
    private Timestamp startDate;

    @Column(length = 200)
    @ApiModelProperty(value = "会议状态（未全部签收，已全部签收）")
    private String status;
}
