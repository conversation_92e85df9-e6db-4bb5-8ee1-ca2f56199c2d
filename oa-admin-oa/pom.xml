<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hzwangda.aigov</groupId>
        <artifactId>np-service</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>oa-admin-oa</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <!--同一待办服务集成-->
        <dependency>
            <groupId>com.hzwangda.message</groupId>
            <artifactId>todo-list-service</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.hzwangda.aigov</groupId>
            <artifactId>aigov-workflow</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.hzwangda.aigov</groupId>
            <artifactId>aigov-zzkk-database</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.hzwangda.aigov</groupId>
            <artifactId>aigov-officeonline</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.hzwangda.aigov</groupId>
            <artifactId>aigov-storage-oss</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.hzwangda.aigov</groupId>-->
        <!--            <artifactId>aigov-officeonline-wps</artifactId>-->
        <!--            <version>1.1.0-SNAPSHOT</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-maven-plugin</artifactId>
            <version>4.33.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.liquibase</groupId>
                    <artifactId>liquibase-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
            <version>4.33.0</version>
        </dependency>
        <dependency>
            <groupId>com.hzwangda.opensearch</groupId>
            <artifactId>hzwangda-opensearch-springboot-start</artifactId>
            <version>1.5.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-http</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--axis2 begin-->
        <dependency>
            <groupId>org.apache.axis</groupId>
            <artifactId>axis</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.axis2</groupId>
            <artifactId>axis2-adb</artifactId>
            <version>1.8.2</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.woodstox</groupId>
                    <artifactId>wstx-asl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.mail</groupId>
                    <artifactId>mail</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>xerces</groupId>
                    <artifactId>xercesImpl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--axis2 end-->
        <!-- ureport2报表 -->
        <dependency>
            <groupId>com.syyai.spring.boot</groupId>
            <artifactId>ureport-spring-boot-starter</artifactId>
            <version>2.2.9</version>
        </dependency>

        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- polardb数据库驱动 -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>polardb-jdbc18</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <!--        泰宇归档-->
        <dependency>
            <groupId>com.taiyu</groupId>
            <artifactId>typre-center</artifactId>
            <version>1.0.0</version>
        </dependency>
        <!-- 显示引用 -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>
        <!-- fenix JPA扩展库 -->
        <dependency>
            <groupId>com.blinkfox</groupId>
            <artifactId>fenix-spring-boot-starter</artifactId>
            <version>2.7.0</version>
        </dependency>
        <dependency>
            <groupId>net.sf.cssbox</groupId>
            <artifactId>pdf2dom</artifactId>
            <version>1.7</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>3.0.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox-tools</artifactId>
            <version>2.0.12</version>
        </dependency>

        <dependency>
            <groupId>xml-apis</groupId>
            <artifactId>xml-apis</artifactId>
            <version>1.4.01</version>
        </dependency>

    </dependencies>

    <!-- 打包 -->
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.18.RELEASE</version>
            </plugin>
            <!-- 跳过单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
                <configuration>
                    <!-- 允许移动生成的文件 -->
                    <verbose>true</verbose>
                    <!-- 是否覆盖 -->
                    <overwrite>true</overwrite>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>HzWangdaRepo</id>
            <url>https://mvnrepository.hzwangda.com/repository/maven-public/</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>HzWangdaRepo</id>
            <url>https://mvnrepository.hzwangda.com/repository/maven-public/</url>
        </pluginRepository>
    </pluginRepositories>
</project>
