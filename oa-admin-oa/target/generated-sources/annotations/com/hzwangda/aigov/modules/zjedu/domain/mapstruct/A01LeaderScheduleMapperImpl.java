package com.hzwangda.aigov.modules.zjedu.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.domain.dto.A01LeaderScheduleDto;
import com.hzwangda.aigov.modules.zjedu.domain.entity.A01LeaderSchedule;
import com.wangda.oa.modules.system.service.mapstruct.UserMapperUtil;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A01LeaderScheduleMapperImpl implements A01LeaderScheduleMapper {

    @Autowired
    private UserMapperUtil userMapperUtil;

    @Override
    public A01LeaderScheduleDto toDto(A01LeaderSchedule entity) {
        if ( entity == null ) {
            return null;
        }

        A01LeaderScheduleDto a01LeaderScheduleDto = new A01LeaderScheduleDto();

        a01LeaderScheduleDto.setAddress( entity.getAddress() );
        a01LeaderScheduleDto.setBeginTime( entity.getBeginTime() );
        a01LeaderScheduleDto.setBeginTimeType( entity.getBeginTimeType() );
        Set<Long> set = entity.getCategory();
        if ( set != null ) {
            a01LeaderScheduleDto.setCategory( new HashSet<Long>( set ) );
        }
        a01LeaderScheduleDto.setEndTime( entity.getEndTime() );
        a01LeaderScheduleDto.setEndTimeType( entity.getEndTimeType() );
        a01LeaderScheduleDto.setId( entity.getId() );
        a01LeaderScheduleDto.setJoinUsers( userMapperUtil.toConvertToSimpleUserDto( entity.getJoinUsers() ) );
        a01LeaderScheduleDto.setMemo( entity.getMemo() );
        a01LeaderScheduleDto.setSubject( entity.getSubject() );
        a01LeaderScheduleDto.setTrafficType( entity.getTrafficType() );

        splitTime( entity, a01LeaderScheduleDto );

        return a01LeaderScheduleDto;
    }

    @Override
    public List<A01LeaderScheduleDto> toDto(List<A01LeaderSchedule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A01LeaderScheduleDto> list = new ArrayList<A01LeaderScheduleDto>( entityList.size() );
        for ( A01LeaderSchedule a01LeaderSchedule : entityList ) {
            list.add( toDto( a01LeaderSchedule ) );
        }

        return list;
    }

    @Override
    public A01LeaderSchedule toEntity(A01LeaderScheduleDto dto) {
        if ( dto == null ) {
            return null;
        }

        A01LeaderSchedule a01LeaderSchedule = new A01LeaderSchedule();

        a01LeaderSchedule.setAddress( dto.getAddress() );
        a01LeaderSchedule.setBeginTime( dto.getBeginTime() );
        a01LeaderSchedule.setBeginTimeType( dto.getBeginTimeType() );
        Set<Long> set = dto.getCategory();
        if ( set != null ) {
            a01LeaderSchedule.setCategory( new HashSet<Long>( set ) );
        }
        a01LeaderSchedule.setEndTime( dto.getEndTime() );
        a01LeaderSchedule.setEndTimeType( dto.getEndTimeType() );
        a01LeaderSchedule.setId( dto.getId() );
        a01LeaderSchedule.setJoinUsers( userMapperUtil.toConvertToUserName( dto.getJoinUsers() ) );
        a01LeaderSchedule.setMemo( dto.getMemo() );
        a01LeaderSchedule.setSubject( dto.getSubject() );
        a01LeaderSchedule.setTrafficType( dto.getTrafficType() );

        handleTime( dto, a01LeaderSchedule );

        return a01LeaderSchedule;
    }

    @Override
    public List<A01LeaderSchedule> toEntity(List<A01LeaderScheduleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A01LeaderSchedule> list = new ArrayList<A01LeaderSchedule>( dtoList.size() );
        for ( A01LeaderScheduleDto a01LeaderScheduleDto : dtoList ) {
            list.add( toEntity( a01LeaderScheduleDto ) );
        }

        return list;
    }
}
