package com.hzwangda.aigov.modules.archive.mapstruct;

import com.hzwangda.aigov.modules.archive.dto.A12ArchivesDto;
import com.hzwangda.aigov.modules.archive.entity.A12Archives;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:56+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class A12ArchivesMapperImpl implements A12ArchivesMapper {

    @Override
    public A12ArchivesDto toDto(A12Archives entity) {
        if ( entity == null ) {
            return null;
        }

        A12ArchivesDto a12ArchivesDto = new A12ArchivesDto();

        a12ArchivesDto.setCreateTime( entity.getCreateTime() );
        a12ArchivesDto.setDirectoryId( entity.getDirectoryId() );
        a12ArchivesDto.setId( entity.getId() );
        a12ArchivesDto.setLocalStorageId( entity.getLocalStorageId() );
        a12ArchivesDto.setReportDate( entity.getReportDate() );
        a12ArchivesDto.setSort( entity.getSort() );
        a12ArchivesDto.setTag( entity.getTag() );
        a12ArchivesDto.setTitle( entity.getTitle() );

        splitTime( entity, a12ArchivesDto );

        return a12ArchivesDto;
    }

    @Override
    public List<A12ArchivesDto> toDto(List<A12Archives> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<A12ArchivesDto> list = new ArrayList<A12ArchivesDto>( entityList.size() );
        for ( A12Archives a12Archives : entityList ) {
            list.add( toDto( a12Archives ) );
        }

        return list;
    }

    @Override
    public A12Archives toEntity(A12ArchivesDto dto) {
        if ( dto == null ) {
            return null;
        }

        A12Archives a12Archives = new A12Archives();

        a12Archives.setCreateTime( dto.getCreateTime() );
        a12Archives.setDirectoryId( dto.getDirectoryId() );
        a12Archives.setId( dto.getId() );
        a12Archives.setLocalStorageId( dto.getLocalStorageId() );
        a12Archives.setReportDate( dto.getReportDate() );
        a12Archives.setSort( dto.getSort() );
        a12Archives.setTag( dto.getTag() );
        a12Archives.setTitle( dto.getTitle() );

        return a12Archives;
    }

    @Override
    public List<A12Archives> toEntity(List<A12ArchivesDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<A12Archives> list = new ArrayList<A12Archives>( dtoList.size() );
        for ( A12ArchivesDto a12ArchivesDto : dtoList ) {
            list.add( toEntity( a12ArchivesDto ) );
        }

        return list;
    }
}
