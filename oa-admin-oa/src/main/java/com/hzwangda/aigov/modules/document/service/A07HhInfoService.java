package com.hzwangda.aigov.modules.document.service;

import com.hzwangda.aigov.modules.collaboration.domain.criteria.SmsInfo;
import com.hzwangda.aigov.modules.document.dto.*;
import com.hzwangda.aigov.modules.document.entity.A07HhInfoTypeTabs;
import com.hzwangda.aigov.modules.document.entity.A07MeetingSea;
import com.wangda.boot.platform.base.ResultJson;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface A07HhInfoService {

    ResultJson openDoc(String docid);

    Page<A07HhInfoListFwDto> getHhInfoFwList(A07HhInfoQueryCriteria criteria, Pageable pageable);

    Page<A07HhInfoListSwDto> getDocExchangeSwList(A07HhInfoQueryCriteria criteria, Pageable pageable);

    Long saveGwlz(A07MeetingSea resources, Integer type);

    A07MeetingSea getMeetingInfo(Long id);

    List<A07HhInfoSignDto> getSignRecords(A07HhInfoSignCriteria criteria);

    String batchDel(Long[] id);

    String sign(Long id);

    String revoke(Long id);

    Map getSms(Long id);

    String smsUrge(SmsInfo smsInfo);

    Page<A07HhInfoListAllDto> getDocExchangeAllList(A07HhInfoQueryCriteria criteria, Pageable pageable);

    String batchPush(Long[] id);

    String batchUnPush(Long[] id);

    List<A07HhInfoTypeTabs> getTypeTabs();
}
