package com.hzwangda.aigov.modules.GoodsReceive.service.impl;

import com.google.common.collect.Lists;
import com.hzwangda.aigov.modules.GoodsReceive.domain.GoodsCategory;
import com.hzwangda.aigov.modules.GoodsReceive.domain.GoodsCategoryDto;
import com.hzwangda.aigov.modules.GoodsReceive.domain.GoodsInventory;
import com.hzwangda.aigov.modules.GoodsReceive.domain.GoodsReceive;
import com.hzwangda.aigov.modules.GoodsReceive.dto.GoodsCriteria;
import com.hzwangda.aigov.modules.GoodsReceive.dto.GoodsReceiveVo;
import com.hzwangda.aigov.modules.GoodsReceive.repository.GoodsCategoryDtoRepostory;
import com.hzwangda.aigov.modules.GoodsReceive.repository.GoodsInventoryRepository;
import com.hzwangda.aigov.modules.GoodsReceive.repository.GoodsReceiveRepository;
import com.hzwangda.aigov.modules.GoodsReceive.service.GoodsReceiveService;
import com.hzwangda.aigov.modules.GoodsReceive.dto.QueryCriteria;
import com.hzwangda.aigov.modules.document.constant.DocumentConstant;
import com.hzwangda.aigov.modules.workflow.dto.ListVo;
import com.wangda.oa.backlog.bo.BacklogBusinessSearchBO;
import com.wangda.oa.backlog.bo.BacklogListBO;
import com.wangda.oa.backlog.dto.BacklogListDto;
import com.wangda.oa.backlog.dto.BacklogListPageDto;
import com.wangda.oa.backlog.repository.WdBacklogLogRepository;
import com.wangda.oa.backlog.service.WdBacklogService;
import com.wangda.oa.config.ElPermissionConfig;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskAssigneeDto;
import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import com.wangda.oa.modules.workflow.factory.RestUrlComponent;
import com.wangda.oa.modules.workflow.service.workflow.FlowInstanceService;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class GoodsReceiveServiceImpl implements GoodsReceiveService {

    @Resource
    private RestUrlComponent restUrlComponent;
    @Resource
    private GoodsReceiveRepository goodsReceiveRepository;
    @Resource
    private UserRepository userRepository;
    @Resource
    protected TaskService taskService;
    @Resource
    private WdBacklogService wdBacklogService;
    @Resource
    private GoodsCategoryDtoRepostory goodsCategoryDtoRepostory;
    @PersistenceContext
    private EntityManager entityManager;
    @Resource
    private GoodsInventoryRepository goodsInventoryRepository;

    @Resource
    private FlowInstanceService flowInstanceService;

    @Override
    public Object goodsPageList(GoodsCriteria queryCriteria, Pageable pageable)  {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> cq = cb.createTupleQuery();
        Root<GoodsCategoryDto> root = cq.from(GoodsCategoryDto.class);
        cq.multiselect(
                root.get("id").alias("id"),
                root.get("mcxh").alias("mcxh"),
                root.get("ytfl").alias("ytfl"),
                root.get("lysl").alias("lysl"),
                root.get("goodsReceive").get("bpmInstanceId").alias("bpmInstanceId"),
                root.get("goodsReceive").get("bpmStatus").alias("bpmStatus"),
                root.get("createTime").alias("createTime"),
                root.get("borrowDay").alias("borrowDay")
                );
     /*   cq.multiselect(
                cb.sum(root.get("lysl")).alias("count"),
                cb.max(root.get("wpfl")).alias("wpfl"),
                cb.max(root.get("ytfl")).alias("ytfl"),
                cb.max(root.get("borrowDay")).alias("borrowDay"),
                root.get("mcxh").alias("mcxh"),
                cb.max(root.get("createTime")).alias("createTime")ß
        );*/
        Predicate p = QueryHelp.getPredicate(root,queryCriteria,cb);
        Predicate p1 = cb.notEqual(root.get("goodsReceive").get("bpmStatus"), ProcStatusEnum.JJ.getValue());
        p = cb.and(p,p1);
        cq.where(
                p
        );//.groupBy(root.get("mcxh"));
        int total = entityManager.createQuery(cq).getResultList().size();
        TypedQuery<Tuple> tupleTypedQuery = entityManager.createQuery(cq).setFirstResult(pageable.getPageNumber() * pageable.getPageSize()).setMaxResults(pageable.getPageSize());
        List<Tuple> resultList = tupleTypedQuery.getResultList();
        List<Map> collect = resultList.stream().map(item -> {
            Map map = new HashMap<>();
            map.put("id", item.get("id"));
            map.put("bpmInstanceId", item.get("bpmInstanceId"));
            map.put("goodsNum", (int)item.get("lysl"));
           // map.put("wpfl", item.get("wpfl"));
            map.put("goodType", item.get("ytfl"));
            map.put("goodName", item.get("mcxh"));
            map.put("borrowDay", item.get("borrowDay"));
            map.put("createTime", item.get("createTime"));
            map.put("useType", Objects.isNull(item.get("borrowDay"))?"申领":"借用");
            if(!"TG".equals(String.valueOf(item.get("bpmStatus")))) {
                FlowTaskAssigneeDto taskDto=null;
                List<FlowTaskAssigneeDto> taskList = flowInstanceService.getTaskInfoByProcInsId(String.valueOf(item.get("bpmInstanceId")));
                if(taskList.size()>0){
                    taskDto=taskList.get(0);
                    if(!"task_5".equals(taskDto.getTaskDefKey())){
                        map.put("status", "审核中");
                    }else{
                        map.put("status", "已通过");
                    }
                    map.put("getStatus", "待领取");
                }
                map.put("assigneeName", taskDto.getAssigneeName());
            }else  if("TG".equals(String.valueOf(item.get("bpmStatus")))) {
                map.put("status", "已通过");
                map.put("getStatus", "已领取");
            }
            //TODO 物品未设置管理员
            map.put("goodAdmin","管理员");
            map.put("url","https://ding.nanxun.gov.cn:806/newdd/#/bpmRead/browse?processInstanceId="+item.get("bpmInstanceId"));
            return map;
        }).collect(Collectors.toList());

        return  new PageImpl<>(collect,pageable,total);

    }
    @Override
    public Object queryList(QueryCriteria criteria, Pageable pageable) {
        //封装待办查询条件
        BacklogListBO backlogListBO = new BacklogListBO();
        backlogListBO.setUserId(SecurityUtils.getCurrentUsername());
        backlogListBO.setAppId(restUrlComponent.getBacklogAppId());
        if(StringUtils.isNotBlank(criteria.getProcessDefinitionKey())){
            //实例定义不为空
            backlogListBO.setModuleCode(criteria.getProcessDefinitionKey());
        }else{
            //实例定义为空则查询公文管理对应定义
            backlogListBO.setModuleCodes(DocumentConstant.PROCESS_DEFINITION_KEY_LIST);
        }
        List<BacklogBusinessSearchBO> businessSearchBOList=new ArrayList<>();
        if(StringUtils.isNotBlank(criteria.getSubject())){
            backlogListBO.setBt(criteria.getSubject());
        }
        if(criteria.getTimeRange()!=null && criteria.getTimeRange().size()>1){
            backlogListBO.setCreateStartTime(criteria.getTimeRange().get(0));
            backlogListBO.setCreateEndTime(criteria.getTimeRange().get(1));
        }
        backlogListBO.setBusinessSearchBOList(businessSearchBOList);
        backlogListBO.setStatus(criteria.getStatus());
        if(criteria.getStatus()==null){
            //若不传默认查询所有，则为4
            backlogListBO.setStatus(4);
        }
        backlogListBO.setPage(pageable.getPageNumber());
        backlogListBO.setSize(pageable.getPageSize());

        //查询待办服务
        BacklogListPageDto backlogListPageDto= wdBacklogService.getBacklogList(backlogListBO);
        List<BacklogListDto> backlogListDtoList = backlogListPageDto.getContent();
        if (CollectionUtils.isEmpty(backlogListDtoList)) {
            return Page.empty();
        }

        //获取流程实例id
        Map<String, Date> bpmInstanceIdMap = new LinkedHashMap<>();
        for (BacklogListDto backlogListDto: backlogListDtoList) {
            bpmInstanceIdMap.put(backlogListDto.getBizId(), backlogListDto.getCreateDate());
        }
        criteria.setBpmInstanceId(Lists.newArrayList(bpmInstanceIdMap.keySet()));

        //
        Specification<GoodsReceive> sp = (root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder);
        List<GoodsReceive> list = goodsReceiveRepository.findAll(sp);

        String transactorNames=backlogListDtoList.stream().map(BacklogListDto::getCurrentHandler).collect(Collectors.joining(","));
        //用户名集合放入
        List<String> username=list.stream().map(GoodsReceive::getCreateBy).collect(Collectors.toList());
        //办理人放入用户名集合
        username.addAll(Arrays.asList(transactorNames.split(",")));

        //查询用户信息
        List<User> userList=userRepository.findByUsernameIn(username);
        Map<String, User> finalUserMap = userList.stream().collect(Collectors.toMap(User::getUsername, a -> a, (k1, k2) -> k1));

        List<ListVo> collect = backlogListDtoList.stream().map(backlogListDto -> {
            String curHandler = "";
            if (StringUtils.isNotEmpty(backlogListDto.getCurrentHandler())) {
                String[] split = backlogListDto.getCurrentHandler().split(",");
                for (String name : split) {
                    if (finalUserMap.containsKey(name)) {
                        curHandler += finalUserMap.get(name).getNickName() + ",";
                    }
                }
                if (StringUtils.isNotEmpty(curHandler)) {
                    curHandler.substring(0, curHandler.length() - 2);
                }
            }

            GoodsReceiveVo.GoodsReceiveVoBuilder builder = GoodsReceiveVo.builder()
                    .bpmInstanceId(backlogListDto.getBizId())
                    .curHandler(curHandler)
                    .subject(backlogListDto.getTitle())
                    .bpmStatus(backlogListDto.getHandleStatus());

            Optional<GoodsReceive> first = list.stream().filter(goodsReceive -> goodsReceive.getBpmInstanceId().equals(backlogListDto.getBizId())).findFirst();
            if (first.isPresent()) {
                GoodsReceive goodsReceive = first.get();
                String s = "";
                //拼接物品名称和数量
                for (int i=0;i<goodsReceive.getGoodsCategories().size();){
                    GoodsCategory goodsCategory=goodsReceive.getGoodsCategories().get(i);
                    if (i==goodsReceive.getGoodsCategories().size()-1){
                        s=s+goodsCategory.getMcxh()+goodsCategory.getLysl();
                    }else {
                        s=s+goodsCategory.getMcxh()+goodsCategory.getLysl()+",";
                    }
                    i++;
                }
                User user = userRepository.findByUsername(goodsReceive.getCreateBy());
                 builder.lysm(goodsReceive.getLysm())
                         .wpmc(s)
                         .lysm(goodsReceive.getLysm())
                         .deptName(user.getDept().getName())
                        .createTime(goodsReceive.getCreateTime())
                        .creator(user.getNickName());
            }

            // 当前环节
            List<Task> tasks = taskService.createTaskQuery().processInstanceId(backlogListDto.getBizId()).active().list();
            if (tasks!=null && tasks.size()>0) {
                builder.curTask(tasks.get(0).getName());
            }

            return builder.build();
        }).collect(Collectors.toList());

        return new PageImpl(collect, pageable, backlogListPageDto.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String adopt(String processInstanceId) {
        GoodsReceive goodsReceive = goodsReceiveRepository.findByBpmInstanceId(processInstanceId);
        //查询各个物品的库存量，检查是否够减
        List<GoodsCategory> goodsCategories = goodsReceive.getGoodsCategories();
        goodsCategories.stream().forEach(goodsCategory -> {
            GoodsInventory goodsInventory = goodsCategory.getGoodsInventory();
            Integer lysl = goodsCategory.getLysl();
            Integer num = goodsInventory.getNum();
            if (num!=null) {
                if (lysl>num) {
                    throw new BadRequestException("领用数量大于物品数量");
                } else {
                    goodsInventory.setNum(num - lysl);
                }
                goodsInventoryRepository.save(goodsInventory);

            }
        });

        return null;
    }

    @Override
    public Object queryCount(GoodsCriteria queryCriteria, Pageable pageable) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> cq = cb.createTupleQuery();
        Root<GoodsCategoryDto> root = cq.from(GoodsCategoryDto.class);
        cq.multiselect(
                cb.sum(root.get("lysl")).alias("count"),
                cb.max(root.get("wpfl")).alias("wpfl"),
                cb.max(root.get("ytfl")).alias("ytfl"),
                cb.max(root.get("borrowDay")).alias("borrowDay"),
                root.get("mcxh").alias("mcxh"),
                cb.max(root.get("createTime")).alias("createTime")
        );
        Predicate p = QueryHelp.getPredicate(root,queryCriteria,cb);
        Predicate p1 = cb.equal(root.get("goodsReceive").get("bpmStatus"), ProcStatusEnum.TG.getValue());
        p = cb.and(p,p1);
        cq.where(
                p
        ).groupBy(root.get("mcxh"));
        int total = entityManager.createQuery(cq).getResultList().size();
        TypedQuery<Tuple> tupleTypedQuery = entityManager.createQuery(cq).setFirstResult(pageable.getPageNumber() * pageable.getPageSize()).setMaxResults(pageable.getPageSize());
        List<Tuple> resultList = tupleTypedQuery.getResultList();
        List<Map> collect = resultList.stream().map(item -> {
            Map map = new HashMap<>();
            map.put("lysl", item.get("count"));
            map.put("wpfl", item.get("wpfl"));
            map.put("ytfl", item.get("ytfl"));
            map.put("mcxh", item.get("mcxh"));
            map.put("createTime", item.get("createTime"));
            return map;
        }).collect(Collectors.toList());

        return  new PageImpl<>(collect,pageable,total);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restore(String processInstanceId) {
        GoodsReceive goodsReceive = goodsReceiveRepository.findByBpmInstanceId(processInstanceId);
        if (goodsReceive!=null) {
            List<GoodsCategory> goodsCategories = goodsReceive.getGoodsCategories();
            goodsCategories.stream().forEach(goodsCategory -> {
                GoodsInventory goodsInventory = goodsCategory.getGoodsInventory();
                if (goodsInventory.getNum()!=null) {
                    goodsInventory.setNum(goodsCategory.getLysl() + goodsInventory.getNum());
                    goodsInventoryRepository.save(goodsInventory);
                }
            });
        }
    }

    @Override
    public Object getByMcxh(String mcxh) {
        Specification<GoodsCategoryDto> sp = (root, criteriaQuery, criteriaBuilder) -> {
            Predicate p = criteriaBuilder.equal(root.get("mcxh"),mcxh);
            return p;
        };
        List<GoodsCategoryDto> all = goodsCategoryDtoRepostory.findAll(sp);
        all.stream().forEach(goodsCategoryDto -> {
            String createBy = goodsCategoryDto.getCreateBy();
            User user = userRepository.findByUsername(createBy);
            String nickName = user.getNickName();
            goodsCategoryDto.setCreateBy(nickName);
        });
        return all;
    }


}
