package com.hzwangda.aigov.modules.taskmanagement.domain.mapstruct;

import com.hzwangda.aigov.modules.taskmanagement.domain.dto.TmClassDto;
import com.hzwangda.aigov.modules.taskmanagement.domain.entity.TmClass;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class TmClassMapperImpl implements TmClassMapper {

    @Override
    public TmClassDto toDto(TmClass entity) {
        if ( entity == null ) {
            return null;
        }

        TmClassDto tmClassDto = new TmClassDto();

        tmClassDto.setClassId( entity.getClassId() );
        tmClassDto.setCreateBy( entity.getCreateBy() );
        tmClassDto.setCreateTime( entity.getCreateTime() );
        tmClassDto.setName( entity.getName() );
        tmClassDto.setPid( entity.getPid() );
        tmClassDto.setTmCatalog( entity.getTmCatalog() );
        tmClassDto.setUpdateTime( entity.getUpdateTime() );

        return tmClassDto;
    }

    @Override
    public List<TmClassDto> toDto(List<TmClass> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmClassDto> list = new ArrayList<TmClassDto>( entityList.size() );
        for ( TmClass tmClass : entityList ) {
            list.add( toDto( tmClass ) );
        }

        return list;
    }

    @Override
    public TmClass toEntity(TmClassDto dto) {
        if ( dto == null ) {
            return null;
        }

        TmClass tmClass = new TmClass();

        tmClass.setCreateBy( dto.getCreateBy() );
        tmClass.setCreateTime( dto.getCreateTime() );
        tmClass.setUpdateTime( dto.getUpdateTime() );
        tmClass.setClassId( dto.getClassId() );
        tmClass.setName( dto.getName() );
        tmClass.setPid( dto.getPid() );
        tmClass.setTmCatalog( dto.getTmCatalog() );

        return tmClass;
    }

    @Override
    public List<TmClass> toEntity(List<TmClassDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TmClass> list = new ArrayList<TmClass>( dtoList.size() );
        for ( TmClassDto tmClassDto : dtoList ) {
            list.add( toEntity( tmClassDto ) );
        }

        return list;
    }
}
