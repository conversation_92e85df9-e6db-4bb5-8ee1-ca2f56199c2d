package com.hzwangda.aigov.modules.document.service.impl.handle;

import com.alibaba.fastjson.JSONObject;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.workflow.dto.BacklogListDto;
import com.wangda.oa.modules.workflow.dto.MyWorkDto;
import com.wangda.oa.modules.workflow.service.FlowFormHandle;
import com.wangda.oa.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
@Component
@Slf4j
public class Z01QfbSwFormHandle implements FlowFormHandle {

    @Resource
    private TaskService taskService;
    @Resource
    private UserService userService;

    @Override
    public void handleFormForMyWork(BacklogListDto myWork, MyWorkDto myWorkDto) {

    }

    @Override
    public String handleSubjectRule(JSONObject formDataObj, String subjectRule) {
        return null;
    }

    @Override
    public Object handleFormRecord(String procInstanceId, String taskDefKey, JSONObject bpmFormData) {
        if (bpmFormData!=null) {
            List<Task> list = taskService.createTaskQuery()
                    .active()
                    .taskDefinitionKey("usertask03")
                    .processInstanceId(procInstanceId)
                    .list();
            List<String> usernames = list.stream().map(TaskInfo::getAssignee).collect(Collectors.toList());
            List<UserDto> users = userService.findByUserNames(usernames);
            String collect = users.stream().map(UserDto::getName).collect(Collectors.joining("， "));
            if (StringUtils.isNotEmpty(collect)) {
                bpmFormData.put("units", "已送： " + collect);
            } else {
                bpmFormData.put("units", null);
            }
        }
        return bpmFormData;
    }

    @Override
    public void deleteFormRecord(String instanceId) {

    }
}
