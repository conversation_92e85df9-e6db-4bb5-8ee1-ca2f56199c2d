package com.hzwangda.aigov.modules.zjedu.meeting.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35MeetingDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35MeetingNoteDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35Meeting;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35MeetingNote;
import com.wangda.oa.modules.system.service.mapstruct.UserMapperUtil;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class C35MeetingNoteMapperImpl implements C35MeetingNoteMapper {

    @Autowired
    private UserMapperUtil userMapperUtil;

    @Override
    public C35MeetingNoteDto toDto(C35MeetingNote entity) {
        if ( entity == null ) {
            return null;
        }

        C35MeetingNoteDto c35MeetingNoteDto = new C35MeetingNoteDto();

        c35MeetingNoteDto.setCreateBy( entity.getCreateBy() );
        c35MeetingNoteDto.setCreateTime( entity.getCreateTime() );
        c35MeetingNoteDto.setUpdateTime( entity.getUpdateTime() );
        c35MeetingNoteDto.setUpdatedBy( entity.getUpdatedBy() );
        c35MeetingNoteDto.setDescription( entity.getDescription() );
        c35MeetingNoteDto.setFile( entity.getFile() );
        c35MeetingNoteDto.setId( entity.getId() );
        c35MeetingNoteDto.setMeeting( c35MeetingToC35MeetingDto( entity.getMeeting() ) );
        c35MeetingNoteDto.setName( entity.getName() );

        return c35MeetingNoteDto;
    }

    @Override
    public List<C35MeetingNoteDto> toDto(List<C35MeetingNote> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<C35MeetingNoteDto> list = new ArrayList<C35MeetingNoteDto>( entityList.size() );
        for ( C35MeetingNote c35MeetingNote : entityList ) {
            list.add( toDto( c35MeetingNote ) );
        }

        return list;
    }

    @Override
    public C35MeetingNote toEntity(C35MeetingNoteDto dto) {
        if ( dto == null ) {
            return null;
        }

        C35MeetingNote c35MeetingNote = new C35MeetingNote();

        c35MeetingNote.setCreateBy( dto.getCreateBy() );
        c35MeetingNote.setCreateTime( dto.getCreateTime() );
        c35MeetingNote.setUpdateTime( dto.getUpdateTime() );
        c35MeetingNote.setUpdatedBy( dto.getUpdatedBy() );
        c35MeetingNote.setDescription( dto.getDescription() );
        c35MeetingNote.setFile( dto.getFile() );
        c35MeetingNote.setId( dto.getId() );
        c35MeetingNote.setMeeting( c35MeetingDtoToC35Meeting( dto.getMeeting() ) );
        c35MeetingNote.setName( dto.getName() );

        return c35MeetingNote;
    }

    @Override
    public List<C35MeetingNote> toEntity(List<C35MeetingNoteDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<C35MeetingNote> list = new ArrayList<C35MeetingNote>( dtoList.size() );
        for ( C35MeetingNoteDto c35MeetingNoteDto : dtoList ) {
            list.add( toEntity( c35MeetingNoteDto ) );
        }

        return list;
    }

    protected C35MeetingDto c35MeetingToC35MeetingDto(C35Meeting c35Meeting) {
        if ( c35Meeting == null ) {
            return null;
        }

        C35MeetingDto c35MeetingDto = new C35MeetingDto();

        c35MeetingDto.setCreateBy( c35Meeting.getCreateBy() );
        c35MeetingDto.setCreateTime( c35Meeting.getCreateTime() );
        c35MeetingDto.setUpdateTime( c35Meeting.getUpdateTime() );
        c35MeetingDto.setUpdatedBy( c35Meeting.getUpdatedBy() );
        c35MeetingDto.setAddress( c35Meeting.getAddress() );
        c35MeetingDto.setEndTime( c35Meeting.getEndTime() );
        c35MeetingDto.setId( c35Meeting.getId() );
        c35MeetingDto.setStartTime( c35Meeting.getStartTime() );
        c35MeetingDto.setStatus( c35Meeting.getStatus() );
        c35MeetingDto.setSubject( c35Meeting.getSubject() );
        c35MeetingDto.setType( c35Meeting.getType() );
        c35MeetingDto.setUserIds( c35Meeting.getUserIds() );
        c35MeetingDto.setViewUsers( userMapperUtil.toConvertToSimpleUserDto( c35Meeting.getViewUsers() ) );

        return c35MeetingDto;
    }

    protected C35Meeting c35MeetingDtoToC35Meeting(C35MeetingDto c35MeetingDto) {
        if ( c35MeetingDto == null ) {
            return null;
        }

        C35Meeting c35Meeting = new C35Meeting();

        c35Meeting.setCreateBy( c35MeetingDto.getCreateBy() );
        c35Meeting.setCreateTime( c35MeetingDto.getCreateTime() );
        c35Meeting.setUpdateTime( c35MeetingDto.getUpdateTime() );
        c35Meeting.setUpdatedBy( c35MeetingDto.getUpdatedBy() );
        c35Meeting.setAddress( c35MeetingDto.getAddress() );
        c35Meeting.setEndTime( c35MeetingDto.getEndTime() );
        c35Meeting.setId( c35MeetingDto.getId() );
        c35Meeting.setStartTime( c35MeetingDto.getStartTime() );
        c35Meeting.setStatus( c35MeetingDto.getStatus() );
        c35Meeting.setSubject( c35MeetingDto.getSubject() );
        c35Meeting.setType( c35MeetingDto.getType() );
        c35Meeting.setUserIds( c35MeetingDto.getUserIds() );
        c35Meeting.setViewUsers( userMapperUtil.toConvertToUserName( c35MeetingDto.getViewUsers() ) );

        return c35Meeting;
    }
}
