package com.hzwangda.aigov.modules.matter.service.mapstruct;

import com.hzwangda.aigov.modules.matter.domain.Matter;
import com.hzwangda.aigov.modules.matter.service.dto.dataCollecttion.MatterInfoDto;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class MatterInfoMapperImpl implements MatterInfoMapper {

    @Override
    public MatterInfoDto toDto(Matter entity) {
        if ( entity == null ) {
            return null;
        }

        MatterInfoDto matterInfoDto = new MatterInfoDto();

        matterInfoDto.setApplicants( entity.getApplicants() );
        matterInfoDto.setBelongTheme( entity.getBelongTheme() );
        matterInfoDto.setBusinessOffice( entity.getBusinessOffice() );
        matterInfoDto.setDecisionBodies( entity.getDecisionBodies() );
        matterInfoDto.setDeptName( entity.getDeptName() );
        matterInfoDto.setHandleOffice( entity.getHandleOffice() );
        if ( entity.getId() != null ) {
            matterInfoDto.setId( String.valueOf( entity.getId() ) );
        }
        if ( entity.getIsInternet() != null ) {
            matterInfoDto.setIsInternet( String.valueOf( entity.getIsInternet() ) );
        }
        if ( entity.getIsUnion() != null ) {
            matterInfoDto.setIsUnion( String.valueOf( entity.getIsUnion() ) );
        }
        matterInfoDto.setLegalBasis( entity.getLegalBasis() );
        matterInfoDto.setMatterName( entity.getMatterName() );
        if ( entity.getMatterStatus() != null ) {
            matterInfoDto.setMatterStatus( String.valueOf( entity.getMatterStatus() ) );
        }
        matterInfoDto.setModeOfService( entity.getModeOfService() );
        if ( entity.getModifiedDate() != null ) {
            matterInfoDto.setModifiedDate( new Timestamp( entity.getModifiedDate().getTime() ) );
        }
        if ( entity.getRunTimes() != null ) {
            matterInfoDto.setRunTimes( String.valueOf( entity.getRunTimes() ) );
        }
        matterInfoDto.setTimeNum( entity.getTimeNum() );

        return matterInfoDto;
    }

    @Override
    public List<MatterInfoDto> toDto(List<Matter> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MatterInfoDto> list = new ArrayList<MatterInfoDto>( entityList.size() );
        for ( Matter matter : entityList ) {
            list.add( toDto( matter ) );
        }

        return list;
    }

    @Override
    public Matter toEntity(MatterInfoDto dto) {
        if ( dto == null ) {
            return null;
        }

        Matter matter = new Matter();

        if ( dto.getId() != null ) {
            matter.setId( Long.parseLong( dto.getId() ) );
        }
        matter.setModifiedDate( dto.getModifiedDate() );
        matter.setApplicants( dto.getApplicants() );
        matter.setBelongTheme( dto.getBelongTheme() );
        matter.setBusinessOffice( dto.getBusinessOffice() );
        matter.setDecisionBodies( dto.getDecisionBodies() );
        matter.setDeptName( dto.getDeptName() );
        matter.setHandleOffice( dto.getHandleOffice() );
        if ( dto.getIsInternet() != null ) {
            matter.setIsInternet( Integer.parseInt( dto.getIsInternet() ) );
        }
        if ( dto.getIsUnion() != null ) {
            matter.setIsUnion( Integer.parseInt( dto.getIsUnion() ) );
        }
        matter.setLegalBasis( dto.getLegalBasis() );
        matter.setMatterName( dto.getMatterName() );
        if ( dto.getMatterStatus() != null ) {
            matter.setMatterStatus( Integer.parseInt( dto.getMatterStatus() ) );
        }
        matter.setModeOfService( dto.getModeOfService() );
        if ( dto.getRunTimes() != null ) {
            matter.setRunTimes( Integer.parseInt( dto.getRunTimes() ) );
        }
        matter.setTimeNum( dto.getTimeNum() );

        return matter;
    }

    @Override
    public List<Matter> toEntity(List<MatterInfoDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Matter> list = new ArrayList<Matter>( dtoList.size() );
        for ( MatterInfoDto matterInfoDto : dtoList ) {
            list.add( toEntity( matterInfoDto ) );
        }

        return list;
    }
}
