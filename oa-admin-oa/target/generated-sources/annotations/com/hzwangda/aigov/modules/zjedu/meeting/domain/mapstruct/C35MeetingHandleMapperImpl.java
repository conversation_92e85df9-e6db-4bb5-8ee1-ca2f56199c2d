package com.hzwangda.aigov.modules.zjedu.meeting.domain.mapstruct;

import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35MeetingDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.dto.C35MeetingHandleDto;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35Meeting;
import com.hzwangda.aigov.modules.zjedu.meeting.domain.entity.C35MeetingHandle;
import com.wangda.oa.modules.system.service.mapstruct.UserMapperUtil;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T17:33:55+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.43.0.v20250819-1513, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class C35MeetingHandleMapperImpl implements C35MeetingHandleMapper {

    @Autowired
    private UserMapperUtil userMapperUtil;

    @Override
    public C35MeetingHandleDto toDto(C35MeetingHandle entity) {
        if ( entity == null ) {
            return null;
        }

        C35MeetingHandleDto c35MeetingHandleDto = new C35MeetingHandleDto();

        c35MeetingHandleDto.setCreateBy( entity.getCreateBy() );
        c35MeetingHandleDto.setCreateTime( entity.getCreateTime() );
        c35MeetingHandleDto.setUpdateTime( entity.getUpdateTime() );
        c35MeetingHandleDto.setUpdatedBy( entity.getUpdatedBy() );
        c35MeetingHandleDto.setCorrelationId( entity.getCorrelationId() );
        c35MeetingHandleDto.setCorrelationName( entity.getCorrelationName() );
        c35MeetingHandleDto.setId( entity.getId() );
        c35MeetingHandleDto.setMeeting( c35MeetingToC35MeetingDto( entity.getMeeting() ) );
        c35MeetingHandleDto.setModuleName( entity.getModuleName() );
        c35MeetingHandleDto.setStatus( entity.getStatus() );
        c35MeetingHandleDto.setUserName( entity.getUserName() );

        return c35MeetingHandleDto;
    }

    @Override
    public List<C35MeetingHandleDto> toDto(List<C35MeetingHandle> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<C35MeetingHandleDto> list = new ArrayList<C35MeetingHandleDto>( entityList.size() );
        for ( C35MeetingHandle c35MeetingHandle : entityList ) {
            list.add( toDto( c35MeetingHandle ) );
        }

        return list;
    }

    @Override
    public C35MeetingHandle toEntity(C35MeetingHandleDto dto) {
        if ( dto == null ) {
            return null;
        }

        C35MeetingHandle c35MeetingHandle = new C35MeetingHandle();

        c35MeetingHandle.setCreateBy( dto.getCreateBy() );
        c35MeetingHandle.setCreateTime( dto.getCreateTime() );
        c35MeetingHandle.setUpdateTime( dto.getUpdateTime() );
        c35MeetingHandle.setUpdatedBy( dto.getUpdatedBy() );
        c35MeetingHandle.setCorrelationId( dto.getCorrelationId() );
        c35MeetingHandle.setCorrelationName( dto.getCorrelationName() );
        c35MeetingHandle.setId( dto.getId() );
        c35MeetingHandle.setMeeting( c35MeetingDtoToC35Meeting( dto.getMeeting() ) );
        c35MeetingHandle.setModuleName( dto.getModuleName() );
        c35MeetingHandle.setStatus( dto.getStatus() );
        c35MeetingHandle.setUserName( dto.getUserName() );

        return c35MeetingHandle;
    }

    @Override
    public List<C35MeetingHandle> toEntity(List<C35MeetingHandleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<C35MeetingHandle> list = new ArrayList<C35MeetingHandle>( dtoList.size() );
        for ( C35MeetingHandleDto c35MeetingHandleDto : dtoList ) {
            list.add( toEntity( c35MeetingHandleDto ) );
        }

        return list;
    }

    protected C35MeetingDto c35MeetingToC35MeetingDto(C35Meeting c35Meeting) {
        if ( c35Meeting == null ) {
            return null;
        }

        C35MeetingDto c35MeetingDto = new C35MeetingDto();

        c35MeetingDto.setCreateBy( c35Meeting.getCreateBy() );
        c35MeetingDto.setCreateTime( c35Meeting.getCreateTime() );
        c35MeetingDto.setUpdateTime( c35Meeting.getUpdateTime() );
        c35MeetingDto.setUpdatedBy( c35Meeting.getUpdatedBy() );
        c35MeetingDto.setAddress( c35Meeting.getAddress() );
        c35MeetingDto.setEndTime( c35Meeting.getEndTime() );
        c35MeetingDto.setId( c35Meeting.getId() );
        c35MeetingDto.setStartTime( c35Meeting.getStartTime() );
        c35MeetingDto.setStatus( c35Meeting.getStatus() );
        c35MeetingDto.setSubject( c35Meeting.getSubject() );
        c35MeetingDto.setType( c35Meeting.getType() );
        c35MeetingDto.setUserIds( c35Meeting.getUserIds() );
        c35MeetingDto.setViewUsers( userMapperUtil.toConvertToSimpleUserDto( c35Meeting.getViewUsers() ) );

        return c35MeetingDto;
    }

    protected C35Meeting c35MeetingDtoToC35Meeting(C35MeetingDto c35MeetingDto) {
        if ( c35MeetingDto == null ) {
            return null;
        }

        C35Meeting c35Meeting = new C35Meeting();

        c35Meeting.setCreateBy( c35MeetingDto.getCreateBy() );
        c35Meeting.setCreateTime( c35MeetingDto.getCreateTime() );
        c35Meeting.setUpdateTime( c35MeetingDto.getUpdateTime() );
        c35Meeting.setUpdatedBy( c35MeetingDto.getUpdatedBy() );
        c35Meeting.setAddress( c35MeetingDto.getAddress() );
        c35Meeting.setEndTime( c35MeetingDto.getEndTime() );
        c35Meeting.setId( c35MeetingDto.getId() );
        c35Meeting.setStartTime( c35MeetingDto.getStartTime() );
        c35Meeting.setStatus( c35MeetingDto.getStatus() );
        c35Meeting.setSubject( c35MeetingDto.getSubject() );
        c35Meeting.setType( c35MeetingDto.getType() );
        c35Meeting.setUserIds( c35MeetingDto.getUserIds() );
        c35Meeting.setViewUsers( userMapperUtil.toConvertToUserName( c35MeetingDto.getViewUsers() ) );

        return c35Meeting;
    }
}
